//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/assets/freeparkingoverlay/inc/FreeparkingManager.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingOverlay.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingUtils.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/imgui/inc/imgui_manager.h"
#include "cc/sm/viewmode/inc/ViewModeStateMachine.h"
#include "cc/views/planview/inc/PlanView.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/animation/inc/AnimationManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

void FreeparkingManager::getSlitherStartEndPoint()
{
    if (!isInsideViewport(&m_hmiData, m_viewport, m_horizontalPad))
    {
        m_SlitheringFlag = false;
        return;
    }

    if ((m_touchStatus == TouchStatus::Invalid || m_touchStatus == TouchStatus::Up) &&
        (m_twoFingerTouchStatus == TwoFingerTouchStatus::TwoFingerInvalid ||
         m_twoFingerTouchStatus == TwoFingerTouchStatus::TwoFingerEnd))
    {
        m_twoFingerState.active = false;
        m_SlitheringFlag        = false;
    }

    if (m_touchStatus == TouchStatus::Move || m_touchStatus == TouchStatus::Down)
    {
        m_SlitherPos.EndPos.x() = static_cast<vfc::float32_t>(m_hmiData.m_huX);
        m_SlitherPos.EndPos.y() = static_cast<vfc::float32_t>(m_hmiData.m_huY);
        if (!m_SlitheringFlag)
        {
            m_SlitherPos.StartPos.x() = static_cast<vfc::float32_t>(m_hmiData.m_huX);
            m_SlitherPos.StartPos.y() = static_cast<vfc::float32_t>(m_hmiData.m_huY);
            m_SlitherBeginPos         = m_SlitherPos.StartPos;
            m_SlitheringFlag          = true;
        }
    }

    static TwoFingerTouchStatus l_preTwoFingerTouchStatus = TwoFingerTouchStatus::TwoFingerInvalid;
    if (m_twoFingerTouchStatus == TwoFingerTouchStatus::TwoFingerStart ||
        (m_twoFingerTouchStatus == TwoFingerTouchStatus::TwoFingerMove &&
         l_preTwoFingerTouchStatus != TwoFingerTouchStatus::TwoFingerMove) &&
            (isInTranslationArea(
                m_FrontLeft_vertex_screen,
                m_FrontRight_vertex_sreen,
                m_RearLeft_vertex_screen,
                m_RearRight_vertex_screen,
                osg::Vec2f(m_hmiData.m_huTwoFinger1X, m_hmiData.m_huTwoFinger1Y),
                osg::Vec2f(m_hmiData.m_huTwoFinger2X, m_hmiData.m_huTwoFinger2Y))))
    {
        XLOG_INFO(g_AppContext, "Two fingers are all in parking slot, start handling two fingers");
        m_twoFingerState.active     = true;
        m_twoFingerState.start[0]   = osg::Vec2f(m_hmiData.m_huTwoFinger1X, m_hmiData.m_huTwoFinger1Y);
        m_twoFingerState.start[1]   = osg::Vec2f(m_hmiData.m_huTwoFinger2X, m_hmiData.m_huTwoFinger2Y);
        m_twoFingerState.current[0] = m_twoFingerState.start[0];
        m_twoFingerState.current[1] = m_twoFingerState.start[1];

        const osg::Vec2f center(m_Center_vertex_screen.x(), m_Center_vertex_screen.y());
        const osg::Vec2f vecStart       = (m_twoFingerState.start[0] - center) - (m_twoFingerState.start[1] - center);
        m_twoFingerState.initialAngle   = std::atan2(vecStart.y(), vecStart.x());
        m_twoFingerState.initialSlotYaw = m_SlotOrientation.m_yawAngleRaw;
    }
    else if (m_twoFingerTouchStatus == TwoFingerTouchStatus::TwoFingerMove)
    {
        XLOG_INFO(
            g_AppContext, " Finger 1 Pos: (" << m_hmiData.m_huTwoFinger1X << ", " << m_hmiData.m_huTwoFinger1Y << ")");
        XLOG_INFO(
            g_AppContext, "Finger 2 Pos: (" << m_hmiData.m_huTwoFinger2X << ", " << m_hmiData.m_huTwoFinger2Y << ")");
        if (m_twoFingerState.active)
        {
            XLOG_INFO(
                g_AppContext,
                "2finger Actived, initialAngle: " << m_twoFingerState.initialAngle
                                                        << ", initialSlotYaw: " << m_twoFingerState.initialSlotYaw);
            if (m_hmiData.m_huTwoFinger1X != 0 || m_hmiData.m_huTwoFinger1Y != 0 || m_hmiData.m_huTwoFinger2X != 0 ||
                m_hmiData.m_huTwoFinger2Y != 0)
            {
                // Store previous positions to calculate velocity if needed
                const osg::Vec2f prevPos1 = m_twoFingerState.current[0];
                const osg::Vec2f prevPos2 = m_twoFingerState.current[1];

                // Update current positions
                m_twoFingerState.current[0] = osg::Vec2f(m_hmiData.m_huTwoFinger1X, m_hmiData.m_huTwoFinger1Y);
                m_twoFingerState.current[1] = osg::Vec2f(m_hmiData.m_huTwoFinger2X, m_hmiData.m_huTwoFinger2Y);

                // Check for large jumps in finger positions which could indicate tracking issues
                constexpr float MAX_JUMP_DISTANCE = 50.0f; // Adjust based on your UI scale
                if ((m_twoFingerState.current[0] - prevPos1).length() > MAX_JUMP_DISTANCE ||
                    (m_twoFingerState.current[1] - prevPos2).length() > MAX_JUMP_DISTANCE)
                {
                    // If there's a large jump, reinitialize the rotation to avoid sudden movements
                    m_twoFingerState.start[0] = m_twoFingerState.current[0];
                    m_twoFingerState.start[1] = m_twoFingerState.current[1];

                    const osg::Vec2f center(m_Center_vertex_screen.x(), m_Center_vertex_screen.y());
                    const osg::Vec2f vecStart =
                        (m_twoFingerState.start[0] - center) - (m_twoFingerState.start[1] - center);
                    m_twoFingerState.initialAngle   = std::atan2(vecStart.y(), vecStart.x());
                    m_twoFingerState.initialSlotYaw = m_SlotOrientation.m_yawAngleRaw;

                    XLOG_INFO(g_AppContext, "Large finger position jump detected - reinitializing rotation state");
                }
            }
        }
        else
        {
        }
    }
    else
    {
    }

    l_preTwoFingerTouchStatus = m_twoFingerTouchStatus;
}

void FreeparkingManager::getSpotCornerCoorandSlitherType()
{
    osg::Vec4f l_FrontLeft_vertex  = osg::Vec4f(0.f, 0.f, 0.f, 0.f);
    osg::Vec4f l_FrontRight_vertex = osg::Vec4f(0.f, 0.f, 0.f, 0.f);
    osg::Vec4f l_RearLeft_vertex   = osg::Vec4f(0.f, 0.f, 0.f, 0.f);
    osg::Vec4f l_RearRight_vertex  = osg::Vec4f(0.f, 0.f, 0.f, 0.f);
    osg::Vec4f l_Center_vertex     = osg::Vec4f(0.f, 0.f, 0.f, 0.f);
    const auto       freeparkingOverlay  = getFreeparkingOverlay(0u);
    osg::Vec4f rotateButtonCenter  = osg::Vec4f{freeparkingOverlay->getRotateButtonCenter(), 1.0f};
    osg::Vec2f l_frontLeft, l_frontRight, l_rearLeft, l_rearRight;
    m_spotRect.updateRectPoints(l_frontLeft, l_rearLeft, l_rearRight, l_frontRight);

    l_FrontLeft_vertex  = osg::Vec4f(l_frontLeft.x(), l_frontLeft.y(), 0.0f, 1.0f);
    l_FrontRight_vertex = osg::Vec4f(l_frontRight.x(), l_frontRight.y(), 0.0f, 1.0f);
    l_RearLeft_vertex   = osg::Vec4f(l_rearLeft.x(), l_rearLeft.y(), 0.0f, 1.0f);
    l_RearRight_vertex  = osg::Vec4f(l_rearRight.x(), l_rearRight.y(), 0.0f, 1.0f);

    l_Center_vertex = osg::Vec4f(m_SlotOrientation.m_CenterPos.x(), m_SlotOrientation.m_CenterPos.y(), 0.0f, 1.0f);
    const osg::ref_ptr<osg::Viewport> osgViewport  = m_viewport->toOsgViewport();
    const auto                        windowMatrix = osgViewport->computeWindowMatrix();

    l_FrontLeft_vertex = l_FrontLeft_vertex * m_MVPmatrix;
    l_FrontLeft_vertex /= l_FrontLeft_vertex.w();
    l_FrontLeft_vertex = l_FrontLeft_vertex * windowMatrix;

    l_FrontRight_vertex = l_FrontRight_vertex * m_MVPmatrix;
    l_FrontRight_vertex /= l_FrontRight_vertex.w();
    l_FrontRight_vertex = l_FrontRight_vertex * windowMatrix;

    l_RearLeft_vertex = l_RearLeft_vertex * m_MVPmatrix;
    l_RearLeft_vertex /= l_RearLeft_vertex.w();
    l_RearLeft_vertex = l_RearLeft_vertex * windowMatrix;

    l_RearRight_vertex = l_RearRight_vertex * m_MVPmatrix;
    l_RearRight_vertex /= l_RearRight_vertex.w();
    l_RearRight_vertex = l_RearRight_vertex * windowMatrix;

    l_Center_vertex = l_Center_vertex * m_MVPmatrix;
    l_Center_vertex /= l_Center_vertex.w();
    l_Center_vertex = l_Center_vertex * windowMatrix;

    rotateButtonCenter = rotateButtonCenter * m_MVPmatrix;
    rotateButtonCenter /= rotateButtonCenter.w();
    rotateButtonCenter = rotateButtonCenter * windowMatrix;

    if (m_horizontalPad)
    {
        l_FrontLeft_vertex.y()  = pc::core::g_systemConf->m_mainViewport.m_size.y() - l_FrontLeft_vertex.y();
        l_FrontRight_vertex.y() = pc::core::g_systemConf->m_mainViewport.m_size.y() - l_FrontRight_vertex.y();
        l_RearLeft_vertex.y()   = pc::core::g_systemConf->m_mainViewport.m_size.y() - l_RearLeft_vertex.y();
        l_RearRight_vertex.y()  = pc::core::g_systemConf->m_mainViewport.m_size.y() - l_RearRight_vertex.y();
        l_Center_vertex.y()     = pc::core::g_systemConf->m_mainViewport.m_size.y() - l_Center_vertex.y();
        rotateButtonCenter.y()  = pc::core::g_systemConf->m_mainViewport.m_size.y() - rotateButtonCenter.y();
    }
    else
    {
        std::swap(l_FrontLeft_vertex.x(), l_FrontLeft_vertex.y());
        std::swap(l_FrontRight_vertex.x(), l_FrontRight_vertex.y());
        std::swap(l_RearLeft_vertex.x(), l_RearLeft_vertex.y());
        std::swap(l_RearRight_vertex.x(), l_RearRight_vertex.y());
        std::swap(l_Center_vertex.x(), l_Center_vertex.y());
        std::swap(rotateButtonCenter.x(), rotateButtonCenter.y());
    }
    m_FrontLeft_vertex_screen           = osg::Vec3f{l_FrontLeft_vertex.x(), l_FrontLeft_vertex.y(), 0.0f};
    m_FrontRight_vertex_sreen           = osg::Vec3f{l_FrontRight_vertex.x(), l_FrontRight_vertex.y(), 0.0f};
    m_RearLeft_vertex_screen            = osg::Vec3f{l_RearLeft_vertex.x(), l_RearLeft_vertex.y(), 0.0f};
    m_RearRight_vertex_screen           = osg::Vec3f{l_RearRight_vertex.x(), l_RearRight_vertex.y(), 0.0f};
    m_Center_vertex_screen              = osg::Vec3f{l_Center_vertex.x(), l_Center_vertex.y(), 0.0f};
    const osg::Vec3f rotateButtonCenterScreen = osg::Vec3f{rotateButtonCenter.x(), rotateButtonCenter.y(), 0.0f};

    // IMGUI_LOG("RotateButton", "m_touchStatus",
    //     m_touchStatus == Down ? "Down" :
    //     m_touchStatus == Move ? "Move" :
    //     m_touchStatus == Up ? "Up" : "Invalid"
    // );
    osg::Vec3f l_rotateStartPos = osg::Vec3f(0.f, 0.f, 0.f);
    l_rotateStartPos.x()        = m_SlitherBeginPos.x();
    l_rotateStartPos.y()        = m_SlitherBeginPos.y();
    IMGUI_LOG(
        "FreeParking",
        "rotateButtonCenterScreen",
        "X: " + std::to_string(rotateButtonCenterScreen.x()) + " Y: " + std::to_string(rotateButtonCenterScreen.y()));
    // IMGUI_LOG("FreeParking", "l_rotateStartPos", "X: " +
    // std::to_string(l_rotateStartPos.x()) + " Y: " +
    // std::to_string(l_rotateStartPos.y()));

    static APSlitherActionType prevSlitherActionType = UNKOWN;
    const bool                       insideViewport        = isInsideViewport(&m_hmiData, m_viewport, m_horizontalPad);

    if (m_twoFingerState.active)
    {
        // const osg::Vec2f worldPos1 =
        // getWorldPointFromScreenPoint(m_twoFingerState.current[0]); const
        // osg::Vec2f worldPos2 =
        // getWorldPointFromScreenPoint(m_twoFingerState.current[1]);
        m_SlitherActionType = prevSlitherActionType;
        XLOG_INFO(
            g_AppContext,
            "Center of FreeParking Slot: (" << m_Center_vertex_screen.x() << ", " << m_Center_vertex_screen.y() << ")");
        XLOG_INFO(
            g_AppContext,
            "Start Pos of finger 1: (" << m_twoFingerState.start[0].x() << ", " << m_twoFingerState.start[0].y()
                                       << ")");
        XLOG_INFO(
            g_AppContext,
            "Start Pos of finger 2: (" << m_twoFingerState.start[1].x() << ", " << m_twoFingerState.start[1].y()
                                       << ")");

        if (m_twoFingerTouchStatus == TwoFingerTouchStatus::TwoFingerMove)
        {
            XLOG_INFO(
                g_AppContext,
                "Current Pos of finger 1: (" << m_twoFingerState.current[0].x() << ", "
                                             << m_twoFingerState.current[0].y() << ")");
            XLOG_INFO(
                g_AppContext,
                "Current Pos of finger 2: (" << m_twoFingerState.current[1].x() << ", "
                                             << m_twoFingerState.current[1].y() << ")");
        }

        m_SlitherActionType = TWO_FINGER_ROTATION;
        return;
    }

    if (!m_SlitheringFlag)
    {
        if (m_touchStatus == Down && (insideViewport) &&
            (pc::util::isInsideCircle(
                 rotateButtonCenterScreen, g_managerSettings->m_rotateButtonRadius, l_rotateStartPos) ||
             (getFreeparkingOverlay(0u)->getSlitherActionType() == ROTATION)))
        {
            getFreeparkingOverlay(0u)->setMousePressed(m_touchStatus == Down || m_touchStatus == Move);
            getFreeparkingOverlay(0u)->setSlitherActionType(ROTATION);
        }
        if (m_touchStatus == Up || !insideViewport)
        {
            getFreeparkingOverlay(0u)->setMousePressed(false);
            getFreeparkingOverlay(0u)->setSlitherActionType(UNKOWN);
        }
        prevSlitherActionType = UNKOWN;
        m_SlitherActionType   = UNKOWN;
        return;
    }

    if (pc::util::isInsideCircle(rotateButtonCenterScreen, g_managerSettings->m_rotateButtonRadius, l_rotateStartPos) &&
        (prevSlitherActionType == UNKOWN))
    {
        m_SlitherActionType   = ROTATION;
        prevSlitherActionType = m_SlitherActionType;
    }
    else if (
        isInTranslationArea(
            m_FrontLeft_vertex_screen,
            m_FrontRight_vertex_sreen,
            m_RearLeft_vertex_screen,
            m_RearRight_vertex_screen,
            m_SlitherPos) &&
        (prevSlitherActionType == UNKOWN))
    {
        m_SlitherActionType   = TRANSLATION;
        prevSlitherActionType = m_SlitherActionType;
    }
    else if (insideViewport && prevSlitherActionType == UNKOWN)
    {
        m_SlitherActionType   = TELEPORTATION;
        prevSlitherActionType = m_SlitherActionType;
    }
    else
    {
        // m_SlitherActionType = UNKOWN;
        m_SlitherActionType = prevSlitherActionType;
    }

    getFreeparkingOverlay(0u)->setMousePressed(true);
    getFreeparkingOverlay(0u)->setSlitherActionType(m_SlitherActionType);
}

void FreeparkingManager::clampRotateAngleInNormalMode()
{
    const vfc::float32_t VALID_RANGE  = osg::DegreesToRadians(g_managerSettings->m_rotateRange);
    const bool                 l_isLeft     = m_SlotOrientation.m_CenterPos.y() > 0.0f;
    const bool                 l_isRight    = !l_isLeft;
    const bool                 l_isParallel = (m_freeparkingType == freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL);
    const bool                 l_isCross    = (m_freeparkingType == freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_CROSS);
    const bool                 l_isDiagonal = (m_freeparkingType == freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_DIAGONAL);

    if (l_isParallel)
    {
        // 0
        m_SlotOrientation.m_yawAngleRaw = pc::util::clamp(
            m_SlotOrientation.m_yawAngleRaw, static_cast<float>(-VALID_RANGE), static_cast<float>(VALID_RANGE));

        if (m_SlotOrientation.m_yawAngleRaw > VALID_RANGE)
        {
            XLOG_INFO(
                g_AppContext,
                "Clamp from " << osg::RadiansToDegrees(m_SlotOrientation.m_yawAngleRaw) << " to "
                              << osg::DegreesToRadians(VALID_RANGE));
            m_SlotOrientation.m_yawAngleRaw = VALID_RANGE;
        }
        if (m_SlotOrientation.m_yawAngleRaw < -VALID_RANGE)
        {
            XLOG_INFO(
                g_AppContext,
                "Clamp from " << osg::RadiansToDegrees(m_SlotOrientation.m_yawAngleRaw) << " to "
                              << osg::DegreesToRadians(-VALID_RANGE));
            m_SlotOrientation.m_yawAngleRaw = -VALID_RANGE;
        }
    }
    if (l_isLeft && l_isCross)
    {
        // -90
        m_SlotOrientation.m_yawAngleRaw = pc::util::clamp(
            m_SlotOrientation.m_yawAngleRaw,
            static_cast<float>(-osg::PI_2 - VALID_RANGE),
            static_cast<float>(-osg::PI_2 + VALID_RANGE));
    }
    if (l_isRight && l_isCross)
    {
        // 90
        m_SlotOrientation.m_yawAngleRaw = pc::util::clamp(
            m_SlotOrientation.m_yawAngleRaw,
            static_cast<float>(osg::PI * 0.5 - VALID_RANGE),
            static_cast<float>(osg::PI * 0.5 + VALID_RANGE));
    }
    if (l_isLeft && l_isDiagonal)
    {
        // -45
        m_SlotOrientation.m_yawAngleRaw = pc::util::clamp(
            m_SlotOrientation.m_yawAngleRaw,
            static_cast<float>(-osg::PI_4 - VALID_RANGE),
            static_cast<float>(-osg::PI_4 + VALID_RANGE));
    }
    if (l_isRight && l_isDiagonal)
    {
        // 45
        m_SlotOrientation.m_yawAngleRaw = pc::util::clamp(
            m_SlotOrientation.m_yawAngleRaw,
            static_cast<float>(osg::PI_4 - VALID_RANGE),
            static_cast<float>(osg::PI_4 + VALID_RANGE));
    }
}

void FreeparkingManager::UpdateSlotOrientation()
{
    if (m_SlitherActionType == TWO_FINGER_ROTATION)
    {
        XLOG_INFO(g_AppContext, "===== Start TWO_FINGER_ROTATION Computing =====");

        XLOG_INFO(
            g_AppContext,
            "TWO_FINGER_ROTATION: initialSlotYaw: " << m_twoFingerState.initialSlotYaw << " rad ("
                                        << osg::RadiansToDegrees(m_twoFingerState.initialSlotYaw) << " Deg)");

        const osg::Vec2f center(m_Center_vertex_screen.x(), m_Center_vertex_screen.y());

        // Calculate vectors from center to finger positions
        osg::Vec2f finger1StartVector   = m_twoFingerState.start[0] - center;
        osg::Vec2f finger2StartVector   = m_twoFingerState.start[1] - center;
        osg::Vec2f finger1CurrentVector = m_twoFingerState.current[0] - center;
        osg::Vec2f finger2CurrentVector = m_twoFingerState.current[1] - center;

        // Calculate angles and improve angle delta computation
        const float startAngle1   = std::atan2(finger1StartVector.y(), finger1StartVector.x());
        const float currentAngle1 = std::atan2(finger1CurrentVector.y(), finger1CurrentVector.x());
        const float startAngle2   = std::atan2(finger2StartVector.y(), finger2StartVector.x());
        const float currentAngle2 = std::atan2(finger2CurrentVector.y(), finger2CurrentVector.x());

        // Calculate angle deltas with proper normalization
        float deltaAngle1 = currentAngle1 - startAngle1;
        float deltaAngle2 = currentAngle2 - startAngle2;
        // Normalize angles to [-Pi, Pi]
        if (deltaAngle1 > osg::PI)
        {
            deltaAngle1 -= 2.0f * static_cast<vfc::float32_t>(osg::PI);
        }
        else if (deltaAngle1 < -osg::PI)
        {
            deltaAngle1 += 2.0f * static_cast<vfc::float32_t>(osg::PI);
        }
        else
        {

        }

        if (deltaAngle2 > osg::PI)
        {
            deltaAngle2 -= 2.0f * static_cast<vfc::float32_t>(osg::PI);
        }
        else if (deltaAngle2 < -osg::PI)
        {
            deltaAngle2 += 2.0f * static_cast<vfc::float32_t>(osg::PI);
        }
        else
        {

        }


        // Use a weighted average of both deltas for smoother rotation
        // Consider finger distances from center for better weighting
        const float len1Start   = finger1StartVector.length();
        const float len2Start   = finger2StartVector.length();
        const float len1Current = finger1CurrentVector.length();
        const float len2Current = finger2CurrentVector.length();

        // Weight based on average distance from center
        const float weight1     = (len1Start + len1Current) * 0.5f;
        const float weight2     = (len2Start + len2Current) * 0.5f;
        const float totalWeight = weight1 + weight2;

        // Weighted average delta angle - gives more influence to fingers farther from center
        float weightedDeltaAngle = 0.0f;
        if (totalWeight > 0.0001f)
        { // Avoid division by zero
            weightedDeltaAngle = (deltaAngle1 * weight1 + deltaAngle2 * weight2) / totalWeight;
        }
        else
        {
            weightedDeltaAngle = (deltaAngle1 + deltaAngle2) * 0.5f;
        }

        // Apply rotation with correct direction and use current finger positions for next calculation
        m_SlotOrientation.m_yawAngleRaw = m_twoFingerState.initialSlotYaw - weightedDeltaAngle;

        // Update start positions to current for continuous rotation
        m_twoFingerState.start[0]       = m_twoFingerState.current[0];
        m_twoFingerState.start[1]       = m_twoFingerState.current[1];
        m_twoFingerState.initialSlotYaw = m_SlotOrientation.m_yawAngleRaw;

        // Clamp and normalize angle
        clampAngleRad(m_SlotOrientation.m_yawAngleRaw);
        if (!m_360Rotate)
        {
            clampRotateAngleInNormalMode();
        }

        XLOG_INFO(
            g_AppContext,
            "TWO_FINGER_ROTATION: CurrentAngle of finger 2: " << m_SlotOrientation.m_yawAngleRaw << " rad ("
                                        << osg::RadiansToDegrees(m_SlotOrientation.m_yawAngleRaw) << " Deg)");
        XLOG_INFO(g_AppContext, "===== Finish TWO_FINGER_ROTATION Computing =====");
    }

    if (m_SlitherActionType == ROTATION)
    {
        XLOG_INFO(
            g_AppContext,
            "m_SlitherActionType == ROTATION,  m_SlitherPos.StartPos.x()"
                << m_SlitherPos.StartPos.x() << ", m_SlitherPos.StartPos.y()" << m_SlitherPos.StartPos.y()
                << "m_SlitherPos.EndPos.x()" << m_SlitherPos.EndPos.x() << ", m_SlitherPos.EndPos.y()"
                << m_SlitherPos.EndPos.y() << ", m_SlitheringFlag" << static_cast<int>(m_SlitheringFlag));
        // the angle is between negativ Y direction in screen coordinate, anti-clock
        // wise is positive angle, rotate around geometry center
        // ------------------------------------------------------------------> +x
        // |                  C ________ B
        // |     start         |        |              end
        // \                     (add this to fix qac) |                   | C----|
        // ____ l_a            \   C    \     _______ l_a' |                   |
        // \   |      |                \    \   \           | |                   |
        // \  |      | l_b             \     \  \          |  l_b'
        // \/                  |      \ |      | \       \\         | +y
        // D|_______\|A     |                   \                         (add this
        // to fix qac)

        vfc::float32_t l_a_Start            = 0.0f;
        vfc::float32_t l_b_Start            = 0.0f;
        vfc::float32_t l_a_End              = 0.0f;
        vfc::float32_t l_b_End              = 0.0f;
        vfc::float32_t l_rotate_angle_start = 0.0f;
        vfc::float32_t l_rotate_angle_end   = 0.0f;

        l_a_Start            = +m_Center_vertex_screen.x() - m_SlitherPos.StartPos.x();
        l_b_Start            = +m_Center_vertex_screen.y() - m_SlitherPos.StartPos.y();
        l_rotate_angle_start = std::atan2(l_b_Start, l_a_Start);

        l_a_End            = +m_Center_vertex_screen.x() - m_SlitherPos.EndPos.x();
        l_b_End            = +m_Center_vertex_screen.y() - m_SlitherPos.EndPos.y();
        l_rotate_angle_end = std::atan2(l_b_End, l_a_End);

        if (vfc::notEqual(l_rotate_angle_end, l_rotate_angle_start))
        {
            m_SlotOrientation.m_yawAngleRaw -= (l_rotate_angle_end - l_rotate_angle_start);
            // XLOG_INFO(g_AppContext, "Rotated Angle: " <<
            // osg::RadiansToDegrees(m_SlotOrientation.m_yawAngleRaw));
            m_SlitherPos.StartPos.x() = m_SlitherPos.EndPos.x();
            m_SlitherPos.StartPos.y() = m_SlitherPos.EndPos.y();
            clampAngleRad(m_SlotOrientation.m_yawAngleRaw);
            // XLOG_INFO(g_AppContext, "Clamped Angle: " <<
            // osg::RadiansToDegrees(m_SlotOrientation.m_yawAngleRaw));
            if (!m_360Rotate)
            {
                clampRotateAngleInNormalMode();
            }
        }
    }

    //! TRANSLATION
    if (m_SlitherActionType == TRANSLATION)
    {
        const osg::Vec2f mouseWorldEnd   = getWorldPointFromScreenPoint(m_SlitherPos.EndPos);
        const osg::Vec2f mouseWorldStart = getWorldPointFromScreenPoint(m_SlitherPos.StartPos);
        const osg::Vec2f mouseDiff       = mouseWorldEnd - mouseWorldStart;
        m_SlotOrientation.m_CenterPos += (mouseWorldEnd - mouseWorldStart);
        m_SlitherPos.StartPos = m_SlitherPos.EndPos;
    }

    if (m_SlitherActionType == TELEPORTATION)
    {
        const int yOffset = pc::core::g_systemConf->m_mainViewport.m_size.y()
                  - m_viewport->m_origin.y()
                  - m_viewport->m_size.y();

        const osg::Vec2f viewportOrigin{
            static_cast<float>(m_viewport->m_origin.x()),
            static_cast<float>(yOffset)
        };
        // const osg::Vec2f viewportOrigin = {
        //     static_cast<float>(m_viewport->m_origin.x()),
        //     static_cast<float>(
        //         pc::core::g_systemConf->m_mainViewport.m_size.y() - m_viewport->m_origin.y() - m_viewport->m_size.y())};
        const osg::Vec2f mouseEnd = getWorldPointFromScreenPoint(m_SlitherPos.EndPos - viewportOrigin);
        // IMGUI_LOG("FreeParking", "mouseEnd", std::to_string(mouseEnd.x()) + " " + std::to_string(mouseEnd.y()));
        // IMGUI_LOG("FreeParking", "slotCenterPos", std::to_string(m_SlotOrientation.m_CenterPos.x()) + " " +
        // std::to_string(m_SlotOrientation.m_CenterPos.y()));
        m_SlotOrientation.m_CenterPos = (mouseEnd);
    }

    const bool l_isLeft  = m_SlotOrientation.m_CenterPos.y() > 0.0f;
    const bool l_isRight = !l_isLeft;
    if (!m_360Rotate && (m_previousIsLeft != l_isLeft) && (m_SlitherActionType != ROTATION))
    {
        if (l_isLeft && isSlotHeadingLeft((m_SlotOrientation.m_yawAngleRaw)))
        {
            m_SlotOrientation.m_yawAngleRaw = static_cast<vfc::float32_t>(2.0 * osg::PI - m_SlotOrientation.m_yawAngleRaw);
        }
        else if (l_isRight && isSlotHeadingRight((m_SlotOrientation.m_yawAngleRaw)))
        {
            m_SlotOrientation.m_yawAngleRaw = static_cast<vfc::float32_t>(2.0 * osg::PI - m_SlotOrientation.m_yawAngleRaw);
        }
        else
        {
        }
    }

    // if (m_SlitherActionType == UNKOWN && (m_prevSlitherActionType == TRANSLATION || m_prevSlitherActionType ==
    // TELEPORTATION))
    if (m_SlitherActionType != ROTATION && m_SlitherActionType != TWO_FINGER_ROTATION && m_isSlotMovedByUser)
    {
        slotBoundingBoxCheckAndCorrect();
    }
}

osg::Vec2f FreeparkingManager::getWorldPointFromScreenPoint(osg::Vec2f f_screenPoint)
{
    vfc::float32_t ndc_x = 0.f;
    vfc::float32_t ndc_y = 0.f;
    if (m_horizontalPad)
    {
        ndc_x = static_cast<vfc::float32_t>((2.0 * (f_screenPoint.x())) / m_viewport->m_size.x() - 1.0f);
        ndc_y = static_cast<vfc::float32_t>((2.0 * (m_viewport->m_size.y() - f_screenPoint.y())) / m_viewport->m_size.y() - 1.0f);
    }
    else
    {
        ndc_x = static_cast<vfc::float32_t>((2.0 * (f_screenPoint.y())) / m_viewport->m_size.x() - 1.0f);
        ndc_y = static_cast<vfc::float32_t>((2.0 * (f_screenPoint.x())) / m_viewport->m_size.y() - 1.0f);
    }
    const osg::Vec4f   clipCoords  = {ndc_x, ndc_y, -1.0f, 1.0f};
    const osg::Matrixd inverseMVP  = osg::Matrixf::inverse(m_MVPmatrix);
    osg::Vec4f   worldCoords = clipCoords * inverseMVP;
    worldCoords /= worldCoords.w();
    return osg::Vec2f{worldCoords.x(), worldCoords.y()};
}

void FreeparkingManager::slotBoundingBoxCheckAndCorrect()
{
    const bool l_isHeadingUp    = isSlotHeadingUp(m_SlotOrientation.m_yawAngleRaw);
    const bool l_isHeadingDown  = isSlotHeadingDown(m_SlotOrientation.m_yawAngleRaw);
    const bool l_isHeadingLeft  = isSlotHeadingLeft(m_SlotOrientation.m_yawAngleRaw);
    const bool l_isHeadingRight = isSlotHeadingRight(m_SlotOrientation.m_yawAngleRaw);

    const auto yOffset = static_cast<int>(
    pc::core::g_systemConf->m_mainViewport.m_size.y()
    - m_viewport->m_origin.y()
    - m_viewport->m_size.y());

    const osg::Vec2f viewportOrigin{
        static_cast<float>(m_viewport->m_origin.x()),
        static_cast<float>(yOffset)
    };
    // const osg::Vec2f viewportOrigin = {
    //     static_cast<float>(m_viewport->m_origin.x()),
    //     static_cast<float>(
    //         pc::core::g_systemConf->m_mainViewport.m_size.y() - m_viewport->m_origin.y() - m_viewport->m_size.y())};
    m_viewportTopLeft    = getWorldPointFromScreenPoint(viewportOrigin);
    m_viewportBottomLeft = getWorldPointFromScreenPoint(viewportOrigin + osg::Vec2f(0.0f, m_viewport->m_size.y()));
    m_viewportBottomRight =
        getWorldPointFromScreenPoint(viewportOrigin + osg::Vec2f(m_viewport->m_size.x(), m_viewport->m_size.y()));
    m_viewportTopRight = getWorldPointFromScreenPoint(viewportOrigin + osg::Vec2f(m_viewport->m_size.x(), 0.0f));

    osg::BoundingBox boundingBox;
    boundingBox.expandBy(osg::Vec3f{m_viewportTopLeft * g_managerSettings->m_viewportBoundbackScale, 0.0f});
    boundingBox.expandBy(osg::Vec3f{m_viewportBottomLeft * g_managerSettings->m_viewportBoundbackScale, 0.0f});
    boundingBox.expandBy(osg::Vec3f{m_viewportBottomRight * g_managerSettings->m_viewportBoundbackScale, 0.0f});
    boundingBox.expandBy(osg::Vec3f{m_viewportTopRight * g_managerSettings->m_viewportBoundbackScale, 0.0f});

    m_SlotOrientation.m_CenterPos.x() =
        pc::util::clamp(m_SlotOrientation.m_CenterPos.x(), boundingBox.xMin(), boundingBox.xMax());
    m_SlotOrientation.m_CenterPos.y() =
        pc::util::clamp(m_SlotOrientation.m_CenterPos.y(), boundingBox.yMin(), boundingBox.yMax());

    osg::Vec2f l_topReach =
        pc::util::rotate(
            osg::Vec2f{m_spotSize.x(), 0.0f} * (g_freeparkingRotateButtonSettings->m_offsetPercentage * 0.5f + 0.5f),
            m_SlotOrientation.m_yawAngleRaw) +
        m_centerPosition;
    osg::Vec2f l_bottomReach =
        pc::util::rotate(
            osg::Vec2f{-m_spotSize.x(), 0.0f} * (g_freeparkingRotateButtonSettings->m_offsetPercentage * 0.5f + 0.5f),
            m_SlotOrientation.m_yawAngleRaw) +
        m_centerPosition;

    // osg::Vec2f l_topReach =
    //     pc::util::rotate(
    //         osg::Vec2f{m_spotSize.x(), 0.0f} * (g_freeparkingRotateButtonSettings->m_offsetPercentage * 0.5f + 0.5f),
    //         m_SlotOrientation.m_yawAngleRaw) +
    //     m_SlotOrientation.m_CenterPos;

    const auto flipHorizontal = [](vfc::float32_t& f_angleRad) { f_angleRad = static_cast<vfc::float32_t>(osg::PI * 2.0 - f_angleRad); };
    const auto flipVertical   = [](vfc::float32_t& f_angleRad) { f_angleRad = static_cast<vfc::float32_t>(osg::PI - f_angleRad); };

    bool topReachCollideTop    = false;
    bool topReachCollideBottom = false;
    bool topReachCollideLeft   = false;
    bool topReachCollideRight  = false;

    bool bottomReachCollideTop    = false;
    bool bottomReachCollideBottom = false;
    bool bottomReachCollideLeft   = false;
    bool bottomReachCollideRight  = false;

    bool topCollide    = false;
    bool bottomCollide = false;

    FreeparkingOverlay* const l_freeparkingOverlay = getFreeparkingOverlay(0u);
    const RotateButton::ButtonDirection currentButtonDirection = l_freeparkingOverlay->getRotateButtonDirection();

    if (m_360Rotate)
    {
        // clang-format off
        if ((l_topReach.x() > boundingBox.xMax()) && l_isHeadingUp) { topReachCollideTop = true; }
        if ((l_topReach.x() < boundingBox.xMin()) && l_isHeadingDown) { topReachCollideBottom = true; }
        if ((l_topReach.y() > boundingBox.yMax()) && l_isHeadingLeft) { topReachCollideLeft = true; }
        if ((l_topReach.y() < boundingBox.yMin()) && l_isHeadingRight) { topReachCollideRight = true; }

        if ((l_bottomReach.x() > boundingBox.xMax()) && l_isHeadingDown) { bottomReachCollideTop = true; }
        if ((l_bottomReach.x() < boundingBox.xMin()) && l_isHeadingUp) { bottomReachCollideBottom = true; }
        if ((l_bottomReach.y() > boundingBox.yMax()) && l_isHeadingRight) { bottomReachCollideLeft = true; }
        if ((l_bottomReach.y() < boundingBox.yMin()) && l_isHeadingLeft) { bottomReachCollideRight = true; }
        // clang-format on

        topCollide = topCollide || topReachCollideTop;
        topCollide = topCollide || topReachCollideBottom;
        topCollide = topCollide || topReachCollideLeft;
        topCollide = topCollide || topReachCollideRight;
        bottomCollide = bottomCollide || bottomReachCollideTop;
        bottomCollide = bottomCollide || bottomReachCollideBottom;
        bottomCollide = bottomCollide || bottomReachCollideLeft;
        bottomCollide = bottomCollide || bottomReachCollideRight;

        if (topCollide && bottomCollide)
        {
            // flipVertical(m_SlotOrientation.m_yawAngleRaw); // flip horizontal
            // Do nothing
        }
        else
        {
            l_freeparkingOverlay->setRotateButtonDirection(RotateButton::ButtonDirection::TOP);
            const bool currentTop = currentButtonDirection == RotateButton::ButtonDirection::TOP;
            const bool currentBottom = !currentTop;

            if (topCollide && currentTop)
            {
                // l_freeparkingOverlay->setRotateButtonDirection(RotateButton::ButtonDirection::BOTTOM);
                // if (topReachCollideLeft || topReachCollideRight)
                // {
                //     flipHorizontal(m_SlotOrientation.m_yawAngleRaw);
                // }
                // if (topReachCollideBottom || topReachCollideTop)
                // {
                //     flipVertical(m_SlotOrientation.m_yawAngleRaw);
                // }
                m_SlotOrientation.m_yawAngleRaw += static_cast<vfc::float32_t>(osg::PI);
            }
            // else if (bottomCollide && currentBottom)
            // {
            //     l_freeparkingOverlay->setRotateButtonDirection(RotateButton::ButtonDirection::TOP);
            // }
            // else
            // {
            // }

            // if (topReachCollideTop && l_isHeadingUp && currentTop)
            // {
            //     l_freeparkingOverlay->setRotateButtonDirection(RotateButton::ButtonDirection::BOTTOM);
            // }
            // else if (topReachCollideBottom && l_isHeadingDown && currentTop)
            // {
            //     l_freeparkingOverlay->setRotateButtonDirection(RotateButton::ButtonDirection::BOTTOM);
            // }
            // else if (topReachCollideLeft && l_isHeadingLeft && currentTop)
            // {
            //     l_freeparkingOverlay->setRotateButtonDirection(RotateButton::ButtonDirection::BOTTOM);
            // }
            // else if (topReachCollideRight && l_isHeadingRight && currentTop)
            // {
            //     l_freeparkingOverlay->setRotateButtonDirection(RotateButton::ButtonDirection::BOTTOM);
            // }
            // else
            // {
            //     // do nothing
            // }

            // if ((topReachCollideTop && l_isHeadingUp) || (topReachCollideBottom && l_isHeadingDown) ||
            //     (topReachCollideLeft && l_isHeadingLeft) || (topReachCollideRight && l_isHeadingRight))
            // {
            //     // m_SlotOrientation.m_yawAngleRaw += osg::PI; // flip 180
            //     if (currentButtonDirection == RotateButton::ButtonDirection::TOP)
            //     {
            //         l_freeparkingOverlay->setRotateButtonDirection(RotateButton::ButtonDirection::BOTTOM);
            //     }
            //     else
            //     {
            //         l_freeparkingOverlay->setRotateButtonDirection(RotateButton::ButtonDirection::TOP);
            //     }
            // }
        }

        clampAngleRad(m_SlotOrientation.m_yawAngleRaw);
    }
}

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc
