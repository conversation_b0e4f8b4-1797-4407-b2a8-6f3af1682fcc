//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/trajectory/inc/TrailerAssistLine.h"
#include "cc/assets/trajectory/inc/Helper.h"

#include "osg/Texture2D"
#include "osgDB/ReadFile"
#include "osgDB/WriteFile"

#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Quantization.h"

/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3804
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace trajectory
{

// This multiplier is to make the quad stripe bigger to have enough room for the blur on the downsampled mipmaps
constexpr vfc::float32_t g_blurMul = 1.2f; // (1 <= )


/**
 * Calculates the parameter for each track regarding to the f_side it is and pass them to Frame.cpp to generate the vertices for the geometry
 * @param f_side: If it is for the tyres on the right or left side
 * @param f_trajParams: General const parameters for all trackTypes that are given by TrajectoryCodingParams with CodingParamters.xml
 * @param f_numOfVerts: Number of vertices to generate the geometry afterwards
 */
TrailerAssistLine::TrailerAssistLine(
  pc::core::Framework* f_framework,
  vfc::float32_t f_height,
  const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
  vfc::uint32_t f_numOfVerts)
  : GeneralTrajectoryLine(
      f_framework,
      cc::assets::trajectory::commontypes::Middle_enm /* DUMMY */,
      2u,
      f_height,
      f_trajParams,
      true)
  , mc_numOfVerts(f_numOfVerts)
{
  m_geometry->addPrimitiveSet(new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES)));  // PRQA S 3804  // PRQA S 3803

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray());
  l_colors->setBinding(osg::Array::BIND_PER_VERTEX);

  osg::Vec2ubArray* const l_texCoords = new osg::Vec2ubArray;
  l_texCoords->setNormalize(true);
  l_texCoords->reserve(2u * mc_numOfVerts);
  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < mc_numOfVerts; l_vertexIndex++)
  {
    l_texCoords->push_back(osg::Vec2ub(0u, 127u));
  }
  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < mc_numOfVerts; l_vertexIndex++)
  {
    l_texCoords->push_back(osg::Vec2ub(255u, 127u));
  }
  m_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);

  const vfc::float32_t lc_halfGradientWidth = std::abs(m_trajParams.GradientWidth) * 0.5f;
  const vfc::float32_t lc_halfWholeWidth    =  m_trajParams.THTraj_Width * 0.5f; // Half of the whole width of the Trailer assist line
  const vfc::float32_t lc_halfGeometryWidth = (lc_halfWholeWidth + lc_halfGradientWidth) * g_blurMul;
  m_lineGeometryWidth              = lc_halfGeometryWidth * 2.0f;
}


TrailerAssistLine::~TrailerAssistLine() =default;


osg::Image* TrailerAssistLine::create1DTexture() const  // PRQA S 6043
{
  constexpr vfc::uint32_t  lc_imageWidth  = 256u; // Image width in pixels.
  constexpr vfc::uint32_t  lc_imageHeight = 1u;   // Image height in pixels.
  constexpr vfc::uint32_t  lc_imageDepth  = 1u;   // Image depth in pixels, in case of a 3D image.
  constexpr vfc::float32_t         lc_imageGeometryWidth = static_cast<vfc::float32_t>(lc_imageWidth - 1);

  const vfc::float32_t lc_halfGradientWidth = std::abs(m_trajParams.GradientWidth) * 0.5f;
  const vfc::float32_t lc_halfWholeWidth = m_trajParams.WheelTrack_Width_Whole * 0.5f; // Half of the whole width of the wheel track
  const vfc::float32_t lc_halfGeometryWidth = (lc_halfWholeWidth + lc_halfGradientWidth) * g_blurMul;

  std::array<vfc::float32_t, 4> l_absDistancesFromCenter; // 0..3: From outermost to innermost
  l_absDistancesFromCenter[0u] = lc_halfWholeWidth + lc_halfGradientWidth;
  l_absDistancesFromCenter[1u] = lc_halfWholeWidth - lc_halfGradientWidth;
  l_absDistancesFromCenter[2u] = lc_halfWholeWidth - m_trajParams.WheelTrack_Width_BorderLine + lc_halfGradientWidth;
  l_absDistancesFromCenter[3u] = lc_halfWholeWidth - m_trajParams.WheelTrack_Width_BorderLine - lc_halfGradientWidth;

  std::array<vfc::float32_t, 8> l_normalizedPositions;    // 0..7: From left to right
  l_normalizedPositions[0u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[0u]) / m_lineGeometryWidth;
  l_normalizedPositions[1u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[1u]) / m_lineGeometryWidth;
  l_normalizedPositions[2u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[2u]) / m_lineGeometryWidth;
  l_normalizedPositions[3u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[3u]) / m_lineGeometryWidth;
  l_normalizedPositions[4u] = 1.0f - l_normalizedPositions[3u];
  l_normalizedPositions[5u] = 1.0f - l_normalizedPositions[2u];
  l_normalizedPositions[6u] = 1.0f - l_normalizedPositions[1u];
  l_normalizedPositions[7u] = 1.0f - l_normalizedPositions[0u];

  osg::Vec4ub l_lineColor_Inside;
  osg::Vec4ub l_lineColor_BorderLine;

  l_lineColor_Inside = pc::util::osgx::toVec4ub(m_trajParams.THTraj_Color);
  l_lineColor_BorderLine = pc::util::osgx::toVec4ub(m_trajParams.THTrajBorder_Color);

  osg::Vec4ub l_lineColor_Outside = l_lineColor_BorderLine;
  l_lineColor_Outside.a() = 0u;

  osg::Image* const l_image = new osg::Image;
  l_image->allocateImage(static_cast<vfc::int32_t>(lc_imageWidth), static_cast<vfc::int32_t>(lc_imageHeight), static_cast<vfc::int32_t>(lc_imageDepth), static_cast<GLenum>(GL_RGBA), static_cast<GLenum>(GL_UNSIGNED_BYTE));
  osg::Vec4ub* l_data = reinterpret_cast<osg::Vec4ub*> (l_image->data());
  for (vfc::uint32_t x = 0u; x < lc_imageWidth; ++x)
  {
    const vfc::float32_t l_x_normalized = static_cast<vfc::float32_t>(x) / lc_imageGeometryWidth;

    if ( (l_x_normalized < l_normalizedPositions[0u])
      || (l_x_normalized > l_normalizedPositions[7u]) )
    {
      // Outside the wheel track
      (*l_data) = l_lineColor_Outside;
    }
    else if ( (l_x_normalized > l_normalizedPositions[3u])
           && (l_x_normalized < l_normalizedPositions[4u]) )
    {
      // Middle of the wheel track
      (*l_data) = l_lineColor_Inside;
    }
    else if ( (l_x_normalized > l_normalizedPositions[1u])
           && (l_x_normalized < l_normalizedPositions[2u]) )
    {
      // Middle of the left border line
      (*l_data) = l_lineColor_BorderLine;
    }
    else if ( (l_x_normalized > l_normalizedPositions[5u])
           && (l_x_normalized < l_normalizedPositions[6u]) )
    {
      // Middle of the right border line
      (*l_data) = l_lineColor_BorderLine;
    }
    else
    {
      // Gradient
      if (l_x_normalized <= l_normalizedPositions[1u])
      {
        // Left border line, left gradient
        (*l_data) = helper::TrajectoryHelper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_Outside, l_lineColor_BorderLine, l_normalizedPositions[0u], l_normalizedPositions[1u], l_x_normalized);
      }
      else if (l_x_normalized >= l_normalizedPositions[6u])
      {
        // Right border line, right gradient
        (*l_data) = helper::TrajectoryHelper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_BorderLine, l_lineColor_Outside, l_normalizedPositions[6u], l_normalizedPositions[7u], l_x_normalized);
      }
      else
      {
        // Inner gradient of either border lines
        if (l_x_normalized <= l_normalizedPositions[3u])
        {
          // Left border line, right gradient
          (*l_data) = helper::TrajectoryHelper::smoothstep_Vec4ub( // PRQA S 2759
            l_lineColor_BorderLine, l_lineColor_Inside, l_normalizedPositions[2u], l_normalizedPositions[3u], l_x_normalized);
        }
        else //(l_x_normalized >= l_normalizedPositions[4u])
        {
          // Right border line, left gradient
          (*l_data) = helper::TrajectoryHelper::smoothstep_Vec4ub( // PRQA S 2759
            l_lineColor_Inside, l_lineColor_BorderLine, l_normalizedPositions[4u], l_normalizedPositions[5u], l_x_normalized);
        }
      }
    }
    ++l_data;
  }

  return l_image;
}


void TrailerAssistLine::generateVertexData()
{
  generateVertexData_usingTexture();
}


void TrailerAssistLine::generateVertexData_usingTexture()  // PRQA S 6040  // PRQA S 6041  // PRQA S 6044
{
  // *** 1. Create frame ***
  m_frame.removeAllPoints();

  vfc::float32_t l_trailerAssistAngle = 0.0f;
  vfc::float32_t l_trailerAssistRadius = 0.0f;
  vfc::float32_t l_TrailerAssistCenterLongitudinalPos = 0.0f;
  const vfc::float32_t lc_halfGeometryWidth = m_lineGeometryWidth * 0.5f;

  l_TrailerAssistCenterLongitudinalPos = sm_mainLogicRefPtr->getModelDataRef().TrailerAssistCenter.Pos.x();

  if (cc::assets::trajectory::commontypes::Rotation_enm == sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType)
  {
        // When it's rotation
        m_frameRadiuses[0u] = sm_mainLogicRefPtr->getModelDataRef().TrailerAssistCenter.Radius
                               + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
        m_frameRadiuses[1u] = sm_mainLogicRefPtr->getModelDataRef().TrailerAssistCenter.Radius
                               + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

        m_frame.setVertexLineRadius(0u, m_frameRadiuses[0u]);
        m_frame.setVertexLineRadius(1u, m_frameRadiuses[1u]);
        l_trailerAssistAngle = sm_mainLogicRefPtr->getModelDataRef().TrailerAssistCenter.Angle;
        l_trailerAssistRadius = sm_mainLogicRefPtr->getModelDataRef().TrailerAssistCenter.Radius;
  }
  else
  {
        // when it is translation
        m_frameLateralOffsets[0u] = sm_mainLogicRefPtr->getModelDataRef().TrailerAssistCenter.Pos.y()
                                     + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
        m_frameLateralOffsets[1u] = sm_mainLogicRefPtr->getModelDataRef().TrailerAssistCenter.Pos.y()
                                     + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

        m_frame.setVertexLineOffset(0u, m_frameLateralOffsets[0u]);
        m_frame.setVertexLineOffset(1u, m_frameLateralOffsets[1u]);
  }
  m_frame.setBumperLineAngle(0u, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Angle);
  m_frame.setBumperLinePos  (0u, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Pos.x());

  const osg::Vec4f l_lineColor = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f); // With texturing just the alpha matters in the shader.
  cc::assets::trajectory::commontypes::ControlPoint_st l_controlPoint;

  vfc::float32_t l_startAngle = 0.0f;
  vfc::float32_t l_endAngle = 0.0f;
  vfc::float32_t l_startPos = 0.0f;
  vfc::float32_t l_endPos = 0.0f;

  // green part from wheelbase till the ActionPoint begins

  l_startAngle = l_trailerAssistAngle;
  l_endAngle =
      m_frame.getBumperLineAngle(0u)
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        * (m_trajParams.THTraj_Length / sm_mainLogicRefPtr->getModelDataRef().BumperCenterPoint.Radius);
  l_startPos = l_TrailerAssistCenterLongitudinalPos;
  l_endPos =
      m_frame.getBumperLinePos(0u)
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        * m_trajParams.THTraj_Length;

  // Controllpoint left(0),right(1)
  l_controlPoint.Angle = l_startAngle;
  l_controlPoint.LongitudinalPos = l_startPos;
  l_controlPoint.Color = l_lineColor;
  l_controlPoint.Index = 0u; // Dummy value. Will be calculated later.
  m_frame.addControlPoint(0u, l_controlPoint);
  m_frame.addControlPoint(1u, l_controlPoint);


  const vfc::float32_t l_fadeInStartAngle =
      l_trailerAssistAngle
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * (m_trajParams.THTraj_ColorGradientPosRatio / m_frame.getVertexLineRadius(0u));

  const vfc::float32_t l_fadeInStartPos =
      l_TrailerAssistCenterLongitudinalPos
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * m_trajParams.THTraj_ColorGradientPosRatio;

  const vfc::float32_t l_fadeInEndAngle = l_trailerAssistAngle;
  const vfc::float32_t l_fadeInEndPos = l_TrailerAssistCenterLongitudinalPos;


  m_frame.setFadeInStartAngle(0u, l_fadeInStartAngle);
  m_frame.setFadeInStartAngle(1u, l_fadeInStartAngle);

  m_frame.setFadeInStartPos(0u, l_fadeInStartPos);
  m_frame.setFadeInStartPos(1u, l_fadeInStartPos);

  m_frame.setFadeInEndAngle(0u, l_fadeInEndAngle);
  m_frame.setFadeInEndAngle(1u, l_fadeInEndAngle);

  m_frame.setFadeInEndPos(0u, l_fadeInEndPos);
  m_frame.setFadeInEndPos(1u, l_fadeInEndPos);


  // Controllpoint left(0),right(1)
  l_controlPoint.Angle = l_endAngle;
  l_controlPoint.LongitudinalPos = l_endPos;
  l_controlPoint.Color = l_lineColor;
  m_frame.addControlPoint(0u, l_controlPoint);
  m_frame.addControlPoint(1u, l_controlPoint);


  vfc::float32_t l_fadeOutStartAngle = 0.0f;
  vfc::float32_t l_fadeOutStartPos = 0.0f;
  vfc::float32_t l_fadeOutEndAngle = 0.0f;
  vfc::float32_t l_fadeOutEndPos = 0.0f;

  const vfc::float32_t l_trajEndAngle =
      m_frame.getBumperLineAngle(0u)
    + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
      * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
      * (m_trajParams.THTraj_Length / sm_mainLogicRefPtr->getModelDataRef().BumperCenterPoint.Radius);

  l_fadeOutStartAngle = m_frame.getBumperLineAngle(0u)
      + 0.5f * (l_trajEndAngle - m_frame.getBumperLineAngle(0u));

  const vfc::float32_t l_trajEndPos =
      m_frame.getBumperLinePos(0u)
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        * m_trajParams.THTraj_Length;

  l_fadeOutStartPos = m_frame.getBumperLinePos(0u)
      + 0.5f * (l_trajEndPos - m_frame.getBumperLinePos(0u));

  l_fadeOutEndAngle = l_trajEndAngle;

  l_fadeOutEndPos = l_trajEndPos;


  m_frame.setFadeOutStartAngle(0u, l_fadeOutStartAngle);
  m_frame.setFadeOutStartAngle(1u, l_fadeOutStartAngle);

  m_frame.setFadeOutStartPos(0u, l_fadeOutStartPos);
  m_frame.setFadeOutStartPos(1u, l_fadeOutStartPos);

  m_frame.setFadeOutEndAngle(0u, l_fadeOutEndAngle);
  m_frame.setFadeOutEndAngle(1u, l_fadeOutEndAngle);

  m_frame.setFadeOutEndPos(0u, l_fadeOutEndPos);
  m_frame.setFadeOutEndPos(1u, l_fadeOutEndPos);


  // *** 2. Create vertices (and colors) ***
  // Generate line vertices
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (m_geometry->getVertexArray());
  l_vertices->clear();

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray());
  l_colors->clear();

  const vfc::float32_t l_translationAngle_Rad = osg::DegreesToRadians(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle);
  m_frame.generateVertices(  // PRQA S 3803
      0u, 0u, 1u, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, (m_height + g_trajCodingParams->m_THTraj_StartPoint.z()),
      l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm,
      mc_numOfVerts, sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
      l_translationAngle_Rad);

  m_frame.generateVertices(  // PRQA S 3803
      1u, 0u, 1u, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, (m_height + g_trajCodingParams->m_THTraj_StartPoint.z()),
      l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm,
      mc_numOfVerts, sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
      l_translationAngle_Rad);

  // *** 3. Create indices ***
  osg::DrawElementsUShort* const l_indices = static_cast<osg::DrawElementsUShort*> (m_geometry->getPrimitiveSet(0u));
  l_indices->clear();
  m_frame.generateIndices(0u, 0u, 1u, 0u, mc_numOfVerts, l_indices);


  l_vertices->dirty();
  l_colors->dirty();
  l_indices->dirty();
  m_geometry->dirtyBound();

  setCull(false);
}


} // namespace trajectory
} // namespace assets
} // namespace cc
