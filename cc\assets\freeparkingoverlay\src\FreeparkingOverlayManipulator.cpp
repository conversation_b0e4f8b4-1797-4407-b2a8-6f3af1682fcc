//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/freeparkingoverlay/inc/FreeparkingOverlayManipulator.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "pc/generic/util/cli/inc/CommandCallback.h"
#include "pc/generic/util/cli/inc/CommandLineInterface.h"
#include "pc/generic/util/cli/inc/CommandRegistry.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/c2w/inc/Utils.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/Scene.h"
#include "pc/svs/core/inc/View.h"
#include "pc/svs/util/cli/inc/BaseCommands.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/virtcam/inc/CameraFirstPersonManipulator.h"

using pc::util::logging::g_EngineContext;

#define ENABLE_FREEPARKING_MOUSE_DEBUG 0

/// @deviation NRCS2_076
/// Rule QACPP-4.3.0-3803
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{
//! @brief : the freeparking slot can also be manipulated through socket command during developing phase
FreeparkingOverlayManipulator::FreeparkingOverlayManipulator(cc::core::CustomFramework* f_framework)
    : m_framework(f_framework) // PRQA S 2323 // PRQA S 4052
    , m_currentViewId(0u) // PRQA S 2323
    , m_resetCameraManipulator(false) // PRQA S 2323
    , m_mouseSensitivity(0.f) // PRQA S 2323
    , m_stepSize(1.0f) // PRQA S 2323
    , m_globalMoveModifierMask(0) // PRQA S 2323
    , m_position(g_managerSettings->m_defaultPositionParallel) // PRQA S 2323
{
}

FreeparkingOverlayManipulator::~FreeparkingOverlayManipulator() = default;

#if ENABLE_FREEPARKING_MOUSE_DEBUG
static const vfc::float32_t VERT_X_OFFSET              = 216.0f;
static const vfc::float32_t APA_PLANVIEW_Y_OFFSET_HORI = 78.0f;

enum HuTouchStatus
{
    Invalid = 0,
    Down    = 1,
    Up      = 2,
    Move    = 3
};

inline void sendHuTouchStatus(HuTouchStatus f_value)
{
    cc::daddy::HUtouchEvenTypeDaddy_t& l_touchEvent =
        cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.reserve();
    l_touchEvent.m_Data = static_cast<vfc::uint8_t>(f_value);
    cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.deliver();
}

inline void sendHuCoordinate(uint16_t f_hux, uint16_t f_huy)
{
    cc::daddy::HmiData_Daddy& l_hmiData = cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.reserve();
    l_hmiData.m_Data.m_huX              = f_hux;
    l_hmiData.m_Data.m_huY              = f_huy;
    l_hmiData.m_Data.m_zoomFactorRq     = 0u;
    cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.deliver();
}

void handleCoordinate(
    vfc::float32_t&                  f_x,
    vfc::float32_t&                  f_y,
    const osgGA::GUIEventAdapter&    f_ea,
    const cc::core::CustomFramework* f_framework)
{
    cc::target::common::EThemeTypeHU l_curThemeType = cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI;
    if (f_framework->m_SVSRotateStatusDaddy_Receiver.hasData())
    {
        const cc::daddy::SVSRotateStatusDaddy_t* l_themeType = f_framework->m_SVSRotateStatusDaddy_Receiver.getData();
        l_curThemeType = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 3013
    }
    if (l_curThemeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    {
        using cc::core::g_views;
        static vfc::float32_t HORI_SCALE_FACTOR =
            static_cast<vfc::float32_t>(g_views->m_realVirtualPlanviewPort.m_size.x()) /
            static_cast<vfc::float32_t>(g_views->m_apaPlanViewport.m_size.x());
        f_x = f_ea.getX() * HORI_SCALE_FACTOR;
        f_y = (cc::core::g_views->m_apaPlanViewport.m_size.y() + APA_PLANVIEW_Y_OFFSET_HORI - f_ea.getY()) *
              HORI_SCALE_FACTOR;
    }
    else // (l_curThemeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT)
    {
        // coordinate is swapped in vertical mode
        f_y = f_ea.getX() + VERT_X_OFFSET;
        f_x = f_ea.getY();
    }

    return;
}
#endif // ENABLE_FREEPARKING_MOUSE_DEBUG

bool FreeparkingOverlayManipulator::handle(
    const osgGA::GUIEventAdapter& f_ea,
    osgGA::GUIActionAdapter&      f_aa,
    osg::Object*                  /*f_obj*/,
    osg::NodeVisitor*            /*f_nv*/ )
{
    if (f_ea.getHandled())
    {
        return false;
    }
    const osgGA::GUIEventAdapter::EventType feaEventType = f_ea.getEventType();
    if (feaEventType == osgGA::GUIEventAdapter::KEYUP)
    {
        handleKeyDown(f_ea, f_aa);
        return true;
    }
#if ENABLE_FREEPARKING_MOUSE_DEBUG
    else if (feaEventType == osgGA::GUIEventAdapter::PUSH)
    {
        vfc::float32_t x, y;
        handleCoordinate(x, y, f_ea, m_framework);
        sendHuTouchStatus(HuTouchStatus::Down);
        sendHuCoordinate(x, y);
        XLOG_INFO_OS(g_EngineContext) << "Push" << " X: " << x << " Y: " << y << XLOG_ENDL;
        return true;
    }
    else if (feaEventType == osgGA::GUIEventAdapter::RELEASE)
    {
        vfc::float32_t x, y;
        handleCoordinate(x, y, f_ea, m_framework);
        sendHuTouchStatus(HuTouchStatus::Up);
        sendHuCoordinate(x, y);
        XLOG_INFO_OS(g_EngineContext) << "Release" << " X: " << x << " Y: " << y << XLOG_ENDL;
        return true;
    }
    else if (feaEventType == osgGA::GUIEventAdapter::MOVE)
    {
        return true;
    }
    else if (feaEventType == osgGA::GUIEventAdapter::DRAG)
    {
        vfc::float32_t x, y;
        handleCoordinate(x, y, f_ea, m_framework);
        sendHuTouchStatus(HuTouchStatus::Move);
        sendHuCoordinate(x, y);
        XLOG_INFO_OS(g_EngineContext) << "Drag" << " X: " << x << " Y: " << y << XLOG_ENDL;
        return true;
    }
#endif // ENABLE_FREEPARKING_MOUSE_DEBUG
    else
    {
        // Do nothing.
    }
    return false;
}

//* during developing phase, the slot can be moved by keyboad up, down, left, right
void FreeparkingOverlayManipulator::handleKeyDown(
    const osgGA::GUIEventAdapter&  f_ea,
    const osgGA::GUIActionAdapter& /*f_aa*/)
{
    vfc::int32_t l_key = f_ea.getKey(); // PRQA S 3803
    if (0 != f_ea.getUnmodifiedKey())
    {
        l_key = f_ea.getUnmodifiedKey();
    }
    switch (l_key)
    {
    case FP_MOVE_UP:
    {
        moveUp(m_position, m_stepSize);
        break;
    }
    case FP_MOVE_DOWN:
    {
        moveDown(m_position, m_stepSize);
        break;
    }
    case FP_MOVE_LEFT:
    {
        moveLeft(m_position, m_stepSize);
        break;
    }
    case FP_MOVE_RIGHT:
    {
        moveRight(m_position, m_stepSize);
        break;
    }
    default:
    {
        break;
    }
    }
    // as we have implemented the manipulation through touch screen, disable this senderport from keyboard manipulation
    // cc::daddy::FreeparkingSocket_Daddy& l_delta =
    // cc::daddy::CustomDaddyPorts::sm_freeparkingSocketSenderPort.reserve(); l_delta.m_Data.m_CenterPos = m_position;
    // cc::daddy::CustomDaddyPorts::sm_freeparkingSocketSenderPort.deliver();
}

//!
//! SetFreeparkingCommand
//!
class SetFreeparkingCommand : public pc::util::cli::CommandCallback
{
public:
    bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
    {
        vfc::uint32_t  l_type      = 0u;
        vfc::float32_t l_angle     = 0.0f;
        vfc::uint32_t  l_entryType = 0u;
        f_input >> l_type >> l_angle >> l_entryType; // PRQA S 3803

        if (f_input.fail())
        {
            f_output << parseError; // PRQA S 3803
            getHelp(f_output);
            return false;
        }
        else
        {
            // Do nothing
        }

        cc::daddy::FreeparkingData_Daddy& l_fp = cc::daddy::CustomDaddyPorts::sm_freeparkingDataSenderPort.reserve();
        l_fp.m_Data.m_type                     = l_type;
        l_fp.m_Data.m_angle                    = l_angle;
        l_fp.m_Data.m_entryType                = l_entryType;
        cc::daddy::CustomDaddyPorts::sm_freeparkingDataSenderPort.deliver();
        return true;
    }

    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Set type& angle & entry type of anywhere parking" << newline; // PRQA S 3803
    }

    void getHelp(std::ostream& f_output) const override
    {
        f_output << "type <1> = parallel " << newline;         // PRQA S 3803
        f_output << "type <2> = cross " << newline;            // PRQA S 3803
        f_output << "type <9> = Position is okay " << newline; // PRQA S 3803
        f_output << "angle in degrees" << newline;             // PRQA S 3803
        f_output << "entry type <1> = forward " << newline;    // PRQA S 3803
        f_output << "entry type <2> = backward " << newline;   // PRQA S 3803
    }
};

static SetFreeparkingCommand g_setFreeparkingCommand;

//!
//! handling free parking Command
//!
class FreeParkingSignalCommand : public pc::util::cli::SetSignalCommand
{
public:
    FreeParkingSignalCommand()
    {
        addCommand("cmd", &g_setFreeparkingCommand);
    }
};

static pc::util::cli::Command<FreeParkingSignalCommand> g_setSignalCommand("fp");

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc
