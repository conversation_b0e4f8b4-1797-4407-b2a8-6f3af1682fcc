//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC BYD DENZA&MR
/// @file  ArrowTrajectory.cpp
/// @brief
//=============================================================================

#include "cc/assets/trajectory/inc/ArrowTrajectory.h"
#include "cc/assets/trajectory/inc/Helper.h"

#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/src/ViewModeStateTransitionManager.h"

#include "cc/imgui/inc/imgui_manager.h"

#include "osg/Vec2ui"
#include "osg/Depth"
#include "osg/Geometry"
#include "osg/Texture2D"
#include "osgDB/ReadFile"


namespace cc
{
namespace assets
{
namespace trajectory
{

//!
//! ArrowTrajectorySettings
//!
class ArrowTrajectorySettings : public pc::util::coding::ISerializable
{
public:

    ArrowTrajectorySettings()
    : m_filenameArrowTexture(CONCAT_PATH("cc/resources/trajectory/arrow.png"))  //PRQA S 4052
    , m_filenameArrowTextureAVM(CONCAT_PATH("cc/resources/trajectory/arrow_avm.png"))  //PRQA S 4052
	  , m_ArrowLength(1.25f)
	  , m_ArrowWidth(0.5f)
    , m_StartDistanceFromFrontBumper(2.0f)
    , m_StartDistanceFromRearBumper(2.0f)
    , m_GapDistance(0.5f)
    , m_AnimationSpeed(0.5f)
    , m_groundLevel(0.0f)
    , m_mipmapBias(-0.4f)
    {
    }

    SERIALIZABLE(ArrowTrajectorySettings)   //PRQA S 2428
    {
      ADD_STRING_MEMBER(filenameArrowTexture);
      ADD_STRING_MEMBER(filenameArrowTextureAVM);
      ADD_MEMBER(vfc::float32_t,ArrowLength);
      ADD_MEMBER(vfc::float32_t,ArrowWidth);
      ADD_MEMBER(vfc::float32_t,StartDistanceFromFrontBumper);
      ADD_MEMBER(vfc::float32_t,StartDistanceFromRearBumper);
      ADD_MEMBER(vfc::float32_t,GapDistance);
      ADD_MEMBER(vfc::float32_t,AnimationSpeed);
      ADD_MEMBER(vfc::float32_t,groundLevel);
      ADD_MEMBER(vfc::float32_t,mipmapBias);
    }

    std::string m_filenameArrowTexture;
    std::string m_filenameArrowTextureAVM;
    vfc::float32_t m_ArrowLength;
    vfc::float32_t m_ArrowWidth;
    vfc::float32_t m_StartDistanceFromFrontBumper;
    vfc::float32_t m_StartDistanceFromRearBumper;
    vfc::float32_t m_GapDistance;
    vfc::float32_t m_AnimationSpeed;
    vfc::float32_t m_groundLevel;
    vfc::float32_t m_mipmapBias;
};
pc::util::coding::Item<ArrowTrajectorySettings> g_arrowTrajectorySettings("ArrowTrajectory");


//!
//! ArrowTrajectoryPlane
//!
ArrowTrajectoryPlane::ArrowTrajectoryPlane()  //PRQA S 4207
  : osg::MatrixTransform()
  , ArrowTrajectoryNode()
  , m_size(1.f,1.f,1.f)
  , m_position(0.f, 0.f, 0.f)
  , m_updateGeometry(false)  //PRQA S 4052
  , m_updateOrientation(false)
  , m_planeGeode(nullptr)
  , m_alpha(1.f)
{
  {

    const vfc::float32_t l_ArrowHalfLengthCross = g_arrowTrajectorySettings->m_ArrowLength;
    const vfc::float32_t l_ArrowHalfWidthCross  = g_arrowTrajectorySettings->m_ArrowWidth;

    osg::VertexBufferObject* const l_vbo = new osg::VertexBufferObject;
    l_vbo->setUsage(GL_DYNAMIC_DRAW_ARB); // PRQA S 3143
    //build the plane for the parking slot itself
    // to make the origin to center of the plane's geometry
    osg::Geometry* const l_geometry = new osg::Geometry;
    l_geometry->setUseDisplayList(false);
    l_geometry->setUseVertexBufferObjects(true);

    osg::Vec3Array* const l_vertices = new osg::Vec3Array(4u);
    l_vertices->setVertexBufferObject(l_vbo);

    //        3                     2
    //        ----------------------
    //        |                    |
    //        |      (origin)      |
    //        |                    |
    //        ----------------------
    //        0                     1

    (*l_vertices)[0u] = osg::Vec3(-l_ArrowHalfWidthCross, -l_ArrowHalfLengthCross, 2.0f);
    (*l_vertices)[1u] = osg::Vec3( l_ArrowHalfWidthCross, -l_ArrowHalfLengthCross, 2.0f);
    (*l_vertices)[2u] = osg::Vec3( l_ArrowHalfWidthCross,  l_ArrowHalfLengthCross, 2.0f);
    (*l_vertices)[3u] = osg::Vec3(-l_ArrowHalfWidthCross,  l_ArrowHalfLengthCross, 2.0f);

    l_geometry->setVertexArray(l_vertices);

    osg::Vec2Array* const l_texCoords = new osg::Vec2Array(4u);
    (*l_texCoords)[0u] = osg::Vec2f(1.0f, 0.0f);
    (*l_texCoords)[1u] = osg::Vec2f(1.0f, 1.0f);
    (*l_texCoords)[2u] = osg::Vec2f(0.0f, 1.0f);
    (*l_texCoords)[3u] = osg::Vec2f(0.0f, 0.0f);
    l_geometry->setTexCoordArray(0u, l_texCoords);

    osg::Vec4Array* const l_colours = new osg::Vec4Array(1u);
    (*l_colours)[0u] = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f);
    l_geometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);

    osg::Vec3Array* const l_normals = new osg::Vec3Array(1u);
    (*l_normals)[0u] = osg::Z_AXIS;
    l_geometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);

    osg::DrawElementsUByte* const l_indices = new osg::DrawElementsUByte(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), 6u);
    (*l_indices)[0u] = 1u;
    (*l_indices)[1u] = 0u;
    (*l_indices)[2u] = 2u;
    (*l_indices)[3u] = 2u;
    (*l_indices)[4u] = 0u;
    (*l_indices)[5u] = 3u;
    l_geometry->addPrimitiveSet(l_indices);    // PRQA S 3803

    m_planeGeode = new osg::Geode;
    m_planeGeode->addDrawable(l_geometry);    // PRQA S 3803
  }

  osg::Group::addChild(m_planeGeode.get());    // PRQA S 3803

  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1);
}


ArrowTrajectoryPlane::ArrowTrajectoryPlane(const ArrowTrajectoryPlane& f_other, const osg::CopyOp& f_copyOp) //PRQA S 4206
  : osg::MatrixTransform(f_other, f_copyOp)  //PRQA S 4050  //PRQA S 4052
  , m_size{}
  , m_position{}
  , m_planeGeode{}
{
}

void ArrowTrajectoryPlane::updateShaderUniform()
{
  osg::StateSet* const l_stateSet = getOrCreateStateSet();
  l_stateSet->getOrCreateUniform("alpha", osg::Uniform::FLOAT)->set(m_alpha);
}


void ArrowTrajectoryPlane::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    // The update traversal function only can be called
    setMatrix(osg::Matrix::rotate(m_angle_rad, osg::Z_AXIS) *
              osg::Matrix::translate(m_position));

    updateShaderUniform();

    osg::MatrixTransform::traverse(f_nv);
  }
  else
  {
    osg::MatrixTransform::traverse(f_nv);
  }
}


//!
//! ArrowTrajectory
//!
ArrowTrajectory::ArrowTrajectory()
  : osg::MatrixTransform()
  , m_visible(false)
  , m_arrowIcon(nullptr)
  , m_position{osg::Vec2f(0.f, 0.f),osg::Vec2f(0.f, 0.f),osg::Vec2f(0.f, 0.f)}
  , m_angle_rad{0.f, 0.f, 0.f}
  , m_alpha{0.f, 0.f, 0.f}
  , m_animationStyle(AnimationStyle::FADEIN_FADEOUT_EFFECT)
  , m_animationCounter(0.f)
  , m_arrowTrajectoryModel(nullptr)
  , m_framework(nullptr)
{

}



ArrowTrajectory::ArrowTrajectory(pc::core::Framework* f_framework, const TrajectoryParams_st& f_params, bool f_isArrowAVM)
  : osg::MatrixTransform()  //PRQA S 4050
  , m_visible(false)
  , m_arrowIcon(nullptr)
  , m_position{osg::Vec2f(0.f, 0.f),osg::Vec2f(0.f, 0.f),osg::Vec2f(0.f, 0.f)}
  , m_angle_rad{0.f, 0.f, 0.f}
  , m_alpha{0.f, 0.f, 0.f}
  , m_animationStyle(AnimationStyle::FADEIN_FADEOUT_EFFECT)
  , m_animationCounter(0.f)
  , m_arrowTrajectoryModel(nullptr)
  , m_framework(f_framework)
  , m_isArrowAVM(f_isArrowAVM)
{
  //Each parking spot has 12 spotPlanes
  std::array<osg::ref_ptr<ArrowTrajectoryPlane>,   NUM_ARROWS> l_spotsPlane;
  osg::Image*                                                   l_spotsImage = nullptr;
  osg::Texture2D*                                               l_spotTexture2D = nullptr;
  osg::observer_ptr<osg::StateSet>                              l_stateSet;

  m_arrowIcon = new osg::Switch;
  m_arrowIcon->setName("ArrowTrajectoryIcon");

  for(vfc::uint32_t l_ArrowIndex = 0u; l_ArrowIndex < NUM_ARROWS; l_ArrowIndex++)
  {
    l_spotsPlane[l_ArrowIndex]    = new ArrowTrajectoryPlane;

    const std::string l_filename = m_isArrowAVM ? g_arrowTrajectorySettings->m_filenameArrowTextureAVM : \
                                       g_arrowTrajectorySettings->m_filenameArrowTexture;

    l_spotsImage = osgDB::readImageFile(l_filename);

    l_spotTexture2D              = new osg::Texture2D(l_spotsImage);

    l_stateSet                 = new osg::StateSet;
    l_spotTexture2D->setDataVariance(osg::Object::STATIC);
    l_spotTexture2D->setUnRefImageDataAfterApply(true);
    l_spotTexture2D->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_spotTexture2D->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_spotTexture2D->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_spotTexture2D->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);

    l_stateSet->setTextureAttribute(0u, l_spotTexture2D);
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);

    l_stateSet->setRenderBinDetails(pc::core::g_renderOrder->m_carShadow - 1, "RenderBin");

    // l_stateSet->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)->set(osg::Vec4f(1.0f, 1.0f, 0.0f, 0.0f));    // PRQA S 3803
    // l_stateSet->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(1.0f);    // PRQA S 3803
    // l_stateSet->getOrCreateUniform("u_bias", osg::Uniform::FLOAT)->set(0.0f);    // PRQA S 3803
    // l_stateSet->getOrCreateUniform("u_Selected", osg::Uniform::BOOL)->set(false);    // PRQA S 3803

    if (m_animationStyle == AnimationStyle::AUGMENTED_WAVE_EFFECT)
    {
      pc::core::TextureShaderProgramDescriptor l_arrowShader("iconAnimationAugmentedWave");
      osg::Uniform* const l_augmentedWaveUniform = l_stateSet->getOrCreateUniform("isAugmentedWave", osg::Uniform::BOOL);
      osg::Uniform* const l_alphaUniform = l_stateSet->getOrCreateUniform("alpha", osg::Uniform::FLOAT);
      l_augmentedWaveUniform->set(true); // PRQA S 3803
      l_alphaUniform->set(1.0f);  // PRQA S 3803
      l_arrowShader.apply(l_stateSet.get());    // PRQA S 3803
    }
    else if (m_animationStyle == AnimationStyle::FADEIN_FADEOUT_EFFECT)
    {
      pc::core::TextureShaderProgramDescriptor l_arrowShader("iconAnimationFadeInFadeOut");
      osg::Uniform* const l_fadingUniform = l_stateSet->getOrCreateUniform("isFadeInOut", osg::Uniform::BOOL);
      osg::Uniform* const l_alphaUniform = l_stateSet->getOrCreateUniform("alpha", osg::Uniform::FLOAT);
      l_fadingUniform->set(true); // PRQA S 3803
      l_alphaUniform->set(1.0f);  // PRQA S 3803
      l_arrowShader.apply(l_stateSet.get());    // PRQA S 3803
    }
    else
    {
      pc::core::TextureShaderProgramDescriptor l_arrowShader("basicTex");
      l_arrowShader.apply(l_stateSet.get());    // PRQA S 3803
    }

    l_spotsPlane[l_ArrowIndex]->setStateSet(l_stateSet.get());

    m_arrowIcon->addChild(l_spotsPlane[l_ArrowIndex]); // PRQA S 3803  #code looks fine
    m_arrowIcon->setValue(l_ArrowIndex, true);

  }
  osg::Group::addChild(m_arrowIcon.get());    // PRQA S 3803

  m_arrowTrajectoryModel = new ArrowTrajectoryModel(
       f_framework, 1.0f,
       f_params);

  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1);

}

ArrowTrajectory::ArrowTrajectory(const ArrowTrajectory& f_other, const osg::CopyOp& f_copyOp) //PRQA S 4206
  : osg::MatrixTransform(f_other, f_copyOp)  //PRQA S 4050
  , m_visible(f_other.m_visible)
  , m_arrowIcon{}
  , m_position{}
  , m_arrowTrajectoryModel{}
{
}

void ArrowTrajectory::calculateColorAnimation()
{
  for (vfc::uint32_t l_arrow_index = 0; l_arrow_index < NUM_ARROWS ; ++l_arrow_index)
  {
    const vfc::float32_t l_phase = 360.f * 0.33333f * static_cast<vfc::float32_t>(l_arrow_index);
    m_alpha[l_arrow_index] = std::abs(std::cos(osg::DegreesToRadians(m_animationCounter + l_phase)));
  }
  m_animationCounter = m_animationCounter + g_arrowTrajectorySettings->m_AnimationSpeed;
}


bool ArrowTrajectory::readDynamicInputs()
{
  bool l_return = false;
  cc::target::common::EPARKStatusR2L  l_parkStatus    = cc::target::common::PARK_Standby;
  setVisibility(false);
  cc::core::CustomFramework* const l_framework = m_framework->asCustomFramework();

  // TODO: Adapt logic
  if (l_framework->m_parkHmiParkingStatusReceiver.hasData())
  {
      l_parkStatus = l_framework->m_parkHmiParkingStatusReceiver.getData()->m_Data;
      bool l_showCar = false;
      if (l_framework->m_ParkDispLowPolyModelStsReceiver.hasData())
      {
        l_showCar = l_framework->m_ParkDispLowPolyModelStsReceiver.getData()->m_Data.m_isCarAvailabe;
      }
      if ( l_showCar && ((l_parkStatus == cc::target::common::PARK_Guidance_active) || (l_parkStatus == cc::target::common::PARK_Guidance_suspend)) )
      {
        setVisibility(true);
        l_return = true;
      }
  }

  if (m_isArrowAVM)
  {
    setVisibility(true);
    l_return = true;
  }

  // setVisibility(IMGUI_GET_CHECKBOX_BOOL("ArrowTrajectory", "ShowArrow"));
  // return IMGUI_GET_CHECKBOX_BOOL("ArrowTrajectory", "ShowArrow");
  return l_return;
}


void ArrowTrajectory::update()
{
  m_visible = true;
  if ((readDynamicInputs() == true)  && (m_arrowTrajectoryModel != nullptr))
  {
    m_isArrowAVM ? m_arrowTrajectoryModel->updateStaticArrow() : m_arrowTrajectoryModel->updateAkermanPos();
    calculateColorAnimation();

    for (vfc::uint32_t l_arrow_index = 0; l_arrow_index < NUM_ARROWS ; ++l_arrow_index)
    {
      m_position[l_arrow_index]  = m_arrowTrajectoryModel->getPosition(l_arrow_index);
      m_angle_rad[l_arrow_index] = m_arrowTrajectoryModel->getAngle_rad(l_arrow_index);

      ArrowTrajectoryNode* const l_arrowDrawable = dynamic_cast<ArrowTrajectoryNode*> (m_arrowIcon->getChild(l_arrow_index));//PRQA S 3400
      if (l_arrowDrawable != nullptr)
      {
        if (m_isArrowAVM) { l_arrowDrawable->setSize(osg::Vec3f(0.5f,0.4f,1.f)); }
        l_arrowDrawable->updateOrientation();
        l_arrowDrawable->setPosition(osg::Vec3f(m_position[l_arrow_index], g_arrowTrajectorySettings->m_groundLevel));
        l_arrowDrawable->setAngleRad(m_angle_rad[l_arrow_index]);
        l_arrowDrawable->setAlpha(m_alpha[l_arrow_index]);
      }
    }
  }
  else
  {
    // no require for traversing, do nothing here
  }
}

void ArrowTrajectory::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    update();
    osg::MatrixTransform::traverse(f_nv);
  }
  else if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
  {
    if (m_visible)
    {
      osg::MatrixTransform::traverse(f_nv);
    }
  }
  else
  {
    osg::MatrixTransform::traverse(f_nv);
  }
}



ArrowTrajectoryModel::ArrowTrajectoryModel(
  pc::core::Framework* f_framework,
  vfc::float32_t f_height,
  const cc::assets::trajectory::TrajectoryParams_st& f_trajParams)
  : GeneralTrajectoryLine(
      f_framework,
      cc::assets::trajectory::commontypes::Middle_enm /* DUMMY */,
      2u,
      f_height,
      f_trajParams,
      true),
      mc_numOfVerts(48u)
  , m_geomHeight{}
  , m_position{osg::Vec2f(0.f, 0.f),osg::Vec2f(0.f, 0.f),osg::Vec2f(0.f, 0.f)}
  , m_angle_rad{0.f, 0.f, 0.f}

  {

  }

void ArrowTrajectoryModel::updateStaticArrow()
{
  const osg::Vec2f l_startPoint = sm_mainLogicRefPtr->getModelDataRef().ArrowStartCenter.Pos;

  const bool l_isForwardMoving = (cc::assets::trajectory::mainlogic::Forward_enm ==
                            sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection)  ? true : false;

  #ifdef ARROW_TRAJECTORY_EQUALITY
  const vfc::float32_t l_equalityHoriOffset = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront
                                        + pc::vehicle::g_mechanicalData->m_wheelbase
                                        - pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear;
  #endif

  vfc::float32_t l_startDistance = 0.f;

  if (l_isForwardMoving == true)
  {
    #ifdef ARROW_TRAJECTORY_EQUALITY
    l_startDistance = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear;
    #else
    l_startDistance = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront + g_arrowTrajectorySettings->m_StartDistanceFromFrontBumper;
    #endif
  }
  else
  {
    l_startDistance =  pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear;
  }


  // When it's translation
  for (vfc::uint32_t l_arrow_index = 0u; l_arrow_index < NUM_ARROWS; ++l_arrow_index)
  {
    vfc::float32_t l_distance = l_startDistance + g_arrowTrajectorySettings->m_GapDistance*static_cast<vfc::float32_t>(l_arrow_index);

    if (l_isForwardMoving == false)
    {
      l_distance = (-1.f)*l_distance;
    }

    m_position[l_arrow_index] = l_startPoint + osg::Vec2f(l_distance, 0.f);

    if (l_isForwardMoving)
    {
      m_angle_rad[l_arrow_index] = static_cast<vfc::float32_t>(osg::PI);

      #ifdef ARROW_TRAJECTORY_EQUALITY
      m_position[l_arrow_index] +=  osg::Vec2f(l_equalityHoriOffset, 0.f);
      #endif
    }
    else
    {
      m_angle_rad[l_arrow_index] = 0.f;
    }
  }

}

void ArrowTrajectoryModel::updateAkermanPos()
{
  const osg::Vec2f l_rotCenter = sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint;
  const osg::Vec2f l_startPoint = sm_mainLogicRefPtr->getModelDataRef().ArrowStartCenter.Pos;

  const bool l_isForwardMoving = (cc::assets::trajectory::mainlogic::Forward_enm ==
                            sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection)  ? true : false;
  const bool l_isTurningLeft   = (isEqual(1.0f, sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul)) ? true : false;

  #ifdef ARROW_TRAJECTORY_EQUALITY
  const vfc::float32_t l_equalityHoriOffset = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront
                                        + pc::vehicle::g_mechanicalData->m_wheelbase
                                        - pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear;
  #endif

  vfc::float32_t l_startDistance = 0.f;

  if (l_isForwardMoving == true)
  {
    #ifdef ARROW_TRAJECTORY_EQUALITY
    l_startDistance = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear + g_arrowTrajectorySettings->m_StartDistanceFromFrontBumper;
    #else
    l_startDistance = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront + g_arrowTrajectorySettings->m_StartDistanceFromFrontBumper;
    #endif
  }
  else
  {
    l_startDistance =  pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear + g_arrowTrajectorySettings->m_StartDistanceFromRearBumper;
  }

  if (cc::assets::trajectory::commontypes::Rotation_enm == sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType)
  {
    const vfc::float32_t l_akermanRadius = (l_startPoint - l_rotCenter).length();

    for (vfc::uint32_t l_arrow_index = 0u; l_arrow_index < NUM_ARROWS; ++l_arrow_index)
    {
      const vfc::float32_t l_distance = l_startDistance + g_arrowTrajectorySettings->m_GapDistance*static_cast<vfc::float32_t>(l_arrow_index);
      m_position[l_arrow_index] = l_startPoint;

      vfc::float32_t l_translateAngle_Rad = osg::DegreesToRadians((180.f*l_distance)/(static_cast<vfc::float32_t>(osg::PI)*l_akermanRadius));
      l_translateAngle_Rad = l_translateAngle_Rad*sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul*sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul;

      cc::assets::trajectory::helper::TrajectoryHelper::Rotate2DPoint(m_position[l_arrow_index], l_rotCenter, l_translateAngle_Rad);

      const osg::Vec2f l_orignalImageDir = osg::Vec2f(0.f, sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul);
      osg::Vec2f l_orientedDir = l_rotCenter - m_position[l_arrow_index];

      //l_orignalImageDir.normalize();  // the input's length is 1.0 already
      l_orientedDir.normalize();  // PRQA S 3804
      m_angle_rad[l_arrow_index] = std::acos(l_orientedDir*sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul*l_orignalImageDir);

      if (l_isForwardMoving)
      {
        if (l_isTurningLeft)
        {
          m_angle_rad[l_arrow_index]  = m_angle_rad[l_arrow_index] +  static_cast<vfc::float32_t>(osg::PI);
        }
        else
        {
          m_angle_rad[l_arrow_index] = m_angle_rad[l_arrow_index];
        }

        #ifdef ARROW_TRAJECTORY_EQUALITY
        m_position[l_arrow_index] +=  osg::Vec2f(l_equalityHoriOffset, 0.f);
        #endif
      }
      else
      {
        if (l_isTurningLeft)
        {
          m_angle_rad[l_arrow_index]  = (-1.f)*m_angle_rad[l_arrow_index];
        }
        else
        {
          m_angle_rad[l_arrow_index]  = (-1.f)*m_angle_rad[l_arrow_index] + static_cast<vfc::float32_t>(osg::PI);
        }
      }
    }
  }
  else
  {
    // When it's translation
    for (vfc::uint32_t l_arrow_index = 0u; l_arrow_index < NUM_ARROWS; ++l_arrow_index)
    {
      vfc::float32_t l_distance = l_startDistance + g_arrowTrajectorySettings->m_GapDistance*static_cast<vfc::float32_t>(l_arrow_index);

      if (l_isForwardMoving == false)
      {
        l_distance = (-1.f)*l_distance;
      }

      m_position[l_arrow_index] = l_startPoint + osg::Vec2f(l_distance, 0.f);

      if (l_isForwardMoving)
      {
        m_angle_rad[l_arrow_index] = static_cast<vfc::float32_t>(osg::PI);

        #ifdef ARROW_TRAJECTORY_EQUALITY
        m_position[l_arrow_index] +=  osg::Vec2f(l_equalityHoriOffset, 0.f);
        #endif
      }
      else
      {
        m_angle_rad[l_arrow_index] = 0.f;
      }
    }
  }
}


} // namespace trajectory
} // namespace assets
} // namespace cc

