//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/assets/common/inc/VehicleTransparentHandler.h"
#include "cc/assets/common/inc/Vehicle.h"
#include "cc/assets/uielements/inc/Vehicle2DOverlay.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
// #include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h"
// #include "cc/util/logging/inc/LoggingContexts.h"
// #include "cc/views/parkview/inc/ParkView.h"
// #include "cc/views/surroundview/inc/SurroundView.h"
// #include "cc/virtcam/inc/VirtualCameraUpdater.h"
#include "cc/imgui/inc/imgui_manager.h"

// #include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/animation/inc/Action.h"
#include "pc/svs/animation/inc/Animation.h"
#include "pc/svs/animation/inc/AnimationManager.h"
// #include "pc/svs/animation/inc/BowlAnimationFactory.h"
// #include "pc/svs/animation/inc/CameraAnimationFactory.h"
// #include "pc/svs/animation/inc/SerialAnimation.h"
// #include "pc/svs/core/inc/Asset.h"
// #include "pc/svs/core/inc/Scene.h"
// #include "pc/svs/core/inc/View.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/math/inc/FloatComp.h"

// #include "cc/assets/parkingspace/inc/ParkingSpace.h"
// #include "cc/assets/parkingspots/inc/ParkingSpotManager.h"

#define CHECK_PORT_DATA(containerName, port, flag)                                                                     \
    auto(containerName) = (port).getData();                                                                            \
    if ((containerName) == nullptr)                                                                                    \
    {                                                                                                                  \
        (flag) = false;                                                                                                \
        std::cout << #port << " doesn't have data\n";                                                                  \
    }

#define GET_PORT_DATA(dataDaddy, port, allPortHaveDataFlag)                                                            \
    const auto dataDaddy  = (port).getData(); /* PRQA S 1030 */                                                        \
    (allPortHaveDataFlag) = (allPortHaveDataFlag) && ((dataDaddy) != nullptr);                                         \
    if ((dataDaddy) == nullptr)                                                                                        \
    {                                                                                                                  \
        XLOG_ERROR(g_AppContext, #port << " doesn't have data!\n");                                                    \
    }

namespace cc
{
namespace core
{

using pc::util::logging::g_AppContext;

std::atomic<int> g_transparentLevel(0);
std::atomic<bool> g_enableTransparentAnimation(true);

class VehicleTransparentSetterAnimation : public pc::animation::Animation
{
public:
    VehicleTransparentSetterAnimation(
        const vfc::float32_t& f_startTransparency,
        const vfc::float32_t& f_targetTransparency,
        float                 f_duration);

    bool  hasFixedDuration() const override;
    float getFixedDuration() const override;

private:
    void onBegin() override;
    bool onUpdate(float f_animationTime) override;
    void onEnd(bool f_canceled) override;

    float          m_duration;
    vfc::float32_t m_startTransparency, m_targetTransparency;
};

VehicleTransparentSetterAnimation::VehicleTransparentSetterAnimation(
    const vfc::float32_t& f_startTransparency,
    const vfc::float32_t& f_targetTransparency,
    float                 f_duration)
    : m_duration(f_duration)
    , m_startTransparency(f_startTransparency)
    , m_targetTransparency(f_targetTransparency)
{
}

bool VehicleTransparentSetterAnimation::hasFixedDuration() const
{
    return true;
}

float VehicleTransparentSetterAnimation::getFixedDuration() const
{
    return m_duration;
}

void VehicleTransparentSetterAnimation::onBegin()
{
    // do nothing
}

bool VehicleTransparentSetterAnimation::onUpdate(float f_animationTime)
{
    // assert(m_duration >= 0.0f);
    if (m_duration < 0.0f)
    {
        return true;
    }
    float t = 1.0f;
    if (m_duration > 0.0f)
    {
        t = std::max(0.0f, std::min(1.0f, f_animationTime / m_duration));
    }
    const float transparencyLevel = static_cast<float>(t * m_targetTransparency + (1.0f - t) * m_startTransparency);
    cc::assets::common::VehicleTransparencyUpdateCallback::setTransparencyLevel(transparencyLevel);
    cc::assets::uielements::Vehicle2DOverlayManager::setVehicleTransparent(transparencyLevel);
    return f_animationTime >= m_duration;
}

void VehicleTransparentSetterAnimation::onEnd(bool f_canceled)
{
    if (!f_canceled)
    {
        cc::assets::common::VehicleTransparencyUpdateCallback::setTransparencyLevel(m_targetTransparency);
        cc::assets::uielements::Vehicle2DOverlayManager::setVehicleTransparent(m_targetTransparency);
    }
}

vfc::float32_t VehicleTransparentHandler::ANIMATION_STEPS = 20.0f;

//!
//! VehicleTransparentHandler
//!
VehicleTransparentHandler::VehicleTransparentHandler(pc::core::Framework* f_framework) // PRQA S 4207
    : m_sequenceNumber(0u) // PRQA S 2323 // PRQA S 4052
    , m_framework(f_framework) // PRQA S 2323
{
    if (f_framework != nullptr)
    {
        m_scene           = f_framework->getScene();
    }
    //! we always want to receive frame events regardless if
    //! marked as handled
    setIgnoreHandledEventsMask(osgGA::GUIEventAdapter::FRAME);
    // m_transitionQueue = m_framework->getAnimationManager()->createAnimationQueue(0u);
    setName("VehicleTransparentHandler");
}

void VehicleTransparentHandler::updateImgui()
{
    // float l_transparencyLevel = IMGUI_GET_SLIDER_FLOAT("VehicleTransparentHandler", "TransparencyLevel", 0.0f, 1.0f);
    constexpr vfc::float32_t l_transparencyLevel = 0.2f;
    cc::assets::common::VehicleTransparencyUpdateCallback::setTransparencyLevel(l_transparencyLevel);
    cc::assets::uielements::Vehicle2DOverlayManager::setVehicleTransparent(l_transparencyLevel);
}

static vfc::float32_t getTransparency(vfc::int32_t f_level)
{
    switch (f_level)
    {
    case 0:
    {
        return 1.0f;
    }
    case 1:
    {
        // return IMGUI_GET_SLIDER_FLOAT("VehicleTransparentHandler", "Level_1_Transparent", 0.0f, 1.0f);
        return 0.25f;
    }
    case 2:
    {
        return 0.1f;
    }

    case 3:
    {
        return 0.15f;
    }
    default:
    {
        return 1.0f;
    }
    }
}

void VehicleTransparentHandler::updateInput()
{
    cc::core::CustomFramework* const l_framework      = m_framework->asCustomFramework();
    bool                             allPortsHaveData = true;

    GET_PORT_DATA(vehTransLevelDaddy, l_framework->m_HUvehTransLevelReceiver, allPortsHaveData)
    GET_PORT_DATA(vehTransDaddy, l_framework->m_HUvehTransCCReceiver, allPortsHaveData)
    GET_PORT_DATA(parkHmiContainer, l_framework->m_ParkhmiToSvs_ReceiverPort, allPortsHaveData)

    if (!allPortsHaveData)
    {
        XLOG_ERROR(g_AppContext, " doesn't have data in parking view animation!\n");
        return;
    }

    // m_enableAnimation = IMGUI_GET_CHECKBOX_BOOL("Settings", "VehicleTransparentAnimation");

    const bool         l_previousEnableTransparent = m_enableTransparent;
    const vfc::uint8_t l_previousTransparentLevel  = m_transparentLevel;

    m_enableTransparent = vehTransDaddy->m_Data;
    m_transparentLevel  = vehTransLevelDaddy->m_Data;
    m_apaStatus         = parkHmiContainer->m_Data.m_apaStatus;
    g_transparentLevel = m_transparentLevel;
    m_dirty = m_dirty || (m_enableTransparent != l_previousEnableTransparent);
    m_dirty = m_dirty || (m_transparentLevel != l_previousTransparentLevel);
    m_dirty = m_dirty || (m_apaStatus != m_previousApaStatus);
}

void VehicleTransparentHandler::onIdle()
{
    if (m_dirty)
    {
        m_state = State::Transitioning;
    }
}

void VehicleTransparentHandler::onTransition()
{
    if (m_dirty)
    {
        m_currentTransparency = cc::assets::uielements::Vehicle2DOverlayManager::getVehicleTransparent();
        m_targetTransparency  = getTransparency(m_transparentLevel);
        m_step                = (m_targetTransparency - m_currentTransparency) / ANIMATION_STEPS;
        m_dirty               = false;
        XLOG_INFO(
            g_AppContext,
            "VehicleTransparentHandler: Received updates: " << "EnableTransparent: "
                                                            << (m_enableTransparent ? "TRUE" : "FALSE")
                                                            << "TransparencyLevel: " << m_targetTransparency);
    }
    // XLOG_INFO(
    //     g_AppContext,
    //     "VehicleTransparentHandler: m_appPreStatus" << static_cast<int>(m_appPreStatus) << "; g_transparentLevel"
    //                                                 << static_cast<int>(g_transparentLevel));
    if (!vfc::isEqual(m_currentTransparency, m_targetTransparency))
    {
        const bool l_enableTransparentAnimation = g_enableTransparentAnimation.load(std::memory_order_relaxed);
        if (!l_enableTransparentAnimation)
        {
            m_currentTransparency = m_targetTransparency;
            XLOG_INFO(g_AppContext, "VehicleTransparentHandler: setTransparencyLevel directly");
            g_enableTransparentAnimation = true;
        }
        else
        {
            m_currentTransparency += m_step;
            if (vfc::abs(m_currentTransparency - m_targetTransparency) < 0.01f)
            {
                m_currentTransparency = m_targetTransparency;
            }
        }

        switch (m_apaStatus)
        {
        case cc::target::common::EApaStatus::PassiveStandBy:
        case cc::target::common::EApaStatus::AutomaticParkingIsNotAvailable:
        {
            cc::assets::common::VehicleTransparencyUpdateCallback::setTransparencyLevel(m_currentTransparency);
            break;
        }
        case cc::target::common::EApaStatus::Searching:
        case cc::target::common::EApaStatus::GuidanceActive:
        case cc::target::common::EApaStatus::GuidanceSuspend:
        case cc::target::common::EApaStatus::GuidanceTerminate:
        case cc::target::common::EApaStatus::GuidanceCompleted:
        case cc::target::common::EApaStatus::ParkAssistStandby:
        {
            cc::assets::common::VehicleTransparencyUpdateCallback::setTransparencyLevel(1.0f);
            break;
        }
        default:
        {
            break;
        }
        }
        cc::assets::uielements::Vehicle2DOverlayManager::setVehicleTransparent(m_currentTransparency);
        // XLOG_INFO_OS(g_AppContext) << "VehicleTransparentHandler: Updating Transparency: "
        //     << "m_currentTransparency: " << m_currentTransparency
        //     << "m_targetTransparency: " << m_targetTransparency
        //     << XLOG_ENDL;
        m_state = State::Transitioning;
    }
    else
    {
        m_state = State::Idle;
    }

}

void VehicleTransparentHandler::update()
{
    // float animationDuration = IMGUI_GET_SLIDER_FLOAT("Settings", "FadeDuration", 0.1f, 5.0f);
    // XLOG_INFO_OS(g_AppContext) << "VehicleTransparentHandler: State: "
    //                            << (m_state == State::Transitioning ? "Transition" : "Idle") << XLOG_ENDL;
    switch (m_state)
    {
    case State::Idle:
    {
        onIdle();
        break;
    }
    case State::Transitioning:
    {
        onTransition();
        break;
    }
    default:
    {
        break;
    }
    }

    switch (m_apaStatus)
    {
    case cc::target::common::EApaStatus::PassiveStandBy:
    case cc::target::common::EApaStatus::AutomaticParkingIsNotAvailable:
    {
        cc::assets::common::VehicleTransparencyUpdateCallback::setTransparencyLevel(m_currentTransparency);
        break;
    }
    case cc::target::common::EApaStatus::Searching:
    case cc::target::common::EApaStatus::GuidanceActive:
    case cc::target::common::EApaStatus::GuidanceSuspend:
    case cc::target::common::EApaStatus::GuidanceTerminate:
    case cc::target::common::EApaStatus::GuidanceCompleted:
    case cc::target::common::EApaStatus::ParkAssistStandby:
    {
        cc::assets::common::VehicleTransparencyUpdateCallback::setTransparencyLevel(1.0f);
        break;
    }
    default:
    {
        break;
    }
    }

    m_previousApaStatus = m_apaStatus;
    IMGUI_LOG("Settings", "CurrentTransparency", m_currentTransparency);
    IMGUI_LOG("Settings", "TargetTransparency", m_targetTransparency);
    IMGUI_LOG("Settings", "State", m_state == State::Transitioning ? "Transition" : "Idle");
    return;
}

bool VehicleTransparentHandler::handle(
    const osgGA::GUIEventAdapter& f_ea,
    osgGA::GUIActionAdapter& /* f_aa */) // PRQA S 1724
{
    if (osgGA::GUIEventAdapter::FRAME == f_ea.getEventType())
    {
        using namespace cc::target::common;
        updateInput();

        // Using Imgui to SIL test
        if (!IMGUI_GET_CHECKBOX_BOOL("VehicleTransparentHandler", "ImguiOverwrite"))
        {
            // Using real data
            // cc::assets::common::VehicleTransparencyUpdateCallback::setTransparencyLevel(1.0f);
            // cc::assets::uielements::Vehicle2DOverlayManager::setVehicleTransparent(1.0f);
            // auto level1 = IMGUI_GET_SLIDER_FLOAT("VehicleTransparentHandler", "Level_1_Transparent", 0.0f, 1.0f);
            update();
        }
        else
        {
            // Using fake Imgui data
            updateImgui();
        }
    }
    return false;
}

} // namespace core
} // namespace cc
