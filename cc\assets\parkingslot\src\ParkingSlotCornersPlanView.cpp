//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/assets/parkingslot/inc/ParkingSlotCornersPlanView.h"
#include "cc/assets/parkingslot/inc/ParkingSlotCorners.h"
#include "cc/assets/parkingslot/inc/ParkingSlotUtils.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingManager.h"

#include "cc/util/polygonmath/inc/PolygonMath.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Utils.h"

#include "cc/imgui/inc/imgui_manager.h"
#ifdef TARGET_STANDALONE
#include "cc/imgui/inc/imgui_manager.h"
#include "cc/imgui/inc/implot/implot.h"
#endif

#include <algorithm>
#include <iterator>
#include <osgDB/ReadFile>

using cc::util::logging::g_parkingSlotContext;

#define GET_PORT_DATA(dataDaddy, port, allPortHaveDataFlag)                                                            \
    auto(dataDaddy)       = (port).getData();                                                                          \
    (allPortHaveDataFlag) = (allPortHaveDataFlag) && ((dataDaddy) != nullptr);                                         \
    if ((dataDaddy) == nullptr)                                                                                        \
    {                                                                                                                  \
        XLOG_ERROR(g_parkingSlotContext, #port << " doesn't have data!\n");                                            \
    }

namespace cc
{
namespace assets
{
namespace parkingslot
{
namespace corners
{

pc::util::coding::Item<ParkingSlotCornersPlanViewSettings>
    g_managerCornersPlanViewSettings("ManagerCornersPlanViewSettings");

osg::ref_ptr<osg::Texture2D> ParkingSlotCornersPlanView::m_selectedTexturePlanviewCross    = nullptr;
osg::ref_ptr<osg::Texture2D> ParkingSlotCornersPlanView::m_selectedTexturePlanviewParallel = nullptr;
osg::ref_ptr<osg::Texture2D> ParkingSlotCornersPlanView::m_slotCodeTexture_P_selectable    = nullptr;
osg::ref_ptr<osg::Texture2D> ParkingSlotCornersPlanView::m_slotCodeTexture_P_selected      = nullptr;

#if DEBUG_CORNER
osg::ref_ptr<osg::Texture2D> ParkingSlotCornersPlanView::m_corner1 = nullptr;
osg::ref_ptr<osg::Texture2D> ParkingSlotCornersPlanView::m_corner2 = nullptr;
osg::ref_ptr<osg::Texture2D> ParkingSlotCornersPlanView::m_corner3 = nullptr;
osg::ref_ptr<osg::Texture2D> ParkingSlotCornersPlanView::m_corner4 = nullptr;
#endif

static osg::Texture2D* readTexture(const std::string& f_fileName)
{
    osg::Image* image = osgDB::readImageFile(f_fileName);
    if (nullptr == image)
    {
        XLOG_ERROR(g_parkingSlotContext, "ParkingSlotCornersManager image missing!");
    }
    osg::Texture2D* texture2D = new osg::Texture2D(image);
    texture2D->setDataVariance(osg::Object::STATIC);
    texture2D->setUnRefImageDataAfterApply(true);
    texture2D->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    texture2D->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    texture2D->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    texture2D->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    return texture2D;
}

//!
//! ParkingSlotCornersPlanView
//!
ParkingSlotCornersPlanView::ParkingSlotCornersPlanView(pc::core::Framework* f_framework)
    : osg::Group{}
    , m_framework(f_framework)
{
    setName("ParkingSlotCornersPlanView");
    setNumChildrenRequiringUpdateTraversal(1u);
}

void ParkingSlotCornersPlanView::updateInput()
{
    auto* framework      = m_framework->asCustomFramework();
    bool  allPortHasData = true;
    // GET_PORT_DATA(hmiDataContainer, framework->m_hmiDataReceiver, allPortHasData);
    // GET_PORT_DATA(touchStatusContainer, framework->m_HUTouchTypeReceiver, allPortHasData);
    GET_PORT_DATA(parkHmiContainer, framework->m_ParkhmiToSvs_ReceiverPort, allPortHasData);

    if (!allPortHasData)
    {
        XLOG_ERROR(g_parkingSlotContext, "ParkingSlotCornersPlanView: Some ports do not have data!");
        return;
    }

    // m_touchPosition.x() = static_cast<vfc::float32_t>(hmiDataContainer->m_Data.m_huX);
    // m_touchPosition.y() = static_cast<vfc::float32_t>(hmiDataContainer->m_Data.m_huY);
    // m_touchStatus       = static_cast<TouchStatus>(touchStatusContainer->m_Data);

    // m_apaStatus    = parkHmiContainer->m_Data.m_apaStatus;
    m_parkingStage = parkHmiContainer->m_Data.m_parkingStage;
    m_parkMode     = parkHmiContainer->m_Data.m_parkMode;
    // m_parkInDirection                   = parkHmiContainer->m_Data.m_parkInDirection;
    // m_parkingRealTimeData               = parkHmiContainer->m_Data.m_parkingRealTimeData;
    // m_pocEnabledDir.m_FrntLeParallelSts = parkHmiContainer->m_Data.m_pocEnabledDir.m_FrntLeParallelSts;
    // m_pocEnabledDir.m_FrntLeCrossSts    = parkHmiContainer->m_Data.m_pocEnabledDir.m_FrntLeCrossSts;
    // m_pocEnabledDir.m_FrntRiParallelSts = parkHmiContainer->m_Data.m_pocEnabledDir.m_FrntRiParallelSts;
    // m_pocEnabledDir.m_FrntRiCrossSts    = parkHmiContainer->m_Data.m_pocEnabledDir.m_FrntRiCrossSts;
    // m_pocEnabledDir.m_FrntCrossSts      = parkHmiContainer->m_Data.m_pocEnabledDir.m_FrntCrossSts;
    // m_pocEnabledDir.m_BackCrossSts      = parkHmiContainer->m_Data.m_pocEnabledDir.m_BackCrossSts;
    // m_pocEnabledDir.m_BackLeCrossSts    = parkHmiContainer->m_Data.m_pocEnabledDir.m_BackLeCrossSts;
    // m_pocEnabledDir.m_BackRiCrossSts    = parkHmiContainer->m_Data.m_pocEnabledDir.m_BackRiCrossSts;
    // m_pocRecommandDir                   = parkHmiContainer->m_Data.m_pocRecommandDir;
    m_freeParkingIn.m_is360FreeParking = parkHmiContainer->m_Data.m_freeParkingIn.m_is360FreeParking;
    m_freeParkingIn.m_isSlotParkable   = parkHmiContainer->m_Data.m_freeParkingIn.m_isSlotParkable;
    m_freeParkingIn.m_parkStage        = parkHmiContainer->m_Data.m_freeParkingIn.m_parkStage;
    m_freeParkingIn.m_slotType         = parkHmiContainer->m_Data.m_freeParkingIn.m_slotType;
    if (m_freeParkingIn.m_parkStage ==  EFreeParkingStage::GuidanceStart)
    {
        m_targetSlotPosition               = parkHmiContainer->m_Data.m_targetSlotPosition;
    }
}

#ifdef TARGET_STANDALONE
struct SlotData
{
    osg::Vec2f& GetFrontRightCorner()
    {
        return m_corners[0];
    }
    osg::Vec2f& GetFrontLeftCorner()
    {
        return m_corners[1];
    }
    osg::Vec2f& GetRearLeftCorner()
    {
        return m_corners[2];
    }
    osg::Vec2f& GetRearRightCorner()
    {
        return m_corners[3];
    }

    std::array<osg::Vec2f, 4> m_corners;
    osg::Vec2f                m_center;
    vfc::float32_t            m_orientation;
};

void ParkingSlotCornersPlanView::inputImguiSlot()
{
    using cc::util::polygonmath::RectangleMath;
    std::array<osg::Vec2f, 5> cornersRectangle{};
    std::array<osg::Vec2f, 5> cornersParallelogram{};
    SlotData                  l_slotData;
    bool                      resetOrigin = IMGUI_GET_CHECKBOX_BOOL(getName().c_str(), "resetPosition");
    if (resetOrigin)
    {
        l_slotData.m_center.x() = 0.0f;
        l_slotData.m_center.y() = 0.0f;
    }
    else
    {
        l_slotData.m_center.x() = IMGUI_GET_SLIDER_FLOAT(getName().c_str(), "slotPosX", -8.0f, 8.0f);
        l_slotData.m_center.y() = IMGUI_GET_SLIDER_FLOAT(getName().c_str(), "slotPosY", -8.0f, 8.0f);
    }
    l_slotData.m_orientation           = IMGUI_GET_SLIDER_FLOAT(getName().c_str(), "orientation", -180.0f, 180.0f);
    vfc::float32_t width               = IMGUI_GET_SLIDER_FLOAT(getName().c_str(), "Width", 0.0f, 10.0f);
    vfc::float32_t length              = IMGUI_GET_SLIDER_FLOAT(getName().c_str(), "Length", 0.0f, 10.0f);
    vfc::float32_t parallelogramFactor = IMGUI_GET_SLIDER_FLOAT(getName().c_str(), "parallelogramFactor", 0.1f, 3.0f);
    bool           parallelogramRevertDirection = IMGUI_GET_CHECKBOX_BOOL(getName().c_str(), "parallelogram revert");

    RectangleMath rectangleMath{{}};
    rectangleMath.setWidth(width);
    rectangleMath.setLength(length);
    rectangleMath.setCenterPoint(l_slotData.m_center);
    rectangleMath.setAngle(l_slotData.m_orientation);
    rectangleMath.updateRectPoints(
        l_slotData.GetFrontRightCorner(),
        l_slotData.GetFrontLeftCorner(),
        l_slotData.GetRearLeftCorner(),
        l_slotData.GetRearRightCorner());

    m_targetSlotPosition.m_point1.m_x = l_slotData.m_corners[0u].x();
    m_targetSlotPosition.m_point2.m_x = l_slotData.m_corners[1u].x();
    m_targetSlotPosition.m_point3.m_x = l_slotData.m_corners[2u].x();
    m_targetSlotPosition.m_point4.m_x = l_slotData.m_corners[3u].x();

    m_targetSlotPosition.m_point1.m_y = l_slotData.m_corners[0u].y();
    m_targetSlotPosition.m_point2.m_y = l_slotData.m_corners[1u].y();
    m_targetSlotPosition.m_point3.m_y = l_slotData.m_corners[2u].y();
    m_targetSlotPosition.m_point4.m_y = l_slotData.m_corners[3u].y();
}
#endif

void ParkingSlotCornersPlanView::updateSlot()
{
    resetSlot();
    if (!(m_parkingStage == EParkingStage::FreeParking))
    {
        return;
    }
    if (m_freeParkingIn.m_parkStage == EFreeParkingStage::InFpStandstill)
    {
        updateConfirming();
    }
    if (m_freeParkingIn.m_parkStage == EFreeParkingStage::GuidanceStart)
    {
        updateGuidance();
    }
    if (m_freeParkingIn.m_parkStage == EFreeParkingStage::GuidanceFinish)
    {
        // No show slot in last guidence
        m_targetSlotPosition.m_point1.reset();
        m_targetSlotPosition.m_point2.reset();
        m_targetSlotPosition.m_point3.reset();
        m_targetSlotPosition.m_point4.reset();
    }
}

void ParkingSlotCornersPlanView::updateConfirming()
{
    auto scene = m_framework->getScene();
    if (scene == nullptr)
    {
        XLOG_WARN(g_parkingSlotContext, "ParkingSlotCornersPlanView::updateConfirming - scene is nullptr");
        return;
    }
    auto parkingView = scene->getView(cc::core::CustomViews::HORI_PARKING_PLAN_VIEW_VEHICLE2D);
    if (parkingView == nullptr)
    {
        XLOG_WARN(g_parkingSlotContext, "ParkingSlotCornersPlanView::updateConfirming - parkingView is nullptr");
        return;
    }
    auto freeparkingManager = dynamic_cast<cc::assets::freeparkingoverlay::FreeparkingManager*>(
        parkingView->getAsset(cc::core::AssetId::EASSETS_FREEPARKING_OVERLAY)->getAsset()
    );
    if (freeparkingManager == nullptr)
    {
        XLOG_WARN(g_parkingSlotContext, "ParkingSlotCornersPlanView::updateConfirming - cannot retrieve freeparking manager");
        return;
    }

    m_isTargetSideLeft = freeparkingManager->getSlotCenter().y() > 0.0f;
}

void ParkingSlotCornersPlanView::updateGuidance()
{
#ifdef TARGET_STANDALONE
    inputImguiSlot();
#endif
    m_targetSlot->updateGeometry(
        {m_targetSlotPosition.m_point1.m_x, m_targetSlotPosition.m_point1.m_y},
        {m_targetSlotPosition.m_point2.m_x, m_targetSlotPosition.m_point2.m_y},
        {m_targetSlotPosition.m_point3.m_x, m_targetSlotPosition.m_point3.m_y},
        {m_targetSlotPosition.m_point4.m_x, m_targetSlotPosition.m_point4.m_y});
    m_targetSlot->setNodeMask(~0u);
    m_targetSlot->setImguiEnabled(true);
    m_targetSlot->setFrontNodeVisible(true);
    m_targetSlot->setBackNodeVisible(true);
    // m_targetSlot->setSlotSide(m_isSlotLeft ? ParkingSlotCorners::SlotSide::Left : ParkingSlotCorners::SlotSide::Right);
    m_targetSlot->setFrontRotate(m_isTargetSideLeft ? 0.0f : 180.0f);
    // updateSlotGeometry(slot, slotInfo);
    m_targetSlot->setFrontNodeTexture(m_slotCodeTexture_P_selected);
    m_targetSlot->setBackNodeTexture(
        (m_targetSlot->getSlotType() == ParkingSlotCorners::SlotType::Cross) ? m_selectedTexturePlanviewCross
                                                                             : m_selectedTexturePlanviewParallel);

#if DEBUG_CORNER
    m_debugCorners->setNodeMask(~0u);
    for (vfc::uint32_t i = 0u; i < 4u; i++)
    {
        auto       corner = static_cast<osg::MatrixTransform*>(m_debugCorners->getChild(i));
        osg::Vec3f position;
        position.z() = 0.01f;
        switch (i)
        {
        case 0:
        {
            position.x() = m_targetSlotPosition.m_point1.m_x;
            position.y() = m_targetSlotPosition.m_point1.m_y;
            break;
        }
        case 1:
        {
            position.x() = m_targetSlotPosition.m_point2.m_x;
            position.y() = m_targetSlotPosition.m_point2.m_y;
            break;
        }
        case 2:
        {
            position.x() = m_targetSlotPosition.m_point3.m_x;
            position.y() = m_targetSlotPosition.m_point3.m_y;
            break;
        }
        case 3:
        {
            position.x() = m_targetSlotPosition.m_point4.m_x;
            position.y() = m_targetSlotPosition.m_point4.m_y;
            break;
        }
        default:
        {
            break;
        }
        }
        corner->setMatrix(osg::Matrixf::translate(position));
        corner->setNodeMask(~0u);
    }
#endif
}

void ParkingSlotCornersPlanView::resetSlot()
{
    m_targetSlot->setNodeMask(0u);
    m_targetSlot->setMiddleNodeVisible(false);
    m_targetSlot->setFrontNodeVisible(false);
    m_targetSlot->setBackNodeVisible(false);
#if DEBUG_CORNER
    m_debugCorners->setNodeMask(0u);
#endif
}

void ParkingSlotCornersPlanView::init()
{
    osg::Group::removeChildren(0u, getNumChildren());
    m_selectedTexturePlanviewCross =
        readTexture(CONCAT_PATH_VEHICLE_MODEL(g_managerCornersPlanViewSettings->m_freeparkingGuidanceCross));
    m_selectedTexturePlanviewParallel =
        readTexture(CONCAT_PATH_VEHICLE_MODEL(g_managerCornersPlanViewSettings->m_freeparkingGuidanceParallel));
    m_slotCodeTexture_P_selectable =
        readTexture(CONCAT_PATH_VEHICLE_MODEL(g_managerCornersPlanViewSettings->m_slotCode_P_selectable));
    m_slotCodeTexture_P_selected = readTexture(CONCAT_PATH_VEHICLE_MODEL(g_managerCornersPlanViewSettings->m_slotCode_P_selected));

    m_targetSlot = new ParkingSlotCorners(m_framework);
    osg::Group::addChild(m_targetSlot);

#if DEBUG_CORNER
    m_corner1      = readTexture(CONCAT_PATH(g_managerCornersPlanViewSettings->m_corner1));
    m_corner2      = readTexture(CONCAT_PATH(g_managerCornersPlanViewSettings->m_corner2));
    m_corner3      = readTexture(CONCAT_PATH(g_managerCornersPlanViewSettings->m_corner3));
    m_corner4      = readTexture(CONCAT_PATH(g_managerCornersPlanViewSettings->m_corner4));
    m_debugCorners = new osg::Group;
    m_debugCorners->setNodeMask(0u);
    for (int cornerIndex = 0; cornerIndex < 4; cornerIndex++)
    {
        osg::MatrixTransform* l_transform = createCornerNode(cornerIndex);
        m_debugCorners->addChild(l_transform);
    }
    osg::Group::addChild(m_debugCorners);
#endif

    resetSlot();
}
#if DEBUG_CORNER
osg::MatrixTransform* ParkingSlotCornersPlanView::createCornerNode(vfc::int32_t f_cornerIndex)
{
    osg::MatrixTransform* l_transform = new osg::MatrixTransform;
    osg::Geode*           l_corner    = new osg::Geode;
    osg::Vec3f            l_u         = -osg::Y_AXIS * g_managerCornersPlanViewSettings->m_debugCornerLength;
    osg::Vec3f            l_v         = osg::X_AXIS * g_managerCornersPlanViewSettings->m_debugCornerLength;
    osg::Geometry*        l_geometry  = pc::util::osgx::createTexturePlane(-(l_u + l_v) * 0.5f, l_u, l_v, 1, 1);
    osg::StateSet*        l_stateSet  = l_transform->getOrCreateStateSet();
    switch (f_cornerIndex)
    {
    default:
    case 0:
        l_stateSet->setTextureAttribute(0, m_corner1);
        break;
    case 1:
        l_stateSet->setTextureAttribute(0, m_corner2);
        break;
    case 2:
        l_stateSet->setTextureAttribute(0, m_corner3);
        break;
    case 3:
        l_stateSet->setTextureAttribute(0, m_corner4);
        break;
    }
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(pc::core::g_renderOrder->m_hudElements, "RenderBin");
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet);
    l_corner->addDrawable(l_geometry);
    l_transform->addChild(l_corner);
    return l_transform;
}
#endif

void ParkingSlotCornersPlanView::traverse(osg::NodeVisitor& f_nv)
{
    if (f_nv.getVisitorType() == osg::NodeVisitor::UPDATE_VISITOR)
    {
        if (m_modifiedCount != g_managerCornersPlanViewSettings->getModifiedCount())
        {
            init();
            m_modifiedCount = g_managerCornersPlanViewSettings->getModifiedCount();
        }
        updateInput();
        updateSlot();
    }
    osg::Group::traverse(f_nv);
}

} // namespace corners
} // namespace parkingslot
} // namespace assets
} // namespace cc
