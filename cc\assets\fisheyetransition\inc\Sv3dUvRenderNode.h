//-------------------------------------------------------------------------------
// Copyright (c) 2024 by <PERSON>. All rights reserved.
// This file is property of <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_FISHEYE_TRANSITION_SV3D_UV_RENDER_NODE_HPP
#define CC_ASSETS_FISHEYE_TRANSITION_SV3D_UV_RENDER_NODE_HPP
#include "pc/svs/factory/inc/SV3DGeode.h"

#include "osg/Group"
#include "osg/ref_ptr"

namespace pc
{
namespace core
{
class Framework;
} // namespace core

namespace factory
{
class SV3DNode;
} // namespace factory

namespace views
{
namespace warpfisheye
{
class WarpFisheyeView;
} // namespace warpfisheye
} // namespace views
} // namespace pc

namespace cc
{
namespace assets
{
namespace fisheyetransition
{

/**
 * Node which renders the UV coordinates of SingleCam bowl/floor geometry
 */
class SV3DUVRenderNode : public osg::Group
{
public:
    SV3DUVRenderNode(pc::factory::SV3DNode* f_sv3dNode, pc::factory::SingleCamArea f_area);

    virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:
    SV3DUVRenderNode(const SV3DUVRenderNode&) = delete;
    SV3DUVRenderNode& operator=(const SV3DUVRenderNode&) = delete;
    ~SV3DUVRenderNode() = default;

protected:
    osg::ref_ptr<pc::factory::SV3DNode> m_sv3dNode;
    unsigned int                        m_lastVertexArrayUpdate;
};

} // namespace fisheyetransition
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_FISHEYE_TRANSITION_SV3D_UV_RENDER_NODE_HPP
