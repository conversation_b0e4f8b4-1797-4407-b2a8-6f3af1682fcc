//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BJEV AVAP
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  TileSpline.cpp
/// @brief
//=============================================================================

#include "cc/assets/tileoverlay/inc/TileSpline.h"
#include "cc/assets/tileoverlay/inc/SplineHelper.h"
#include "cc/assets/tileoverlay/inc/TileSettings.h"
#include "cc/core/inc/CustomUltrasonic.h"

#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/math/inc/Interpolator.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "osg/Geometry"
#include "osg/Math"
#include "osg/Image"
#include "osg/Texture2D"

#define USE_SWD
namespace cc
{
namespace assets
{
namespace tileoverlay
{
constexpr vfc::int32_t g_maxByte = 255;
constexpr vfc::int32_t g_fixedTileNum = 4;

TileLayout::TileLayout()
  : m_shieldStartInner(0.0f, 0.0f)
  // , m_shieldStartMiddle(0.0f, 0.0f)
  , m_shieldStartOuter(0.0f, 0.0f)
  , m_shieldStartSolid(0.0f, 0.0f)
  , m_shieldStart3DOuter(0.0f, 0.0f)
  , m_shieldEndInner(0.0f, 0.0f)
  // , m_shieldEndMiddle(0.0f, 0.0f)
  , m_shieldEndOuter(0.0f, 0.0f)
  , m_shieldEndSolid(0.0f, 0.0f)
  , m_shieldEnd3DOuter(0.0f, 0.0f)
  , m_verticalRatioDivideOffset(0.0f, 0.0f)
  , m_verticalOffset(0.0f, 0.0f)
  , m_isStartEdgeSegment(false)
  , m_isEndEdgeSegment(false)
  , m_currentDistance(10.0f)
  , m_currentDistanceForPosDisp(0.0f)
  , m_ObjMovingSts(OBJ_MOVING_UNKNOW)
  , m_pasZoneLocatedArea(EPasZoneFront)
{
}

// TileLayout::~TileLayout()
// {
// }

osg::Vec4f fromRGBA(vfc::int32_t f_r, vfc::int32_t f_g, vfc::int32_t f_b, vfc::int32_t f_a = g_maxByte)
{
  return osg::Vec4f(osg::clampTo(static_cast<vfc::float32_t>(f_r), 0.0f, static_cast<vfc::float32_t>(g_maxByte)) / static_cast<vfc::float32_t> (g_maxByte),
                    osg::clampTo(static_cast<vfc::float32_t>(f_g), 0.0f, static_cast<vfc::float32_t>(g_maxByte)) / static_cast<vfc::float32_t> (g_maxByte),
                    osg::clampTo(static_cast<vfc::float32_t>(f_b), 0.0f, static_cast<vfc::float32_t>(g_maxByte)) / static_cast<vfc::float32_t> (g_maxByte),
                    osg::clampTo(static_cast<vfc::float32_t>(f_a), 0.0f, static_cast<vfc::float32_t>(g_maxByte)) / static_cast<vfc::float32_t> (g_maxByte));
}

osg::Vec4f fromRGBA(const osg::Vec4i& f_rgba)
{
  return fromRGBA(f_rgba.r(), f_rgba.g(), f_rgba.b(), f_rgba.a());
}

osg::Vec4f brighten(const osg::Vec4f& f_color, vfc::float32_t f_add)
{
  return osg::Vec4f(osg::clampBelow(f_color.r() + f_add, 1.0f),
                   osg::clampBelow(f_color.g() + f_add, 1.0f),
                   osg::clampBelow(f_color.b() + f_add, 1.0f),
                   f_color.a());
}

ColorInterpolator g_colorInterpolator;

// typedef pc::util::math::LinearInterpolator<vfc::float32_t> SplineWidthInterpolator;
// SplineWidthInterpolator g_splineWidthInterpolator;

void updateInterpolators()
{
  static vfc::uint32_t s_sequenceNumber = ~0u;
  if (g_tileSettings->getModifiedCount() != s_sequenceNumber)
  {
    s_sequenceNumber = g_tileSettings->getModifiedCount();

    std::vector<vfc::float32_t> l_stepSize(7u);
    l_stepSize[0u] = 0.0f;
    l_stepSize[1u] = 0.3f;
    l_stepSize[2u] = 0.0f;
    l_stepSize[3u] = 0.4f;
    l_stepSize[4u] = 0.0f;
    l_stepSize[5u] = 0.5f;
    //! Color interpolator
    vfc::float32_t l_pos = 0.0f;
    g_colorInterpolator.clear();
    for (vfc::uint32_t i = 0u; i < static_cast<vfc::uint32_t>(ColorValues::NUM_COLORS); ++i)
    {
      l_pos += l_stepSize[i];
      g_colorInterpolator.addSample(l_pos, fromRGBA(g_tileSettings->m_colors.getColor(i)));
    }
    g_colorInterpolator.addSample(g_tileSettings->m_distanceDefault - g_tileSettings->m_extractorOffset, fromRGBA(g_tileSettings->m_colorOn));
    g_colorInterpolator.init();
    //! Spline width interpolator
    // g_splineWidthInterpolator.clear();
    // g_splineWidthInterpolator.addSample(g_tileSettings->m_distanceNear - g_tileSettings->m_extractorOffset, g_tileSettings->m_spline2DWidthInnerNear);
    // g_splineWidthInterpolator.addSample(g_tileSettings->m_distanceFar - g_tileSettings->m_extractorOffset, g_tileSettings->m_spline2DWidthInnerFar);
    // g_splineWidthInterpolator.init();
  }
}


osg::Geometry* createGeometry(const std::string& f_name, bool f_withTexCoors = false)
{
  osg::Vec3Array* const l_vertices = new osg::Vec3Array;
  osg::Vec4Array* const l_colors = new osg::Vec4Array;

  //! create VBO manually in order to set the usage flag
  osg::VertexBufferObject* const l_vbo = new osg::VertexBufferObject;
  l_vbo->setUsage(GL_DYNAMIC_DRAW_ARB);
  l_vertices->setVertexBufferObject(l_vbo);

  osg::Geometry* const l_geometry = pc::util::osgx::createGeometry(f_name);
  l_geometry->setVertexArray(l_vertices);
  l_geometry->setColorArray(l_colors, osg::Array::BIND_PER_VERTEX);
  if (f_withTexCoors)
  {
    osg::Vec2Array* const l_texCoords = new osg::Vec2Array;
    l_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);
  }
  l_geometry->setDataVariance(osg::Object::DYNAMIC);
  return l_geometry;
}


osg::DrawElements* createSurface(
  vfc::uint32_t f_numLayoutPoints,
  vfc::uint32_t f_numSurfacePoints,
  vfc::uint32_t f_vertexOffset)
{
  const vfc::uint32_t l_numVertices = f_numLayoutPoints * f_numSurfacePoints;
  const vfc::uint32_t l_numIndices = f_numLayoutPoints * (f_numSurfacePoints - 1u) * 6u;
  osg::DrawElementsUShort* const l_indices = new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), l_numIndices);

  vfc::uint32_t l_indexCounter = 0u;
  for(vfc::uint32_t i = 0u; i < f_numLayoutPoints; ++i)
  {
    const vfc::uint32_t l_vo =  i * f_numSurfacePoints;
    for (vfc::uint32_t j = 0u; j < f_numSurfacePoints - 1u; ++j)
    {
      // 1st triangle
      (*l_indices)[l_indexCounter]   = static_cast<osg::DrawElementsUShort::value_type>(f_vertexOffset + ((l_vo + j) % l_numVertices));
      (*l_indices)[l_indexCounter+1u] = static_cast<osg::DrawElementsUShort::value_type>(f_vertexOffset + ((l_vo + j + 1u) % l_numVertices));
      (*l_indices)[l_indexCounter+2u] = static_cast<osg::DrawElementsUShort::value_type>(f_vertexOffset + ((l_vo + j + f_numSurfacePoints) % l_numVertices));
      // 2nd triangle
      (*l_indices)[l_indexCounter+3u] = static_cast<osg::DrawElementsUShort::value_type>(f_vertexOffset + ((l_vo + j + 1u) % l_numVertices));
      (*l_indices)[l_indexCounter+4u] = static_cast<osg::DrawElementsUShort::value_type>(f_vertexOffset + ((l_vo + j + 1u + f_numSurfacePoints) % l_numVertices));
      (*l_indices)[l_indexCounter+5u] = static_cast<osg::DrawElementsUShort::value_type>(f_vertexOffset + ((l_vo + j + f_numSurfacePoints) % l_numVertices));
      l_indexCounter += 6u;
    }
  }
  return l_indices;
}


osg::Image* createSmoothingTexture()
{
  const vfc::uint32_t l_imageWidth = osg::clampBelow(g_tileSettings->m_smoothingTextureSize, 512u);
  static constexpr vfc::uint32_t l_imageHeight = 1u;
  static constexpr vfc::uint32_t l_imageDepth = 1u;
  const vfc::float32_t l_gradientWidth = osg::clampTo(g_tileSettings->m_smoothingGradientSize, 0.0f, 0.5f);

  osg::Image* const l_image = new osg::Image;
  l_image->allocateImage(static_cast<vfc::int32_t>(l_imageWidth), static_cast<vfc::int32_t>(l_imageHeight), static_cast<vfc::int32_t>(l_imageDepth), GL_RGBA, GL_UNSIGNED_BYTE);
  osg::Vec4ub* const l_data = reinterpret_cast<osg::Vec4ub*> (l_image->data());
  for (vfc::uint32_t x = 0u; x < l_imageWidth; ++x)
  {
    const vfc::float32_t l_xNormalized = static_cast<vfc::float32_t>(x) / static_cast<vfc::float32_t> (l_imageWidth);
    vfc::float32_t l_alpha = 1.0f;
    if (l_gradientWidth > l_xNormalized)
    {
      l_alpha = pc::util::smootherstep(0.0f, l_gradientWidth, l_xNormalized);
    }
    else if ((1.0f - l_gradientWidth) < l_xNormalized)
    {
      l_alpha = pc::util::smootherstep((1.0f - l_gradientWidth), 1.0f, l_xNormalized);
    }
    else
    {
      //Do nothing
    }
    const vfc::uint32_t l_alphaByte = pc::util::osgx::toUByte(l_alpha);
    l_data[x] = osg::Vec4ub(0xffu, 0xffu, 0xffu, static_cast<osg::Vec4ub::value_type> (l_alphaByte));
  }
  l_image->setDataVariance(osg::Object::STATIC);
  return l_image;
}


osg::StateSet* getOrCreateTexturingStateSet()
{
  static osg::observer_ptr<osg::StateSet> s_stateSet;
  if (!s_stateSet.valid())
  {
    osg::StateSet* const l_stateSet = new osg::StateSet;
    //! load texture
    osg::Image* const l_image = createSmoothingTexture();
    osg::Texture2D* const l_texture = new osg::Texture2D(l_image);
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    l_texture->setResizeNonPowerOfTwoHint(false);
    l_texture->setUnRefImageDataAfterApply(true);
    l_stateSet->setTextureAttribute(0u, l_texture);
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet);  // PRQA S 3803
    s_stateSet = l_stateSet;
  }
  return s_stateSet.get();
}

void updatePasZoneAlphaByThreshold(const EPasZoneLocatedArea f_zoneArea, const vfc::float32_t f_currentDistance, const EObjMovingSts f_ObjMovingSts, vfc::float32_t& f_currentalpha) // PRQA S 6043  // PRQA S 6040
{
  switch (f_zoneArea)
  {
    case EPasZoneFront:
    {
      if (f_currentDistance > (g_tileSettings->m_distanceThreshL3 + g_tileSettings->m_HysteresisDistanceThresh))
      {
        f_currentalpha = 0.0f;
      }
      else if( (f_currentDistance > g_tileSettings->m_distanceThreshL3) &&
           (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV1) &&
           (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV2) &&
           (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV3)    )
      {
        f_currentalpha = 0.0f;
      }
      else
      {
        // do nothing
      }
    }
    break;
    case EPasZoneCorner:
    {
      vfc::float32_t l_distanceThreshL1 = g_tileSettings->m_distanceThreshL1;
      vfc::float32_t l_distanceThreshL2 = g_tileSettings->m_distanceThreshL2;
      vfc::float32_t l_distanceThreshL3 = g_tileSettings->m_distanceThreshL3;

      if (l_distanceThreshL1 > g_tileSettings->m_distanceThreshCorner)
      {
         l_distanceThreshL1 = g_tileSettings->m_distanceThreshCorner;
      }
      else if (l_distanceThreshL2 > g_tileSettings->m_distanceThreshCorner)
      {
        l_distanceThreshL2 = g_tileSettings->m_distanceThreshCorner;
      }
      else if (l_distanceThreshL3 > g_tileSettings->m_distanceThreshCorner)
      {
        l_distanceThreshL3 = g_tileSettings->m_distanceThreshCorner;
      }
      else
      {
        // do nothing
      }

      if (f_currentDistance > (g_tileSettings->m_distanceThreshCorner + g_tileSettings->m_HysteresisDistanceThresh))
      {
        f_currentalpha = 0.0f;
      }
      else if (f_currentDistance > g_tileSettings->m_distanceThreshCorner)
      {
        if ( (g_tileSettings->m_distanceThreshCorner < l_distanceThreshL2) &&
              (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV1) )
        {
          f_currentalpha = 0.0f;
        }
        else if ( (g_tileSettings->m_distanceThreshCorner < l_distanceThreshL3) &&
                  (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV1) &&
                  (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV2) )
        {
          f_currentalpha = 0.0f;
        }
        else if ( (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV1) &&
                  (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV2) &&
                  (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV3) )
        {
          f_currentalpha = 0.0f;
        }
        else
        {
          // do nothing
        }
      }
      else
      {
        // do nothing
      }
    }
    break;
    case EPasZoneSide:
    {
      vfc::float32_t l_distanceThreshL1 = g_tileSettings->m_distanceThreshL1;
      vfc::float32_t l_distanceThreshL2 = g_tileSettings->m_distanceThreshL2;
      vfc::float32_t l_distanceThreshL3 = g_tileSettings->m_distanceThreshL3;

      if (l_distanceThreshL1 > g_tileSettings->m_distanceThreshCorner)
      {
         l_distanceThreshL1 = g_tileSettings->m_distanceThreshCorner;
      }
      else if (l_distanceThreshL2 > g_tileSettings->m_distanceThreshCorner)
      {
        l_distanceThreshL2 = g_tileSettings->m_distanceThreshCorner;
      }
      else if (l_distanceThreshL3 > g_tileSettings->m_distanceThreshCorner)
      {
        l_distanceThreshL3 = g_tileSettings->m_distanceThreshCorner;
      }
      else
      {
        // do nothing
      }

      if (f_currentDistance > (g_tileSettings->m_distanceThreshCorner + g_tileSettings->m_HysteresisDistanceThresh))
      {
        f_currentalpha = 0.0f;
      }
      else if (f_currentDistance > g_tileSettings->m_distanceThreshCorner)
      {
        if ( (g_tileSettings->m_distanceThreshCorner < l_distanceThreshL2) &&
              (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV1) )
        {
          f_currentalpha = 0.0f;
        }
        else if ( (g_tileSettings->m_distanceThreshCorner < l_distanceThreshL3) &&
                  (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV1) &&
                  (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV2) )
        {
          f_currentalpha = 0.0f;
        }
        else if ( (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV1) &&
                  (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV2) &&
                  (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV3) )
        {
          f_currentalpha = 0.0f;
        }
        else
        {
          // do nothing
        }
      }
      else
      {
        // do nothing
      }
      // f_currentalpha = 0.0f;
    }
    break;
    case EPasZoneRear:
    {
      if (f_currentDistance > (g_tileSettings->m_distanceThreshL3 + g_tileSettings->m_HysteresisDistanceThresh))
      {
        f_currentalpha = 0.0f;
      }
      else if ( (f_currentDistance > g_tileSettings->m_distanceThreshL3) &&
           (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV1) &&
           (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV2) &&
           (f_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV3)    )
      {
        f_currentalpha = 0.0f;
      }
      else
      {
        // do nothing
      }
    }
    break;
    default:
      break;
  }
}

//!
//! TileSpleine::UpdateVisitor
//!
// TileSpline::UpdateVisitor::UpdateVisitor(const pc::util::Polygon2D& f_layout, const DistanceList& f_distances)
//   : osg::NodeVisitor(osg::NodeVisitor::TRAVERSE_ALL_CHILDREN)
//   , m_layout(f_layout)
//   , m_distances(f_distances)
// {
//   m_normals = pc::util::computeNormals(f_layout);
// }

//!
//! TileUpdateVisitor
//!
TileUpdateVisitor::TileUpdateVisitor()
: osg::Referenced()
, osg::NodeVisitor(osg::NodeVisitor::TRAVERSE_ALL_CHILDREN)
, m_layout()
, m_layoutShadow()
, m_InnerControlPoints({{{0,0}}})
, m_OuterControlPoints({{{0,0}}})
, m_InnerPoints({{{0,0}}})
, m_OuterPoints({{{0,0}}})
, m_InnerPointsArray()
{
}


TileUpdateVisitor::~TileUpdateVisitor() = default;


void TileUpdateVisitor::copyInterpolateArray(PointArrayZone* f_array)
{
  std::memcpy(static_cast<void*>(f_array), static_cast<void*>(&m_InnerPointsArray), sizeof(m_InnerPointsArray));
}


void TileUpdateVisitor::apply(osg::Node& f_node)
{
  TileSpline* const l_tileSpline = dynamic_cast<TileSpline*> (&f_node);
  if (l_tileSpline != nullptr)
  {
    l_tileSpline->update(*this);
  }
  traverse(f_node);
}

// void TileUpdateVisitor::setLayoutData(const std::vector<TileSectorData>& f_tileSectors)  // PRQA S 6043
// {
//   const std::size_t l_numSectors = f_tileSectors.size();
//   m_layout.clear();

//   //! find the points
//   for (vfc::uint32_t i = 0u; i < l_numSectors; ++i)
//   {
//     //! shield vertices
//     std::vector<osg::Vec2f> l_pts(4u);
//     osg::Vec2f l_l2r, l_r2l;
//     osg::Vec2f l_shiftDir;
//     vfc::float32_t l_offsetWidth = g_tileSettings->m_shieldOffset;
//     vfc::float32_t l_shieldThickness = g_tileSettings->m_shieldThickness;

//     //! set the basic 4 points at the bottom
//     if (0u == i)
//     {
//       l_l2r = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint - f_tileSectors[i].m_leftBorderRefPoint;
//       l_l2r.normalize();  // PRQA S 3803

//       l_r2l = f_tileSectors[i].m_leftBorderRefPoint - f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint;
//       l_r2l.normalize();  // PRQA S 3803

//       l_pts[0u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * f_tileSectors[i].m_currentDistance) + l_l2r * l_offsetWidth;
//       l_pts[1u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * (f_tileSectors[i].m_currentDistance + l_shieldThickness)) + l_l2r * l_offsetWidth;
//       l_pts[2u] = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint + (f_tileSectors[l_numSectors-1u].m_leftBorderDir * (f_tileSectors[i].m_currentDistance + l_shieldThickness)) + l_r2l * l_offsetWidth;
//       l_pts[3u] = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint + (f_tileSectors[l_numSectors-1u].m_leftBorderDir * f_tileSectors[i].m_currentDistance) + l_r2l * l_offsetWidth;

//       //! shift direction
//       l_shiftDir = (f_tileSectors[l_numSectors-1u].m_leftBorderDir + f_tileSectors[i].m_leftBorderDir) * 0.5f;
//     }
//     else
//     {
//       l_l2r = f_tileSectors[i-1u].m_leftBorderRefPoint - f_tileSectors[i].m_leftBorderRefPoint;
//       l_l2r.normalize();  // PRQA S 3803

//       l_r2l = f_tileSectors[i].m_leftBorderRefPoint - f_tileSectors[i-1u].m_leftBorderRefPoint;
//       l_r2l.normalize();  // PRQA S 3803

//       l_pts[0u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * f_tileSectors[i].m_currentDistance) + l_l2r * l_offsetWidth;
//       l_pts[1u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * (f_tileSectors[i].m_currentDistance + l_shieldThickness)) + l_l2r * l_offsetWidth;
//       l_pts[2u] = f_tileSectors[i-1u].m_leftBorderRefPoint + (f_tileSectors[i-1u].m_leftBorderDir * (f_tileSectors[i].m_currentDistance + l_shieldThickness)) + l_r2l * l_offsetWidth;
//       l_pts[3u] = f_tileSectors[i-1u].m_leftBorderRefPoint + (f_tileSectors[i-1u].m_leftBorderDir * f_tileSectors[i].m_currentDistance) + l_r2l * l_offsetWidth;

//       //! shift direction
//       l_shiftDir = (f_tileSectors[i-1u].m_leftBorderDir + f_tileSectors[i].m_leftBorderDir) * 0.5f;
//     }

//     // map the layoput data
//     // End-----Start
//     // 1 ------ 2  outer
//     // --0----3--  inner
//     TileLayout l_tempLayout;
//     l_tempLayout.m_currentDistance  = f_tileSectors[i].m_currentDistance;
//     l_tempLayout.m_ObjMovingSts     = f_tileSectors[i].m_ObjMovingSts;
//     l_tempLayout.m_shieldStartInner = l_pts[3u];
//     l_tempLayout.m_shieldStartOuter = l_pts[2u];
//     l_tempLayout.m_shieldEndInner   = l_pts[0u];
//     l_tempLayout.m_shieldEndOuter   = l_pts[1u];

//     //! Vertical offset
//     l_tempLayout.m_verticalRatioDivideOffset = l_shiftDir * g_tileSettings->m_shieldTopOffset * g_tileSettings->m_shieldRatioDivide;
//     l_tempLayout.m_verticalOffset            = l_shiftDir * g_tileSettings->m_shieldTopOffset;

//     //! map the pas zone areas
//     if ((i < (cc::core::PasZonesDescription::NUM_PAS_FRONT >> 1u))
//         || ((i >= (cc::core::PasZonesDescription::NUM_PAS_FRONTRIGHT >> 1u))
//         && (i < (cc::core::PasZonesDescription::NUM_PAS_ZONES >> 1u))) )
//     {
//       // Front
//       l_tempLayout.m_pasZoneLocatedArea = EPasZoneFront;
//     }
//     else if (i < (cc::core::PasZonesDescription::NUM_PAS_FRONTLEFT >> 1u))
//     {
//       // Front left corner
//       l_tempLayout.m_pasZoneLocatedArea = EPasZoneCorner;
//     }
//     else if (i < (cc::core::PasZonesDescription::NUM_PAS_LEFT >> 1u))
//     {
//       // Left side
//       l_tempLayout.m_pasZoneLocatedArea = EPasZoneSide;
//     }
//     else if (i < (cc::core::PasZonesDescription::NUM_PAS_REARLEFT >> 1u))
//     {
//       // Rear left side
//       l_tempLayout.m_pasZoneLocatedArea = EPasZoneCorner;
//     }
//     else if (i < (cc::core::PasZonesDescription::NUM_PAS_REAR >> 1u))
//     {
//       // Rear
//       l_tempLayout.m_pasZoneLocatedArea = EPasZoneRear;
//     }
//     else if (i < (cc::core::PasZonesDescription::NUM_PAS_REARRIGHT >> 1u))
//     {
//       // Rear right corner
//       l_tempLayout.m_pasZoneLocatedArea = EPasZoneCorner;
//     }
//     else if (i < (cc::core::PasZonesDescription::NUM_PAS_RIGHT >> 1u))
//     {
//       // Right side
//       l_tempLayout.m_pasZoneLocatedArea = EPasZoneSide;
//     }
//     else if (i < (cc::core::PasZonesDescription::NUM_PAS_FRONTRIGHT >> 1u))
//     {
//       // Front right side
//       l_tempLayout.m_pasZoneLocatedArea = EPasZoneCorner;
//     }
//     else
//     {
//       // Front
//       l_tempLayout.m_pasZoneLocatedArea = EPasZoneFront;
//     }

//     m_layout.push_back(l_tempLayout);
//   }
// }

void TileUpdateVisitor::setSplineTileLayoutData(const std::vector<TileSectorData>& f_tileSectors)  // PRQA S 6043  // PRQA S 6044  // PRQA S 6040
{
  const std::size_t l_numSectors = f_tileSectors.size();
  m_layout.clear();
  m_layoutShadow.clear();

#ifdef SVG_WRITER_DEBUG
  std::ofstream l_outfile("poly_line.svg");
  SvgWriter l_svgWriter(l_outfile, -10, -15, 20, 30);
#endif

  //! find the points
  for (vfc::uint32_t i = 0u; i < l_numSectors; ++i)
  {
    osg::Vec2f l_l2r;
    osg::Vec2f l_r2l;
    osg::Vec2f l_shiftDir;
    const vfc::float32_t l_offsetWidth = g_tileSettings->m_shieldOffset;
    const vfc::float32_t l_shieldThickness = g_tileSettings->m_shieldThickness;

    if (
          ( (i >= (cc::core::PasZonesDescription::NUM_PAS_FRONTLEFT >> 1u))
          && (i < (cc::core::PasZonesDescription::NUM_PAS_LEFT      >> 1u)) ) ||
          ( (i >= (cc::core::PasZonesDescription::NUM_PAS_REARRIGHT >> 1u))
          && (i < (cc::core::PasZonesDescription::NUM_PAS_RIGHT     >> 1u)) )
        )
    {
      #ifdef USE_SWD
      //! shield vertices
      // map the layoput data
      // End-----Start
      // 1 ------ 2  outer
      // --0----3--  inner
      std::vector<osg::Vec2f> l_pts(4u);

      //! set the basic 4 points at the bottom
      if (0u == i)
      {
        l_l2r = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint - f_tileSectors[i].m_leftBorderRefPoint;//Code is unreachable  // PRQA S 2880
        l_l2r.normalize();  // PRQA S 3803  // PRQA S 3804

        l_r2l = f_tileSectors[i].m_leftBorderRefPoint - f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint;
        l_r2l.normalize();  // PRQA S 3803  // PRQA S 3804

        l_pts[0u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * f_tileSectors[i].m_currentDistanceForPosDisp) + l_l2r * l_offsetWidth;
        l_pts[1u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_l2r * l_offsetWidth;
        l_pts[2u] = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint + (f_tileSectors[l_numSectors-1u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_r2l * l_offsetWidth;
        l_pts[3u] = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint + (f_tileSectors[l_numSectors-1u].m_leftBorderDir * f_tileSectors[i].m_currentDistanceForPosDisp) + l_r2l * l_offsetWidth;

        //! shift direction
        l_shiftDir = (f_tileSectors[l_numSectors-1u].m_leftBorderDir + f_tileSectors[i].m_leftBorderDir) * 0.5f;
      }
      else
      {
        l_l2r = f_tileSectors[i-1u].m_leftBorderRefPoint - f_tileSectors[i].m_leftBorderRefPoint;
        l_l2r.normalize();  // PRQA S 3803  // PRQA S 3804

        l_r2l = f_tileSectors[i].m_leftBorderRefPoint - f_tileSectors[i-1u].m_leftBorderRefPoint;
        l_r2l.normalize();  // PRQA S 3803  // PRQA S 3804

        l_pts[0u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * f_tileSectors[i].m_currentDistanceForPosDisp) + l_l2r * l_offsetWidth;
        l_pts[1u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_l2r * l_offsetWidth;
        l_pts[2u] = f_tileSectors[i-1u].m_leftBorderRefPoint + (f_tileSectors[i-1u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_r2l * l_offsetWidth;
        l_pts[3u] = f_tileSectors[i-1u].m_leftBorderRefPoint + (f_tileSectors[i-1u].m_leftBorderDir * f_tileSectors[i].m_currentDistanceForPosDisp) + l_r2l * l_offsetWidth;

        //! shift direction
        l_shiftDir = (f_tileSectors[i-1u].m_leftBorderDir + f_tileSectors[i].m_leftBorderDir) * 0.5f;
      }

      TileLayout l_tempLayout;

      l_tempLayout.m_currentDistance  = f_tileSectors[i].m_currentDistance;
      l_tempLayout.m_currentDistanceForPosDisp  = f_tileSectors[i].m_currentDistanceForPosDisp;
      l_tempLayout.m_ObjMovingSts     = f_tileSectors[i].m_ObjMovingSts;

      l_tempLayout.m_shieldStartInner = l_pts[3u];
      l_tempLayout.m_shieldStartOuter = l_pts[2u];

      l_tempLayout.m_shieldEndInner   = l_pts[0u];
      l_tempLayout.m_shieldEndOuter   = l_pts[1u];

      l_tempLayout.m_shieldStartSolid = lerp(l_tempLayout.m_shieldStartInner, l_tempLayout.m_shieldStartOuter, g_tileSettings->m_solidLineThicknessRatio);
      l_tempLayout.m_shieldEndSolid   = lerp(l_tempLayout.m_shieldEndInner, l_tempLayout.m_shieldEndOuter, g_tileSettings->m_solidLineThicknessRatio);

      l_tempLayout.m_shieldStart3DOuter = lerp(l_tempLayout.m_shieldStartInner, l_tempLayout.m_shieldStartOuter, g_tileSettings->m_shield3DThicknessRatio);
      l_tempLayout.m_shieldEnd3DOuter   = lerp(l_tempLayout.m_shieldEndInner, l_tempLayout.m_shieldEndOuter, g_tileSettings->m_shield3DThicknessRatio);

      l_tempLayout.m_isStartEdgeSegment = true;
      l_tempLayout.m_isEndEdgeSegment   = true;


      //! Vertical offset
      l_tempLayout.m_verticalRatioDivideOffset = l_shiftDir * g_tileSettings->m_shieldTopOffset * g_tileSettings->m_shieldRatioDivide;
      l_tempLayout.m_verticalOffset            = l_shiftDir * g_tileSettings->m_shieldTopOffset;

      //! map the pas zone areas
      if ((i < (cc::core::PasZonesDescription::NUM_PAS_FRONT >> 1u))
          || ((i >= (cc::core::PasZonesDescription::NUM_PAS_FRONTRIGHT >> 1u))
          && (i < (cc::core::PasZonesDescription::NUM_PAS_ZONES >> 1u))) )
      {
        // Front
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneFront;//Code is unreachable  // PRQA S 2880
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_FRONTLEFT >> 1u))
      {
        // Front left corner
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneCorner;//Code is unreachable  // PRQA S 2880
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_LEFT >> 1u))
      {
        // Left side
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneSide;
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_REARLEFT >> 1u))
      {
        // Rear left side
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneCorner;//Code is unreachable  // PRQA S 2880
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_REAR >> 1u))
      {
        // Rear
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneRear;//Code is unreachable  // PRQA S 2880
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_REARRIGHT >> 1u))
      {
        // Rear right corner
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneCorner;//Code is unreachable  // PRQA S 2880
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_RIGHT >> 1u))
      {
        // Right side
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneSide;
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_FRONTRIGHT >> 1u))//Code is unreachable  // PRQA S 2880
      {
        // Front right side
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneCorner;
      }
      else
      {
        // Front
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneFront;
      }

      m_layout.push_back(l_tempLayout);
      #endif
    }
    else
    {
      //! shield vertices
      std::vector<osg::Vec2f> l_pts(8u);

      // map the layoput data
      // End-----Start
      //  5   1 ------ 2   6  outer
      //  4   --0----3--   7  inner

      //! set the basic 4 points at the bottom
      if (0u == i)
      {
        l_l2r = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint - f_tileSectors[i].m_leftBorderRefPoint;
        l_l2r.normalize();  // PRQA S 3803  // PRQA S 3804

        l_r2l = f_tileSectors[i].m_leftBorderRefPoint - f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint;
        l_r2l.normalize();  // PRQA S 3803  // PRQA S 3804

        l_pts[0u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * f_tileSectors[i].m_currentDistanceForPosDisp) + l_l2r * l_offsetWidth;
        l_pts[1u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_l2r * l_offsetWidth;
        l_pts[2u] = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint + (f_tileSectors[l_numSectors-1u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_r2l * l_offsetWidth;
        l_pts[3u] = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint + (f_tileSectors[l_numSectors-1u].m_leftBorderDir * f_tileSectors[i].m_currentDistanceForPosDisp) + l_r2l * l_offsetWidth;

        l_r2l = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint - f_tileSectors[l_numSectors-2u].m_leftBorderRefPoint;
        l_r2l.normalize();  // PRQA S 3803  // PRQA S 3804
        l_pts[7u] = f_tileSectors[l_numSectors-2u].m_leftBorderRefPoint + (f_tileSectors[l_numSectors-2u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp )) + l_r2l * l_offsetWidth;
        l_pts[6u] = f_tileSectors[l_numSectors-2u].m_leftBorderRefPoint + (f_tileSectors[l_numSectors-2u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_r2l * l_offsetWidth;

        l_l2r = f_tileSectors[i+1u].m_leftBorderRefPoint - f_tileSectors[i].m_leftBorderRefPoint;
        l_l2r.normalize();  // PRQA S 3803  // PRQA S 3804
        l_pts[4u] = f_tileSectors[i+1u].m_leftBorderRefPoint + (f_tileSectors[i+1u].m_leftBorderDir * f_tileSectors[i].m_currentDistanceForPosDisp) + l_l2r * l_offsetWidth;
        l_pts[5u] = f_tileSectors[i+1u].m_leftBorderRefPoint + (f_tileSectors[i+1u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_l2r * l_offsetWidth;
        //! shift direction
        l_shiftDir = (f_tileSectors[l_numSectors-1u].m_leftBorderDir + f_tileSectors[i].m_leftBorderDir) * 0.5f;
      }
      else if ((i==1u) || (i==14u) || (i==6u) || (i==9u)) // corner segment
      {

        vfc::float32_t l_smoothingCornerSplineType = 0.0f;

        if ((i==1u) || (i==14u)) // front corner
        {
          l_smoothingCornerSplineType = g_tileSettings->m_smoothingFrontCornerSplineType;
        }
        else // rear corner
        {
          l_smoothingCornerSplineType = g_tileSettings->m_smoothingRearCornerSplineType;
        }

        l_l2r = f_tileSectors[i-1u].m_leftBorderRefPoint - f_tileSectors[i].m_leftBorderRefPoint;
        l_l2r.normalize();  // PRQA S 3803  // PRQA S 3804

        l_r2l = f_tileSectors[i].m_leftBorderRefPoint - f_tileSectors[i-1u].m_leftBorderRefPoint;
        l_r2l.normalize();  // PRQA S 3803  // PRQA S 3804

        l_pts[0u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * f_tileSectors[i].m_currentDistanceForPosDisp) + l_l2r * l_offsetWidth;
        l_pts[1u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_l2r * l_offsetWidth;
        l_pts[2u] = f_tileSectors[i-1u].m_leftBorderRefPoint + (f_tileSectors[i-1u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_r2l * l_offsetWidth;
        l_pts[3u] = f_tileSectors[i-1u].m_leftBorderRefPoint + (f_tileSectors[i-1u].m_leftBorderDir * f_tileSectors[i].m_currentDistanceForPosDisp) + l_r2l * l_offsetWidth;

        if (i!=1u)
        {
          l_r2l = f_tileSectors[i-1u].m_leftBorderRefPoint - f_tileSectors[i-2u].m_leftBorderRefPoint;
          l_r2l.normalize();  // PRQA S 3803  // PRQA S 3804
          l_pts[7u] = f_tileSectors[i-2u].m_leftBorderRefPoint + (f_tileSectors[i-2u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp - l_smoothingCornerSplineType)) + l_r2l * l_offsetWidth;
          l_pts[6u] = f_tileSectors[i-2u].m_leftBorderRefPoint + (f_tileSectors[i-2u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp - l_smoothingCornerSplineType + l_shieldThickness)) + l_r2l * l_offsetWidth;
        }
        else
        {
          l_r2l = f_tileSectors[i-1u].m_leftBorderRefPoint - f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint;
          l_r2l.normalize();  // PRQA S 3803  // PRQA S 3804
          l_pts[7u] = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint + (f_tileSectors[l_numSectors-1u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp - l_smoothingCornerSplineType)) + l_r2l * l_offsetWidth;
          l_pts[6u] = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint + (f_tileSectors[l_numSectors-1u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp - l_smoothingCornerSplineType + l_shieldThickness)) + l_r2l * l_offsetWidth;
        }

        if (i != (l_numSectors-1u))
        {
          l_l2r = f_tileSectors[i+1u].m_leftBorderRefPoint - f_tileSectors[i].m_leftBorderRefPoint;
          l_l2r.normalize();  // PRQA S 3803  // PRQA S 3804
          l_pts[4u] = f_tileSectors[i+1u].m_leftBorderRefPoint + (f_tileSectors[i+1u].m_leftBorderDir *  (f_tileSectors[i].m_currentDistanceForPosDisp - l_smoothingCornerSplineType)) + l_l2r * l_offsetWidth;
          l_pts[5u] = f_tileSectors[i+1u].m_leftBorderRefPoint + (f_tileSectors[i+1u].m_leftBorderDir *  (f_tileSectors[i].m_currentDistanceForPosDisp - l_smoothingCornerSplineType + l_shieldThickness)) + l_l2r * l_offsetWidth;
        }
        else
        {
          l_l2r = f_tileSectors[i-1u].m_leftBorderRefPoint - f_tileSectors[i].m_leftBorderRefPoint;
          l_l2r.normalize();  // PRQA S 3803  // PRQA S 3804
          l_pts[4u] = f_tileSectors[0u].m_leftBorderRefPoint + (f_tileSectors[0u].m_leftBorderDir *  (f_tileSectors[i].m_currentDistanceForPosDisp - l_smoothingCornerSplineType)) + l_l2r * l_offsetWidth;
          l_pts[5u] = f_tileSectors[0u].m_leftBorderRefPoint + (f_tileSectors[0u].m_leftBorderDir *  (f_tileSectors[i].m_currentDistanceForPosDisp - l_smoothingCornerSplineType + l_shieldThickness)) + l_l2r * l_offsetWidth;
        }

        //! shift direction
        l_shiftDir = (f_tileSectors[i-1u].m_leftBorderDir + f_tileSectors[i].m_leftBorderDir) * 0.5f;
      }
      else
      {
        l_l2r = f_tileSectors[i-1u].m_leftBorderRefPoint - f_tileSectors[i].m_leftBorderRefPoint;
        l_l2r.normalize();  // PRQA S 3803  // PRQA S 3804

        l_r2l = f_tileSectors[i].m_leftBorderRefPoint - f_tileSectors[i-1u].m_leftBorderRefPoint;
        l_r2l.normalize();  // PRQA S 3803  // PRQA S 3804

        l_pts[0u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * f_tileSectors[i].m_currentDistanceForPosDisp) + l_l2r * l_offsetWidth;
        l_pts[1u] = f_tileSectors[i].m_leftBorderRefPoint + (f_tileSectors[i].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_l2r * l_offsetWidth;
        l_pts[2u] = f_tileSectors[i-1u].m_leftBorderRefPoint + (f_tileSectors[i-1u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_r2l * l_offsetWidth;
        l_pts[3u] = f_tileSectors[i-1u].m_leftBorderRefPoint + (f_tileSectors[i-1u].m_leftBorderDir * f_tileSectors[i].m_currentDistanceForPosDisp) + l_r2l * l_offsetWidth;

        if (i!=1u)
        {
          l_r2l = f_tileSectors[i-1u].m_leftBorderRefPoint - f_tileSectors[i-2u].m_leftBorderRefPoint;
          l_r2l.normalize();  // PRQA S 3803  // PRQA S 3804
          l_pts[7u] = f_tileSectors[i-2u].m_leftBorderRefPoint + (f_tileSectors[i-2u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp )) + l_r2l * l_offsetWidth;
          l_pts[6u] = f_tileSectors[i-2u].m_leftBorderRefPoint + (f_tileSectors[i-2u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_r2l * l_offsetWidth;
        }
        else
        {
          l_r2l = f_tileSectors[i-1u].m_leftBorderRefPoint - f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint;//Code is unreachable  // PRQA S 2880
          l_r2l.normalize();  // PRQA S 3803  // PRQA S 3804
          l_pts[7u] = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint + (f_tileSectors[l_numSectors-1u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp )) + l_r2l * l_offsetWidth;
          l_pts[6u] = f_tileSectors[l_numSectors-1u].m_leftBorderRefPoint + (f_tileSectors[l_numSectors-1u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_r2l * l_offsetWidth;
        }

        if (i != (l_numSectors-1u))
        {
          l_l2r = f_tileSectors[i+1u].m_leftBorderRefPoint - f_tileSectors[i].m_leftBorderRefPoint;
          l_l2r.normalize();  // PRQA S 3803  // PRQA S 3804
          l_pts[4u] = f_tileSectors[i+1u].m_leftBorderRefPoint + (f_tileSectors[i+1u].m_leftBorderDir * f_tileSectors[i].m_currentDistanceForPosDisp) + l_l2r * l_offsetWidth;
          l_pts[5u] = f_tileSectors[i+1u].m_leftBorderRefPoint + (f_tileSectors[i+1u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_l2r * l_offsetWidth;
        }
        else
        {
          l_l2r = f_tileSectors[i-1u].m_leftBorderRefPoint - f_tileSectors[i].m_leftBorderRefPoint;
          l_l2r.normalize();  // PRQA S 3803  // PRQA S 3804
          l_pts[4u] = f_tileSectors[0u].m_leftBorderRefPoint + (f_tileSectors[0u].m_leftBorderDir * f_tileSectors[i].m_currentDistanceForPosDisp) + l_l2r * l_offsetWidth;
          l_pts[5u] = f_tileSectors[0u].m_leftBorderRefPoint + (f_tileSectors[0u].m_leftBorderDir * (f_tileSectors[i].m_currentDistanceForPosDisp + l_shieldThickness)) + l_l2r * l_offsetWidth;
        }

        //! shift direction
        l_shiftDir = (f_tileSectors[i-1u].m_leftBorderDir + f_tileSectors[i].m_leftBorderDir) * 0.5f;
      }

      m_InnerControlPoints = {{ l_pts[4u], l_pts[0u], l_pts[3u], l_pts[7u] }};
      m_OuterControlPoints = {{ l_pts[5u], l_pts[1u], l_pts[2u], l_pts[6u] }};

      computeSpline(m_InnerControlPoints, m_InnerPoints.begin(), NUM_INTERPOLATED_POINTS);  // PRQA S 3804
      computeSpline(m_OuterControlPoints, m_OuterPoints.begin(), NUM_INTERPOLATED_POINTS);  // PRQA S 3804

#ifdef SVG_WRITER_DEBUG
      if (l_outfile && i <16)
      {
        l_svgWriter.addPolyline(m_InnerPoints);
        l_svgWriter.addCircles(m_InnerControlPoints, 0.01f);
        l_svgWriter.addCircles(m_InnerPoints, 0.005f);
      }

      if (l_outfile && i <16)
      {
        //l_svgWriter.addPolyline(m_OuterPoints);
        l_svgWriter.addCircles(m_OuterControlPoints, 0.01f);
        l_svgWriter.addCircles(m_OuterPoints, 0.005f);
      }
#endif

      TileLayout l_tempLayout;

      l_tempLayout.m_currentDistance  = f_tileSectors[i].m_currentDistance;
      l_tempLayout.m_currentDistanceForPosDisp  = f_tileSectors[i].m_currentDistanceForPosDisp;
      l_tempLayout.m_ObjMovingSts     = f_tileSectors[i].m_ObjMovingSts;

      //! Vertical offset
      l_tempLayout.m_verticalRatioDivideOffset = l_shiftDir * g_tileSettings->m_shieldTopOffset * g_tileSettings->m_shieldRatioDivide;
      l_tempLayout.m_verticalOffset            = l_shiftDir * g_tileSettings->m_shieldTopOffset;

      //! map the pas zone areas
      if ((i < (cc::core::PasZonesDescription::NUM_PAS_FRONT >> 1u))
          || ((i >= (cc::core::PasZonesDescription::NUM_PAS_FRONTRIGHT >> 1u))
          && (i < (cc::core::PasZonesDescription::NUM_PAS_ZONES >> 1u))) )
      {
        // Front
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneFront;
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_FRONTLEFT >> 1u))
      {
        // Front left corner
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneCorner;
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_LEFT >> 1u))
      {
        // Left side
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneSide;//Code is unreachable  // PRQA S 2880
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_REARLEFT >> 1u))
      {
        // Rear left side
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneCorner;
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_REAR >> 1u))
      {
        // Rear
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneRear;
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_REARRIGHT >> 1u))
      {
        // Rear right corner
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneCorner;
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_RIGHT >> 1u))
      {
        // Right side
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneSide;//Code is unreachable  // PRQA S 2880
      }
      else if (i < (cc::core::PasZonesDescription::NUM_PAS_FRONTRIGHT >> 1u))
      {
        // Front right side
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneCorner;
      }
      else
      {
        // Front
        l_tempLayout.m_pasZoneLocatedArea = EPasZoneFront;
      }

      for (vfc::uint32_t j = 0u; j < (NUM_INTERPOLATED_POINTS - 1u); j++)
      {
        l_tempLayout.m_shieldStartInner   = m_InnerPoints[j];
        l_tempLayout.m_shieldStartOuter   = m_OuterPoints[j];
        l_tempLayout.m_shieldStartSolid   = lerp(l_tempLayout.m_shieldStartInner, l_tempLayout.m_shieldStartOuter, g_tileSettings->m_solidLineThicknessRatio);
        l_tempLayout.m_shieldStart3DOuter = lerp(l_tempLayout.m_shieldStartInner, l_tempLayout.m_shieldStartOuter, g_tileSettings->m_shield3DThicknessRatio);
        l_tempLayout.m_shieldEndInner     = m_InnerPoints[j+1u];
        l_tempLayout.m_shieldEndOuter     = m_OuterPoints[j+1u];
        l_tempLayout.m_shieldEndSolid     = lerp(l_tempLayout.m_shieldEndInner, l_tempLayout.m_shieldEndOuter, g_tileSettings->m_solidLineThicknessRatio);
        l_tempLayout.m_shieldEnd3DOuter   = lerp(l_tempLayout.m_shieldEndInner, l_tempLayout.m_shieldEndOuter, g_tileSettings->m_shield3DThicknessRatio);

        if (j == 0u)
        {
          l_tempLayout.m_isStartEdgeSegment = true;
          l_tempLayout.m_isEndEdgeSegment   = false;
        }
        else if (j == (NUM_INTERPOLATED_POINTS - 2u))
        {
          l_tempLayout.m_isStartEdgeSegment = false;
          l_tempLayout.m_isEndEdgeSegment   = true;
        }
        else
        {
          l_tempLayout.m_isStartEdgeSegment = false;
          l_tempLayout.m_isEndEdgeSegment   = false;
        }

        m_layout.push_back(l_tempLayout);
      }
    }

    //! Copy interpolated points to buffer -> deliver to DistanceDigitalDisplay
    std::memcpy(static_cast<void*>(&m_InnerPointsArray[i]), static_cast<void*>(&m_InnerPoints), sizeof(m_InnerPoints));
  }
}

//!
//! TileSpline
//!
TileSpline::TileSpline()
  : m_numLayoutPoints(0u)
{
  // setStateSet(getOrCreateTexturingStateSet());
}


TileSpline::TileSpline(const TileSpline& f_other, const osg::CopyOp& f_copyOp)
  : osg::Geode(f_other, f_copyOp)
  , m_numLayoutPoints(f_other.m_numLayoutPoints)
{
}


TileSpline::~TileSpline() =default;


osg::Vec4f getColorShieldWithThreshold(const vfc::float32_t f_currentDistance, const EObjMovingSts f_ObjMovingSts)
{
  osg::Vec4f l_colorShield;

  if ( (f_currentDistance > g_tileSettings->m_HysteresisDistanceThresh) &&
       ( (f_ObjMovingSts == OBJ_MOVING_HYSTERESIS_LV1) ||
         (f_ObjMovingSts == OBJ_MOVING_HYSTERESIS_LV2) ||
         (f_ObjMovingSts == OBJ_MOVING_HYSTERESIS_LV3)   ))
  {
      l_colorShield = g_colorInterpolator.getValue(f_currentDistance - g_tileSettings->m_HysteresisDistanceThresh);
  }
  else
  {
    l_colorShield = g_colorInterpolator.getValue(f_currentDistance);
  }
  return l_colorShield;
}

//!
//! TileShield3DInner
//!
TileShield3DInner::TileShield3DInner()
{
  osg::Geode::addDrawable(createGeometry("TileShield3DInner"));  // PRQA S 3803
}


TileShield3DInner::TileShield3DInner(const TileShield3DInner& f_other, const osg::CopyOp& f_copyOp)
  : TileSpline(f_other, f_copyOp)
{
}


TileShield3DInner::~TileShield3DInner() = default;

void TileShield3DInner::update(const TileUpdateVisitor& f_visitor)
{
  updateInterpolators();

  osg::Geometry* const l_geometry  = getDrawable(0u)->asGeometry();
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (l_geometry->getVertexArray());
  osg::Vec4Array* const l_colors   = static_cast<osg::Vec4Array*> (l_geometry->getColorArray());
  //! no color
  osg::Vec4f const l_colorNone = osg::Vec4f(0.f, 0.f, 0.f, 0.f);

  const std::vector<TileLayout>& l_layout = f_visitor.getLayout();
  const vfc::uint32_t l_numLayoutPoints = static_cast<vfc::uint32_t> (l_layout.size());
  constexpr vfc::uint32_t l_numShieldPoints = static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT) * static_cast<vfc::uint32_t>(g_fixedTileNum);

  //! set the vertex offset
  const vfc::uint32_t l_vertexOffsetHairlines       = l_numLayoutPoints * l_numShieldPoints;

  if (l_numLayoutPoints != m_numLayoutPoints)
  {
    m_numLayoutPoints = l_numLayoutPoints;
    const vfc::uint32_t l_numVerticesShield = l_numLayoutPoints * l_numShieldPoints;
    const vfc::uint32_t l_numVerticesHairlines = l_numLayoutPoints*4u;
    const vfc::uint32_t l_numVertices = l_numVerticesShield + l_numVerticesHairlines;

    l_vertices->resize(l_numVertices);
    l_colors->resize(l_numVertices);
    l_geometry->removePrimitiveSet(0u, l_geometry->getNumPrimitiveSets()); //! clear primitive sets,  //PRQA S 3803

    l_geometry->addPrimitiveSet(createSurface(l_numLayoutPoints * static_cast<vfc::uint32_t>(g_fixedTileNum), static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT), 0u));  // PRQA S 3803
    l_geometry->addPrimitiveSet(new osg::DrawArrays(static_cast<GLenum>(osg::PrimitiveSet::LINES), static_cast<GLint>(l_vertexOffsetHairlines), static_cast<GLsizei>(l_numVerticesHairlines)));  // PRQA S 3803
  }

  vfc::uint32_t l_vs = 0u;
  vfc::uint32_t l_vsHairlines = l_vertexOffsetHairlines;

  if (0u == l_numLayoutPoints)
  {
    return;
  }

  //! set the vertices
  for (vfc::uint32_t i = 0u; i < l_numLayoutPoints; ++i)
  {
    //! colors shield
    osg::Vec4f l_colorShield = getColorShieldWithThreshold(l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts);
    l_colorShield.a() = g_tileSettings->m_shieldAlpha;
    osg::Vec4f l_colorHairline = l_colorShield;
    l_colorHairline.a() = g_tileSettings->m_hairlineAlpha;

    //! set some position to transparant shield
    updatePasZoneAlphaByThreshold(l_layout[i].m_pasZoneLocatedArea, l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts, l_colorShield.a());
    updatePasZoneAlphaByThreshold(l_layout[i].m_pasZoneLocatedArea, l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts, l_colorHairline.a());

    //! First start point to be transparant
    (*l_vertices)[l_vs]     = osg::Vec3f(l_layout[i].m_shieldStartInner, g_tileSettings->m_heightOverGround);
    (*l_vertices)[l_vs + 1u] = osg::Vec3f(l_layout[i].m_shieldStartInner + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldRatioDivide));
    (*l_vertices)[l_vs + 2u] = osg::Vec3f(l_layout[i].m_shieldStartInner + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

    (*l_colors)[l_vs]     = l_colorNone;
    (*l_colors)[l_vs + 1u] = l_colorNone;
    (*l_colors)[l_vs + 2u] = l_colorNone;

    //! First start point with color
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT]      = osg::Vec3f(l_layout[i].m_shieldStartInner, g_tileSettings->m_heightOverGround);
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT + 1u] = osg::Vec3f(l_layout[i].m_shieldStartInner + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldRatioDivide));
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT + 2u] = osg::Vec3f(l_layout[i].m_shieldStartInner + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT]      = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DAlphaRatioBottom);
    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT + 1u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DAlphaRatioDivide);
    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT + 2u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DAlphaRatioTop);

    //! Second start point with color
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u]      = osg::Vec3f(l_layout[i].m_shieldEndInner, g_tileSettings->m_heightOverGround);
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 1u] = osg::Vec3f(l_layout[i].m_shieldEndInner + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldRatioDivide));
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 2u] = osg::Vec3f(l_layout[i].m_shieldEndInner + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u]      = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DAlphaRatioBottom);
    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 1u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DAlphaRatioDivide);
    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 2u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DAlphaRatioTop);

    //! Second start point to be transparant
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u]      = osg::Vec3f(l_layout[i].m_shieldEndInner, g_tileSettings->m_heightOverGround);
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 1u] = osg::Vec3f(l_layout[i].m_shieldEndInner + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldRatioDivide));
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 2u] = osg::Vec3f(l_layout[i].m_shieldEndInner + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u]      = l_colorNone;
    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 1u] = l_colorNone;
    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 2u] = l_colorNone;

    l_vs += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT) * static_cast<vfc::uint32_t>(g_fixedTileNum);

    // for Hair Lines
    (*l_vertices)[l_vsHairlines]          = osg::Vec3f(l_layout[i].m_shieldStartInner , g_tileSettings->m_heightOverGround );
    (*l_vertices)[l_vsHairlines + 1u]     = osg::Vec3f(l_layout[i].m_shieldEndInner   , g_tileSettings->m_heightOverGround );
    (*l_vertices)[l_vsHairlines + 2u]     = osg::Vec3f(l_layout[i].m_shieldStartInner + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);
    (*l_vertices)[l_vsHairlines + 3u]     = osg::Vec3f(l_layout[i].m_shieldEndInner   + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);
    (*l_colors)[l_vsHairlines]      = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioBottom);
    (*l_colors)[l_vsHairlines + 1u] = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioBottom);
    (*l_colors)[l_vsHairlines + 2u] = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioTop);
    (*l_colors)[l_vsHairlines + 3u] = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioTop);
    l_vsHairlines += 4u;

  }


  l_vertices->dirty();
  l_colors->dirty();
  l_geometry->dirtyBound();
}


//!
//! TileShield3DOuter
//!
TileShield3DOuter::TileShield3DOuter()
{
  osg::Geode::addDrawable(createGeometry("TileShield3DOuter"));  // PRQA S 3803
}


TileShield3DOuter::TileShield3DOuter(const TileShield3DOuter& f_other, const osg::CopyOp& f_copyOp)
  : TileSpline(f_other, f_copyOp)
{
}


TileShield3DOuter::~TileShield3DOuter() = default;

void TileShield3DOuter::update(const TileUpdateVisitor& f_visitor)
{
  updateInterpolators();

  osg::Geometry* const l_geometry  = getDrawable(0u)->asGeometry();
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (l_geometry->getVertexArray());
  osg::Vec4Array* const l_colors   = static_cast<osg::Vec4Array*> (l_geometry->getColorArray());
  //! no color
  osg::Vec4f const l_colorNone = osg::Vec4f(0.f, 0.f, 0.f, 0.f);

  const std::vector<TileLayout>& l_layout = f_visitor.getLayout();
  const vfc::uint32_t l_numLayoutPoints = static_cast<vfc::uint32_t> (l_layout.size());
  constexpr vfc::uint32_t l_numShieldPoints = static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT) * static_cast<vfc::uint32_t>(g_fixedTileNum);

  //! set the vertex offset
  const vfc::uint32_t l_vertexOffsetHairlines       = l_numLayoutPoints * l_numShieldPoints;

  if (l_numLayoutPoints != m_numLayoutPoints)
  {
    m_numLayoutPoints = l_numLayoutPoints;
    const vfc::uint32_t l_numVerticesShield = l_numLayoutPoints * l_numShieldPoints;
    const vfc::uint32_t l_numVerticesHairlines = l_numLayoutPoints*4u;
    const vfc::uint32_t l_numVertices = l_numVerticesShield + l_numVerticesHairlines;

    l_vertices->resize(l_numVertices);
    l_colors->resize(l_numVertices);
    l_geometry->removePrimitiveSet(0u, l_geometry->getNumPrimitiveSets()); //! clear primitive sets,  //PRQA S 3803

    l_geometry->addPrimitiveSet(createSurface(l_numLayoutPoints * static_cast<vfc::uint32_t>(g_fixedTileNum), static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT), 0u));  // PRQA S 3803
    l_geometry->addPrimitiveSet(new osg::DrawArrays(static_cast<GLenum>(osg::PrimitiveSet::LINES), static_cast<GLint>(l_vertexOffsetHairlines), static_cast<GLsizei>(l_numVerticesHairlines)));  // PRQA S 3803
  }

  vfc::uint32_t l_vs = 0u;
  vfc::uint32_t l_vsHairlines = l_vertexOffsetHairlines;

  if (0u == l_numLayoutPoints)
  {
    return;
  }

  //! set the vertices
  for (vfc::uint32_t i = 0u; i < l_numLayoutPoints; ++i)
  {
    //! colors shield
    osg::Vec4f l_colorShield = getColorShieldWithThreshold(l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts);
    l_colorShield.a() = g_tileSettings->m_shieldAlpha;
    osg::Vec4f l_colorHairline = l_colorShield;
    l_colorHairline.a() = g_tileSettings->m_hairlineAlpha;

    //! set some position to transparant shield
    updatePasZoneAlphaByThreshold(l_layout[i].m_pasZoneLocatedArea, l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts, l_colorShield.a());
    updatePasZoneAlphaByThreshold(l_layout[i].m_pasZoneLocatedArea, l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts, l_colorHairline.a());

    //! First start point to be transparant
    (*l_vertices)[l_vs]      = osg::Vec3f(l_layout[i].m_shieldStart3DOuter, g_tileSettings->m_heightOverGround);
    (*l_vertices)[l_vs + 1u] = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldRatioDivide));
    (*l_vertices)[l_vs + 2u] = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

    (*l_colors)[l_vs]      = l_colorNone;
    (*l_colors)[l_vs + 1u] = l_colorNone;
    (*l_colors)[l_vs + 2u] = l_colorNone;

    //! First start point with color
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT]      = osg::Vec3f(l_layout[i].m_shieldStart3DOuter, g_tileSettings->m_heightOverGround);
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT + 1u] = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldRatioDivide));
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT + 2u] = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT]      = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DAlphaRatioBottom);
    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT + 1u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DAlphaRatioDivide);
    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT + 2u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DAlphaRatioTop);

    //! Second start point with color
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u]      = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter, g_tileSettings->m_heightOverGround);
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 1u] = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldRatioDivide));
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 2u] = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u]      = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DAlphaRatioBottom);
    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 1u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DAlphaRatioDivide);
    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 2u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DAlphaRatioTop);

    //! Second start point to be transparant
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u]      = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter, g_tileSettings->m_heightOverGround);
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 1u] = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldRatioDivide));
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 2u] = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);
    (*l_vertices)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 2u] = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u]      = l_colorNone;
    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 1u] = l_colorNone;
    (*l_colors)[l_vs + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 2u] = l_colorNone;

    l_vs += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT) * static_cast<vfc::uint32_t>(g_fixedTileNum);

    // for Hair Lines
    (*l_vertices)[l_vsHairlines]          = osg::Vec3f(l_layout[i].m_shieldStart3DOuter , g_tileSettings->m_heightOverGround );
    (*l_vertices)[l_vsHairlines + 1u]     = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter   , g_tileSettings->m_heightOverGround );
    (*l_vertices)[l_vsHairlines + 2u]     = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);
    (*l_vertices)[l_vsHairlines + 3u]     = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter   + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);
    (*l_colors)[l_vsHairlines]      = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioBottom);
    (*l_colors)[l_vsHairlines + 1u] = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioBottom);
    (*l_colors)[l_vsHairlines + 2u] = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioTop);
    (*l_colors)[l_vsHairlines + 3u] = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioTop);
    l_vsHairlines += 4u;

  }

  l_vertices->dirty();
  l_colors->dirty();
  l_geometry->dirtyBound();
}

//!
//! TileCoverTopBottom2D
//!
TileCoverTopBottom2D::TileCoverTopBottom2D()
  : m_offset(0.f)
{
  osg::Geode::addDrawable(createGeometry("TileCoverTopBottom2D"));  // PRQA S 3803
}


TileCoverTopBottom2D::TileCoverTopBottom2D(const TileCoverTopBottom2D& f_other, const osg::CopyOp& f_copyOp)
  : TileSpline(f_other, f_copyOp)
  , m_offset(f_other.m_offset)
{
}


TileCoverTopBottom2D::~TileCoverTopBottom2D() = default;

void TileCoverTopBottom2D::setOffset(const vfc::float32_t f_offet)
{
  m_offset = f_offet;
}

void TileCoverTopBottom2D::update(const TileUpdateVisitor& f_visitor)
{
  updateInterpolators();

  osg::Geometry* const l_geometry  = getDrawable(0u)->asGeometry();
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (l_geometry->getVertexArray());
  osg::Vec4Array* const l_colors   = static_cast<osg::Vec4Array*> (l_geometry->getColorArray());
  //! no color
  osg::Vec4f const l_colorNone = osg::Vec4f(0.f, 0.f, 0.f, 0.f);

  const std::vector<TileLayout>& l_layout = f_visitor.getLayout();
  const vfc::uint32_t l_numLayoutPoints = static_cast<vfc::uint32_t> (l_layout.size());

  constexpr vfc::uint32_t l_numCoverPoint = static_cast<vfc::uint32_t>(NUM_VERTICES_PER_COVER_SEGMENT) * static_cast<vfc::uint32_t>(g_fixedTileNum);

  if (l_numLayoutPoints != m_numLayoutPoints)
  {
    m_numLayoutPoints = l_numLayoutPoints;
    const vfc::uint32_t l_numVerticesShield          = l_numLayoutPoints * l_numCoverPoint;

    const vfc::uint32_t l_numVertices = l_numVerticesShield;
    l_vertices->resize(l_numVertices);
    l_colors->resize(l_numVertices);
    l_geometry->removePrimitiveSet(0u, l_geometry->getNumPrimitiveSets()); //! clear primitive sets  // PRQA S 3803

    l_geometry->addPrimitiveSet(createSurface(l_numLayoutPoints * static_cast<vfc::uint32_t>(g_fixedTileNum), static_cast<vfc::uint32_t>(NUM_VERTICES_PER_COVER_SEGMENT), 0u));  // PRQA S 3803
  }

  vfc::uint32_t l_vs = 0u;

  if (0u == l_numLayoutPoints)
  {
    return;
  }

  //! set the vertices
  for (vfc::uint32_t i = 0u; i < l_numLayoutPoints; ++i)
  {
    osg::Vec2f l_verticalOffset = osg::Vec2f(0.f, 0.f);
    vfc::float32_t l_verticalAlphaRatio = 1.f;
    if (std::abs(m_offset) <= g_tileSettings->m_heightOverGround)
    {
      l_verticalOffset = osg::Vec2f(0.f, 0.f);
      l_verticalAlphaRatio = g_tileSettings->m_shield3DBottomCoverAlphaRatio;
    }
    else
    {
      l_verticalOffset = l_layout[i].m_verticalOffset;
      l_verticalAlphaRatio = g_tileSettings->m_shield3DAlphaRatioTop;
    }

    //! colors shield
    osg::Vec4f l_colorShield = getColorShieldWithThreshold(l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts);
    l_colorShield.a() = g_tileSettings->m_shieldAlpha * l_verticalAlphaRatio;

    //! set some position to transparant shield
    updatePasZoneAlphaByThreshold(l_layout[i].m_pasZoneLocatedArea, l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts, l_colorShield.a());

    //! First start point to be transparant
    (*l_vertices)[l_vs]     = osg::Vec3f(l_layout[i].m_shieldStartInner + l_verticalOffset, g_tileSettings->m_heightOverGround + m_offset);
    (*l_vertices)[l_vs + 1u] = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_verticalOffset, g_tileSettings->m_heightOverGround + m_offset);

    (*l_colors)[l_vs]     = l_colorNone;
    (*l_colors)[l_vs + 1u] = l_colorNone;

    //! First start point with color
    (*l_vertices)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT]      = osg::Vec3f(l_layout[i].m_shieldStartInner + l_verticalOffset, g_tileSettings->m_heightOverGround + m_offset);
    (*l_vertices)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT + 1u] = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_verticalOffset, g_tileSettings->m_heightOverGround + m_offset);

    (*l_colors)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT]      = l_colorShield;
    (*l_colors)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT + 1u] = l_colorShield;

    //! Second start point with color
    (*l_vertices)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 2u]      = osg::Vec3f(l_layout[i].m_shieldEndInner + l_verticalOffset, g_tileSettings->m_heightOverGround + m_offset);
    (*l_vertices)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 2u + 1u] = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_verticalOffset, g_tileSettings->m_heightOverGround + m_offset);

    (*l_colors)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 2u]      = l_colorShield;
    (*l_colors)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 2u + 1u] = l_colorShield;

    //! Second start point to be transparant
    (*l_vertices)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 3u]      = osg::Vec3f(l_layout[i].m_shieldEndInner + l_verticalOffset, g_tileSettings->m_heightOverGround + m_offset);
    (*l_vertices)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 3u + 1u] = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_verticalOffset, g_tileSettings->m_heightOverGround + m_offset);

    (*l_colors)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 3u]     = l_colorNone;
    (*l_colors)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 3u + 1u] = l_colorNone;

    l_vs += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_COVER_SEGMENT) * static_cast<vfc::uint32_t>(g_fixedTileNum);

  }

  l_vertices->dirty();
  l_colors->dirty();
  l_geometry->dirtyBound();
}

//!
//! Tile2DTopview
//!
Tile2DTopview::Tile2DTopview()
{
  osg::Geode::addDrawable(createGeometry("Tile2DTopview"));  // PRQA S 3803
  osg::Geode::addDrawable(createGeometry("Tile2DTopviewShadow"));  // PRQA S 3803
}


Tile2DTopview::Tile2DTopview(const Tile2DTopview& f_other, const osg::CopyOp& f_copyOp)
  : TileSpline(f_other, f_copyOp)
{
}


Tile2DTopview::~Tile2DTopview() = default;

void Tile2DTopview::update(const TileUpdateVisitor& f_visitor)
{
  updateInterpolators();

  osg::Geometry* const l_geometry  = getDrawable(0u)->asGeometry();
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (l_geometry->getVertexArray());
  osg::Vec4Array* const l_colors   = static_cast<osg::Vec4Array*> (l_geometry->getColorArray());

  osg::Geometry* const l_geometryShadow  = getDrawable(1u)->asGeometry();
  osg::Vec3Array* const l_verticesShadow = static_cast<osg::Vec3Array*> (l_geometryShadow->getVertexArray());
  osg::Vec4Array* const l_colorsShadow   = static_cast<osg::Vec4Array*> (l_geometryShadow->getColorArray());
  //! no color
  osg::Vec4f const l_colorNone = osg::Vec4f(0.f, 0.f, 0.f, 0.f);

  const std::vector<TileLayout>& l_layout = f_visitor.getLayout();
  const vfc::uint32_t l_numLayoutPoints = static_cast<vfc::uint32_t> (l_layout.size());

  constexpr vfc::uint32_t l_numCoverPoint = static_cast<vfc::uint32_t>(NUM_VERTICES_PER_COVER_SEGMENT) * static_cast<vfc::uint32_t>(g_fixedTileNum);

  if (l_numLayoutPoints != m_numLayoutPoints)
  {
    m_numLayoutPoints = l_numLayoutPoints;
    const vfc::uint32_t l_numVerticesShield          = l_numLayoutPoints * l_numCoverPoint;

    const vfc::uint32_t l_numVertices = l_numVerticesShield;
    l_vertices->resize(l_numVertices);
    l_colors->resize(l_numVertices);
    l_verticesShadow->resize(l_numVertices);
    l_colorsShadow->resize(l_numVertices);
    l_geometry->removePrimitiveSet(0u, l_geometry->getNumPrimitiveSets()); //! clear primitive sets  // PRQA S 3803
    l_geometryShadow->removePrimitiveSet(0u, l_geometryShadow->getNumPrimitiveSets()); //! clear primitive sets  // PRQA S 3803

    l_geometry->addPrimitiveSet(createSurface(l_numLayoutPoints * static_cast<vfc::uint32_t>(g_fixedTileNum), static_cast<vfc::uint32_t>(NUM_VERTICES_PER_COVER_SEGMENT), 0u));  // PRQA S 3803
    l_geometryShadow->addPrimitiveSet(createSurface(l_numLayoutPoints * static_cast<vfc::uint32_t>(g_fixedTileNum), static_cast<vfc::uint32_t>(NUM_VERTICES_PER_COVER_SEGMENT), 0u));  // PRQA S 3803
  }

  vfc::uint32_t l_vs = 0u;

  if (0u == l_numLayoutPoints)
  {
    return;
  }

  //! set the vertices
  for (vfc::uint32_t i = 0u; i < l_numLayoutPoints; ++i)
  {

    //! colors shield
    osg::Vec4f l_colorShield = getColorShieldWithThreshold(l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts);
    osg::Vec4f l_colorSolidLine = l_colorShield;
    l_colorShield.a() = g_tileSettings->m_shadow2DAlpha;

    //! set some position to transparant shield
    updatePasZoneAlphaByThreshold(l_layout[i].m_pasZoneLocatedArea, l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts, l_colorShield.a());
    updatePasZoneAlphaByThreshold(l_layout[i].m_pasZoneLocatedArea, l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts, l_colorSolidLine.a());

    //! First start point to be transparant
    (*l_vertices)[l_vs]     = osg::Vec3f(l_layout[i].m_shieldStartInner , g_tileSettings->m_heightOverGround);
    (*l_vertices)[l_vs + 1u] = osg::Vec3f(l_layout[i].m_shieldStartSolid , g_tileSettings->m_heightOverGround);
    (*l_verticesShadow)[l_vs]     = osg::Vec3f(l_layout[i].m_shieldStartInner , g_tileSettings->m_heightOverGround);
    (*l_verticesShadow)[l_vs + 1u] = osg::Vec3f(l_layout[i].m_shieldStartOuter , g_tileSettings->m_heightOverGround);

    (*l_colors)[l_vs]     = l_colorNone;
    (*l_colors)[l_vs + 1u] = l_colorNone;
    (*l_colorsShadow)[l_vs]     = l_colorNone;
    (*l_colorsShadow)[l_vs + 1u] = l_colorNone;

    //! First start point with color
    (*l_vertices)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT]      = osg::Vec3f(l_layout[i].m_shieldStartInner , g_tileSettings->m_heightOverGround);
    (*l_vertices)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT + 1u] = osg::Vec3f(l_layout[i].m_shieldStartSolid , g_tileSettings->m_heightOverGround);
    (*l_verticesShadow)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT]      = osg::Vec3f(l_layout[i].m_shieldStartInner , g_tileSettings->m_heightOverGround);
    (*l_verticesShadow)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT + 1u] = osg::Vec3f(l_layout[i].m_shieldStartOuter , g_tileSettings->m_heightOverGround);

    (*l_colors)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT]      = osg::Vec4f(pc::util::toVec3(l_colorSolidLine), l_colorSolidLine.a() * g_tileSettings->m_solidLine2DAlpha);
    (*l_colors)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT + 1u] = osg::Vec4f(pc::util::toVec3(l_colorSolidLine), l_colorSolidLine.a() * g_tileSettings->m_solidLine2DAlpha);
    (*l_colorsShadow)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT]      = l_colorShield;
    (*l_colorsShadow)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT + 1u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shadow2DAlphaInner);

    //! Second start point with color
    (*l_vertices)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 2u]      = osg::Vec3f(l_layout[i].m_shieldEndInner , g_tileSettings->m_heightOverGround);
    (*l_vertices)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 2u + 1u] = osg::Vec3f(l_layout[i].m_shieldEndSolid , g_tileSettings->m_heightOverGround);
    (*l_verticesShadow)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 2u]     = osg::Vec3f(l_layout[i].m_shieldEndInner , g_tileSettings->m_heightOverGround);
    (*l_verticesShadow)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 2u + 1u] = osg::Vec3f(l_layout[i].m_shieldEndOuter , g_tileSettings->m_heightOverGround);

    (*l_colors)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 2u]      = osg::Vec4f(pc::util::toVec3(l_colorSolidLine), l_colorSolidLine.a() * g_tileSettings->m_solidLine2DAlpha);
    (*l_colors)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 2u + 1u] = osg::Vec4f(pc::util::toVec3(l_colorSolidLine), l_colorSolidLine.a());
    (*l_colorsShadow)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 2u]      = l_colorShield;
    (*l_colorsShadow)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 2u + 1u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shadow2DAlphaInner);
    //! Second start point to be transparant
    (*l_vertices)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 3u]      = osg::Vec3f(l_layout[i].m_shieldEndInner , g_tileSettings->m_heightOverGround);
    (*l_vertices)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 3u + 1u] = osg::Vec3f(l_layout[i].m_shieldEndSolid , g_tileSettings->m_heightOverGround);
    (*l_verticesShadow)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 3u]      = osg::Vec3f(l_layout[i].m_shieldEndInner , g_tileSettings->m_heightOverGround);
    (*l_verticesShadow)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 3u + 1u] = osg::Vec3f(l_layout[i].m_shieldEndOuter , g_tileSettings->m_heightOverGround);

    (*l_colors)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 3u]      = l_colorNone;
    (*l_colors)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 3u + 1u] = l_colorNone;
    (*l_colorsShadow)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 3u]      = l_colorNone;
    (*l_colorsShadow)[l_vs + NUM_VERTICES_PER_COVER_SEGMENT * 3u + 1u] = l_colorNone;

    l_vs += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_COVER_SEGMENT) * static_cast<vfc::uint32_t>(g_fixedTileNum);

  }

  l_vertices->dirty();
  l_colors->dirty();
  l_geometry->dirtyBound();
  l_verticesShadow->dirty();
  l_colorsShadow->dirty();
  l_geometryShadow->dirtyBound();
}

//!
//! TileSideSquare3D
//!
TileSideSquare3D::TileSideSquare3D()
{
  osg::Geode::addDrawable(createGeometry("TileSideSquare3DLeft"));  // PRQA S 3803
  osg::Geode::addDrawable(createGeometry("TileSideSquare3DRight"));  // PRQA S 3803
}


TileSideSquare3D::TileSideSquare3D(const TileSideSquare3D& f_other, const osg::CopyOp& f_copyOp)
  : TileSpline(f_other, f_copyOp)
{
}


TileSideSquare3D::~TileSideSquare3D() = default;

void TileSideSquare3D::update(const TileUpdateVisitor& f_visitor) // PRQA S 6044
{
  updateInterpolators();

  osg::Geometry* const l_geometryLeft  = getDrawable(0u)->asGeometry();
  osg::Vec3Array* const l_verticesLeft = static_cast<osg::Vec3Array*> (l_geometryLeft->getVertexArray());
  osg::Geometry* const l_geometryRight  = getDrawable(1u)->asGeometry();
  osg::Vec3Array* const l_verticesRight = static_cast<osg::Vec3Array*> (l_geometryRight->getVertexArray());
  osg::Vec4Array* const l_colorsLeft   = static_cast<osg::Vec4Array*> (l_geometryLeft->getColorArray());
  osg::Vec4Array* const l_colorsRight   = static_cast<osg::Vec4Array*> (l_geometryRight->getColorArray());
  //! no color
  osg::Vec4f const l_colorNone = osg::Vec4f(0.f, 0.f, 0.f, 0.f);

  const std::vector<TileLayout>& l_layout = f_visitor.getLayout();
  const vfc::uint32_t l_numLayoutPoints = static_cast<vfc::uint32_t> (l_layout.size());

  //! Square is removed shield inner side
  constexpr vfc::uint32_t l_numShieldPoints = static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT) * (static_cast<vfc::uint32_t>(g_fixedTileNum));
  constexpr vfc::uint32_t l_numHairPoints   = static_cast<vfc::uint32_t>(NUM_VERTICES_PER_HAIRLINE_SEGMENT) * (static_cast<vfc::uint32_t>(g_fixedTileNum + 2u));

  //! set the vertex offset
  const vfc::uint32_t l_vertexOffsetHairlines       = l_numLayoutPoints * l_numShieldPoints;

  if (l_numLayoutPoints != m_numLayoutPoints)
  {
    m_numLayoutPoints = l_numLayoutPoints;
    const vfc::uint32_t l_numVerticesShield          = l_numLayoutPoints * l_numShieldPoints;
    const vfc::uint32_t l_numVerticesHairlines       = l_numLayoutPoints * l_numHairPoints;

    const vfc::uint32_t l_numVertices = l_numVerticesShield + l_numVerticesHairlines;
    l_verticesLeft->resize(l_numVertices);
    l_verticesRight->resize(l_numVertices);
    l_colorsLeft->resize(l_numVertices);
    l_colorsRight->resize(l_numVertices);
    l_geometryLeft->removePrimitiveSet(0u, l_geometryLeft->getNumPrimitiveSets()); //! clear primitive sets  // PRQA S 3803
    l_geometryRight->removePrimitiveSet(0u, l_geometryRight->getNumPrimitiveSets()); //! clear primitive sets  // PRQA S 3803

    l_geometryLeft->addPrimitiveSet(createSurface(l_numLayoutPoints * (static_cast<vfc::uint32_t>(g_fixedTileNum)), static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT), 0u));  // PRQA S 3803
    l_geometryLeft->addPrimitiveSet(new osg::DrawArrays(static_cast<vfc::uint32_t>(osg::PrimitiveSet::LINES), static_cast<vfc::int32_t>(l_vertexOffsetHairlines), static_cast<vfc::int32_t>(l_numVerticesHairlines)));  // PRQA S 3803

    l_geometryRight->addPrimitiveSet(createSurface(l_numLayoutPoints * (static_cast<vfc::uint32_t>(g_fixedTileNum)), static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT), 0u));  // PRQA S 3803
    l_geometryRight->addPrimitiveSet(new osg::DrawArrays(static_cast<vfc::uint32_t>(osg::PrimitiveSet::LINES), static_cast<vfc::int32_t>(l_vertexOffsetHairlines), static_cast<vfc::int32_t>(l_numVerticesHairlines)));  // PRQA S 3803
  }

  vfc::uint32_t l_vs_left = 0u;
  vfc::uint32_t l_vsHairlines_left = l_vertexOffsetHairlines;

  vfc::uint32_t l_vs_right = 0u;
  vfc::uint32_t l_vsHairlines_right = l_vertexOffsetHairlines;

  if (0u == l_numLayoutPoints)
  {
    return;
  }

  //! set the vertices
  for (vfc::uint32_t i = 0u; i < l_numLayoutPoints; ++i)
  {
    if (l_layout[i].m_isStartEdgeSegment == true)
    {
      //! colors shield
      osg::Vec4f l_colorShield = getColorShieldWithThreshold(l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts);
      l_colorShield.a() = g_tileSettings->m_shieldAlpha;
      //! colors hairlines
      // osg::Vec4f l_colorHairline = brighten(l_colorShield, g_tileSettings->m_hairlineBlooming);
      osg::Vec4f l_colorHairline = l_colorShield;
      l_colorHairline.a() = g_tileSettings->m_hairlineAlpha;

      //! set some position to transparant shield
      updatePasZoneAlphaByThreshold(l_layout[i].m_pasZoneLocatedArea, l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts, l_colorShield.a());
      updatePasZoneAlphaByThreshold(l_layout[i].m_pasZoneLocatedArea, l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts, l_colorHairline.a());

      //! First start point to be transparant
      (*l_verticesLeft)[l_vs_left]      = osg::Vec3f(l_layout[i].m_shieldStartInner, g_tileSettings->m_heightOverGround);
      (*l_verticesLeft)[l_vs_left + 1u] = osg::Vec3f(l_layout[i].m_shieldStartInner + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_verticesLeft)[l_vs_left + 2u] = osg::Vec3f(l_layout[i].m_shieldStartInner + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

      (*l_colorsLeft)[l_vs_left]      = l_colorNone;
      (*l_colorsLeft)[l_vs_left + 1u] = l_colorNone;
      (*l_colorsLeft)[l_vs_left + 2u] = l_colorNone;

      //! First start point with color
      (*l_verticesLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 1u]      = osg::Vec3f(l_layout[i].m_shieldStartInner, g_tileSettings->m_heightOverGround);
      (*l_verticesLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 1u + 1u] = osg::Vec3f(l_layout[i].m_shieldStartInner + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_verticesLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 1u + 2u] = osg::Vec3f(l_layout[i].m_shieldStartInner + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

      (*l_colorsLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 1u]      = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DSideAlphaRatioBottom);
      (*l_colorsLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 1u + 1u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DSideAlphaRatioDivide);
      (*l_colorsLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 1u + 2u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DSideAlphaRatioTop);

      //! Second end point with color
      (*l_verticesLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u]      = osg::Vec3f(l_layout[i].m_shieldStart3DOuter, g_tileSettings->m_heightOverGround);
      (*l_verticesLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 1u] = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_verticesLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 2u] = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

      (*l_colorsLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u]      = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DSideAlphaRatioBottom);
      (*l_colorsLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 1u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DSideAlphaRatioDivide);
      (*l_colorsLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 2u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DSideAlphaRatioTop);

      //! Second end point to be transparant
      (*l_verticesLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u]      = osg::Vec3f(l_layout[i].m_shieldStart3DOuter, g_tileSettings->m_heightOverGround);
      (*l_verticesLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 1u] = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_verticesLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 2u] = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

      (*l_colorsLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u]      = l_colorNone;
      (*l_colorsLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 1u] = l_colorNone;
      (*l_colorsLeft)[l_vs_left + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 2u] = l_colorNone;

      // for Hair Lines
      (*l_verticesLeft)[l_vsHairlines_left]      = osg::Vec3f(l_layout[i].m_shieldStartInner, g_tileSettings->m_heightOverGround);
      (*l_verticesLeft)[l_vsHairlines_left + 1u] = osg::Vec3f(l_layout[i].m_shieldStart3DOuter, g_tileSettings->m_heightOverGround);
      (*l_colorsLeft)[l_vsHairlines_left]        = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioBottom);
      (*l_colorsLeft)[l_vsHairlines_left + 1u]   = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioBottom);

      (*l_verticesLeft)[l_vsHairlines_left + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 1u]       = osg::Vec3f(l_layout[i].m_shieldStart3DOuter, g_tileSettings->m_heightOverGround);
      (*l_verticesLeft)[l_vsHairlines_left + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 1u + 1u]  = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_colorsLeft)[l_vsHairlines_left   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 1u]       = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioBottom);
      (*l_colorsLeft)[l_vsHairlines_left   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 1u + 1u]  = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioDivide);

      (*l_verticesLeft)[l_vsHairlines_left + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 2u]       = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_verticesLeft)[l_vsHairlines_left + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 2u + 1u]  = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);
      (*l_colorsLeft)[l_vsHairlines_left   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 2u]       = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioDivide);
      (*l_colorsLeft)[l_vsHairlines_left   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 2u + 1u]  = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioTop);

      (*l_verticesLeft)[l_vsHairlines_left + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 3u]       = osg::Vec3f(l_layout[i].m_shieldStart3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);
      (*l_verticesLeft)[l_vsHairlines_left + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 3u + 1u]  = osg::Vec3f(l_layout[i].m_shieldStartInner + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);
      (*l_colorsLeft)[l_vsHairlines_left   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 3u]       = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioTop);
      (*l_colorsLeft)[l_vsHairlines_left   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 3u + 1u]  = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioTop);

      (*l_verticesLeft)[l_vsHairlines_left + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 4u]       = osg::Vec3f(l_layout[i].m_shieldStartInner + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);
      (*l_verticesLeft)[l_vsHairlines_left + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 4u + 1u]  = osg::Vec3f(l_layout[i].m_shieldStartInner + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_colorsLeft)[l_vsHairlines_left   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 4u]       = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioTop);
      (*l_colorsLeft)[l_vsHairlines_left   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 4u + 1u]  = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioDivide);

      (*l_verticesLeft)[l_vsHairlines_left + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 5u]       = osg::Vec3f(l_layout[i].m_shieldStartInner + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_verticesLeft)[l_vsHairlines_left + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 5u + 1u]  = osg::Vec3f(l_layout[i].m_shieldStartInner, g_tileSettings->m_heightOverGround);
      (*l_colorsLeft)[l_vsHairlines_left   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 5u]       = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioDivide);
      (*l_colorsLeft)[l_vsHairlines_left   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 5u + 1u]  = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioBottom);

      l_vs_left += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT) * (static_cast<vfc::uint32_t>(g_fixedTileNum));
      l_vsHairlines_left += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_HAIRLINE_SEGMENT) * (static_cast<vfc::uint32_t>(g_fixedTileNum + 2u));
    }

    if (l_layout[i].m_isEndEdgeSegment == true)
    {
      //! colors shield
      osg::Vec4f l_colorShield = getColorShieldWithThreshold(l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts);
      l_colorShield.a() = g_tileSettings->m_shieldAlpha;
      //! colors hairlines
      // osg::Vec4f l_colorHairline = brighten(l_colorShield, g_tileSettings->m_hairlineBlooming);
      osg::Vec4f l_colorHairline = l_colorShield;
      l_colorHairline.a() = g_tileSettings->m_hairlineAlpha;

      //! set some position to transparant shield
      updatePasZoneAlphaByThreshold(l_layout[i].m_pasZoneLocatedArea, l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts, l_colorShield.a());
      updatePasZoneAlphaByThreshold(l_layout[i].m_pasZoneLocatedArea, l_layout[i].m_currentDistance, l_layout[i].m_ObjMovingSts, l_colorHairline.a());

      //! First start point to be transparant
      (*l_verticesRight)[l_vs_right]      = osg::Vec3f(l_layout[i].m_shieldEndInner, g_tileSettings->m_heightOverGround);
      (*l_verticesRight)[l_vs_right + 1u] = osg::Vec3f(l_layout[i].m_shieldEndInner + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_verticesRight)[l_vs_right + 2u] = osg::Vec3f(l_layout[i].m_shieldEndInner + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

      (*l_colorsRight)[l_vs_right]      = l_colorNone;
      (*l_colorsRight)[l_vs_right + 1u] = l_colorNone;
      (*l_colorsRight)[l_vs_right + 2u] = l_colorNone;

      //! First start point with color
      (*l_verticesRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 1u]      = osg::Vec3f(l_layout[i].m_shieldEndInner, g_tileSettings->m_heightOverGround);
      (*l_verticesRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 1u + 1u] = osg::Vec3f(l_layout[i].m_shieldEndInner + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_verticesRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 1u + 2u] = osg::Vec3f(l_layout[i].m_shieldEndInner + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

      (*l_colorsRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 1u]      = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DSideAlphaRatioBottom);
      (*l_colorsRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 1u + 1u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DSideAlphaRatioDivide);
      (*l_colorsRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 1u + 2u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DSideAlphaRatioTop);

      //! Second end point with color
      (*l_verticesRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u]      = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter, g_tileSettings->m_heightOverGround);
      (*l_verticesRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 1u] = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_verticesRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 2u] = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

      (*l_colorsRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u]      = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DSideAlphaRatioBottom);
      (*l_colorsRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 1u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DSideAlphaRatioDivide);
      (*l_colorsRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 2u + 2u] = osg::Vec4f(pc::util::toVec3(l_colorShield), l_colorShield.a() * g_tileSettings->m_shield3DSideAlphaRatioTop);

      //! Second end point to be transparant
      (*l_verticesRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u]      = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter, g_tileSettings->m_heightOverGround);
      (*l_verticesRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 1u] = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_verticesRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 2u] = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);

      (*l_colorsRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u]      = l_colorNone;
      (*l_colorsRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 1u] = l_colorNone;
      (*l_colorsRight)[l_vs_right + NUM_VERTICES_PER_SHIELD_SEGMENT * 3u + 2u] = l_colorNone;

      // for Hair Lines
      (*l_verticesRight)[l_vsHairlines_right]      = osg::Vec3f(l_layout[i].m_shieldEndInner, g_tileSettings->m_heightOverGround);
      (*l_verticesRight)[l_vsHairlines_right + 1u] = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter, g_tileSettings->m_heightOverGround);
      (*l_colorsRight)[l_vsHairlines_right]        = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioBottom);
      (*l_colorsRight)[l_vsHairlines_right + 1u]   = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioBottom);

      (*l_verticesRight)[l_vsHairlines_right + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 1u]       = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter, g_tileSettings->m_heightOverGround);
      (*l_verticesRight)[l_vsHairlines_right + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 1u + 1u]  = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_colorsRight)[l_vsHairlines_right   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 1u]       = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioBottom);
      (*l_colorsRight)[l_vsHairlines_right   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 1u + 1u]  = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioDivide);

      (*l_verticesRight)[l_vsHairlines_right + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 2u]       = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_verticesRight)[l_vsHairlines_right + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 2u + 1u]  = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);
      (*l_colorsRight)[l_vsHairlines_right   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 2u]       = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioDivide);
      (*l_colorsRight)[l_vsHairlines_right   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 2u + 1u]  = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioTop);

      (*l_verticesRight)[l_vsHairlines_right + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 3u]       = osg::Vec3f(l_layout[i].m_shieldEnd3DOuter + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);
      (*l_verticesRight)[l_vsHairlines_right + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 3u + 1u]  = osg::Vec3f(l_layout[i].m_shieldEndInner + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);
      (*l_colorsRight)[l_vsHairlines_right   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 3u]       = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioTop);
      (*l_colorsRight)[l_vsHairlines_right   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 3u + 1u]  = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioTop);

      (*l_verticesRight)[l_vsHairlines_right + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 4u]       = osg::Vec3f(l_layout[i].m_shieldEndInner + l_layout[i].m_verticalOffset, g_tileSettings->m_heightOverGround + g_tileSettings->m_shieldHeight);
      (*l_verticesRight)[l_vsHairlines_right + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 4u + 1u]  = osg::Vec3f(l_layout[i].m_shieldEndInner + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_colorsRight)[l_vsHairlines_right   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 4u]       = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioTop);
      (*l_colorsRight)[l_vsHairlines_right   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 4u + 1u]  = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioDivide);

      (*l_verticesRight)[l_vsHairlines_right + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 5u]       = osg::Vec3f(l_layout[i].m_shieldEndInner + l_layout[i].m_verticalRatioDivideOffset, g_tileSettings->m_heightOverGround + (g_tileSettings->m_shieldHeight * g_tileSettings->m_shieldSideRatioDivide));
      (*l_verticesRight)[l_vsHairlines_right + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 5u + 1u]  = osg::Vec3f(l_layout[i].m_shieldEndInner, g_tileSettings->m_heightOverGround);
      (*l_colorsRight)[l_vsHairlines_right   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 5u]       = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioDivide);
      (*l_colorsRight)[l_vsHairlines_right   + NUM_VERTICES_PER_HAIRLINE_SEGMENT * 5u + 1u]  = osg::Vec4f(pc::util::toVec3(l_colorHairline), l_colorHairline.a() * g_tileSettings->m_hairlineAlphaRatioBottom);

      l_vs_right += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT) * (static_cast<vfc::uint32_t>(g_fixedTileNum));
      l_vsHairlines_right += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_HAIRLINE_SEGMENT) * (static_cast<vfc::uint32_t>(g_fixedTileNum + 2u));
    }
  }

  l_verticesLeft->dirty();
  l_colorsLeft->dirty();
  l_geometryLeft->dirtyBound();

  l_verticesRight->dirty();
  l_colorsRight->dirty();
  l_geometryRight->dirtyBound();

}
} // namespace tileoverlay
} // namespace assets
} // namespace cc

