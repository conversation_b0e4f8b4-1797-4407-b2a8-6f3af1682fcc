//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/core/inc/CustomUltrasonic.h"
#include <cassert>



namespace cc
{
namespace core
{

pc::util::coding::Item<PasZonesDescription> g_pasZones("UltrasonicZones");
pc::util::coding::Item<DAIPasZonesDescription> g_DAIpasZones("DAIUltrasonicZones");
pc::util::coding::Item<VehicleContour> g_vehicleContour("VehicleContour");


//!
//! PasZonesDescription
//!
DAIPasZonesDescription::DAIPasZonesDescription()
  : m_frontArea1(3.28f, 0.86f, 3.28f,  2.87f)
  , m_frontArea2(3.49f, 0.86f, 4.005f, 2.75f)
  , m_frontArea3(3.70f, 0.86f, 4.73f,  2.63f)
  , m_frontArea4(3.84f, 0.67f, 5.29f,  1.895f)
  , m_frontArea5(3.98f, 0.48f, 5.85f,  1.16f)
  , m_frontArea6(3.98f, 0.24f, 5.85f,  0.58f)
  , m_leftArea1(-0.125f, 0.86f, -0.125f, 2.53f)
  , m_leftArea2(0.343f,  0.86f,  0.343f, 2.53f)
  , m_leftArea3(0.81f,   0.86f,  0.81f,  2.53f)
  , m_leftArea4(1.21f,   0.86f,  1.21f,  2.53f)
  , m_leftArea5(1.6f,    0.86f,  1.61f,  2.53f)
  , m_leftArea6(2.015f,  0.86f,  2.015f, 2.53f)
  , m_leftArea7(2.42f,   0.86f,  2.42f,  2.53f)
  , m_leftArea8(2.85f,   0.86f,  2.85f,  2.53f)
  , m_rearArea1(-0.348f, -0.86f, -0.848f, -2.880f)
  , m_rearArea2(-0.570f, -0.86f, -1.570f, -2.710f)
  , m_rearArea3(-0.825f, -0.71f, -2.148f, -1.985f)
  , m_rearArea4(-1.080f, -0.56f, -2.725f, -1.260f)
  , m_rearArea5(-1.080f, -0.28f, -2.725f, -0.630f)
  , m_rearArea6(-1.080f,  0.0f,  -2.725f,  0.0f)
{
}

const osg::Vec4f& DAIPasZonesDescription::getFrontArea(unsigned int f_index) const
{
  assert(f_index < NUM_PAS_ZONES_FRONT);
  switch (f_index)
  {
    case 0:
    {
      return m_frontArea1;
    }
    case 1:
    {
      return m_frontArea2;
    }
    case 2:
    {
      return m_frontArea3;
    }
    case 3:
    {
      return m_frontArea4;
    }
    case 4:
    {
      return m_frontArea5;
    }
    case 5:
    {
      return m_frontArea6;
    }
    default:
    {
      break; // PRQA S 2880
    }
  }
}


void DAIPasZonesDescription::setFrontArea(unsigned int f_index, const osg::Vec4f& f_value)
{
  assert(f_index < NUM_PAS_ZONES_FRONT);
  switch (f_index)
  {
    case 0:
    {
      m_frontArea1 = f_value;
      break;
    }
    case 1:
    {
      m_frontArea2 = f_value;
      break;
    }
    case 2:
    {
      m_frontArea3 = f_value;
      break;
    }
    case 3:
    {
      m_frontArea4 = f_value;
      break;
    }
    case 4:
    {
      m_frontArea5 = f_value;
      break;
    }
    case 5:
    {
      m_frontArea6 = f_value;
      break;
    }
    default:
    {
      break;
    }
  }
}


const osg::Vec4f& DAIPasZonesDescription::getLeftArea(unsigned int f_index) const
{
  assert(f_index < NUM_PAS_ZONES_SIDE);
  switch (f_index)
  {
    case 0:
    {
      return m_leftArea1;
    }
    case 1:
    {
      return m_leftArea2;
    }
    case 2:
    {
      return m_leftArea3;
    }
    case 3:
    {
      return m_leftArea4;
    }
    case 4:
    {
      return m_leftArea5;
    }
    case 5:
    {
      return m_leftArea6;
    }
    case 6:
    {
      return m_leftArea7;
    }
    case 7:
    {
      return m_leftArea8;
    }
    default:
    {
      break; // PRQA S 2880
    }
  }
}


void DAIPasZonesDescription::setLeftArea(unsigned int f_index, const osg::Vec4f& f_value)
{
  assert(f_index < NUM_PAS_ZONES_SIDE);
  switch (f_index)
  {
    case 0:
    {
      m_leftArea1 = f_value;
      break;
    }
    case 1:
    {
      m_leftArea2 = f_value;
      break;
    }
    case 2:
    {
      m_leftArea3 = f_value;
      break;
    }
    case 3:
    {
      m_leftArea4 = f_value;
      break;
    }
    case 4:
    {
      m_leftArea5 = f_value;
      break;
    }
    case 5:
    {
      m_leftArea6 = f_value;
      break;
    }
    case 6:
    {
      m_leftArea7 = f_value;
      break;
    }
    case 7:
    {
      m_leftArea8 = f_value;
      break;
    }
    default:
    {
      break; // PRQA S 2880
    }
  }
}


const osg::Vec4f& DAIPasZonesDescription::getRearArea(unsigned int f_index) const
{
  assert(f_index < NUM_PAS_ZONES_REAR);
  switch (f_index)
  {
    case 0:
    {
      return m_rearArea1;
    }
    case 1:
    {
      return m_rearArea2;
    }
    case 2:
    {
      return m_rearArea3;
    }
    case 3:
    {
      return m_rearArea4;
    }
    case 4:
    {
      return m_rearArea5;
    }
    case 5:
    {
      return m_rearArea6;
    }
    default:
    {
      break; // PRQA S 2880
    }
  }
}


void DAIPasZonesDescription::setRearArea(unsigned int f_index, const osg::Vec4f& f_value)
{
  assert(f_index < NUM_PAS_ZONES_REAR);
  switch (f_index)
  {
    case 0:
    {
      m_rearArea1 = f_value;
      break;
    }
    case 1:
    {
      m_rearArea2 = f_value;
      break;
    }
    case 2:
    {
      m_rearArea3 = f_value;
      break;
    }
    case 3:
    {
      m_rearArea4 = f_value;
      break;
    }
    case 4:
    {
      m_rearArea5 = f_value;
      break;
    }
    case 5:
    {
      m_rearArea6 = f_value;
      break;
    }
    default:
    {
      break;
    }
  }
}


//!
//! PasZonesDescription
//!
PasZonesDescription::PasZonesDescription() // (InnerPoint.x, InnerPoint.y, OuterPoint.x, OuterPoint.y)
    : m_zone00middleLine( 3.7768f,    0.221068f,  4.9875f,    0.43f)
    , m_zone01middleLine( 3.72354f,   0.550592f,  4.9225f,    1.215f)
    , m_zone02middleLine( 3.59673f,   0.763380f,  4.5575f,    1.82f)
    , m_zone03middleLine( 3.411f,     0.942207f,  3.76f,      2.135f)
    , m_zone04middleLine( 3.03406f,   0.989631f,  3.03406f,   2.2f)
    , m_zone05middleLine( 2.54219f,   0.989631f,  2.54219f,   2.2f)
    , m_zone06middleLine( 2.05031f,   0.989631f,  2.05031f,   2.2f)
    , m_zone07middleLine( 1.55844f,   0.989631f,  1.55844f,   2.2f)
    , m_zone08middleLine( 1.06656f,   0.989631f,  1.06656f,   2.2f)
    , m_zone09middleLine( 0.574687f,  0.989631f,  0.574687f,  2.2f)
    , m_zone10middleLine( 0.082813f,  0.989631f,  0.082813f,  2.2f)
    , m_zone11middleLine(-0.409062f,  0.989631f, -0.409062f,  2.2f)
    , m_zone12middleLine(-0.762475f,  0.828764f, -1.168f,     2.6125f)
    , m_zone13middleLine(-0.994866f,  0.728724f, -1.988f,     2.26f)
    , m_zone14middleLine(-1.13327f,   0.431802f, -2.5145f,    1.5225f)
    , m_zone15middleLine(-1.18492f,   0.130567f, -2.822f,     0.5375f)
    , m_zone16middleLine(-1.18492f,  -0.130567f, -2.822f,    -0.5375f)
    , m_zone17middleLine(-1.13327f,  -0.431802f, -2.5145f,   -1.5225f)
    , m_zone18middleLine(-0.994866f, -0.728724f, -1.988f,    -2.26f)
    , m_zone19middleLine(-0.762475f, -0.828764f, -1.168f,    -2.6125f)
    , m_zone20middleLine(-0.409062f, -0.989631f, -0.409062f, -2.2f)
    , m_zone21middleLine( 0.082813f, -0.989631f,  0.082813f, -2.2f)
    , m_zone22middleLine( 0.574687f, -0.989631f,  0.574687f, -2.2f)
    , m_zone23middleLine( 1.06656f,  -0.989631f,  1.06656f,  -2.2f)
    , m_zone24middleLine( 1.55844f,  -0.989631f,  1.55844f,  -2.2f)
    , m_zone25middleLine( 2.05031f,  -0.989631f,  2.05031f,  -2.2f)
    , m_zone26middleLine( 2.54219f,  -0.989631f,  2.54219f,  -2.2f)
    , m_zone27middleLine( 3.03406f,  -0.989631f,  3.03406f,  -2.2f)
    , m_zone28middleLine( 3.411f,    -0.942207f,  3.76f,     -2.135f)
    , m_zone29middleLine( 3.59673f,  -0.763380f,  4.5575f,   -1.82f)
    , m_zone30middleLine( 3.72354f,  -0.550592f,  4.9225f,   -1.215f)
    , m_zone31middleLine( 3.7768f,   -0.221068f,  4.9875f,   -0.43f)

    , m_zone00leftBorderLine( 3.75786f,   0.435001f,  4.97f,      0.86f)
    , m_zone01leftBorderLine( 3.67022f,   0.658399f,  4.875f,     1.57f)
    , m_zone02leftBorderLine( 3.51754f,   0.862855f,  4.24f,      2.07f)
    , m_zone03leftBorderLine( 3.28f,      0.975622f,  3.28f,      2.2f)
    , m_zone04leftBorderLine( 2.78812f,   0.989631f,  2.78812f,   2.2f)
    , m_zone05leftBorderLine( 2.29625f,   0.989631f,  2.29625f,   2.2f)
    , m_zone06leftBorderLine( 1.80437f,   0.989631f,  1.80437f,   2.2f)
    , m_zone07leftBorderLine( 1.3125f,    0.989631f,  1.3125f,    2.2f)
    , m_zone08leftBorderLine( 0.820625f,  0.989631f,  0.820625f,  2.2f)
    , m_zone09leftBorderLine( 0.32875f,   0.989631f,  0.32875f,   2.2f)
    , m_zone10leftBorderLine(-0.163125f,  0.989631f, -0.163125f,  2.2f)
    , m_zone11leftBorderLine(-0.655f,     0.875284f, -0.655f,     2.2f)
    , m_zone12leftBorderLine(-0.87093f,   0.7917f  , -1.684f,     2.55f)
    , m_zone13leftBorderLine(-1.07999f,   0.600438f, -2.292f,     1.97f)
    , m_zone14leftBorderLine(-1.16646f,   0.253902f, -2.737f,     1.075f)
    , m_zone15leftBorderLine(-1.18403f,   0.0f     , -2.902f,     0.0f)
    , m_zone16leftBorderLine(-1.16646f,  -0.253902f, -2.737f,    -1.075f)
    , m_zone17leftBorderLine(-1.07999f,  -0.600438f, -2.292f,    -1.97f)
    , m_zone18leftBorderLine(-0.87093f,  -0.7917f  , -1.684f,    -2.55f)
    , m_zone19leftBorderLine(-0.655f,    -0.875284f, -0.655f,    -2.2f)
    , m_zone20leftBorderLine(-0.163125f, -0.989631f, -0.163125f, -2.2f)
    , m_zone21leftBorderLine( 0.32875f,  -0.989631f,  0.32875f,  -2.2f)
    , m_zone22leftBorderLine( 0.820625f, -0.989631f,  0.820625f, -2.2f)
    , m_zone23leftBorderLine( 1.3125f,   -0.989631f,  1.3125f,   -2.2f)
    , m_zone24leftBorderLine( 1.80437f,  -0.989631f,  1.80437f,  -2.2f)
    , m_zone25leftBorderLine( 2.29625f,  -0.989631f,  2.29625f,  -2.2f)
    , m_zone26leftBorderLine( 2.78812f,  -0.989631f,  2.78812f,  -2.2f)
    , m_zone27leftBorderLine( 3.28f,     -0.975622f,  3.28f,     -2.2f)
    , m_zone28leftBorderLine( 3.51754f,  -0.862855f,  4.24f,     -2.07f)
    , m_zone29leftBorderLine( 3.67022f,  -0.658399f,  4.875f,    -1.57f)
    , m_zone30leftBorderLine( 3.75786f,  -0.435001f,  4.97f,     -0.86f)
    , m_zone31leftBorderLine( 3.79151f,   0.0f     ,  5.005f,     0.0f)
{
}


const osg::Vec4f& PasZonesDescription::getLeftBorderLine(vfc::uint32_t f_index) const
{
    assert(f_index < NUM_PAS_ZONES);
    return *(&m_zone00leftBorderLine + f_index);  //PRQA S 3705
}


void PasZonesDescription::setLeftBorderLine(vfc::uint32_t f_index, const osg::Vec4f& f_value)
{
    assert(f_index < NUM_PAS_ZONES);
    *(&m_zone00leftBorderLine + f_index) = f_value;  //PRQA S 3705

}


const osg::Vec4f& PasZonesDescription::getMiddleLine(vfc::uint32_t f_index) const
{
    assert(f_index < NUM_PAS_ZONES);
    return *(&m_zone00middleLine + f_index);  //PRQA S 3705
}


void PasZonesDescription::setMiddleBorderLine(vfc::uint32_t f_index, const osg::Vec4f& f_value)
{
    assert(f_index < NUM_PAS_ZONES);
    *(&m_zone00middleLine + f_index) = f_value;  //PRQA S 3705
}


//!
//! VehicleContour
//!
VehicleContour::VehicleContour()
  : m_front0( 3.540f, 0.960f)
  , m_front1( 3.722f, 0.920f)
  , m_front2( 3.922f, 0.802f)
  , m_front3( 4.018f, 0.584f)
  , m_front4( 4.082f, 0.340f)
  , m_front5( 4.110f, 0.096f)
  , m_side0( 3.540f, 0.960f)
  , m_side1( 2.904f, 0.977f)
  , m_side2( 2.269f, 0.977f)
  , m_side3(-0.073f, 0.977f)
  , m_side4(-0.378f, 0.960f)
  , m_rear0(-0.378f, 0.960f)
  , m_rear1(-0.857f, 0.886f)
  , m_rear2(-1.028f, 0.830f)
  , m_rear3(-1.134f, 0.712f)
  , m_rear4(-1.196f, 0.430f)
  , m_rear5(-1.195f, 0.071f)
  , m_unfoldedMirror0(0.0f, 0.0f) // if getting via IPC, values can be added here as default
  , m_unfoldedMirror1(0.0f, 0.0f)
  , m_unfoldedMirror2(0.0f, 0.0f)
  , m_unfoldedMirror3(0.0f, 0.0f)
  , m_foldedMirror0(0.0f, 0.0f)
  , m_foldedMirror1(0.0f, 0.0f)
  , m_foldedMirror2(0.0f, 0.0f)
  , m_foldedMirror3(0.0f, 0.0f)
{
}


const osg::Vec2f& VehicleContour::getFront(vfc::uint32_t f_index) const
{
  assert(f_index < NUM_POINTS_FRONT);
  return *(&m_front0 + f_index);  //PRQA S 3705
}


void VehicleContour::setFront(vfc::uint32_t f_index, const osg::Vec2f& f_point)
{
  assert(f_index < NUM_POINTS_FRONT);
  *(&m_front0 + f_index) = f_point;  //PRQA S 3705
}


const osg::Vec2f& VehicleContour::getSide(vfc::uint32_t f_index) const
{
  assert(f_index < NUM_POINTS_SIDE);
  return *(&m_side0 + f_index);  //PRQA S 3705
}


void VehicleContour::setSide(vfc::uint32_t f_index, const osg::Vec2f& f_point)
{
  assert(f_index < NUM_POINTS_SIDE);
  *(&m_side0 + f_index) = f_point;  //PRQA S 3705
}


const osg::Vec2f& VehicleContour::getRear(vfc::uint32_t f_index) const
{
  assert(f_index < NUM_POINTS_REAR);
  return *(&m_rear0 + f_index);  //PRQA S 3705
}


void VehicleContour::setRear(vfc::uint32_t f_index, const osg::Vec2f& f_point)
{
  assert(f_index < NUM_POINTS_REAR);
  *(&m_rear0 + f_index) = f_point;  //PRQA S 3705
}

const osg::Vec2f& VehicleContour::getUnfoldedMirror(vfc::uint32_t f_index) const
{
  assert(f_index < NUM_POINTS_MIRROR_UNFOLDED);
  return *(&m_unfoldedMirror0 + f_index);  //PRQA S 3705
}


void VehicleContour::setUnfoldedMirror(vfc::uint32_t f_index, const osg::Vec2f& f_point)
{
  assert(f_index < NUM_POINTS_MIRROR_UNFOLDED);
  *(&m_unfoldedMirror0 + f_index) = f_point;  //PRQA S 3705
}

const osg::Vec2f& VehicleContour::getFoldedMirror(vfc::uint32_t f_index) const
{
  assert(f_index < NUM_POINTS_MIRROR_FOLDED);
  return *(&m_foldedMirror0 + f_index);  //PRQA S 3705
}


void VehicleContour::setFoldedMirror(vfc::uint32_t f_index, const osg::Vec2f& f_point)
{
  assert(f_index < NUM_POINTS_MIRROR_FOLDED);
  *(&m_foldedMirror0 + f_index) = f_point;  //PRQA S 3705
}

pc::util::Polygon2D VehicleContour::toPolygon2D() const
{
  pc::util::Polygon2D l_contour(NUM_POINTS_TOTAL_WITHOUT_MIRROR);
  vfc::uint32_t l_pointIndex = 0u;
  //! start at the front center point and continue counter-clock-wise
  //! reading needs to be reversed since the data source is given from left to right
  for (vfc::uint32_t i = NUM_POINTS_FRONT; i > 0u; --i)
  {
    l_contour[l_pointIndex] = getFront(i - 1u);
    ++l_pointIndex;
  }
  //! side is given form front to back so we can continue without reversing
  for (vfc::uint32_t i = 0u; i < static_cast<vfc::uint32_t>(NUM_POINTS_SIDE); ++i)
  {
    l_contour[l_pointIndex] = getSide(i);
    ++l_pointIndex;
  }
  //! rear
  for (vfc::uint32_t i = 0u; i < static_cast<vfc::uint32_t>(NUM_POINTS_REAR); ++i)
  {
    l_contour[l_pointIndex] = getRear(i);
    ++l_pointIndex;
  }
    //! Now mirror the rest
  while (l_pointIndex < static_cast<vfc::uint32_t>(NUM_POINTS_TOTAL_WITHOUT_MIRROR))
  {
    const vfc::uint32_t l_mirroredIndex = static_cast<vfc::uint32_t>(NUM_POINTS_TOTAL_WITHOUT_MIRROR) - l_pointIndex - 1u;
    osg::Vec2f l_point = l_contour[l_mirroredIndex];
    l_point.y() = -l_point.y();
    l_contour[l_pointIndex] = l_point;
    ++l_pointIndex;
  }
  return l_contour;
}


namespace
{

inline pc::vehicle::LineData convert(const osg::Vec4f& f_pasLine)
{
  return pc::vehicle::LineData(
    osg::Vec2f(f_pasLine.x(), f_pasLine.y()),
    osg::Vec2f(f_pasLine.z(), f_pasLine.w()));
}

} //! namespace


//!
//! CustomZoneLayout
//!
CustomZoneLayout::CustomZoneLayout()
{
    // // static_assert(static_cast<vfc::uint32_t> (NUM_ZONES) <= static_cast<vfc::uint32_t> (PasZonesDescription::NUM_PAS_ZONES),
    // //     "CustomZoneLayout exceeds number of PAS zones");

    for (vfc::uint32_t l_zoneIndex = 0u; l_zoneIndex < NUM_ZONES; ++l_zoneIndex)
    {
        const osg::Vec4f& l_middleLine = g_pasZones->getMiddleLine(l_zoneIndex);
        m_middleLines[l_zoneIndex].m_innerPoint = osg::Vec2f(l_middleLine.x(), l_middleLine.y());
        m_middleLines[l_zoneIndex].m_outerPoint = osg::Vec2f(l_middleLine.z(), l_middleLine.w());
        m_middleLines[l_zoneIndex].m_direction = m_middleLines[l_zoneIndex].m_outerPoint - m_middleLines[l_zoneIndex].m_innerPoint;
        m_middleLines[l_zoneIndex].m_direction.normalize();  // PRQA S 3803  // PRQA S 3804

        const osg::Vec4f& l_leftBorderLine = g_pasZones->getLeftBorderLine(l_zoneIndex);
        m_leftBorderLines[l_zoneIndex].m_innerPoint = osg::Vec2f(l_leftBorderLine.x(), l_leftBorderLine.y());
        m_leftBorderLines[l_zoneIndex].m_outerPoint = osg::Vec2f(l_leftBorderLine.z(), l_leftBorderLine.w());
        m_leftBorderLines[l_zoneIndex].m_direction = m_leftBorderLines[l_zoneIndex].m_outerPoint - m_leftBorderLines[l_zoneIndex].m_innerPoint;
        m_leftBorderLines[l_zoneIndex].m_direction.normalize();  // PRQA S 3803  // PRQA S 3804
    }
}

//!
//! CustomZoneLayout
//! Only for PTS overlay
//!
CustomZoneLayout::CustomZoneLayout(bool /*is_TfsLayout*/)
{
  //! build a full-blown zone layout which starts at the X axis and goes around the
  //! vehicle in a mathematical positive sense (counter clock-wise)
  unsigned int l_zoneIndex = 0u;
  //! begin with the front left zones
  //! Since the PAS zone description goes from left to right, we need to reverse the reading order
  for (unsigned int i = DAIPasZonesDescription::NUM_PAS_ZONES_FRONT; i > 0u; --i)
  {
    m_leftBorderLines[l_zoneIndex] = convert(g_DAIpasZones->getFrontArea(i - 1));
    ++l_zoneIndex;
  }

  //! same here...
  for (unsigned int i = DAIPasZonesDescription::NUM_PAS_ZONES_SIDE; i > 0u; --i)
  {
    m_leftBorderLines[l_zoneIndex] = convert(g_DAIpasZones->getLeftArea(i - 1));
    ++l_zoneIndex;
  }

  //! Continue with the rear left area.
  //! The PAS zone description only contains the rear zones from left to right,
  //! so we mirror the Y-axis values to get them for the opposite side.
  //! However, the reading order needs to stay normal then (outer to inner)
  for (unsigned int i = 0u; i < DAIPasZonesDescription::NUM_PAS_ZONES_REAR; ++i)
  {
    const osg::Vec4f& l_line = g_DAIpasZones->getRearArea(i);
    m_leftBorderLines[l_zoneIndex] = pc::vehicle::LineData(
      osg::Vec2f(l_line.x(), -l_line.y()),
      osg::Vec2f(l_line.z(), -l_line.w()));
    ++l_zoneIndex;
  }

  //! Now mirror the rest
  while (l_zoneIndex < ( static_cast<vfc::uint32_t>(DAIPasZonesDescription::NUM_PAS_ZONES_TOTAL) - 1u))
  {
    const unsigned int l_mirroredIndex = static_cast<vfc::uint32_t>(DAIPasZonesDescription::NUM_PAS_ZONES_TOTAL) - l_zoneIndex - 2u;
    pc::vehicle::LineData l_line = m_leftBorderLines[l_mirroredIndex];
    l_line.m_innerPoint.y() = -l_line.m_innerPoint.y();
    l_line.m_outerPoint.y() = -l_line.m_outerPoint.y();
    l_line.m_direction.y() = -l_line.m_direction.y();
    m_leftBorderLines[l_zoneIndex] = l_line;
    ++l_zoneIndex;
  }

  //! ...and close the loop with right side of first sector, which is the mirror axis
  pc::vehicle::LineData l_line = m_leftBorderLines[0u];
  l_line.m_innerPoint.y() = 0.0f;
  l_line.m_outerPoint.y() = 0.0f;
  l_line.m_direction = osg::Vec2f(1.0f, 0.0f);
  m_leftBorderLines[l_zoneIndex] = l_line;

    // Calculate middle lines
  for (size_t i = 0u; i < static_cast<vfc::uint32_t>(BaseZoneLayout::NUM_ZONES) - 1u; ++i)
  {
    m_middleLines[i + 1u].m_innerPoint =
        (m_leftBorderLines[i].m_innerPoint + m_leftBorderLines[i + 1u].m_innerPoint) * 0.5f;
    m_middleLines[i + 1u].m_outerPoint =
        (m_leftBorderLines[i].m_outerPoint + m_leftBorderLines[i + 1u].m_outerPoint) * 0.5f;
    m_middleLines[i + 1u].m_direction = m_middleLines[i + 1u].m_outerPoint - m_middleLines[i + 1u].m_innerPoint;
    m_middleLines[i + 1u].m_direction.normalize();
  }
    // Handle the last middle line separately
  m_middleLines[0u].m_innerPoint =
    (m_leftBorderLines[0u].m_innerPoint + m_leftBorderLines[static_cast<vfc::uint32_t>(NUM_ZONES) - 1u].m_innerPoint) * 0.5f;
  m_middleLines[0u].m_outerPoint =
    (m_leftBorderLines[0u].m_outerPoint + m_leftBorderLines[static_cast<vfc::uint32_t>(NUM_ZONES) - 1u].m_outerPoint) * 0.5f;
  m_middleLines[0u].m_direction = (m_middleLines[0u].m_outerPoint - m_middleLines[0u].m_innerPoint);
  m_middleLines[0u].m_direction.normalize();
}


const pc::vehicle::LineData& CustomZoneLayout::getMiddleLine(unsigned int f_index) const
{
    return m_middleLines[f_index % static_cast<vfc::uint32_t>(NUM_ZONES)];
}

} // namespace core
} // namespace cc
