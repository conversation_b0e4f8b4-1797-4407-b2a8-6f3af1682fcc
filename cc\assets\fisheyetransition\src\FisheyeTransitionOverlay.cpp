/// @copyright (C) 2022 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "cc/assets/fisheyetransition/inc/FisheyeTransitionOverlay.h"
#include "cc/assets/fisheyetransition/inc/Sv3dUvRenderNode.h"

#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/views/customwarpfisheyeview/inc/CustomWarpFisheyeView.h"

#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/texfloor/odometry/inc/OdometrySimulatorNode.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/core/inc/VideoTexture.h"
#include "pc/svs/factory/inc/SV3DNode.h"
#include "pc/svs/views/warpfisheyeview/inc/WarpFisheyeView.h"
#include "pc/svs/util/osgx/inc/Utils.h"

#include "cc/imgui/inc/imgui_manager.h" // PRQA S 1060
#include "cc/imgui/inc/implot/implot.h"

#include "osgDB/WriteFile"
#include "osgDB/ReadFile"
#include "osgDB/FileUtils"
#include <cassert>

namespace cc
{
namespace assets
{
namespace fisheyetransition
{
using pc::util::logging::g_AppContext;

osg::ref_ptr<osg::Texture2D> createTexture(const std::string& f_filename)
{
  const osg::ref_ptr<osg::Image> l_image = osgDB::readImageFile(f_filename);
  if (nullptr != l_image)
  {
    const osg::ref_ptr<osg::Texture2D> l_texture = new osg::Texture2D(l_image);
    l_texture->setDataVariance(osg::Object::STATIC);
    l_texture->setUnRefImageDataAfterApply(true);
    l_texture->setResizeNonPowerOfTwoHint(false);
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP);
    l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP);
    return l_texture;
  }
  else
  {
    XLOG_ERROR(g_AppContext, "Failed to load parking space texture: " << f_filename);
  }
  return nullptr;
}

namespace
{

class SaveImageCallback : public osg::Camera::DrawCallback // PRQA S 2113
{
public:
    SaveImageCallback(osg::Image* f_image, const std::string& f_filename)
        : m_filename(f_filename) // PRQA S 4052
        , m_image(f_image) // PRQA S 2323
    {
    }

    void operator()(osg::RenderInfo& /*f_renderInfo*/) const override
    {
        osgDB::writeImageFile(*m_image, m_filename); // PRQA S 3803
    }

private:
    std::string m_filename;
    osg::Image* m_image;
};

void interpolateTexCoords(
    const std::vector<osg::Vec2us>& f_sourceUV,
    const std::vector<osg::Vec2us>& f_targetUV,
    float                           f_t, // PRQA S 2446
    osg::Vec2usArray*               f_texCoords)
{
    if (f_texCoords == nullptr)
    {
        XLOG_ERROR(g_AppContext, "interpolateTexCoords: f_texCoords is nullptr");
        return;
    }
    assert(f_sourceUV.size() == f_targetUV.size());
    assert(f_texCoords->size() == f_sourceUV.size());

    const unsigned int t1 = pc::util::round2uInt(f_t * 65535.0f); // PRQA S 2427
    const unsigned int t0 = 65535u - t1; // PRQA S 2427
    for (std::size_t i = 0; i < f_sourceUV.size(); i++)
    {
        const unsigned int x0 = f_sourceUV[i].x(); // PRQA S 2427
        const unsigned int y0 = f_sourceUV[i].y(); // PRQA S 2427

        const unsigned int x1 = f_targetUV[i].x(); // PRQA S 2427
        const unsigned int y1 = f_targetUV[i].y(); // PRQA S 2427

        (*f_texCoords)[i] = osg::Vec2us((x0 * t0 + x1 * t1) / 65535u, (y0 * t0 + y1 * t1) / 65535u); // PRQA S 3010
    }
    f_texCoords->dirty();
}

osg::Geode* createTextureDisplayGeode(
    unsigned int               f_resX, // PRQA S 2427
    unsigned int               f_resY, // PRQA S 2427
    pc::factory::SingleCamArea f_area,
    pc::core::Framework*       f_framework)
{
    if (f_resX == 0 || f_resY == 0 || nullptr == f_framework)
    {
        XLOG_ERROR(
            g_AppContext,
            "Big Widget dimensions are zero. Could not generate TextureDisplayGeode of FisheyeTransitionOverlay. Check "
            "viewport sizes (Display_Bigw_Size).");
        return nullptr;
    }

    osg::ref_ptr<osg::Geode> l_geode = new osg::Geode();

    osg::StateSet* const l_stateSet = l_geode->getOrCreateStateSet();
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::OFF);
    const unsigned int l_cameraIndex = static_cast<unsigned int>(f_area); // PRQA S 2427
    l_stateSet->setTextureAttribute(0u, f_framework->getVideoTexture()->getCameraTexture(l_cameraIndex));
    pc::core::setCameraTextureShader(l_stateSet, l_cameraIndex);
    // pc::core::TextureShaderProgramDescriptor l_rawTexShader("rawTex");
    // l_rawTexShader.apply(l_stateSet); // PRQA S 3803
    // l_stateSet->getOrCreateUniform("u_brightFactor", osg::Uniform::FLOAT)->set(1.0f);

    const osg::ref_ptr<osg::Geometry> l_geometry = new osg::Geometry();

    const osg::ref_ptr<osg::Vec2usArray> l_texCoords = new osg::Vec2usArray(f_resX * f_resY);
    l_texCoords->setNormalize(true);

    const osg::ref_ptr<osg::Vec2usArray> l_vertexCoords = new osg::Vec2usArray(osg::Array::BIND_PER_VERTEX, f_resX * f_resY);
    l_vertexCoords->setNormalize(true);

    for (std::size_t y = 0; y < f_resY; y++)
    {
        for (std::size_t x = 0; x < f_resX; x++)
        {
            const unsigned int l_i       = f_resX * y + x; // PRQA S 2427 // PRQA S 3010
            (*l_vertexCoords)[l_i] = osg::Vec2us(static_cast<vfc::uint16_t>((x * 65535u) / (f_resX - 1u)), static_cast<vfc::uint16_t>((y * 65535u) / (f_resY - 1u))); // PRQA S 2908
        }
    }

    const osg::ref_ptr<osg::DrawElementsUShort> l_index =
        new osg::DrawElementsUShort(GL_TRIANGLES, (f_resX - 1) * (f_resY - 1) * 6);
    for (std::size_t y = 0; y < f_resY - 1; y++) // PRQA S 3000
    {
        const std::size_t i0 = y * (f_resX - 1); // PRQA S 3000
        const std::size_t i1 = i0 + f_resX - 1;
        for (std::size_t i = i0, v = y * f_resX; i < i1; i++, v++) // PRQA S 4107
        {
            (*l_index)[i * 6 + 0] = v; // PRQA S 3010
            (*l_index)[i * 6 + 1] = v + 1; // PRQA S 3000 // PRQA S 3010
            (*l_index)[i * 6 + 2] = v + f_resX; // PRQA S 3010
            (*l_index)[i * 6 + 3] = v + f_resX; // PRQA S 3010
            (*l_index)[i * 6 + 4] = v + 1; // PRQA S 3000 // PRQA S 3010
            (*l_index)[i * 6 + 5] = v + f_resX + 1; // PRQA S 3000 // PRQA S 3010
        }
    }

    l_geometry->setTexCoordArray(0, l_texCoords);
    l_geometry->setVertexArray(l_vertexCoords);
    l_geometry->addPrimitiveSet(l_index); // PRQA S 3803
    l_geometry->setUseDisplayList(false);
    l_geometry->setUseVertexBufferObjects(true);

    l_geode->addDrawable(l_geometry); // PRQA S 3803
    return l_geode.release();
}

namespace
{
inline unsigned short decodeUshort(unsigned char r, unsigned char g) // PRQA S 2427
{
    // Combines two steps:
    // float v = (r/255.0) + (g/255.0)/255.0;
    // unsigned short us = v*65535.0f;
    const vfc::float32_t v = (static_cast<vfc::float32_t>(r)/255.0f) + (static_cast<vfc::float32_t>(g)/255.0f)/255.0f;
    const unsigned short us = static_cast<unsigned short>(pc::util::round2uInt(v*65535.0f)); // PRQA S 2427
    return us;
}
} // namespace

std::vector<osg::Vec2us> createTexCoordsFromUVImage(osg::Image& f_image)
{
    unsigned char*     const      l_texCoordBuffer = reinterpret_cast<unsigned char*>(f_image.data()); // PRQA S 2427
    const unsigned int             l_size           = f_image.t() * f_image.s(); // PRQA S 2427 // PRQA S 3000
    std::vector<osg::Vec2us> l_texCoords(l_size);

    for (std::size_t i = 0; i < l_size; i++)
    {
        const unsigned short l_u = decodeUshort(l_texCoordBuffer[i * 4 + 0], l_texCoordBuffer[i * 4 + 1]); // PRQA S 2427
        const unsigned short l_v = decodeUshort(l_texCoordBuffer[i * 4 + 2], l_texCoordBuffer[i * 4 + 3]); // PRQA S 2427
        l_texCoords[i]     = osg::Vec2us(l_u, l_v);
    }
    return l_texCoords;
}

osg::Matrix expandFrustumByHalfPixel(const osg::Matrix& f_projectionMatrix, unsigned int f_resX, unsigned f_resY) // PRQA S 2427
{
    float l_left = 0.f; // PRQA S 2446
    float l_right = 0.f; // PRQA S 2446
    float l_bottom = 0.f; // PRQA S 2446
    float l_top = 0.f; // PRQA S 2446
    float l_near = 0.f; // PRQA S 2446
    float l_far = 0.f; // PRQA S 2446
    if (!f_projectionMatrix.getFrustum(l_left, l_right, l_bottom, l_top, l_near, l_far))
    {
        return f_projectionMatrix;
    }

    // Expand width
    const float l_w  = l_right - l_left; // PRQA S 2446
    const float l_dw = (l_w / f_resX) * 0.5f; // PRQA S 2446 // PRQA S 3011
    l_left -= l_dw;
    l_right += l_dw;

    // Expand height
    const float l_h  = l_top - l_bottom; // PRQA S 2446
    const float l_dh = (l_h / f_resY) * 0.5f; // PRQA S 2446 // PRQA S 3011
    l_bottom -= l_dh;
    l_top += l_dh;

    osg::Matrix l_expanded;
    l_expanded.makeFrustum(l_left, l_right, l_bottom, l_top, l_near, l_far);
    return l_expanded;
}

} // namespace

FisheyeTransitionOverlay::FisheyeTransitionOverlay( // PRQA S 4207
    cc::core::AssetId                        f_assetId,
    pc::core::View&                          f_sourceView,
    cc::core::AssetId                        f_sv3dAsset,
    unsigned int                             f_subsampleFactor, // PRQA S 2427
    pc::factory::SV3DNode&                   f_floor,
    pc::factory::SV3DNode&                   f_bowl,
    pc::factory::SingleCamArea               f_area,
    pc::views::warpfisheye::WarpFisheyeView& f_fisheyeView,
    pc::views::warpfisheye::FisheyeModel&    f_pModel,
    pc::core::Framework*                     f_framework)
    : pc::core::Asset{f_assetId} // PRQA S 2323
    , m_framework{f_framework} // PRQA S 2323
    , m_parentView{f_sourceView} // PRQA S 2323
    , m_parentViewSv3dAsset{f_sv3dAsset} // PRQA S 2323
    , m_fisheyeView{f_fisheyeView} // PRQA S 2323
    , m_calibSeqNumber{std::numeric_limits<vfc::uint16_t>::max()} // PRQA S 2323
    , m_capturedSteeringAngle{std::numeric_limits<float>::max()} // PRQA S 2323 // PRQA S 2446
    , m_requiresUpdate{true} // PRQA S 2323
    , m_transitionEnabled{false} // PRQA S 2323
    , m_hasSourceData{false} // PRQA S 2323
    , m_hasTargetData{false} // PRQA S 2323
    , m_isFromFisheye{false} // PRQA S 2323
    , m_sourceDataCaptureFrame{0u} // PRQA S 2323
    , m_sourceDataDownloadFrame{0u} // PRQA S 2323
    , m_width{0u} // PRQA S 2323
    , m_height{0u} // PRQA S 2323
    , m_sourceUVMap{nullptr} // PRQA S 2323
    , m_captureUVCamera{nullptr} // PRQA S 2323
    , m_textureDisplayCamera{nullptr} // PRQA S 2323
    , m_textureDisplayGeode{nullptr} // PRQA S 2323
    , m_model{f_pModel} // PRQA S 2323
    , m_sourceUV{} // PRQA S 2323
    , m_targetUV{} // PRQA S 2323
    , m_blendValue{0.0f} // PRQA S 2323
    , m_updateSteeringValue{false} // PRQA S 2323
{
    m_targetUV              = (m_fisheyeView.getUVMap(m_width, m_height));
    m_sourceUVMap           = new osg::Image();
    m_captureUVCamera       = new osg::Camera();
    m_textureDisplayCamera  = new osg::Camera();

    m_width = (pc::util::round2uInt(static_cast<vfc::float32_t>(f_sourceView.getViewport()->width())) + f_subsampleFactor - 1U) / f_subsampleFactor;
    m_height = (pc::util::round2uInt(static_cast<vfc::float32_t>(f_sourceView.getViewport()->height())) + f_subsampleFactor - 1U) / f_subsampleFactor;
    m_textureDisplayGeode = (createTextureDisplayGeode(m_width, m_height, f_area, f_framework));
    m_captureUVCamera->setViewport(new osg::Viewport(0, 0, m_width, m_height)); // PRQA S 3011

    // Adjust projection matrix to account for grid vs center sampling
    const osg::Matrix l_expandedProjection = expandFrustumByHalfPixel(f_sourceView.getProjectionMatrix(), m_width, m_height);
    osg::Matrixf l_mirrorTheWorld;
    l_mirrorTheWorld(0, 0) = -1.f;
    if (f_area == pc::factory::SingleCamArea::SINGLE_CAM_REAR)
    {
        m_captureUVCamera->setProjectionMatrix(l_expandedProjection * l_mirrorTheWorld);
    }
    else
    {
        m_captureUVCamera->setProjectionMatrix(l_expandedProjection);
    }

    m_captureUVCamera->setViewMatrix(f_sourceView.getViewMatrix());
    m_captureUVCamera->setReferenceFrame(f_sourceView.getReferenceFrame());
    m_captureUVCamera->setClearMask(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
    m_captureUVCamera->setClearColor(osg::Vec4(1.0f, 0.0f, 0.0f, 1.0f));
    m_captureUVCamera->setCullingActive(false);

    m_captureUVCamera->setRenderOrder(osg::Camera::PRE_RENDER);
    m_captureUVCamera->setRenderTargetImplementation(osg::Camera::FRAME_BUFFER_OBJECT, osg::Camera::FRAME_BUFFER);

    m_sourceUVMap->allocateImage(m_width, m_height, 1, GL_RGBA, GL_UNSIGNED_BYTE); // PRQA S 3000
    // We need to use GL_RGBA8 on GLES 2.0, where GL_RGBA is not a valid internal format
    m_sourceUVMap->setInternalTextureFormat(GL_RGBA8);

    // CAVEAT: Possible roadblock! Capturing to osg::Image may be a performance issue, since GL ES 2.0 does not support
    // PBOs and we have to rely on glReadPixels, forcing a pipeline sync Possible solutions:
    // - render to texture, then copy data from the texture at a later time, hopefully avoiding the GPU flush
    // - look into whether we can create an EGLimage buffer
    m_captureUVCamera->attach(osg::Camera::COLOR_BUFFER, m_sourceUVMap);
    m_captureUVCamera->addChild(new cc::assets::fisheyetransition::SV3DUVRenderNode(&f_floor, f_area)); // PRQA S 3803
    m_captureUVCamera->addChild(new cc::assets::fisheyetransition::SV3DUVRenderNode(&f_bowl, f_area)); // PRQA S 3803

    // m_captureUVCamera->setFinalDrawCallback(new TestCallback(m_sourceUVMap));

    setNumChildrenRequiringUpdateTraversal(1);

    m_textureDisplayCamera->setClearMask(0u);
    m_textureDisplayCamera->setRenderOrder(osg::Camera::POST_RENDER);
    m_textureDisplayCamera->setReferenceFrame(osg::Transform::ABSOLUTE_RF);
    m_textureDisplayCamera->setViewport(f_sourceView.getViewport());
    m_textureDisplayCamera->setCullingActive(false);
    m_textureDisplayCamera->setComputeNearFarMode(osg::Camera::DO_NOT_COMPUTE_NEAR_FAR);
    m_textureDisplayCamera->setProjectionMatrixAsOrtho2D(0.0f, 1.0f, 0.0f, 1.0f);
    if (f_area == pc::factory::SingleCamArea::SINGLE_CAM_REAR)
    {
        m_textureDisplayCamera->setProjectionMatrix(m_textureDisplayCamera->getProjectionMatrix() * l_mirrorTheWorld);
    }
    m_textureDisplayCamera->setViewMatrix(osg::Matrix::identity());
    m_textureDisplayCamera->addChild(m_textureDisplayGeode); // PRQA S 3803
    // m_textureDisplayCamera->setNumChildrenRequiringUpdateTraversal(1);
    // m_textureDisplayCamera->addChild(m_testGeode);

    // We currently manually dispatch node visitors to the cameras in ::traverse(), so do not add them as children
    // addChild(m_captureUVCamera);
    // addChild(m_textureDisplayCamera);
}

FisheyeTransitionOverlay::TransitionAnimation::TransitionAnimation(
    float                     f_startBlend, // PRQA S 2446
    float                     f_endBlend, // PRQA S 2446
    float                     f_duration, // PRQA S 2446
    FisheyeTransitionOverlay& f_overlay,
    bool                      f_isFromFisheye)
    : m_overlay(f_overlay) // PRQA S 2323 // PRQA S 4052
    , m_startBlend(f_startBlend) // PRQA S 2323
    , m_endBlend(f_endBlend) // PRQA S 2323
    , m_duration(f_duration) // PRQA S 2323
    , m_isFromFisheye(f_isFromFisheye) // PRQA S 2323
{
    // cc::core::CustomFramework* const l_cusFramework = m_overlay.getFramework()->asCustomFramework();
    // l_cusFramework->setViewTransitionState(cc::daddy::Transition::Morphing);
}

float FisheyeTransitionOverlay::TransitionAnimation::getFixedDuration() const // PRQA S 2446
{
    return m_duration;
}

bool FisheyeTransitionOverlay::TransitionAnimation::hasFixedDuration() const
{
    return true;
}

bool FisheyeTransitionOverlay::TransitionAnimation::supportsCancellation() const
{
    return false;
}

void FisheyeTransitionOverlay::TransitionAnimation::onBegin()
{

    m_overlay.m_transitionEnabled   = true;
    m_overlay.m_isFromFisheye       = this->m_isFromFisheye;
    m_overlay.m_updateSteeringValue = true;
    const auto customFisheye = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeView*>(&m_overlay.m_fisheyeView); // PRQA S 3400
    if ( (nullptr != customFisheye) && !m_isFromFisheye)
    {
        customFisheye->customUpdate();
    }
    // if using osg::Camera::setViewport(osg::Viewport* viewport) the viewport can change
    if (m_overlay.m_textureDisplayCamera->getViewport() != m_overlay.m_parentView.getViewport())
    {
        m_overlay.m_textureDisplayCamera->setViewport(m_overlay.m_parentView.getViewport());
    }
}

bool FisheyeTransitionOverlay::TransitionAnimation::onUpdate(float f_elapsed) // PRQA S 2446
{
    // Perform linear blending of UV coord sets
    const float l_t                      = std::max(0.0f, std::min<float>(1.0f, f_elapsed / m_duration)); // PRQA S 2446
    const float l_value                  = (1.0f - l_t) * m_startBlend + l_t * m_endBlend; // PRQA S 2446
    m_overlay.m_transitionEnabled = true;
    m_overlay.m_blendValue        = l_value;

    m_overlay.m_transitionEnabled = true;
    return f_elapsed >= m_duration;
}

void FisheyeTransitionOverlay::TransitionAnimation::onEnd(bool /*f_canceled*/)
{
    m_overlay.m_transitionEnabled = false;
}

pc::animation::Animation* FisheyeTransitionOverlay::createTransitionAnimation(
    float f_startBlend, // PRQA S 2446
    float f_endBlend, // PRQA S 2446
    float f_duration, // PRQA S 2446
    bool  f_fromFisheye)
{
    return new TransitionAnimation(f_startBlend, f_endBlend, f_duration, *this, f_fromFisheye);
}

bool FisheyeTransitionOverlay::requiresSourceUVUpdate()
{
    bool l_updateNeeded = false;

    // Update information for steering only on an animation begin to avoid flickering at every frame
    if (m_updateSteeringValue)
    {
        m_updateSteeringValue = false;

        // Check if calibration has been updated. If yes, an update is needed.
        const pc::daddy::CameraCalibrationDaddy* const l_calibDaddy = m_framework->m_cameraCalibrationReceiver.getData();
        if (nullptr != l_calibDaddy && l_calibDaddy->m_sequenceNumber != m_calibSeqNumber)
        {
            m_calibSeqNumber = l_calibDaddy->m_sequenceNumber;
            l_updateNeeded   = true;
        }

        // Check if steering angle is still the same as when we last captured the image. If not we need to update.
        // const pc::daddy::OdometryDataDaddy* l_odometryData = m_framework->m_odometryReceiver.getData();
        // if (l_odometryData != nullptr)
        // {
        //     const float l_steeringFrontAngle =
        //         pc::texfloor::odometry::kappaFwd2SteeringAngleFront(
        //             l_odometryData->m_Data.m_kappaFwd, l_odometryData->m_Data.m_steeringAngleRearFwd)
        //             .value();

        //     if (!vfc::isEqual(l_steeringFrontAngle, m_capturedSteeringAngle))
        //     {
        //         m_capturedSteeringAngle = l_steeringFrontAngle;
        //         l_updateNeeded          = true;
        //     }
        // }

        const auto l_steeringAngleData = m_framework->m_steeringAngleFrontReceiver.getData();
        if (l_steeringAngleData != nullptr)
        {
            if (!vfc::isEqual(l_steeringAngleData->m_Data.value(), m_capturedSteeringAngle))
            {
                m_capturedSteeringAngle = l_steeringAngleData->m_Data.value();
                l_updateNeeded          = true;
            }
        }

        if (m_model.getModificationCount() != m_settingsModifCount)
        {
            m_settingsModifCount = m_model.getModificationCount(); // PRQA S 3010
            l_updateNeeded       = true;
        }
    }

    return l_updateNeeded;
}

void FisheyeTransitionOverlay::updateTransitionAnimation(float f_blendValue) // PRQA S 4211 // PRQA S 2446
{
    // Perform linear blending of UV coord sets
    if (m_textureDisplayGeode != nullptr)
    {
        osg::Geometry* const l_geometry = m_textureDisplayGeode->getDrawable(0)->asGeometry();
        interpolateTexCoords(
            m_sourceUV,
            m_targetUV,
            f_blendValue,
            dynamic_cast<osg::Vec2usArray*>(l_geometry->getTexCoordArray(0))); // PRQA S 3400
    }
}

// this function is used when we want to show solely the fisheyeview without any blending. This is needed, when the
// source texture is not downloaded.
void FisheyeTransitionOverlay::showFisheyeView()
{
    // use solely the captured targetUV (FisheyeView) for the texture
    if (m_textureDisplayGeode != nullptr)
    {
        osg::Geometry*    const l_geometry = m_textureDisplayGeode->getDrawable(0)->asGeometry();
        osg::Vec2usArray* const l_texCoords = dynamic_cast<osg::Vec2usArray*>(l_geometry->getTexCoordArray(0)); // PRQA S 3400
        assert(l_texCoords->size() == m_targetUV.size()); // assert that the target texture (fisheyeview) is valid

        for (std::size_t i = 0; i < m_targetUV.size(); i++) // PRQA S 4297
        {
            const unsigned int x    = m_targetUV[i].x(); // PRQA S 2427
            const unsigned int y    = m_targetUV[i].y(); // PRQA S 2427
            (*l_texCoords)[i] = osg::Vec2us(x, y); // PRQA S 3010
        }
        l_texCoords->dirty();
    }
}

void FisheyeTransitionOverlay::traverse(osg::NodeVisitor& f_nv)
{
    // note: 1-based frame counter (facilitates unsigned "<" comparison)
    const unsigned int l_currentFrame = (f_nv.getFrameStamp() != nullptr) ? f_nv.getFrameStamp()->getFrameNumber() + 1u : 1u; // PRQA S 2427

    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {

    #ifdef TARGET_STANDALONE
        if (IMGUI_GET_BUTTON_BOOL("Settings", "DumpSourceUVMap"))
        {
            osgDB::writeImageFile(*m_sourceUVMap.get(), "sourceUVMap.png");
        }
        if (IMGUI_GET_CHECKBOX_BOOL("Animations", "ImguiOverwrite"))
        {
            m_transitionEnabled = IMGUI_GET_CHECKBOX_BOOL("Animations", "TransitionEnabeld");
            m_blendValue = IMGUI_GET_SLIDER_FLOAT("Animations", "BlendValue", 0.0f, 1.0f);
            m_textureDisplayGeode->getStateSet()->getOrCreateUniform("u_brightFactor", osg::Uniform::FLOAT)->set(
                IMGUI_GET_SLIDER_FLOAT("Animations", "Brightness", 0.0f, 1.0f)
            );
        }

        IMGUI_LOG("Animations", "SourceDataCaptureFrame", m_sourceDataCaptureFrame);
        IMGUI_LOG("Animations", "SourceDataDownloadFrame", m_sourceDataDownloadFrame);
        IMGUI_LOG("Animations", "HasSourceData", m_hasSourceData);
        IMGUI_LOG("Animations", "HasTargetData", m_hasTargetData);
        IMGUI_LOG("Animations", "IsFromFisheye", m_isFromFisheye);
        IMGUI_LOG("Animations", "BlendValue", m_blendValue);
        IMGUI_LOG("Animations", "Show Parent", m_parentView.getAssetValue(m_parentViewSv3dAsset));
    #endif // TARGET_STANDALONE

        bool l_showTransition = false;
        // We do not want to pre download any images before the transition is activated as the camera might still be
        // moving
        if (m_transitionEnabled)
        {
            // Did we RTT in a past frame, and haven't downloaded it yet?
            // This introduces a one-frame delay between rendering and image download, in preparation for delayed
            // texture download note to the paranoid: Should we worry about frame counter wrap-around? Will occur after
            // approx. 4 years uptime!
            if ((m_sourceDataCaptureFrame <
                 l_currentFrame) && // explicit check to ensure we're idempotent with regards to multiple updates
                (m_sourceDataDownloadFrame < m_sourceDataCaptureFrame))
            {
                m_sourceUV                = createTexCoordsFromUVImage(*m_sourceUVMap);
                m_sourceDataDownloadFrame = l_currentFrame;
                m_hasSourceData           = true;
            }

            const bool l_requireUpdate = requiresSourceUVUpdate();

            // Capture source UV if required (i.e., initial capture and calibration updates)
            if (!m_hasSourceData || l_requireUpdate)
            {
                m_captureUVCamera->setViewMatrix(m_parentView.getViewMatrix());
                m_captureUVCamera->accept(f_nv);
                m_sourceDataCaptureFrame = l_currentFrame;
                // our downloaded texture is outdated, therefore we will show the transition in the next frame after the
                // download of the updated texture is complete
                if (m_sourceDataDownloadFrame < m_sourceDataCaptureFrame)
                {
                    m_hasSourceData = false; // we do not have a valid texture downloaded for the source view
                }
            }
            if (!m_hasTargetData || l_requireUpdate)
            {
                m_targetUV      = m_fisheyeView.getUVMap(m_width, m_height);
                m_hasTargetData = true;
            }

            l_showTransition = (m_hasSourceData && m_hasTargetData);
            if (l_showTransition)
            {
                updateTransitionAnimation(m_blendValue);
                m_textureDisplayCamera->accept(f_nv);
                // plot();
            }
            else
            {
                // if the transition is not ready yet but we are coming from the fisheye, we want to keep showing the
                // fisheye frame while waiting for the texture download if we do not do this, there might be a frame of
                // the singlecam shown while we wait, resulting in a jump of the visual image
                if (m_isFromFisheye && m_hasTargetData)
                {
                    l_showTransition = true; // set to true so the singlecam asset is not turned on
                    showFisheyeView();
                    m_textureDisplayCamera->accept(f_nv);
                }
            }
        }
        // No need to draw the original singlecam asset if transition is running
        // we only disable the parentview, if we have a transition enabled AND we are ready to show the transition, as
        // otherwise it may lead to an early disable which causes a grey frame
        m_parentView.setAssetValue(m_parentViewSv3dAsset, (!m_transitionEnabled || !l_showTransition)); // PRQA S 3803
    }
    else if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
    {
        // Visit RTT camera if we initiated capture in the update traversal
        if (m_sourceDataCaptureFrame == l_currentFrame)
        {
            m_captureUVCamera->accept(f_nv);
        }

        const bool l_showTransition = (m_transitionEnabled && (m_hasSourceData || m_isFromFisheye) && m_hasTargetData);
        if (l_showTransition)
        {
            m_textureDisplayCamera->accept(f_nv);
        }
    }
    else
    {
        //do nothing
    }
    osg::Group::traverse(f_nv);
}

void FisheyeTransitionOverlay::plot() // PRQA S 4212
{
#if defined(TARGET_STANDALONE) && defined(ENABLE_IMGUI)
    if (m_framework->asCustomFramework()->isImguiEnabled())
    {
        if (ImGui::Begin("AnimationsPlot"))
        {
            ImPlotFlags flag = ImPlotFlags_NoLegend || ImPlotFlags_Equal || ImPlotFlags_NoTitle;
            if (ImPlot::BeginPlot("TextureCoordinate", ImVec2(-1, 500), flag))
            {
                osg::Geometry* l_geometry = m_textureDisplayGeode->getDrawable(0)->asGeometry();
                osg::Vec2usArray* l_texCoords = dynamic_cast<osg::Vec2usArray*>(l_geometry->getTexCoordArray(0u));
                osg::Vec2usArray* l_vertexCoords = dynamic_cast<osg::Vec2usArray*>(l_geometry->getVertexArray());
                if (l_texCoords)
                {
                    ImPlot::PlotScatter(
                        "TextureCoordinate",
                        &l_texCoords->at(0)._v[0],
                        &l_texCoords->at(0)._v[1],
                        l_texCoords->size(),
                        0,
                        0,
                        sizeof(osg::Vec2us));
                }
                ImPlot::EndPlot();
            }
            if (ImPlot::BeginPlot("SourceUV", ImVec2(-1, 500), flag))
            {
                ImPlot::PlotScatter(
                    "SourceUV",
                    &m_sourceUV.at(0)._v[0],
                    &m_sourceUV.at(0)._v[1],
                    m_sourceUV.size(),
                    0,
                    0,
                    sizeof(osg::Vec2us));
                ImPlot::EndPlot();
            }
            if (ImPlot::BeginPlot("TargetUV", ImVec2(-1, 500), flag))
            {
                ImPlot::PlotScatter(
                    "TargetUV",
                    &m_targetUV.at(0)._v[0],
                    &m_targetUV.at(0)._v[1],
                    m_targetUV.size(),
                    0,
                    0,
                    sizeof(osg::Vec2us));
                ImPlot::EndPlot();
            }
            if (ImPlot::BeginPlot("VertexArray", ImVec2(-1, 500), flag))
            {
                osg::Geometry* l_geometry = m_textureDisplayGeode->getDrawable(0)->asGeometry();
                osg::Vec2usArray* l_vertexCoords = dynamic_cast<osg::Vec2usArray*>(l_geometry->getVertexArray());
                ImPlot::PlotScatter(
                    "VertexCoordinates",
                    &l_vertexCoords->at(0)._v[0],
                    &l_vertexCoords->at(0)._v[1],
                    l_vertexCoords->size(),
                    0,
                    0,
                    sizeof(osg::Vec2us));
                ImPlot::EndPlot();
            }
        }
        ImGui::End();
    }
#endif // TARGET_STANDALONE
}

} // namespace fisheyetransition
} // namespace assets
} // namespace cc
