//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/trajectory/inc/Helper.h"

#include <limits>

namespace cc
{
namespace assets
{
namespace trajectory
{
namespace helper
{

void TrajectoryHelper::Rotate2DPoint(osg::Vec2f& f_PointToRotate, vfc::float32_t f_cos, vfc::float32_t f_sin) // PRQA S 4287
{
  f_PointToRotate = osg::Vec2f(
    (f_PointToRotate.x() * f_cos) - (f_PointToRotate.y() * f_sin),
    (f_PointToRotate.x() * f_sin) + (f_PointToRotate.y() * f_cos));
}


void TrajectoryHelper::Rotate2DPoint(osg::Vec2f& f_PointToRotate, vfc::float32_t f_Angle) // PRQA S 4287
{
    TrajectoryHelper::Rotate2DPoint(f_PointToRotate, std::cos(f_Angle), std::sin(f_Angle));
}

void TrajectoryHelper::Rotate2DPoint(
    osg::Vec2f&       f_PointToRotate,
    const osg::Vec2f& f_PointToRotateAround,
    vfc::float32_t    f_cos,
    vfc::float32_t    f_sin)
{
    f_PointToRotate -= f_PointToRotateAround;
    TrajectoryHelper::Rotate2DPoint(f_PointToRotate, f_cos, f_sin);
    f_PointToRotate += f_PointToRotateAround;
}

void TrajectoryHelper::Rotate2DPoint(osg::Vec2f& f_PointToRotate, const osg::Vec2f& f_PointToRotateAround, vfc::float32_t f_Angle) // PRQA S 4287
{
    TrajectoryHelper::Rotate2DPoint(f_PointToRotate, f_PointToRotateAround, std::cos(f_Angle), std::sin(f_Angle));
}

void TrajectoryHelper::SwapVerticesVec4f(osg::Vec4f& f_Vertex1, osg::Vec4f& f_Vertex2)
{
    osg::Vec4f const l_Temp = f_Vertex1;
    f_Vertex1               = f_Vertex2;
    f_Vertex2               = l_Temp;
}

vfc::float32_t TrajectoryHelper::smoothstep(
    const vfc::float32_t f_out_0,
    const vfc::float32_t f_out_1,
    const vfc::float32_t f_in_0,
    const vfc::float32_t f_in_1,
    vfc::float32_t       f_in)
{
  // Clamp f_in
  if (f_in_0 < f_in_1)
  {
    f_in = (f_in < f_in_0) ? f_in_0 : ((f_in > f_in_1) ? f_in_1 : f_in);
  }
  else
  {
    f_in = (f_in < f_in_1) ? f_in_1 : ((f_in > f_in_0) ? f_in_0 : f_in);
  }

  const vfc::float32_t lc_inputRange  = f_in_1  - f_in_0;
  const vfc::float32_t lc_outputRange = f_out_1 - f_out_0;

  // Convert from input scale to normalized scale
  const vfc::float32_t l_in_normalized = (f_in - f_in_0) / lc_inputRange;

  // Smoothstep
  const vfc::float32_t l_out_normalized = l_in_normalized * l_in_normalized * (3.0f - 2.0f * l_in_normalized);

  // Convert it to the output scale
  const vfc::float32_t l_out = l_out_normalized * lc_outputRange + f_out_0;

  return l_out;
}

osg::Vec4ub TrajectoryHelper::smoothstep_Vec4ub(
    const osg::Vec4ub&   f_out_0,
    const osg::Vec4ub&   f_out_1,
    const vfc::float32_t f_in_0,
    const vfc::float32_t f_in_1,
    vfc::float32_t       f_in)
{
  // Clamp f_in
  if (f_in_0 < f_in_1)
  {
    f_in = (f_in < f_in_0) ? f_in_0 : ((f_in > f_in_1) ? f_in_1 : f_in);
  }
  else
  {
    f_in = (f_in < f_in_1) ? f_in_1 : ((f_in > f_in_0) ? f_in_0 : f_in);
  }

  const vfc::float32_t lc_inputRange  = f_in_1  - f_in_0;
  osg::Vec4f l_outputRange(
    static_cast<vfc::float32_t>(f_out_1.r()) - static_cast<vfc::float32_t>(f_out_0.r()),
    static_cast<vfc::float32_t>(f_out_1.g()) - static_cast<vfc::float32_t>(f_out_0.g()),
    static_cast<vfc::float32_t>(f_out_1.b()) - static_cast<vfc::float32_t>(f_out_0.b()),
    static_cast<vfc::float32_t>(f_out_1.a()) - static_cast<vfc::float32_t>(f_out_0.a()));

  // Convert from input scale to normalized scale
  const vfc::float32_t l_in_normalized = (f_in - f_in_0) / lc_inputRange;

  // Smoothstep
  const vfc::float32_t l_out_normalized = l_in_normalized * l_in_normalized * (3.0f - 2.0f * l_in_normalized);

  // Convert it to the output scale
  osg::Vec4f l_out(
    l_out_normalized * l_outputRange.r() + static_cast<vfc::float32_t>(f_out_0.r()),
    l_out_normalized * l_outputRange.g() + static_cast<vfc::float32_t>(f_out_0.g()),
    l_out_normalized * l_outputRange.b() + static_cast<vfc::float32_t>(f_out_0.b()),
    l_out_normalized * l_outputRange.a() + static_cast<vfc::float32_t>(f_out_0.a()));

  osg::Vec4f l_out_result(l_out.r() + 0.1f, l_out.g() + 0.1f, l_out.b() + 0.1f, l_out.a() + 0.1f);

  osg::Vec4ub l_result(
    static_cast<vfc::uint8_t>(l_out_result.r()),  //PRQA S 3016
    static_cast<vfc::uint8_t>(l_out_result.g()),  //PRQA S 3016
    static_cast<vfc::uint8_t>(l_out_result.b()),  //PRQA S 3016
    static_cast<vfc::uint8_t>(l_out_result.a())); //PRQA S 3016

  return l_result;
}


// See description in the function body.
osg::Vec4ub TrajectoryHelper::smoothstep_GGX_Vec4ub(
    const osg::Vec4ub&   f_out_0,
    const osg::Vec4ub&   f_out_1,
    const vfc::float32_t f_in_0,
    const vfc::float32_t f_in_1,
    vfc::float32_t       f_in)
{
  // Set the following 3 values based on the excel sheet: \cc\assets\trajectory\helper\doc\ShadowFunc.xlsx
  constexpr vfc::float32_t r = 1.7f;
  constexpr vfc::float32_t c1 = 0.03811136f;
  constexpr vfc::float32_t c2 = 2.620444641f;
  // This function puts the peak of the curve close to f_in_0
  // and the tail of the curve close to f_in_1.

  constexpr vfc::float32_t r4 = r * r * r * r;

  // Clamp f_in
  if (f_in_0 < f_in_1)
  {
    f_in = (f_in < f_in_0) ? f_in_0 : ((f_in > f_in_1) ? f_in_1 : f_in);
  }
  else
  {
    f_in = (f_in < f_in_1) ? f_in_1 : ((f_in > f_in_0) ? f_in_0 : f_in);
  }

  const vfc::float32_t lc_inputRange = f_in_1 - f_in_0;
  const vfc::float32_t l_in_normalized = (f_in - f_in_0) / lc_inputRange;

  vfc::float32_t temp_1 = l_in_normalized * l_in_normalized * (r4 - 1.0f) + 1.0f;
  temp_1 = r4 / (static_cast<vfc::float32_t> (osg::PI) * temp_1 * temp_1);
  temp_1 -= c1;
  const vfc::float32_t l_out_normalized = temp_1 / c2;

  osg::Vec4f l_outputRange(
    static_cast<vfc::float32_t>(f_out_0.r()) - static_cast<vfc::float32_t>(f_out_1.r()),
    static_cast<vfc::float32_t>(f_out_0.g()) - static_cast<vfc::float32_t>(f_out_1.g()),
    static_cast<vfc::float32_t>(f_out_0.b()) - static_cast<vfc::float32_t>(f_out_1.b()),
    static_cast<vfc::float32_t>(f_out_0.a()) - static_cast<vfc::float32_t>(f_out_1.a()));

  // Convert it to the output scale
  osg::Vec4f l_out(
    l_out_normalized * l_outputRange.r() + static_cast<vfc::float32_t>(f_out_1.r()),
    l_out_normalized * l_outputRange.g() + static_cast<vfc::float32_t>(f_out_1.g()),
    l_out_normalized * l_outputRange.b() + static_cast<vfc::float32_t>(f_out_1.b()),
    l_out_normalized * l_outputRange.a() + static_cast<vfc::float32_t>(f_out_1.a()));

  osg::Vec4f l_out_result(l_out.r() + 0.1f, l_out.g() + 0.1f, l_out.b() + 0.1f, l_out.a() + 0.1f);

  osg::Vec4ub l_result(
    static_cast<vfc::uint8_t>(l_out_result.r()),  //PRQA S 3016
    static_cast<vfc::uint8_t>(l_out_result.g()),  //PRQA S 3016
    static_cast<vfc::uint8_t>(l_out_result.b()),  //PRQA S 3016
    static_cast<vfc::uint8_t>(l_out_result.a()));  //PRQA S 3016

  return l_result;
}

osg::Vec4ub TrajectoryHelper::smoothstep_Vec4fIn_Vec4ubOut(
    const osg::Vec4f&    f_out_0,
    const osg::Vec4f&    f_out_1,
    const vfc::float32_t f_in_0,
    const vfc::float32_t f_in_1,
    vfc::float32_t       f_in)
{
  // Clamp f_in
  if (f_in_0 < f_in_1)
  {
    f_in = (f_in < f_in_0) ? f_in_0 : ((f_in > f_in_1) ? f_in_1 : f_in);
  }
  else
  {
    f_in = (f_in < f_in_1) ? f_in_1 : ((f_in > f_in_0) ? f_in_0 : f_in);
  }

  const vfc::float32_t lc_inputRange = f_in_1 - f_in_0;
  osg::Vec4f const     l_outputRange = f_out_1 - f_out_0;

  // Convert from input scale to normalized scale
  const vfc::float32_t l_in_normalized = (f_in - f_in_0) / lc_inputRange;

  // Smoothstep
  const vfc::float32_t l_out_normalized = l_in_normalized * l_in_normalized * (3.0f - 2.0f * l_in_normalized);

  // Convert it to the output scale
  osg::Vec4f l_out = l_outputRange * l_out_normalized + f_out_0;

  osg::Vec4f l_out_result(
    l_out.r() * 255.0f + 0.1f,
    l_out.g() * 255.0f + 0.1f,
    l_out.b() * 255.0f + 0.1f,
    l_out.a() * 255.0f + 0.1f);

  osg::Vec4ub l_result(
    static_cast<vfc::uint8_t>(l_out_result.r()),  //PRQA S 3016
    static_cast<vfc::uint8_t>(l_out_result.g()),  //PRQA S 3016
    static_cast<vfc::uint8_t>(l_out_result.b()),  //PRQA S 3016
    static_cast<vfc::uint8_t>(l_out_result.a()));  //PRQA S 3016

  return l_result;
}


osg::Vec4f TrajectoryHelper::smoothstep_Vec4f(const osg::Vec4f& f_out_0,
                                    const osg::Vec4f& f_out_1,
                                    const vfc::float32_t f_in_0,
                                    const vfc::float32_t f_in_1,
                                          vfc::float32_t f_in)
{
  // Clamp f_in
  if (f_in_0 < f_in_1)
  {
    f_in = (f_in < f_in_0) ? f_in_0 : ((f_in > f_in_1) ? f_in_1 : f_in);
  }
  else
  {
    f_in = (f_in < f_in_1) ? f_in_1 : ((f_in > f_in_0) ? f_in_0 : f_in);
  }

  const vfc::float32_t lc_inputRange  = f_in_1  - f_in_0;
  osg::Vec4f l_outputRange(
    f_out_1.r() - f_out_0.r(),
    f_out_1.g() - f_out_0.g(),
    f_out_1.b() - f_out_0.b(),
    f_out_1.a() - f_out_0.a());

  // Convert from input scale to normalized scale
  const vfc::float32_t l_in_normalized = (f_in - f_in_0) / lc_inputRange;

  // Smoothstep
  const vfc::float32_t l_out_normalized = l_in_normalized * l_in_normalized * (3.0f - 2.0f * l_in_normalized);

  // Convert it to the output scale
  osg::Vec4f l_out(
    l_out_normalized * l_outputRange.r() + f_out_0.r(),
    l_out_normalized * l_outputRange.g() + f_out_0.g(),
    l_out_normalized * l_outputRange.b() + f_out_0.b(),
    l_out_normalized * l_outputRange.a() + f_out_0.a());

  return l_out;
}

bool TrajectoryHelper::isInfinity(vfc::float32_t f_value)
{
  if ((std::numeric_limits<vfc::float32_t>::lowest() <= f_value) &&
      (f_value <= std::numeric_limits<vfc::float32_t>::max()))
  {
    return false;
  }
  else
  {
    return true;
  }
}


RotationFunctor::RotationFunctor(vfc::float32_t f_angle)
  : m_cos(std::cos(f_angle))
  , m_sin(std::sin(f_angle))
{
}


void RotationFunctor::rotate(osg::Vec2f& f_PointToRotate) const
{
    TrajectoryHelper::Rotate2DPoint(f_PointToRotate, m_cos, m_sin);
}


void RotationFunctor::rotate(osg::Vec2f& f_PointToRotate, const osg::Vec2f& f_PointToRotateAround) const
{
    TrajectoryHelper::Rotate2DPoint(f_PointToRotate, f_PointToRotateAround, m_cos, m_sin);
}


Comparator::Comparator() = default;

Comparator::~Comparator() = default;

bool A_LessThan_B::Compare(vfc::float32_t f_A, vfc::float32_t f_B) const  //PRQA S 1724
{
  return f_A < f_B;
}

bool A_GreaterThan_B::Compare(vfc::float32_t f_A, vfc::float32_t f_B) const  //PRQA S 1724
{
  return f_A > f_B;
}


} // namespace helper
} // namespace trajectory
} // namespace assets
} // namespace cc
