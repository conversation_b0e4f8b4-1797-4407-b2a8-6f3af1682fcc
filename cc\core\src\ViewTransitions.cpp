//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/core/inc/ViewTransitions.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/src/ViewModeStateTransitionManager.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h" // PRQA S 1060
#include "cc/util/logging/inc/LoggingContexts.h" // PRQA S 1060

#include "pc/svs/daddy/inc/BaseDaddyTypes.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/vehicle/inc/MechanicalData.h" // PRQA S 1060

namespace cc
{
namespace core
{
using cc::util::logging::g_animationContext;


CustomSkipAnimation::CustomSkipAnimation(pc::animation::Animation* f_a)
    : m_animation(f_a) // PRQA S 2323 // PRQA S 4052
{
}

bool CustomSkipAnimation::supportsCancellation() const
{
    return true;
}

// currently not supported by lib-vis
// bool CustomSkipAnimation::cancelFollowingAnimation() const
// {
//   return true;
// }

void CustomSkipAnimation::onEnd(bool f_canceled)
{
    if (true == f_canceled)
    {
        if (!m_animation->hasFinished())
        {
            m_animation->update(std::numeric_limits<float>::max()); // PRQA S 3803 // PRQA S 2446
        }
    }
}

float CustomSkipAnimation::getFixedDuration() const // PRQA S 2446
{
    return m_animation->getFixedDuration();
}

bool CustomSkipAnimation::hasFixedDuration() const
{
    return m_animation->hasFixedDuration();
}

void CustomSkipAnimation::reset()
{
    Animation::reset();
    m_animation->reset();
}

bool CustomSkipAnimation::onUpdate(float f_animationTime) // PRQA S 2446
{
    return m_animation->update(f_animationTime);
}

} // namespace core
} // namespace cc
