//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/util/common/inc/CommonUtil.hpp"
#include "pc/generic/rapidjson/document.h"

namespace cc
{
namespace util
{
namespace common
{

// SGHZA - Android
void mapViewIDFromSghza(
    const vfc::int32_t               f_BYDViewID,
    EScreenID&                       f_BoschScreenId,
    cc::daddy::SideViewEnableStatus& f_sideEnableSts,
    cc::daddy::TopViewEnableStatus&  f_topEnableSts)
{
    f_BoschScreenId = static_cast<EScreenID>(f_BYDViewID);
    f_sideEnableSts = cc::daddy::SIDEVIEW_DISABLE;
    f_topEnableSts  = cc::daddy::TOPVIEW_DISABLE;
    return;
    // EScreenID_NO_CHANGE = 0,
    // EScreenID_SINGLE_FRONT_NORMAL = 8,
    // EScreenID_SINGLE_STB = 9,
    // EScreenID_SINGLE_FRONT_JUNCTION = 10,
    // EScreenID_PARK_ASSIST_FRONT = 12,
    // EScreenID_SINGLE_REAR_NORMAL_ON_ROAD = 14,
    // EScreenID_SINGLE_REAR_JUNCTION = 15,
    // EScreenID_PARK_ASSIST_REAR = 19,
    // EScreenID_SINGLE_LEFT = 26,
    // EScreenID_SINGLE_RIGHT = 27,
    // EScreenID_WHEEL_FRONT_DUAL = 31,
    // EScreenID_PERSPECTIVE_FL = 32,
    // EScreenID_PERSPECTIVE_FR = 33,
    // EScreenID_PERSPECTIVE_RL = 34,
    // EScreenID_PERSPECTIVE_RR = 35,
    // EScreenID_PERSPECTIVE_PRE = 38,
    // EScreenID_PERSPECTIVE_PLE = 39,
    // EScreenID_WHEEL_REAR_DUAL = 45,
    // EScreenID_FULL_SCREEN = 47,
    // EScreenID_QUAD_RAW = 50,
    // EScreenID_PERSPECTIVE_PFR = 52,
    // EScreenID_PERSPECTIVE_PRI = 53,
    // EScreenID_HORI_PARKING_FRONT = 74,
    // EScreenID_HORI_PARKING_REAR = 75,
    // EScreenID_PLANVIEW_WITH_SEPARATOR = 78,
    // EScreenID_REMOTE_SCREEN_FRONT = 79,
    // EScreenID_REMOTE_SCREEN_REAR = 80,
    // EScreenID_REMOTE_SCREEN_LEFT = 81,
    // EScreenID_REMOTE_SCREEN_RIGHT = 82,
    // EScreenID_LEFTRIGHTVIEW_FRONT_VIEW = 84,  // htebu
    // EScreenID_LEFTRIGHTVIEW_REAR_VIEW = 85,  // htebu
    // EScreenID_MODEL_F_VIEW_ENLARGEMENT = 111,
    // EScreenID_MODEL_B_VIEW_ENLARGEMENT = 112,
    // EScreenID_FULLSCREEN = 113,
    // EScreenID_FULLSCREEN_FRONT_ENLARGE = 114,
    // EScreenID_FULLSCREEN_REAR_ENLARGE = 115,
    // EScreenID_FRONT_BUMPER = 116,
    // EScreenID_REAR_BUMPER = 117,
    // EScreenID_ULTRA_WIDE_SURROUND_VIEW = 118,
    // EScreenID_PLANETARY_VIEW = 119,  //TO BE CHECKED
    // EScreenID_SUPER_TRANSPARENT_VIEW = 120,
    // EScreenID_IMAGE_IN_IMAGE_LEFT = 123,
    // EScreenID_IMAGE_IN_IMAGE_RIGHT = 124,
    // EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT = 125,
    // EScreenID_IMAGE_IN_IMAGE_PLANVIEW = 126,
    // EScreenID_AVM3D_VIEW_FULL_SCREEN_2D_VOT = 127,  //TO BE CHECKED
    // EscreenID_FULL_SCREEN_3D = 128,  //TO BE CHECKED
}

// SGHL - linux
static void mapViewIDFromSghlNew(
    const vfc::int32_t               f_BYDViewID,
    EScreenID&                       f_BoschScreenId,
    cc::daddy::SideViewEnableStatus& f_sideEnableSts,
    cc::daddy::TopViewEnableStatus&  /*f_topEnableSts*/)
{
    switch (f_BYDViewID)
    {
    case -1:
    {
        f_BoschScreenId = EScreenID_NO_CHANGE;
        break;
    }
    case 0: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_RIGHT = 0,      // 0: 2D front view + right view
    {
        f_BoschScreenId = EScreenID_SINGLE_FRONT_NORMAL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 1: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_LEFT,           // 1: 2D front view + left view
    {
        f_BoschScreenId = EScreenID_SINGLE_FRONT_NORMAL_LEFT;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 2: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_RIGHT,           // 2: 2D rear view + right view
    {
        f_BoschScreenId = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 3: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_LEFT,            // 3: 2D rear view + left view
    {
        f_BoschScreenId = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD_LEFT;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 8: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_RIGHT,      // 8: 3D left rear + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 9: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_RIGHT,     // 9: 3D right rear + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 10: // AVM3D_VIEW_FULL_SCREEN_3D_REAR_RIGHT,           // 10: 3D rear + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 11: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_RIGHT,           // 11: 3D left + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PLE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 12: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_RIGHT,          // 12: 3D right + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRI;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 13: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_RIGHT,     // 13: 3D left front + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 14: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_RIGHT,    // 14: 3D right front + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 15: // AVM3D_VIEW_FULL_SCREEN_3D_FRONT_RIGHT,          // 15: 3D front + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PFR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 16: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_LEFT,       // 16: 3D left rear + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RL_LEFT;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 17: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_LEFT,      // 17: 3D right rear + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RR_LEFT;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 18: // AVM3D_VIEW_FULL_SCREEN_3D_REAR_LEFT,            // 18: 3D rear + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRE_LEFT;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 19: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_LEFT,            // 19: 3D left + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PLE_LEFT;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 20: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_LEFT,           // 20: 3D right + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRI_LEFT;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 21: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_LEFT,      // 21: 3D left front + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FL_LEFT;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 22: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_LEFT,     // 22: 3D right front + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FR_LEFT;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 23: // AVM3D_VIEW_FULL_SCREEN_3D_FRONT_LEFT,           // 23: 3D front + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PFR_LEFT;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 24: // AVM3D_VIEW_FULL_SCREEN_3D_TOP_VIEW_FRONT,    // 24: Front bumper top view zoom
    {
        f_BoschScreenId = EScreenID_FULLSCREEN_FRONT_ENLARGE;
        break;
    }
    case 25: // AVM3D_VIEW_FULL_SCREEN_3D_TOP_VIEW_REAR,     // 25: Rear bumper top view zoom
    {
        f_BoschScreenId = EScreenID_FULLSCREEN_REAR_ENLARGE;
        break;
    }
    case 26: // AVM3D_VIEW_FULL_SCREEN_2D_TOP_VIEW_FULL,     // 26: 2D top view full screen
    {
        f_BoschScreenId = EScreenID_FULLSCREEN;
        break;
    }
    case 27: // AVM3D_VIEW_FULL_SCREEN_ASTEROID_FULL,        // 27: Asteroid view
    {
        f_BoschScreenId = EScreenID_PLANETARY_VIEW;
        break;
    }
    case 28: // AVM3D_VIEW_FULL_SCREEN_3D_FULL,              // 28: 3D full screen
    {
        f_BoschScreenId = EscreenID_FULL_SCREEN_3D;
        break;
    }
    case 29: // AVM3D_VIEW_FULL_SCREEN_3D_WIDE_FULL,         // 29: 3D ultra wide angle
    {
        f_BoschScreenId = EScreenID_ULTRA_WIDE_SURROUND_VIEW;
        break;
    }
    case 30: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_HUBVIEW_FRONT,    // 30: AVM2D panoramic front dual view + front -- dual view
    {
        f_BoschScreenId = EScreenID_WHEEL_FRONT_DUAL;
        break;
    }
    case 31: // AVM3D_VIEW_FULL_SCREEN_2D_FULL_HUBVIEW_FRONT,     // 31: AVM2D panoramic full dual view + front -- dual view
    {
        f_BoschScreenId = EScreenID_PLANVIEW_WITH_SEPARATOR;
        break;
    }
    case 32: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_HUBVIEW_FRONT,     // 32: AVM2D panoramic rear dual view + front -- dual view
    {
        f_BoschScreenId = EScreenID_WHEEL_REAR_DUAL;
        break;
    }
    case 33: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_HUBVIEW_REAR,     // 33: AVM2D panoramic front dual view + rear -- dual view
    {
        f_BoschScreenId = EScreenID_WHEEL_FRONT_DUAL_REAR;
        break;
    }
    case 34: // AVM3D_VIEW_FULL_SCREEN_2D_FULL_HUBVIEW_REAR,      // 34: AVM2D panoramic full dual view + rear -- dual view
    {
        f_BoschScreenId = EScreenID_PLANVIEW_WITH_SEPARATOR_REAR;
        break;
    }
    case 35: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_HUBVIEW_REAR,      // 35: AVM2D panoramic rear dual view + rear -- dual view
    {
        f_BoschScreenId = EScreenID_WHEEL_REAR_DUAL_REAR;
        break;
    }
    case 36: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_FRONT_HUBVIEW,         // 36: AVM2D panoramic front+frontdual view --three-view
    {
        f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL;
        break;
    }
    case 37: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_FULL_HUBVIEW,          // 37: AVM2D panoramic front+fulldual view --three-view
    {
        f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_BOTH;
        break;
    }
    case 38: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_REAR_HUBVIEW,          // 38: AVM2D panoramic front+reardual view --three-view
    {
        f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL;
        break;
    }
    case 39: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_FRONT_HUBVIEW,          // 39: AVM2D panoramic rear+frontdual view --three-view
    {
        f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL_REAR;
        break;
    }
    case 40: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_FULL_HUBVIEW,           // 40: AVM2D panoramic rear+full dual view --three-view
    {
        f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_BOTH_REAR;
        break;
    }
    case 41: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_REAR_HUBVIEW,           // 41: AVM2D panoramic rear+reardual view --three-view
    {
        f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL_REAR;
        break;
    }
    case 48: // AVM3D_VIEW_FLOAT_WINDOW_TOP,                 // 48: Picture-in-picture top view
    {
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_PLANVIEW;
        break;
    }
    case 49: // AVM3D_VIEW_FLOAT_WINDOW_LEFT,                // 49: Picture-in-picture left view
    {
        // f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT;
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT;
        break;
    }
    case 50: // AVM3D_VIEW_FLOAT_WINDOW_RIGHT,               // 50: Picture-in-picture right view
    {
        // f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_RIGHT;
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT;
        break;
    }
    case 51: // AVM3D_VIEW_FLOAT_WINDOW_BOARD_LEFT,          // 51: Picture-in-picture left viewdashboard
    {
        // f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT;
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT;
        break;
    }
    case 52: // AVM3D_VIEW_FLOAT_WINDOW_BOARD_RIGHT,         // 52: Picture-in-picture right viewdashboard
    {
        // f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_RIGHT;
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT;
        break;
    }
    case 53: // AVM3D_VIEW_FLOAT_WINDOW_TOP_ZOOMIN_FRONT,    // 53: Picture-in-picture top viewfrontwheel trackzoom
    {
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE;
        break;
    }
    case 54: // AVM3D_VIEW_FLOAT_WINDOW_TOP_ZOOMIN_REAR,     // 54: Picture-in-picture top viewrearwheel trackzoom
    {
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE;
        break;
    }

    case 55: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_ZOOM_LEFT,     // 55: Front bumper top view zoom+left view
    {
        f_BoschScreenId = EScreenID_FRONT_BUMPER;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 56: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_ZOOM_RIGHT,    // 56: Front bumper top view zoom+right view
    {
        f_BoschScreenId = EScreenID_FRONT_BUMPER_RIGHT;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 57: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_ZOOM_LEFT,      // 57: Rear bumper top view zoom+left view
    {
        f_BoschScreenId = EScreenID_REAR_BUMPER;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 58: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_ZOOM_RIGHT,     // 58: Rear bumper top view zoom+right view
    {
        f_BoschScreenId = EScreenID_REAR_BUMPER_RIGHT;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }

    case 59: // AVM3D_VIEW_REMOTEVISION_FRONT,    // 59: Remote vision front view
    {
        f_BoschScreenId = EScreenID_REMOTE_SCREEN_FRONT;
        break;
    }
    case 60: // AVM3D_VIEW_REMOTEVISION_REAR,     // 60: Remote vision rear view
    {
        f_BoschScreenId = EScreenID_REMOTE_SCREEN_REAR;
        break;
    }
    case 61: // AVM3D_VIEW_REMOTEVISION_LEFT,     // 61: Remote vision left view
    {
        f_BoschScreenId = EScreenID_REMOTE_SCREEN_LEFT;
        break;
    }
    case 62: // AVM3D_VIEW_REMOTEVISION_RIGHT,    // 62: Remote vision right view
    {
        f_BoschScreenId = EScreenID_REMOTE_SCREEN_RIGHT;
        break;
    }

    case 63: // AVM3D_VIEW_FULL_SCREEN_2D_VOT,    // 63: 2DVOT(vehicle on turntable)view
    {
        f_BoschScreenId = EScreenID_AVM3D_VIEW_FULL_SCREEN_2D_VOT;
        break;
    }
    case 64: // AVM3D_VIEW_APA_AERIAL,    // 64: APA bird's eye view
    {
        f_BoschScreenId = EScreenID_PARKING_TOP;
        break;
    }
    case 65: // AVM3D_VIEW_APA_SELECT,    // 65: APA self-select
    {
        f_BoschScreenId = EScreenID_PARKING_FREEPARKING;
        break;
    }
    case 66: // AVM3D_VIEW_APA_FRONT,     // 66: APA view - front view
    {
        f_BoschScreenId = EScreenID_PARKING_FRONT;
        break;
    }
    case 67: // AVM3D_VIEW_APA_BACK,      // 67: APA view - rear view
    {
        f_BoschScreenId = EScreenID_PARKING_REAR;
        break;
    }
    case 82: // AVM3D_VIEW_SETTING,  // 82: Settings page
    {
        // do nothing
        break;
    }

    default:
    {
        break;
    }
    }
    return;
}

// SGHL - linux
void mapViewIDFromSghl(
    const vfc::int32_t               f_BYDViewID,
    EScreenID&                       f_BoschScreenId,
    cc::daddy::SideViewEnableStatus& f_sideEnableSts,
    cc::daddy::TopViewEnableStatus&  f_topEnableSts)
{
    switch (f_BYDViewID)
    {
    case -1:
    {
        f_BoschScreenId = EScreenID_NO_CHANGE;
        break;
    }
    case 0: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_RIGHT = 0,      // 0: 2D front view + right view
    {
        f_BoschScreenId = EScreenID_SINGLE_FRONT_NORMAL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 1: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_LEFT,           // 1: 2D front view + left view
    {
        f_BoschScreenId = EScreenID_SINGLE_FRONT_NORMAL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 2: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_RIGHT,           // 2: 2D rear view + right view
    {
        f_BoschScreenId = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 3: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_LEFT,            // 3: 2D rear view + left view
    {
        f_BoschScreenId = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 4: // AVM3D_VIEW_FULL_SCREEN_2D_WIDE_FRONT_LEFT,      // 4: 2D front wide angle + left view
    {
        f_BoschScreenId = EScreenID_SINGLE_FRONT_JUNCTION;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 5: // AVM3D_VIEW_FULL_SCREEN_2D_WIDE_FRONT_RIGHT,     // 5: 2D front wide angle + right view
    {
        f_BoschScreenId = EScreenID_SINGLE_FRONT_JUNCTION;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 6: // AVM3D_VIEW_FULL_SCREEN_2D_WIDE_REAR_LEFT,       // 6: 2D rear wide angle + left view
    {
        f_BoschScreenId = EScreenID_SINGLE_REAR_JUNCTION;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 7: // AVM3D_VIEW_FULL_SCREEN_2D_WIDE_REAR_RIGHT,      // 7: 2D rear wide angle + right view
    {
        f_BoschScreenId = EScreenID_SINGLE_REAR_JUNCTION;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 8: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_RIGHT,      // 8: 3D left rear + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 9: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_RIGHT,     // 9: 3D right rear + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 10: // AVM3D_VIEW_FULL_SCREEN_3D_REAR_RIGHT,           // 10: 3D rear + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 11: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_RIGHT,           // 11: 3D left + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PLE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 12: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_RIGHT,          // 12: 3D right + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRI;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 13: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_RIGHT,     // 13: 3D left front + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 14: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_RIGHT,    // 14: 3D right front + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 15: // AVM3D_VIEW_FULL_SCREEN_3D_FRONT_RIGHT,          // 15: 3D front + right view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PFR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 16: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_LEFT,       // 16: 3D left rear + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 17: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_LEFT,      // 17: 3D right rear + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 18: // AVM3D_VIEW_FULL_SCREEN_3D_REAR_LEFT,            // 18: 3D rear + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 19: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_LEFT,            // 19: 3D left + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PLE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 20: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_LEFT,           // 20: 3D right + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRI;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 21: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_LEFT,      // 21: 3D left front + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 22: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_LEFT,     // 22: 3D right front + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 23: // AVM3D_VIEW_FULL_SCREEN_3D_FRONT_LEFT,           // 23: 3D front + left view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PFR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }

    case 24: // AVM3D_VIEW_FULL_SCREEN_3D_TOP_VIEW_FRONT,    // 24: Front bumper top view zoom
    {
        f_BoschScreenId = EScreenID_FULLSCREEN_FRONT_ENLARGE;
        break;
    }
    case 25: // AVM3D_VIEW_FULL_SCREEN_3D_TOP_VIEW_REAR,     // 25: Rear bumper top view zoom
    {
        f_BoschScreenId = EScreenID_FULLSCREEN_REAR_ENLARGE;
        break;
    }
    case 26: // AVM3D_VIEW_FULL_SCREEN_2D_TOP_VIEW_FULL,     // 26: 2D top view full screen
    {
        f_BoschScreenId = EScreenID_FULLSCREEN;
        break;
    }
    case 27: // AVM3D_VIEW_FULL_SCREEN_ASTEROID_FULL,        // 27: Asteroid view
    {
        f_BoschScreenId = EScreenID_PLANETARY_VIEW;
        break;
    }
    case 28: // AVM3D_VIEW_FULL_SCREEN_3D_FULL,              // 28: 3D full screen
    {
        f_BoschScreenId = EscreenID_FULL_SCREEN_3D;
        break;
    }
    case 29: // AVM3D_VIEW_FULL_SCREEN_3D_WIDE_FULL,         // 29: 3D ultra wide angle
    {
        f_BoschScreenId = EScreenID_ULTRA_WIDE_SURROUND_VIEW;
        break;
    }
    case 30: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_HUBVIEW_FRONT,    // 30: AVM2D panoramic frontdual view+front --dual view
    {
        f_BoschScreenId = EScreenID_WHEEL_FRONT_DUAL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_FRONT_ENABLE;
        break;
    }
    case 31: // AVM3D_VIEW_FULL_SCREEN_2D_FULL_HUBVIEW_FRONT,     // 31: AVM2D panoramic full dual view+front --dual view
    {
        f_BoschScreenId = EScreenID_PLANVIEW_WITH_SEPARATOR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_FRONT_ENABLE;
        break;
    }
    case 32: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_HUBVIEW_FRONT,     // 32: AVM2D panoramic reardual view+front --dual view
    {
        f_BoschScreenId = EScreenID_WHEEL_REAR_DUAL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_FRONT_ENABLE;
        break;
    }
    case 33: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_HUBVIEW_REAR,     // 33: AVM2D panoramic frontdual view+rear --dual view
    {
        f_BoschScreenId = EScreenID_WHEEL_FRONT_DUAL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_REAR_ENABLE;
        break;
    }
    case 34: // AVM3D_VIEW_FULL_SCREEN_2D_FULL_HUBVIEW_REAR,      // 34: AVM2D panoramic full dual view+rear --dual view
    {
        f_BoschScreenId = EScreenID_PLANVIEW_WITH_SEPARATOR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_REAR_ENABLE;
        break;
    }
    case 35: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_HUBVIEW_REAR,      // 35: AVM2D panoramic reardual view+rear --dual view
    {
        f_BoschScreenId = EScreenID_WHEEL_REAR_DUAL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_REAR_ENABLE;
        break;
    }

    case 36: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_FRONT_HUBVIEW,         // 36: AVM2D panoramic front+frontdual view --three-view
    {
        f_BoschScreenId = EScreenID_SINGLE_FRONT_NORMAL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 37: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_FULL_HUBVIEW,          // 37: AVM2D panoramic front+full dual view --three-view
    {
        // f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_BOTH;
        // f_topEnableSts = cc::daddy::TOPVIEW_FRONT_ENABLE;
        break;
    }
    case 38: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_REAR_HUBVIEW,          // 38: AVM2D panoramic front+reardual view --three-view
    {
        f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL;
        f_topEnableSts  = cc::daddy::TOPVIEW_FRONT_ENABLE;
        // f_BoschScreenId = EScreenID_SINGLE_FRONT_NORMAL;  // TODO
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE;
        break;
    }
    case 39: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_FRONT_HUBVIEW,          // 39: AVM2D panoramic rear+frontdual view --three-view
    {
        f_BoschScreenId = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 40: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_FULL_HUBVIEW,           // 40: AVM2D panoramic rear+full dual view --three-view
    {
        // f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_BOTH;
        // f_topEnableSts = cc::daddy::TOPVIEW_REAR_ENABLE;
        break;
    }
    case 41: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_REAR_HUBVIEW,           // 41: AVM2D panoramic rear+reardual view --three-view
    {
        f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL;
        f_topEnableSts  = cc::daddy::TOPVIEW_REAR_ENABLE;
        // f_BoschScreenId = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;  // TODO
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE;
        break;
    }
    case 42: // AVM3D_VIEW_FULL_SCREEN_2D_WIDE_FRONT_FRONT_HUBVIEW,    // 42: AVM2D panoramic front wide+frontdual view --three-view
    {
        f_BoschScreenId = EScreenID_SINGLE_FRONT_JUNCTION;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 43: // AVM3D_VIEW_FULL_SCREEN_2D_WIDE_FRONT_FULL_HUBVIEW,     // 43: AVM2D panoramic front wide+full dual view --three-view
    {
        // f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_BOTH;
        // f_topEnableSts = cc::daddy::TOPVIEW_FRONTJUNCTION_ENABLE;
        break;
    }
    case 44: // AVM3D_VIEW_FULL_SCREEN_2D_WIDE_FRONT_REAR_HUBVIEW,     // 44: AVM2D panoramic front wide+reardual view --three-view
    {
        f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL;
        f_topEnableSts  = cc::daddy::TOPVIEW_FRONTJUNCTION_ENABLE;
        // f_BoschScreenId = EScreenID_SINGLE_FRONT_JUNCTION;  // TODO
        // f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE;
        break;
    }
    case 45: // AVM3D_VIEW_FULL_SCREEN_2D_WIDE_REAR_FRONT_HUBVIEW,     // 45: AVM2D panoramic rear wide+frontdual view --three-view
    {
        f_BoschScreenId = EScreenID_SINGLE_REAR_JUNCTION;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 46: // AVM3D_VIEW_FULL_SCREEN_2D_WIDE_REAR_FULL_HUBVIEW,      // 46: AVM2D panoramic rear wide+full dual view --three-view
    {
        // f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_BOTH;
        // f_topEnableSts = cc::daddy::TOPVIEW_REARJUNCTION_ENABLE;
        break;
    }
    case 47: // AVM3D_VIEW_FULL_SCREEN_2D_WIDE_REAR_REAR_HUBVIEW,      // 47: AVM2D panoramic rear wide+reardual view --three-view
    {
        f_BoschScreenId = EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL;
        f_topEnableSts  = cc::daddy::TOPVIEW_REARJUNCTION_ENABLE;
        // f_BoschScreenId = EScreenID_SINGLE_REAR_JUNCTION;  // TODO
        // f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }

    case 48: // AVM3D_VIEW_FLOAT_WINDOW_TOP,                 // 48: Picture-in-picture top view
    {
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_PLANVIEW;
        break;
    }
    case 49: // AVM3D_VIEW_FLOAT_WINDOW_LEFT,                // 49: Picture-in-picture left view
    {
        // f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT;
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT;
        break;
    }
    case 50: // AVM3D_VIEW_FLOAT_WINDOW_RIGHT,               // 50: Picture-in-picture right view
    {
        // f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_RIGHT;
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT;
        break;
    }
    case 51: // AVM3D_VIEW_FLOAT_WINDOW_BOARD_LEFT,          // 51: Picture-in-picture left view dashboard
    {
        // f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT;
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT;
        break;
    }
    case 52: // AVM3D_VIEW_FLOAT_WINDOW_BOARD_RIGHT,         // 52: Picture-in-picture right view dashboard
    {
        // f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_RIGHT;
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT;
        break;
    }
    case 53: // AVM3D_VIEW_FLOAT_WINDOW_TOP_ZOOMIN_FRONT,    // 53: Picture-in-picture top view front wheel track zoom
    {
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE;
        break;
    }
    case 54: // AVM3D_VIEW_FLOAT_WINDOW_TOP_ZOOMIN_REAR,     // 54: Picture-in-picture top view rear wheel track zoom
    {
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE;
        break;
    }

    case 55: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_ZOOM_LEFT,     // 55: Front bumper top view zoom + left view
    {
        f_BoschScreenId = EScreenID_FRONT_BUMPER;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 56: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_ZOOM_RIGHT,    // 56: Front bumper top view zoom + right view
    {
        f_BoschScreenId = EScreenID_FRONT_BUMPER;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }
    case 57: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_ZOOM_LEFT,      // 57: Rear bumper top view zoom + left view
    {
        f_BoschScreenId = EScreenID_REAR_BUMPER;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 58: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_ZOOM_RIGHT,     // 58: Rear bumper top view zoom + right view
    {
        f_BoschScreenId = EScreenID_REAR_BUMPER;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }

    case 59: // AVM3D_VIEW_REMOTEVISION_FRONT,    // 59: Remote vision front view
    {
        f_BoschScreenId = EScreenID_REMOTE_SCREEN_FRONT;
        break;
    }
    case 60: // AVM3D_VIEW_REMOTEVISION_REAR,     // 60: Remote vision rear view
    {
        f_BoschScreenId = EScreenID_REMOTE_SCREEN_REAR;
        break;
    }
    case 61: // AVM3D_VIEW_REMOTEVISION_LEFT,     // 61: Remote vision left view
    {
        f_BoschScreenId = EScreenID_REMOTE_SCREEN_LEFT;
        break;
    }
    case 62: // AVM3D_VIEW_REMOTEVISION_RIGHT,    // 62: Remote vision right view
    {
        f_BoschScreenId = EScreenID_REMOTE_SCREEN_RIGHT;
        break;
    }

    case 63: // AVM3D_VIEW_FULL_SCREEN_2D_VOT,    // 63: 2DVOT (vehicle on turntable) view
    {
        f_BoschScreenId = EScreenID_AVM3D_VIEW_FULL_SCREEN_2D_VOT;
        break;
    }
    case 64: // AVM3D_VIEW_APA_AERIAL,    // 64: APA bird's eye view
    {
        f_BoschScreenId = EScreenID_PARKING_TOP;
        f_topEnableSts  = cc::daddy::TOPVIEW_DISABLE;
        break;
    }
    case 65: // AVM3D_VIEW_APA_SELECT,    // 65: APA self-select
    {
        f_BoschScreenId = EScreenID_PARKING_FREEPARKING;
        f_topEnableSts  = cc::daddy::TOPVIEW_DISABLE;
        break;
    }
    case 66: // AVM3D_VIEW_APA_FRONT,     // 66: APA view - front view
    {
        f_BoschScreenId = EScreenID_PARKING_FRONT;
        f_topEnableSts  = cc::daddy::PARK_FRONT_VIEW;
        break;
    }
    case 67: // AVM3D_VIEW_APA_BACK,      // 67: APA view - rear view
    {
        f_BoschScreenId = EScreenID_PARKING_REAR;
        f_topEnableSts  = cc::daddy::PARK_REAR_VIEW;
        break;
    }

    // 3D panoramic view
    case 68: // AVM3D_VIEW_FULL_SCREEN_3D_ANGLE_LEFT,     // 68: 3D keep last angle + left view
    {
        f_BoschScreenId = EScreenID_NORMAL3D_KEEP;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_ENABLE;
        break;
    }
    case 69: // AVM3D_VIEW_FULL_SCREEN_3D_ANGLE_RIGHT,    // 69: 3D keep last angle + right view
    {
        f_BoschScreenId = EScreenID_NORMAL3D_KEEP;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_ENABLE;
        break;
    }

    // Hub view (three-view)
    case 70: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_FRONT_HUBVIEW,      // 70: 3D left rear + front hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 71: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_FRONT_HUBVIEW,     // 71: 3D right rear + front hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 72: // AVM3D_VIEW_FULL_SCREEN_3D_REAR_FRONT_HUBVIEW,           // 72: 3D rear + front hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 73: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_HUBVIEW,           // 73: 3D left + front hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PLE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 74: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_HUBVIEW,          // 74: 3D right + front hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRI;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 75: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_FRONT_HUBVIEW,     // 75: 3D left front + front hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 76: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_FRONT_HUBVIEW,    // 76: 3D right front + front hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 77: // AVM3D_VIEW_FULL_SCREEN_3D_FRONT_FRONT_HUBVIEW,          // 77: 3D front + front hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PFR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 78: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_REAR_HUBVIEW,       // 78: 3D ... + rear hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE;
        break;
    }
    case 79: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_REAR_HUBVIEW,      // 79: 3D ... + rear hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE;
        break;
    }
    case 80: // AVM3D_VIEW_FULL_SCREEN_3D_REAR_REAR_HUBVIEW,            // 80: 3D ... + rear hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE;
        break;
    }
    case 81: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_HUBVIEW,            // 81: 3D ... + rear hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PLE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE;
        break;
    }
    case 82: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_HUBVIEW,           // 82: 3D ... + rear hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRI;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE;
        break;
    }
    case 83: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_REAR_HUBVIEW,      // 83: 3D ... + rear hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE;
        break;
    }
    case 84: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_REAR_HUBVIEW,     // 84: 3D ... + rear hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE;
        break;
    }
    case 85: // AVM3D_VIEW_FULL_SCREEN_3D_FRONT_REAR_HUBVIEW,           // 85: 3D ... + rear hub -- three-view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PFR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE;
        break;
    }
    case 86: // AVM3D_VIEW_FULL_SCREEN_3D_ANGLE_FRONT_HUBVIEW,          // 86: 3D ... + front hub -- three-view
    {
        f_BoschScreenId = EScreenID_NORMAL3D_KEEP;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 87: // AVM3D_VIEW_FULL_SCREEN_3D_ANGLE_REAR_HUBVIEW,           // 87: 3D ... + rear hub -- three-view
    {
        f_BoschScreenId = EScreenID_NORMAL3D_KEEP;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE;
        break;
    }

    case 88: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_ZOOM_FRONT_HUBVIEW,    // 88: Front bumper top view zoom+fronthub --three-view
    {
        f_BoschScreenId = EScreenID_FRONT_BUMPER;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE;
        break;
    }
    case 89: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_ZOOM_REAR_HUBVIEW,      // 89: Rear bumper top view zoom+rearhub --three-view
    {
        f_BoschScreenId = EScreenID_REAR_BUMPER;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE; // TODO
        break;
    }
    case 90: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_RIGHT_REAR,          // 90:2Dfront view+rightrear view
    {
        f_BoschScreenId = EScreenID_SINGLE_FRONT_NORMAL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE;
        break;
    }
    case 91: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_LEFT_REAR,           // 91:2Dfront view+leftrear view
    {
        f_BoschScreenId = EScreenID_SINGLE_FRONT_NORMAL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE;
        break;
    }
    case 92: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_RIGHT_REAR,           // 92:2Drear view+rightrear view
    {
        f_BoschScreenId = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE;
        break;
    }
    case 93: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_LEFT_REAR,            // 93:2Drear view+leftrear view
    {
        f_BoschScreenId = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE;
        break;
    }
    case 98: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_RIGHT_REAR,      // 98:3Dleftrear+rightrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE;
        break;
    }
    case 99: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_RIGHT_REAR,     // 99:3Drightrear+rightrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE;
        break;
    }
    case 100: // AVM3D_VIEW_FULL_SCREEN_3D_REAR_RIGHT_REAR,           // 100:3Drear+rightrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE;
        break;
    }
    case 101: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_RIGHT_REAR,           // 101:3Dleft+rightrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PLE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE;
        break;
    }
    case 102: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_RIGHT_REAR,          // 102:3Dright+rightrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRI;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE;
        break;
    }
    case 103: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_RIGHT_REAR,     // 103:3Dleftfront+rightrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE;
        break;
    }
    case 104: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_RIGHT_REAR,    // 104:3Drightfront+rightrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE;
        break;
    }
    case 105: // AVM3D_VIEW_FULL_SCREEN_3D_FRONT_RIGHT_REAR,          // 105:3Dfront+rightrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PFR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE;
        break;
    }
    case 106: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_LEFT_REAR,       // 106:3Dleftrear+leftrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE;
        break;
    }
    case 107: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_LEFT_REAR,      // 107:3Drightrear+leftrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_RR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE;
        break;
    }
    case 108: // AVM3D_VIEW_FULL_SCREEN_3D_REAR_LEFT_REAR,            // 108:3Drear+leftrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE;
        break;
    }
    case 109: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_LEFT_REAR,            // 109:3Dleft+leftrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PLE;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE;
        break;
    }
    case 110: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_LEFT_REAR,           // 110:3Dright+leftrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRI;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE;
        break;
    }
    case 111: // AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_LEFT_REAR,      // 111:3Dleftfront+leftrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FL;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE;
        break;
    }
    case 112: // AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_LEFT_REAR,     // 112:3Drightfront+leftrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_FR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE;
        break;
    }
    case 113: // AVM3D_VIEW_FULL_SCREEN_3D_FRONT_LEFT_REAR,           // 113:3Dfront+leftrear view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PFR;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE;
        break;
    }
    case 114: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_ZOOM_LEFT_REAR,     // 114: Front bumper top view zoom+leftrear view
    {
        f_BoschScreenId = EScreenID_FRONT_BUMPER;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE;
        break;
    }
    case 115: // AVM3D_VIEW_FULL_SCREEN_2D_FRONT_ZOOM_RIGHT_REAR,    // 115: Front bumper top view zoom+rightrear view
    {
        f_BoschScreenId = EScreenID_FRONT_BUMPER;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE;
        break;
    }
    case 116: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_ZOOM_LEFT_REAR,      // 116: Rear bumper top view zoom+leftrear view
    {
        f_BoschScreenId = EScreenID_REAR_BUMPER;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE;
        break;
    }
    case 117: // AVM3D_VIEW_FULL_SCREEN_2D_REAR_ZOOM_RIGHT_REAR,     // 117: Rear bumper top view zoom+rightrear view
    {
        f_BoschScreenId = EScreenID_REAR_BUMPER;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE;
        break;
    }
    case 118: // AVM3D_VIEW_FULL_SCREEN_3D_ANGLE_LEFT_REAR,     // 118:3Dkeep lastrearangle+leftrear view
    {
        f_BoschScreenId = EScreenID_NORMAL3D_KEEP;
        f_sideEnableSts = cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE;
        break;
    }
    case 119: // AVM3D_VIEW_FULL_SCREEN_3D_ANGLE_RIGHT_REAR,    // 119:3Dkeep last rearangle+rightrear view
    {
        f_BoschScreenId = EScreenID_NORMAL3D_KEEP;
        f_sideEnableSts = cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE;
        break;
    }
    case 120: // AVM3D_VIEW_FULL_SCREEN_2D_VOT,    // 120: 2D lateral movement view
    {
        f_BoschScreenId = AVM3D_VIEW_FULL_SCREEN_2D_VSW;
        break;
    }
    case 121: // 121: APA view - narrow road function - front dual hub
    {
        f_BoschScreenId = EScreenID_APA_FRONT_HUBVIEW;
        break;
    }
    case 122: // AVM3D_VIEW_FULL_SCREEN_2D_CRAB_FRONT, //122: Crab walk view front
    {
        f_BoschScreenId = EScreenID_CARB_TURN;
        break;
    }
    case 123: // AVM3D_VIEW_FULL_SCREEN_2D_CRAB_REAR, //123: Crab walk view rear
    {
        f_BoschScreenId = EScreenID_CARB_TURN;
        break;
    }
    case 124: // AVM3D_VIEW_SETTING,     // 124: Settings interface
    {
        break;
    }
    default:
    {
        break;
    }
    }
    return;
}

// ST2 - linux
void mapViewIDFromST2(
    const vfc::int32_t               f_BYDViewID,
    EScreenID&                       f_BoschScreenId,
    cc::daddy::SideViewEnableStatus& /*f_sideEnableSts */,
    cc::daddy::TopViewEnableStatus&  /*f_topEnableSts*/)
{
    switch (f_BYDViewID)
    {
    case -1:
    {
        f_BoschScreenId = EScreenID_NO_CHANGE;
        break;
    }
    case 10000: // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_FRONT = 10000, // 10000: AVM2D panoramic front view
    {
        f_BoschScreenId = EScreenID_SINGLE_FRONT_NORMAL;
        break;
    }
    case 10001: // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_REAR, // 10001: AVM2D panoramic rear view
    {
        f_BoschScreenId = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;
        break;
    }
    case 10002: // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_LEFT_FRONT, // 10002: AVM2D panoramic - left front view
    {
        f_BoschScreenId = EScreenID_SINGLE_LEFT;
        break;
    }
    case 10003: // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_LEFT_REAR, // 10003: AVM2D panoramic - left rear view
    {
        f_BoschScreenId = EScreenID_SINGLE_LEFT_REAR;
        break;
    }
    case 10004: // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_RIGHT_FRONT, // 10004: AVM2D panoramic - right front view
    {
        f_BoschScreenId = EScreenID_SINGLE_RIGHT;
        break;
    }
    case 10005: // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_RIGHT_REAR, // 10005: AVM2D panoramic - right rear view
    {
        f_BoschScreenId = EScreenID_SINGLE_RIGHT_REAR;
        break;
    }
    case 10006:  // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_FRONT_HUBVIEW_FRONT, // 10006: AVM2D panoramic - front + front dual view --- three-view
    {
        f_BoschScreenId = EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL;
        break;
    }
    case 10007:  // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_FRONT_HUBVIEW_REAR, // 10007: AVM2D panoramic - front + rear dual view --- three-view
    {
        f_BoschScreenId = EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL;
        break;
    }
    case 10008:  // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_REAR_HUBVIEW_FRONT, // 10008: AVM2D panoramic - rear + front dual view --- three-view
    {
        f_BoschScreenId = EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL;
        break;
    }
    case 10009:  // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_REAR_HUBVIEW_REAR, // 10009: AVM2D panoramic - rear + rear dual view --- three-view
    {
        f_BoschScreenId = EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL;
        break;
    }
    case 10010: // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_HUBVIEW_FRONT, // 10010: AVM2Dpanoramic-frontdual view---dual view
    {
        f_BoschScreenId = EScreenID_WHEEL_FRONT_DUAL_ENLARGED;
        break;
    }
    case 10011: // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_HUBVIEW_FULL, // 10011: AVM2Dpanoramic-full dual view---dual view
    {
        f_BoschScreenId = EScreenID_PLANVIEW_WITH_SEPARATOR_ENLARGED;
        break;
    }
    case 10012: // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_HUBVIEW_REAR, // 10012: AVM2Dpanoramic-reardual view---dual view
    {
        f_BoschScreenId = EScreenID_WHEEL_REAR_DUAL_ENLARGED;
        break;
    }
    case 10013: // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_FRONT_ZOOM_LEFT_REAR, // 10013: AVM2Dpanoramic-front bumpertop viewzoom
    {
        f_BoschScreenId = EScreenID_FRONT_BUMPER;
        break;
    }
    case 10014: // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_FRONT_ZOOM_RIGHT_REAR, // 10014: AVM2Dpanoramic-rear bumpertop viewzoom
    {
        f_BoschScreenId = EScreenID_REAR_BUMPER;
        break;
    }
    case 10015: // AVM_VIEW_LANDSCAPE_FULL_SCREEN_3D, // 10015: AVM3D view
    {
        f_BoschScreenId = EScreenID_PERSPECTIVE_PRE;
        break;
    }
    case 10016: // AVM_VIEW_LANDSCAPE_FLOAT_WINDOW_TOP, // 10016: Picture-in-picture top view
    {
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_PLANVIEW;
        break;
    }
    case 10017: // AVM_VIEW_LANDSCAPE_FLOAT_WINDOW_LEFT_FRONT, // 10017: Picture-in-picture left front view
    {
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT;
        break;
    }
    case 10018: // AVM_VIEW_LANDSCAPE_FLOAT_WINDOW_LEFT_REAR, // 10018: Picture-in-picture left rear view
    {
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT_REAR;
        break;
    }
    case 10019: // AVM_VIEW_LANDSCAPE_FLOAT_WINDOW_RIGHT_FRONT, // 10019: Picture-in-picture right front view
    {
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT;
        break;
    }
    case 10020: // AVM_VIEW_LANDSCAPE_FLOAT_WINDOW_RIGHT_REAR, // 10020: Picture-in-picture right rear view
    {
        f_BoschScreenId = EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT_REAR;
        break;
    }
    case 10021: // AVM_VIEW_LANDSCAPE_APA_DRIVING_AERIAL, // 10021: APA bird's eye view --- APA driving
    case 10022: // AVM_VIEW_LANDSCAPE_APA_DRIVING_FRONT, // 10022: APA view - front view --- APA driving
    case 10023: // AVM_VIEW_LANDSCAPE_APA_DRIVING_BACK, // 10023: APA view - rear view --- APA driving
    {
        break;
    }
    case 10024: // AVM_VIEW_LANDSCAPE_APA_PARK_FRONT_HUBVIEW_FRONT, // 10024: APA single view - front + front dual view --- APA parking
    {
        f_BoschScreenId = EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL_WITHOUT_PLAN;
        break;
    }
    case 10025: // AVM_VIEW_LANDSCAPE_APA_PARK_FRONT_HUBVIEW_REAR, // 10025: APA single view - front + rear dual view --- APA parking
    {
        f_BoschScreenId = EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL_WITHOUT_PLAN;
        break;
    }
    case 10026: // AVM_VIEW_LANDSCAPE_APA_PARK_REAR_HUBVIEW_FRONT, // 10026: APA single view - rear + front dual view --- APA parking
    {
        f_BoschScreenId = EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL_WITHOUT_PLAN;
        break;
    }
    case 10027: // AVM_VIEW_LANDSCAPE_APA_PARK_REAR_HUBVIEW_REAR, // 10027: APA single view - rear + rear dual view --- APA parking
    {
        f_BoschScreenId = EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL_WITHOUT_PLAN;
        break;
    }
    case 10028: // AVM_VIEW_LANDSCAPE_APA_PARK_AERIAL, // 10028: APA top view --- APA parking
    {
        f_BoschScreenId = EScreenID_PARKING_TOP;
        break;
    }
    case 10029: // AVM_VIEW_LANDSCAPE_REMOTEVISION_FRONT, // 10029: Remote vision front view
    {
        f_BoschScreenId = EScreenID_REMOTE_SCREEN_FRONT;
        break;
    }
    case 10030: // AVM_VIEW_LANDSCAPE_REMOTEVISION_REAR, // 10030: Remote vision rear view
    {
        f_BoschScreenId = EScreenID_REMOTE_SCREEN_REAR;
        break;
    }
    case 10031: // AVM_VIEW_LANDSCAPE_REMOTEVISION_LEFT, // 10031: Remote vision left view
    {
        f_BoschScreenId = EScreenID_REMOTE_SCREEN_LEFT;
        break;
    }
    case 10032: // AVM_VIEW_LANDSCAPE_REMOTEVISION_RIGHT, // 10032: Remote vision right view
    {
        f_BoschScreenId = EScreenID_REMOTE_SCREEN_RIGHT;
        break;
    }
    case 10033: // AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_ROOF_FRONT, // 10033: AVM2D height measurement
    default:
    {
        break;
    }
    }
    return;
}

void parseApaJsonData(const std::string& jsonStr, cc::target::common::ParkhmiToSvs& f_apaData)
{
    rapidjson::Document document;
    document.Parse(jsonStr.c_str());

    // init object info
    for (int i = 0; i < cc::target::common::NUM_OBJECT; i++)
    {
        f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_objectId = 0;
        f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_typeInfo =
            cc::target::common::ObjectType::NoInformation;
    }

    // init slot info
    for (int i = 0; i < cc::target::common::NUM_SLOT; i++)
    {
        f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcID = 0;
        f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcType =
            cc::target::common::EParkingSpaceType::NoInformation;
    }

    // init guideline info
    for (int i = 0; i < cc::target::common::NUM_GUIDELINE_POINT; i++)
    {
        f_apaData.m_parkingRealTimeData.m_GuideLineInfo[i].m_trackPointID = 0;
    }

    if (document["NewParkingRealTimeDataNotify"]["ParkingObjectInfoNotify"]["ObjectArray"].IsArray())
    {
        const rapidjson::Value& objectArray =
            document["NewParkingRealTimeDataNotify"]["ParkingObjectInfoNotify"]["ObjectArray"];

        for (rapidjson::SizeType i = 0; i < objectArray.Size(); ++i)
        {
            const rapidjson::Value& obj = objectArray[i];

            if (obj.HasMember("ObjectID_i") && obj["ObjectID_i"].IsInt())
            {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_objectId =
                    obj["ObjectID_i"].GetInt();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_objectId " <<
                //             static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_objectId) << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("shape_height_i") && obj["shape_height_i"].IsFloat())
            {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_shapeHeight =
                    obj["shape_height_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_shapeHeight " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_shapeHeight << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("shape_length_i") && obj["shape_length_i"].IsFloat())
            {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_shapeLength =
                    obj["shape_length_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_shapeLength " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_shapeLength << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("shape_width_i") && obj["shape_width_i"].IsFloat())
            {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_shapeWidth =
                    obj["shape_width_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_shapeWidth " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_shapeWidth << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("position_z_i") && obj["position_z_i"].IsDouble())
            {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_z =
                    static_cast<vfc::float32_t>(obj["position_z_i"].GetDouble());
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_position.m_z " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_z << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("position_x_i") && obj["position_x_i"].IsDouble())
            {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_x =
                    static_cast<vfc::float32_t>(obj["position_x_i"].GetDouble());
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i <<
                // "].m_position.m_x " <<
                // f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_x << XLOG_ENDL;
                // // PRQA S 4060
            }

            if (obj.HasMember("position_y_i") && obj["position_y_i"].IsDouble())
            {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_y =
                    static_cast<vfc::float32_t>(obj["position_y_i"].GetDouble());
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i <<
                // "].m_position.m_y " <<
                // f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_y << XLOG_ENDL;
                // // PRQA S 4060
            }

            if (obj.HasMember("position_z_i") && obj["position_z_i"].IsDouble())
            {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_z =
                    obj["position_z_i"].GetDouble();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_position.m_z " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_z << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("Heading_i") && obj["Heading_i"].IsDouble())
            {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_heading =
                    obj["Heading_i"].GetDouble();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_heading " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_heading << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("TypeInfo") && obj["TypeInfo"].IsInt())
            {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_typeInfo =
                    static_cast<cc::target::common::ObjectType>(obj["TypeInfo"].GetInt());
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_typeInfo " <<
                //             static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_typeInfo) << XLOG_ENDL; // PRQA S 4060
            }
        }
    }

    if (document["NewParkingRealTimeDataNotify"]["ParkingSlotInfoNotify"].IsArray())
    {
        const rapidjson::Value& slotArray = document["NewParkingRealTimeDataNotify"]["ParkingSlotInfoNotify"];

        for (rapidjson::SizeType i = 0; i < slotArray.Size(); ++i)
        {
            const rapidjson::Value& obj = slotArray[i];

            if (obj.HasMember("ParkngSpcID_i") && obj["ParkngSpcID_i"].IsInt())
            {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcID = obj["ParkngSpcID_i"].GetInt();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_parkngSpcID " <<
                //             static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcID) << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("ParkngSpcSts") && obj["ParkngSpcSts"].IsInt())
            {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcSts =
                    static_cast<cc::target::common::EParkingSpaceStatus>(obj["ParkngSpcSts"].GetInt());
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_parkngSpcSts " <<
                //             static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcSts) << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("ParkngSpcCode_i") && obj["ParkngSpcCode_i"].IsInt())
            {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcCode = obj["ParkngSpcCode_i"].GetInt();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_parkngSpcCode " <<
                //             static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcCode) << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("parkngSpcType") && obj["parkngSpcType"].IsInt())
            {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcType =
                    static_cast<cc::target::common::EParkingSpaceType>(obj["parkngSpcType"].GetInt());
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_parkngSpcType " <<
                //             static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcType) << XLOG_ENDL; // PRQA S 4060
            }

            // if (obj.HasMember("ParkngSpcNum") && obj["ParkngSpcNum"].IsInt()) {
            //     f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcNum =
            //     obj["ParkngSpcNum"].GetInt();
            //     XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i <<
            //     "].m_parkngSpcNum " <<
            //                 static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcNum)
            //                 << XLOG_ENDL; // PRQA S 4060
            // }

            // Corner 1
            if (obj.HasMember("x1_i") && obj["x1_i"].IsFloat())
            {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner1.m_x = obj["x1_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner1.m_x " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner1.m_x << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("y1_i") && obj["y1_i"].IsFloat())
            {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner1.m_y = obj["y1_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner1.m_y " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner1.m_y << XLOG_ENDL; // PRQA S 4060
            }

            // Corner 2
            if (obj.HasMember("x2_i") && obj["x2_i"].IsFloat())
            {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner2.m_x = obj["x2_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner2.m_x " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner2.m_x << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("y2_i") && obj["y2_i"].IsFloat())
            {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner2.m_y = obj["y2_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner2.m_y " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner2.m_y << XLOG_ENDL; // PRQA S 4060
            }

            // Corner 3
            if (obj.HasMember("x3_i") && obj["x3_i"].IsFloat())
            {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner3.m_x = obj["x3_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner3.m_x " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner3.m_x << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("y3_i") && obj["y3_i"].IsFloat())
            {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner3.m_y = obj["y3_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner3.m_y " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner3.m_y << XLOG_ENDL; // PRQA S 4060
            }

            // Corner 4
            if (obj.HasMember("x4_i") && obj["x4_i"].IsFloat())
            {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner4.m_x = obj["x4_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner4.m_x " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner4.m_x << XLOG_ENDL; // PRQA S 4060
            }

            if (obj.HasMember("y4_i") && obj["y4_i"].IsFloat())
            {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner4.m_y = obj["y4_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner4.m_y " <<
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner4.m_y << XLOG_ENDL; // PRQA S 4060
            }
        }
    }
    return;
}

} // namespace common
} // namespace util
} // namespace cc
