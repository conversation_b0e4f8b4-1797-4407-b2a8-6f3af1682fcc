//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_AUGMENTEDVIEWTRANSITION_AUGMENTEDVIEWTRANSITION_H
#define CC_ASSETS_AUGMENTEDVIEWTRANSITION_AUGMENTEDVIEWTRANSITION_H

#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/target/common/inc/linux_svs_interface.hpp"

// #include "cc/virtcam/inc/CameraPositions.h"

// #include "pc/svs/core/inc/Asset.h"
#include "pc/svs/animation/inc/Animation.h"

#include <osg/Array>
#include <osg/Group>
#include <osg/Stencil>
#include <osg/NodeCallback>
#include <osg/NodeVisitor>

#include <cassert>
#include <cmath>
#include <memory>

namespace osg
{
class Geode;
class Geometry;
class Node;
class Switch;
class Texture2D;
} // namespace osg

namespace pc
{
namespace animation
{
class Animation;
} // namespace animation

namespace core
{
class Framework;
class View;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace augmentedview
{

class TransitionWaveAnimation;
class PeriodicScanAnimation;

class AugmentedViewTransition : public osg::MatrixTransform
{
public:
  AugmentedViewTransition(pc::core::Framework* f_framework, cc::target::common::EThemeTypeHU f_themeType);

  void registerWithView(pc::core::View* f_view);

  //! This returns an animation instance which can be used to trigger the transition animation
  pc::animation::Animation* createTransitionToAugmentedViewAnimation(bool f_instantSwitch);
  pc::animation::Animation* createTransitionFromAugmentedViewAnimation(bool f_instantSwitch);

  osg::ref_ptr<osg::Node> m_backgroundQuad;

protected:
  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
  //! Wrapper around the transition manager which can be submitted to an animation queue
  class AugmentedViewTransitionAnimation : public pc::animation::Animation
  {
  public:
    AugmentedViewTransitionAnimation(bool f_toAugmented, bool f_instantSwitch, AugmentedViewTransition* f_augmentedViewTransition);

    virtual vfc::float32_t getFixedDuration() const;
    virtual bool hasFixedDuration() const;

    virtual void onBegin();
    virtual bool onUpdate(vfc::float32_t f_animationTime);
  private:
    bool m_toAugmented;
    bool m_instantSwitch;
    osg::ref_ptr<AugmentedViewTransition> m_augmentedViewTransition;
  };

  bool isTransitioning() const;
  bool isTransitioningToAugmentedView() const;
  bool isTransitioningFromAugmentedView() const;
  bool isInAugmentedView() const;

  //! Starts transition from textured to augmented view
  void transitionToAugmentedView();
  void switchToAugmentedView();

  //! Starts transition from augmented to TV3D view
  void transitionFromAugmentedView();
  void switchFromAugmentedView();

  //! Called when a transition to augmented state has concluded
  void finalizeAugmentedViewState();
  //! Called when a transition to textured state has concluded
  void finalizeSurroundViewState();


  osg::Vec2f getWavefrontCenter() const;
  // bool isBasePlateCovered(vfc::float32_t f_radius) const;
  // bool getBasePlateExtent(osg::Vec2f& f_min, osg::Vec2f& f_max) const;

  void updateTransitionState(vfc::float32_t f_currentTime);

  //! Called before animation from/to augmented view begins
  void prepareAnimationState();

  //! Handles animation if augmented view has been reached, e.g. the regularily emitted radar waves
  void updateAugmentedViewAnimationState(vfc::float32_t f_currentTime);


  /**
   * Creates a ring which represents the alpha-blended/additive blended forefront of the
   * radar wave, and an inner geometry which just renders a black disk.
   */
  bool initializeAugmentedViewTransition(const vfc::uint32_t f_tesselationCount);

  /**
   * Sets up animation of radar waves in the final augmented view.
   * The animation emits radar waves from the scene center with given frequency
   * (where a frequency of one corresponds to one radar wave emission every "animationDuration" seconds)
   */
  bool prepareAugmentedViewAnimation();

  //! Creates full-view quad
  static osg::Node* createBackgroundNode();

  void setupCommonState(osg::StateSet* f_stateSet);

  //! State setup for wavefront disk rendered using additive blending, for overlay of radar wave
  static void setupAdditiveWavefrontState(osg::StateSet* f_stateSet);

  static void setupInnerDiskState(osg::StateSet* f_stateSet);

  void fadeCamerasIn();
  void fadeCamerasOut();

  pc::core::Framework* m_framework;
  //!< True if augmented view is active
  bool m_inAugmentedViewState;
  //! allow the scan wave animation depending on speed and system state
  bool m_scanWaveAllowed;
  vfc::uint32_t m_activeAnimation;

  // Double-buffered animation queues
  std::unique_ptr<TransitionWaveAnimation> m_animation[2];
  std::unique_ptr<PeriodicScanAnimation> m_scanAnimation;

  osg::ref_ptr<osg::Switch> m_scanWavesSwitch;
  std::vector<osg::ref_ptr<osg::Geode> > m_scanWaveGeodes;

  osg::ref_ptr<osg::Uniform> m_waveRadiusUniform, m_waveAlphaUniform;

  osg::ref_ptr<osg::Geometry> m_radarFrontGeometry; //!< Ring geometry used for rendering of a radar wave
  osg::ref_ptr<osg::Geode> m_additiveWavefrontGeode, m_innerRingGeode;
  osg::ref_ptr<osg::Switch> m_rootNode;
  osg::ref_ptr<osg::Texture2D> m_waveTexture; //!< Optional 1D-texture for wave
  osg::ref_ptr<pc::core::View> m_view;
  osg::ref_ptr<osg::StateSet> m_streetOverlayStateSet;

  cc::target::common::EThemeTypeHU m_themeType;
  vfc::float32_t m_scanWaveMaxRadius;         //!< Max. radius of final expansion
  vfc::float32_t m_scanWaveWidth;             //!< The "width" of each scan-wave (in meters)

  vfc::uint32_t m_modifiedCount = ~0u;
};

} // namespace augmentedview
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_AUGMENTEDVIEWTRANSITION_AUGMENTEDVIEWTRANSITION_H

