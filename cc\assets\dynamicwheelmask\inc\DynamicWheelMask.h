/// @copyright (C) 2025 Robert <PERSON>.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef CC_ASSETS_DYNAMICWHEELMASK_H
#define CC_ASSETS_DYNAMICWHEELMASK_H

#include "vfc/core/vfc_types.hpp"
#include "pc/svs/core/inc/Asset.h"
#include "pc/svs/core/inc/Framework.h"

namespace cc
{
namespace assets
{

/// @brief Responsibilities: Cull: Can push single or two phase StateSet on DynWheelMaskGeode. Call update.
class DynWheelMaskCallback : public osg::NodeCallback
{
public:
    /// @brief Ctor
    DynWheelMaskCallback();

    void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;
    void setSingleStateSet(osg::StateSet* f_stateSet);
    void setTwoStateSets(osg::StateSet* f_stateSet1, osg::StateSet* f_stateSet2);

private:
    ~DynWheelMaskCallback() override = default;

    osg::ref_ptr<osg::StateSet> m_stateSet1;
    osg::ref_ptr<osg::StateSet> m_stateSet2;
};

/// @brief Changes will take effect during runtime.
class DynamicWheelMaskSettings : public pc::util::coding::ISerializable
{
public:
    DynamicWheelMaskSettings()
        : m_show{true}
        , m_high_thres_deg{15.0f}
        , m_low_thres_deg{10.0f}
        , m_min_x{2.6f}
        // , m_max_x{4.1f}
        , m_min_y{0.71f}
        , m_width_small{1.15f}
        , m_width_big{1.3f}
        , m_length_small{3.0f}
        , m_length_big{4.5f}
    {
    }

    SERIALIZABLE(DynamicWheelMaskSettings)
    {
        ADD_BOOL_MEMBER(show);
        ADD_FLOAT_MEMBER(high_thres_deg);
        ADD_FLOAT_MEMBER(low_thres_deg);
        ADD_FLOAT_MEMBER(min_x);
        // ADD_FLOAT_MEMBER(max_x);
        ADD_FLOAT_MEMBER(min_y);
        ADD_FLOAT_MEMBER(width_small);
        ADD_FLOAT_MEMBER(width_big);
        ADD_FLOAT_MEMBER(length_small);
        ADD_FLOAT_MEMBER(length_big);
    }

    /// @brief Toggle flag to show/hide the asset.
    bool m_show;

    /// @brief Above this threshold the animation small->big will be triggered.
    vfc::float32_t m_high_thres_deg;

    /// @brief Below this threshold the animation big->small will be triggered.
    vfc::float32_t m_low_thres_deg;

    /// @brief distance on `x` axis from: `Dyn70K (0,0) -> mask's back side`.
    vfc::float32_t m_min_x;

    // /// @brief distance on `x` axis from: `Dyn70K (0,0) -> mask's front side`.
    // vfc::float32_t m_max_x;

    /// @brief distance on `y` axis from: `width / 2.0 -> Dyn70k (0,0)`.
    vfc::float32_t m_min_y;

    /// @brief one side's width for mask in `SMALL` state. Half of combined width.
    vfc::float32_t m_width_small;

    /// @brief one side's width for mask in `BIG` state. Half of combined width.
    vfc::float32_t m_width_big;

    vfc::float32_t m_length_small;

    vfc::float32_t m_length_big;
};

/// @brief Shape changing logic defined here.
class DynWheelMaskGeode : public osg::Geode
{
public:
    struct Params
    {
        vfc::float32_t m_y;
        vfc::float32_t m_yTarget;
        vfc::float32_t m_yCurrent;
        vfc::float32_t m_ySmall;
        vfc::float32_t m_yBig;

        vfc::float32_t m_x;
        vfc::float32_t m_xTarget;
        vfc::float32_t m_xCurrent;
        vfc::float32_t m_xSmall;
        vfc::float32_t m_xBig;
    };


    enum class EShape
    {
        INIT        = 0,
        BIG_SHAPE   = 1,
        SMALL_SHAPE = 2
    };

    /// @brief Number of vertices defining lower-corner curvatures.
    static constexpr vfc::int32_t NUMBER_CURVE_VERTICES{5};

    /// @brief Number of vertices excluding the curved corners.
    static constexpr vfc::int32_t NUMBER_BASIC_VERTICES{10};

    /// @brief CTor
    /// @param[in] f_framework dependency: pointer to Framework to access wheel angle.
    /// @param[in] f_geometry dependency: geometry to as drawable.
    DynWheelMaskGeode(pc::core::Framework* f_framework, osg::Geometry* f_geometry);

    /// @brief Update geode.
    void update();

private:
    /// @brief Updates right vertices
    /// @param[out]  f_vertices Vertex array
    void updateRightVertices(osg::Vec3Array* f_vertices);

    /// @brief Updates left vertices
    /// @param[out]  f_vertices Vertex array
    void updateLeftVertices(osg::Vec3Array* f_vertices);

    /// @brief Get steering angle.
    /// @return steering angle in degree.
    vfc::float32_t getSteeringAngle();

    /// @brief Update member variables from coding/settings.
    void updateMembersFromSettings();

    /// @brief Decide shape based on wheel angle and threshold values.
    void updateTargetShape();

    /// @brief Animate the current value to the target value with small variable steps.
    /// Jump at the end is expected.
    /// Can be improved with osg::Animation with EaseMotion
    void animateVerticesBasic();

    osg::ref_ptr<osg::Geometry> m_geometry;

    // Don't want ownership of the framework so it's a raw pointer.
    pc::core::Framework* m_framework;

    bool           m_show;
    vfc::float32_t m_xMin;
    vfc::float32_t m_xMax;
    Params        m_left;
    Params        m_right;

    /// @brief Animation speed.  Range: `[0, 1]`. Higher value means faster transition, if set to 1 the transition will
    /// happen immediately
    const vfc::float32_t m_animationSpeed = 0.5f;

    /// @brief Difference below this threshold will trigger snap at the end of animation.
    const vfc::float32_t l_targetReachedTh = 0.01f;

    /// @brief Tracks current shape: big or small
    EShape m_currentShape;

    /// @brief  Vertex update calculation only required if there is an animation is in progress.
    bool m_updateRequired;
};

/// @brief StateSet is applied here.
class DynWheelMaskAsset : public pc::core::Asset
{
public:
    DynWheelMaskAsset(const cc::core::AssetId f_assetId, pc::core::Framework* const f_framework);
    void addSingleStateSet(osg::StateSet* f_stateSet);
    void addTwoStateSets(osg::StateSet* f_stateSet1, osg::StateSet* f_stateSet2);

private:
    osg::ref_ptr<DynWheelMaskGeode>                m_dynWheelMaskGeode;
    osg::ref_ptr<cc::assets::DynWheelMaskCallback> m_dynWheelMaskCallback;
};

} // namespace assets
} // namespace cc

#endif // CC_ASSETS_DYNAMICWHEELMASK_H