/// @copyright (C) 2025 <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef CC_ASSETS_E3PARKINGOVERLAY_H
#define CC_ASSETS_E3PARKINGOVERLAY_H

#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "pc/svs/core/inc/Asset.h"

namespace cc
{
namespace assets
{

//************************************************************************************
///  \brief   ETopViewTrajPosType
///
/// Enum indicating the position of the overlay (front or rear of the vehicle)
//************************************************************************************
enum class ETopViewTrajPosType : vfc::uint32_t
{
    FRONT,
    REAR
};

//************************************************************************************
///  \brief   E3ParkingTopViewOverlayBaseClass
///
/// Geometry is defined as a circular section shape, important geometry characteristics includes the circular center
/// point, inner and outer radius etc.
//************************************************************************************
class E3ParkingTopViewOverlayBaseClass : public osg::Geode
{
public:
    E3ParkingTopViewOverlayBaseClass(const ETopViewTrajPosType f_type = ETopViewTrajPosType::FRONT)
        : m_trajectoryPosition(f_type)
    {
    }

    E3ParkingTopViewOverlayBaseClass(const E3ParkingTopViewOverlayBaseClass& other)                 = delete;
    E3ParkingTopViewOverlayBaseClass& operator=(const E3ParkingTopViewOverlayBaseClass& other)      = delete;

    /// @brief method for rendering startup behavior when the overlay is triggered
    /// @param[in] f_percentage percentage of the startup duration
    virtual void updateStartupPercentage(const vfc::float32_t f_percentage) = 0;

protected:
    virtual ~E3ParkingTopViewOverlayBaseClass() = default;

    /// @brief method for defining texture coordinates
    virtual osg::Vec2Array* createTexCoords() = 0;

    /// @brief method for defining stateset
    virtual osg::StateSet* createStateSet() = 0;

    /// @brief method for updating vertices based on the geometry characteristics
    virtual void updateVertices() = 0;

    /// @brief method for generating and adding the geometry drawable
    virtual void generateGeometry();

    /// @brief pointer to the vertices
    osg::ref_ptr<osg::Vec3Array> m_vertices;

    /// @brief center from which the radius and angles are calculated
    osg::Vec2f m_center;

    /// @brief inner radius of the geometry [m]
    vfc::float32_t m_radiusInner;

    /// @brief outer radius of the geometry [m]
    vfc::float32_t m_radiusOuter;

    /// @brief angle starting from which the overlay starts [radiant] (following DIN70K xy plane: counter clockwise is
    /// the positive direction)
    vfc::float32_t m_overlayStartingAngle;

    /// @brief points of vertices
    vfc::uint32_t m_segments;

    /// @brief the total circular distance the overlay shall be occupying(trajectory) or traveling through(arrow)
    /// [radiant]
    vfc::float32_t m_total_line_radiant;

    /// @brief bool indicating if the drawable has been initiated and added (became true in generateGeometry())
    bool m_drawableInitiated{false};

    /// @brief trajectory position type
    ETopViewTrajPosType m_trajectoryPosition;

    /// @brief rotation type
    cc::target::common::ERotationDirection m_currentRotationDirection{
        cc::target::common::ERotationDirection::LEFT_FRONT_WHEEL_ANTICLOCKWISE};
};

//************************************************************************************
///  \brief   E3ParkingTopViewTrajectory
///
/// Implementation for trajectory
//************************************************************************************
class E3ParkingTopViewTrajectory : public E3ParkingTopViewOverlayBaseClass
{
public:
    E3ParkingTopViewTrajectory(const ETopViewTrajPosType f_type = ETopViewTrajPosType::FRONT);
    E3ParkingTopViewTrajectory(const E3ParkingTopViewTrajectory& other)            = delete;
    E3ParkingTopViewTrajectory& operator=(const E3ParkingTopViewTrajectory& other) = delete;

    /// @brief method for rendering startup behavior when the overlay is triggered, cutoff uniform value will be updated
    /// @param[in] f_percentage percentage of the startup duration
    virtual void updateStartupPercentage(const vfc::float32_t f_percentage) override
    {
        if (m_cutOffUniform)
        {
            m_cutOffUniform->set(f_percentage);
        }
    };

    /// @brief method for updating the trajectory based on rotation type and circular center
    /// @param[in] f_direction rotation direction type
    /// @param[in] f_center circular center position
    void updateInputs(const cc::target::common::ERotationDirection f_direction, const osg::Vec2f& f_center);

protected:
    virtual ~E3ParkingTopViewTrajectory() = default;

    virtual osg::Vec2Array* createTexCoords() override;
    virtual osg::StateSet*  createStateSet() override;
    virtual void            updateVertices() override;

private:
    /// @brief pointer to the cutOff uniform
    osg::ref_ptr<osg::Uniform> m_cutOffUniform;

    /// @brief color of the texture
    osg::Vec4i m_color;

    /// @brief gradient position for the 1d texture generation
    osg::Vec4f m_gradientPosition;
};

//************************************************************************************
///  \brief   E3ParkingTopViewArrow
///
/// Implementation for arrow
//************************************************************************************
class E3ParkingTopViewArrow : public E3ParkingTopViewOverlayBaseClass
{
public:
    E3ParkingTopViewArrow(const ETopViewTrajPosType f_type = ETopViewTrajPosType::FRONT, vfc::uint32_t f_number = 1u);
    E3ParkingTopViewArrow(const E3ParkingTopViewArrow& other)            = delete;
    E3ParkingTopViewArrow& operator=(const E3ParkingTopViewArrow& other) = delete;

    virtual void updateStartupPercentage(const vfc::float32_t f_percentage) override;

    /// @brief method for updating the trajectory based on rotation type and circular center
    /// @param[in] f_direction rotation direction type
    /// @param[in] f_center circular center position
    void updateInputs(const cc::target::common::ERotationDirection f_direction, const osg::Vec2f& f_center);

    /// @brief method for updating the alpha uniform value in shader
    /// @param[in] f_value alpha value
    void setAlphaUniform(vfc::float32_t f_value)
    {
        m_alphaUniform->set(f_value);
    };

    /// @brief getter of the arrow index
    /// @return index of the arrow (0, 1 or 2)
    vfc::float32_t getArrowIndex() const
    {
        return m_arrowNumber;
    };

protected:
    virtual ~E3ParkingTopViewArrow() = default;

    virtual osg::Vec2Array* createTexCoords() override;
    virtual osg::StateSet*  createStateSet() override;
    virtual void            updateVertices() override;

private:
    /// @brief method for calculating the arrow position based on its index (3 arrows form a group, the middle one is
    /// the reference while the other two will be offsetting from it)
    void adjustStartingAngle();

    /// @brief pointer to the alpha uniform
    osg::ref_ptr<osg::Uniform> m_alphaUniform;

    /// @brief height of the arrow texture geometry (tangent direction)
    vfc::float32_t m_arrowHeight;

    /// @brief arrow index
    vfc::float32_t m_arrowNumber;

    /// @brief where the arrow shall be starting from during the startup [radiant]
    vfc::float32_t m_refStartingPointAngle;
};

//************************************************************************************
///  \brief   E3ParkingTopViewAsset
///
/// Asset class for all overlays
//************************************************************************************
class E3ParkingTopViewAsset : public pc::core::Asset
{
public:
    E3ParkingTopViewAsset(const cc::core::AssetId f_assetId, cc::core::CustomFramework* f_framework);
    E3ParkingTopViewAsset(const E3ParkingTopViewAsset& other)            = delete;
    E3ParkingTopViewAsset& operator=(const E3ParkingTopViewAsset& other) = delete;

    /// @brief contains the enabling/disabling logic
    virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:
    virtual ~E3ParkingTopViewAsset() = default;

private:
    /// @brief enables overlays
    void enableAll();

    /// @brief triggers startup behavior
    /// @param[in] f_percentage percentage of the startup duration
    void duringStartup(const vfc::float32_t f_percentage);

    /// @brief disables overlays
    void disableAll();

    cc::core::CustomFramework* m_customFramework;

    osg::ref_ptr<E3ParkingTopViewTrajectory>           m_whiteTrajectoryOne;
    osg::ref_ptr<E3ParkingTopViewTrajectory>           m_whiteTrajectoryTwo;
    std::array<osg::ref_ptr<E3ParkingTopViewArrow>, 3> m_frontArrows;
    std::array<osg::ref_ptr<E3ParkingTopViewArrow>, 3> m_rearArrows;

    cc::target::common::EApaStatus            m_currentApaStatus{cc::target::common::EApaStatus::PassiveStandBy};
    cc::target::common::ESpecialParkingStatus m_currentSpecialStatus{cc::target::common::ESpecialParkingStatus::OFF};
    cc::target::common::ERotationDirection    m_currentRotationDirection{
        cc::target::common::ERotationDirection::LEFT_FRONT_WHEEL_ANTICLOCKWISE};

    bool          m_startupTimerActive{false};
    bool          m_timerActive{false};
    bool          m_overlayEnabled{false};
    double        m_startTime;
    vfc::uint32_t m_modifiedCountSettings = ~0u;
    vfc::uint32_t m_modifiedCountOffset   = ~0u;
    vfc::uint32_t m_modifiedCountRadian   = ~0u;
    vfc::uint32_t m_modifiedCountCenterOffset   = ~0u;
};

} // namespace assets
} // namespace cc

#endif // CC_ASSETS_E3PARKINGOVERLAY_H