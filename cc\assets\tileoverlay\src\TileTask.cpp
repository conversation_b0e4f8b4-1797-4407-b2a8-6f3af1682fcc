//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BJEV AVAP
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  TileTask.cpp
/// @brief
//=============================================================================

#include "cc/assets/tileoverlay/inc/TileTask.h"
#include "cc/assets/tileoverlay/inc/TileSettings.h"
#include "cc/worker/core/inc/CustomTaskManager.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/util/math/inc/FloatComp.h"

#include "osg/Math"
#include <cassert>

using pc::util::logging::g_workerContext;

/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3803
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace tileoverlay
{


//!
//! ProcessingTask
//!
ProcessingTask::ProcessingTask(TileOverlay* f_tileOverlay, bool f_isVehOffset)
 : pc::worker::core::Task("TileOverlayTask") // PRQA S 4050
 , m_tileOverlay(f_tileOverlay)
 , m_flankAvailable(true)
 , m_rearAvailable(true)
 , m_staticStateReached(false)
 , m_distanceFilter(pc::util::createSpatialFilter<pc::util::FloatList>(g_tileSettings->m_distanceFilter))
 , m_zoneLayout(new cc::core::CustomZoneLayout)
 , m_ussZoneNum(static_cast<vfc::uint32_t>(cc::target::sysconf::E_ULTRASONIC_NUM_ZONES))
 , m_updateVisitor(new TileUpdateVisitor)
 , m_isVehOffset(f_isVehOffset)
{
  m_sectors = std::vector<TileSectorData>(m_ussZoneNum);

  for (vfc::uint32_t i = 0u; i < m_ussZoneNum; i++)
  {
    m_sectors[i].m_leftBorderRefPoint    = m_zoneLayout->getLeftBorderLine(i).m_innerPoint;
    m_sectors[i].m_leftBorderRefPointEnd = m_zoneLayout->getLeftBorderLine(i).m_outerPoint;
    m_sectors[i].m_leftBorderDir         = m_zoneLayout->getLeftBorderLine(i).m_direction;
    m_sectors[i].m_currentDistance       = 10.0f;
    m_sectors[i].m_currentDistanceForPosDisp = 10.0f;
    m_sectors[i].m_previousDistance      = 10.0f;
    m_sectors[i].m_ObjMovingSts          = OBJ_MOVING_UNKNOW;
  }
}


ProcessingTask::~ProcessingTask() = default;


bool ProcessingTask::onRun(pc::worker::core::TaskManager* f_taskManager)    // PRQA S 6043
{
  cc::worker::core::CustomTaskManager* const l_taskManager = f_taskManager->asCustomTaskManager();
  const pc::daddy::UltrasonicDataDaddy* const l_pDataDaddy = l_taskManager->m_customTaskUltrasonicDataReceiver.getData();

  if (nullptr != l_pDataDaddy)
  {
    const pc::vehicle::UltrasonicData& l_ultrasonicData = l_pDataDaddy->m_Data;
    for (vfc::uint32_t i = 0u; i < m_ussZoneNum; i++)
    {
      if (isLess(l_ultrasonicData[i].getDistance(), std::numeric_limits<vfc::float32_t>::max()))
      {

        m_sectors[i].m_currentDistance = l_ultrasonicData[i].getDistance();

        if ( m_isVehOffset && (m_sectors[i].m_currentDistance < g_tileSettings->m_MinDistanceThresh) )
        {
          m_sectors[i].m_currentDistance = g_tileSettings->m_MinDistanceThresh;
        }

        if ( m_isVehOffset && (m_sectors[i].m_currentDistance < g_tileSettings->m_MinDistanceThreshForPosDisp) )
        {
          m_sectors[i].m_currentDistanceForPosDisp = g_tileSettings->m_MinDistanceThreshForPosDisp;
        }
        else
        {
          m_sectors[i].m_currentDistanceForPosDisp = m_sectors[i].m_currentDistance;
        }

      }

      // specific logic for corner and side sector
      vfc::float32_t l_distanceThreshL1 = g_tileSettings->m_distanceThreshL1;
      vfc::float32_t l_distanceThreshL2 = g_tileSettings->m_distanceThreshL2;
      vfc::float32_t l_distanceThreshL3 = g_tileSettings->m_distanceThreshL3;

      if ( ( i != 0u ) && ( i != 15u ) && ( i != 7u ) && ( i != 8u ) )
      {
        if ( l_distanceThreshL1 > g_tileSettings->m_distanceThreshCorner )
        {
           l_distanceThreshL1 = g_tileSettings->m_distanceThreshCorner;
        }
        else if (l_distanceThreshL2 > g_tileSettings->m_distanceThreshCorner )
        {
          l_distanceThreshL2 = g_tileSettings->m_distanceThreshCorner;
        }
        else if ( l_distanceThreshL3 > g_tileSettings->m_distanceThreshCorner )
        {
          l_distanceThreshL3 = g_tileSettings->m_distanceThreshCorner;
        }
        else
        {
          // do nothing
        }
      }
      else
      {
        // do nothing
      }

      if ( (m_sectors[i].m_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV1) &&
             isLessEqual(m_sectors[i].m_previousDistance , l_distanceThreshL1) &&
           isGreaterEqual(m_sectors[i].m_currentDistance , l_distanceThreshL1) )
      {
        m_sectors[i].m_ObjMovingSts = OBJ_MOVING_HYSTERESIS_LV1;
      }
      else if ( (m_sectors[i].m_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV2) &&
           isLessEqual(m_sectors[i].m_previousDistance ,l_distanceThreshL2) &&
           isGreaterEqual(m_sectors[i].m_currentDistance , l_distanceThreshL2) )
      {
        m_sectors[i].m_ObjMovingSts = OBJ_MOVING_HYSTERESIS_LV2;
      }
      else if ( (m_sectors[i].m_ObjMovingSts != OBJ_MOVING_HYSTERESIS_LV3) &&
           isLessEqual(m_sectors[i].m_previousDistance , l_distanceThreshL3) &&
           isGreaterEqual(m_sectors[i].m_currentDistance , l_distanceThreshL3) )
      {
        m_sectors[i].m_ObjMovingSts = OBJ_MOVING_HYSTERESIS_LV3;
      }
      else if ( (m_sectors[i].m_ObjMovingSts == OBJ_MOVING_HYSTERESIS_LV1) &&
           (isLess(m_sectors[i].m_currentDistance , l_distanceThreshL1) ||
            isGreater(m_sectors[i].m_currentDistance , l_distanceThreshL1 + g_tileSettings->m_HysteresisDistanceThresh)) )
      {
        m_sectors[i].m_ObjMovingSts = OBJ_MOVING_NORMAL;
      }
      else if ( (m_sectors[i].m_ObjMovingSts == OBJ_MOVING_HYSTERESIS_LV2) &&
           (isLess(m_sectors[i].m_currentDistance , l_distanceThreshL2) ||
            isGreater(m_sectors[i].m_currentDistance , l_distanceThreshL2 + g_tileSettings->m_HysteresisDistanceThresh)) )
      {
        m_sectors[i].m_ObjMovingSts = OBJ_MOVING_NORMAL;
      }
      else if ( (m_sectors[i].m_ObjMovingSts == OBJ_MOVING_HYSTERESIS_LV3) &&
           (isLess(m_sectors[i].m_currentDistance , l_distanceThreshL3) ||
            isGreater(m_sectors[i].m_currentDistance , l_distanceThreshL3 + g_tileSettings->m_HysteresisDistanceThresh)) )
      {
        m_sectors[i].m_ObjMovingSts = OBJ_MOVING_NORMAL;
      }
      else
      {
        // do nothing
      }

      if(isNotEqual(m_sectors[i].m_previousDistance, m_sectors[i].m_currentDistance))
      {
        m_sectors[i].m_previousDistance = m_sectors[i].m_currentDistance;
      }

    }

  }

  osg::ref_ptr<TileOverlay> l_tileOverlay;
  if (m_tileOverlay.lock(l_tileOverlay))
  {
    TileOverlayComposite* const l_tileOverlayNode = static_cast<TileOverlayComposite*> (l_tileOverlay->reserve());
    if (l_tileOverlayNode != nullptr)
    {
      l_tileOverlayNode->accept(*m_updateVisitor.get());
// //#ifndef CC_ASSETS_2D_TILEOVERLAY
//       if (g_tileSettings->m_SplineTypeForEachSegment == false)
//       {
//         m_updateVisitor->setLayoutData(m_sectors);
//       }
// //#else
//       else
//       {
      m_updateVisitor->setSplineTileLayoutData(m_sectors);
      {
        if (cc::daddy::CustomDaddyPorts::sm_tileSplineInterpolateArrayDaddySenderPort.isConnected())
        {
          cc::daddy::TileSplineInterpolateArrayDaddy& l_interpolateArrayDaddy = cc::daddy::CustomDaddyPorts::sm_tileSplineInterpolateArrayDaddySenderPort.reserve();
          m_updateVisitor->copyInterpolateArray(&l_interpolateArrayDaddy.m_Data);
          cc::daddy::CustomDaddyPorts::sm_tileSplineInterpolateArrayDaddySenderPort.deliver();
        }
//       }
      }
//#endif
    //   VisibilityUpdateVisitor l_visibilityUpdateVisitor(m_flankAvailable, m_rearAvailable);
    //   l_tileOverlayNode->accept(l_visibilityUpdateVisitor);
      l_tileOverlay->deliver();
    }
  }
  else
  {
    // ! if acquiring the tile overlay fails this task is done
    return true;
  }

  return false;
}



} // namespace tileoverlay
} // namespace assets
} // namespace cc
