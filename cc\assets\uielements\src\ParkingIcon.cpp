//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ParkingIcon.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/ParkingIcon.h"
#include "cc/assets/uielements/inc/HmiElementsSettings.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "CustomSystemConf.h"

using pc::util::logging::g_EngineContext;

namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for parking icons
enum ParkingIconType
{
  PARKING_CONFIRM_BTN,
  PARKING_CONFIRM_BTN_OFF,
  PARKING_SEARCHING_EXIT_GEAR_R,
  PARKING_ON_GOING_TEXT,
  PARKING_FINISHED,
  PARKING_FINISHED_TEXT_BOX,
  PARKING_SUSPEND_DOOR_TO_CLOSE_TEXT_BOX,
  PARKING_SUSPEND_PRESS_DEADMAN_SWITCH,
  PARKING_SUSPEND_OBJ_IN_TRAJ,
  PARKING_GUIDANCE_RELEASE_BRAKE,
  PARKING_QUIT_EXTERNALECUFAILURE,
  PARKING_QUIT_PASFAILURE,
  PARKING_QUIT_SPEEDHIGH,
  PARKING_QUIT_TIMINGOVERFLOW,
  PARKING_QUIT_MOVETIMESOVERFLOW,
  PARKING_QUIT_RECORVERTIMESOVERFLOW,
  PARKING_QUIT_GASPEDAL,
  PARKING_QUIT_GEARINTERVENTION,
  PARKING_QUIT_EXTERNALECUACTIVE,
  PARKING_QUIT_OTHER_REASON,
  PARKING_QUIT_TRAJECTORY,
  PARKING_QUIT_ACC_AEB,
  PARKING_QUIT_SPACELIMIT,
  PARKING_QUIT_SEATBELTUNBUCKLE,
  PARKING_QUIT_DRIVERDOOROPEN,
  PARKING_QUIT_EPBAPPLY,
  PARKING_QUIT_VEHICLEBLOCK,
  PARKING_QUIT_GEARINTERRUPT,
  PARKING_QUIT_TRUNKDOOROPEN,
  PARKING_QUIT_STEERINGWHEELHANDSON,
  PARKING_QUIT_SURROUNDVIEW,
  PARKING_QUIT_UNSAFE_BEHAVIOR,
  PARKING_QUIT_TJA_HWA,
  PARKING_QUIT_FAILURE,
};

//!
//! @brief Construct a new ParkingIcon Manager:: ParkingIcon Manager object
//!
//! @param f_config
//!
ParkingIconManager::ParkingIconManager()
  : m_lastConfigUpdate(~0u)
  , m_lastParkReqReleaseBtn(false)
  {
  }


// ! transfer to start from bottom left
osg::Vec2f ParkingIconManager::transferToBottomLeft(const osg::Vec2f f_iconPos)
{
  // return osg::Vec2f(f_iconPos.x() - cc::core::g_views->m_mainViewport.m_origin.x(), cc::core::g_views->m_mainViewport.m_size.y() - f_iconPos.y());
  return osg::Vec2f(f_iconPos.x(), cc::core::g_views->m_mainViewport.m_size.y() - f_iconPos.y());
}

ParkingIconManager::~ParkingIconManager()
{
}

void ParkingIconManager::init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays)
{
  // ! init icons
  m_settingParkIcon.clear(f_imageOverlays);
  // parking confirm button, locate at the top left corner of the second screen
  //  ------
  // | ---- |(P) [text box]
  // | ---- |
  // | ---- |
  // | ---- |
  // | ---- |
  //  ------
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkIcon,                   transferToBottomLeft(g_uiSettings->m_settingPARKIcon.m_iconCenter),     f_imageOverlays->getImageSize(g_uiSettings->m_texturePathParkIcon                  )));  // P parking icon
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkIconOFF,                transferToBottomLeft(g_uiSettings->m_settingPARKIcon.m_iconCenter),     f_imageOverlays->getImageSize(g_uiSettings->m_texturePathParkIconOFF               )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxExitGearR,           transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxExitGearR          )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxOnGoing,             transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxOnGoing            )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkFinished,               transferToBottomLeft(g_uiSettings->m_settingPARKFinished.m_iconCenter), f_imageOverlays->getImageSize(g_uiSettings->m_texturePathParkFinished              )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxCompleted,           transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxCompleted          )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxDoorToClose,         transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxDoorToClose        )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxPressDeadmanSwitch,  transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxPressDeadmanSwitch )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxObjInTraj,           transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxObjInTraj          )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxReleaseBrake,        transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxReleaseBrake       )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxExternalEcuFailure,  transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxExternalEcuFailure )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxPasFailure,          transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxPasFailure         )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxSpeedHigh,           transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxSpeedHigh          )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxTimingOverflow,      transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxTimingOverflow     )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxMovetimesOverflow,   transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxMovetimesOverflow  )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxRecovertimesOverflow,transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxRecovertimesOverflow)));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxGasPedal,            transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxGasPedal           )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxGearIntervention,    transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxGearIntervention   )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxExternalEcuActive,   transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxExternalEcuActive  )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxOtherReason,         transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxOtherReason        )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxTrajOutRange,        transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxTrajOutRange       )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxAccAeb,              transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxAccAeb             )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxSpaceLimit,          transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxSpaceLimit         )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxSeatbeltUnbuckle,    transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxSeatbeltUnbuckle   )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxDriverDoorOpen,      transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxDriverDoorOpen     )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxEPBApply,            transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxEPBApply           )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxVehicleBlock,        transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxVehicleBlock       )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxGearInterrupt,       transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxGearInterrupt      )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxTrunkdoorOpen,       transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxTrunkdoorOpen      )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxSteeringwheelHandon, transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxSteeringwheelHandon)));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxSurroundView,        transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxSurroundView       )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxUnsafeBehavior,      transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxUnsafeBehavior     )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxQuitTJAHWA,          transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxQuitTJAHWA         )));
  m_settingParkIcon.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxFailure,             transferToBottomLeft(g_uiSettings->m_settingTextBox.m_iconCenter),      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathTextBoxFailure            )));
}


void ParkingIconManager::update(cc::assets::uielements::CustomImageOverlays* f_imageOverlays, core::CustomFramework* f_framework)   // PRQA S 6040  // PRQA S 6043  // PRQA S 6044
{
  // ! check if config has changed
  if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
  {
    init(f_imageOverlays);
    m_lastConfigUpdate = g_uiSettings->getModifiedCount();
  }

  m_settingParkIcon.getIcon(PARKING_CONFIRM_BTN_OFF)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_CONFIRM_BTN)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_SEARCHING_EXIT_GEAR_R)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_ON_GOING_TEXT)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_FINISHED)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_FINISHED_TEXT_BOX)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_SUSPEND_DOOR_TO_CLOSE_TEXT_BOX)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_SUSPEND_PRESS_DEADMAN_SWITCH)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_SUSPEND_OBJ_IN_TRAJ)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_GUIDANCE_RELEASE_BRAKE)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_EXTERNALECUFAILURE)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_PASFAILURE)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_SPEEDHIGH)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_TIMINGOVERFLOW)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_MOVETIMESOVERFLOW)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_RECORVERTIMESOVERFLOW)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_GASPEDAL)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_GEARINTERVENTION)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_EXTERNALECUACTIVE)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_SURROUNDVIEW)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_UNSAFE_BEHAVIOR)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_OTHER_REASON)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_TRAJECTORY)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_ACC_AEB)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_SPACELIMIT)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_SEATBELTUNBUCKLE)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_DRIVERDOOROPEN)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_EPBAPPLY)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_VEHICLEBLOCK)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_GEARINTERRUPT)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_TRUNKDOOROPEN)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_STEERINGWHEELHANDSON)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_TJA_HWA)->setEnabled(false);
  m_settingParkIcon.getIcon(PARKING_QUIT_FAILURE)->setEnabled(false);

  if (f_framework->m_displayedView_ReceiverPort.hasData())
  {
    const cc::daddy::SVSDisplayedViewDaddy_t* l_displayid = f_framework->m_displayedView_ReceiverPort.getData();
    EScreenID l_curviewid = l_displayid->m_Data;

    if ((l_curviewid == EScreenID_SINGLE_FRONT_NORMAL) || (l_curviewid == EScreenID_SINGLE_REAR_NORMAL_ON_ROAD)
          || (l_curviewid == EScreenID_PERSPECTIVE_RL) || (l_curviewid == EScreenID_PERSPECTIVE_RR))
    {
    if(f_framework->m_parkHmiParkingStatusReceiver.hasData())
    {
    const cc::daddy::ParkStatusDaddy_t* l_parkStatus = f_framework->m_parkHmiParkingStatusReceiver.getData();
    cc::target::common::EPARKStatusR2L l_curparkStatus = l_parkStatus->m_Data;
    // XLOG_INFO_OS(g_EngineContext) << "Park status get"<< XLOG_ENDL;
    switch (l_curparkStatus)
    {
      case cc::target::common::PARK_Completed:
      {
        // XLOG_INFO_OS(g_EngineContext) << "park completed"<< XLOG_ENDL;
        m_settingParkIcon.getIcon(PARKING_FINISHED)->setEnabled(true);
        m_settingParkIcon.getIcon(PARKING_FINISHED_TEXT_BOX)->setEnabled(true);
      }
      break;
      case cc::target::common::EPARKStatusR2L::PARK_Searching:
      {
        if(f_framework->m_parkHmiParkDriverIndReceiver.hasData())
        {
          const cc::daddy::ParkDriverIndDaddy_t* l_parkDriverInd = f_framework->m_parkHmiParkDriverIndReceiver.getData();
          cc::target::common::EPARKDriverIndR2L l_curparkDriverInd = l_parkDriverInd->m_Data;

          switch (l_curparkDriverInd)
          {
          // case PARKDRV_ExitGearR:
          //   m_settingParkIcon.getIcon(PARKING_SEARCHING_EXIT_GEAR_R)->setEnabled(true);
          //   break;
          default:
            break;
          }
        }
      }
    break;
    case cc::target::common::EPARKStatusR2L::PARK_Guidance_active:
    case cc::target::common::PARK_Guidance_suspend:
    {
      if(f_framework->m_parkHmiParkingTypeReceiver.hasData())
      {
        const cc::daddy::ParkTypeDaddy_t* l_parkTypeind = f_framework->m_parkHmiParkingTypeReceiver.getData();
        cc::target::common::EPARKTypeR2L l_curparkTypeind = l_parkTypeind->m_Data;
        if (PARKTYPE_APA ==l_curparkTypeind)
        {
          if(f_framework->m_parkHmiParkingRecoverIndReceiver.hasData())
          {
            const cc::daddy::ParkRecoverIndDaddy_t* l_parkrecoverind = f_framework->m_parkHmiParkingRecoverIndReceiver.getData();
            cc::target::common::EPARKRecoverIndR2L l_curparkrecoverind = l_parkrecoverind->m_Data;
            // XLOG_INFO_OS(g_EngineContext) << "Park Guidance active, recover ind has data"<< XLOG_ENDL;
            switch (l_curparkrecoverind)
            {
              case PARKREC_DoorOpen:
              {
                // XLOG_INFO_OS(g_EngineContext) << "Park Guidance active, close door needed, grey confirm button"<< XLOG_ENDL;
                m_settingParkIcon.getIcon(PARKING_SUSPEND_DOOR_TO_CLOSE_TEXT_BOX)->setEnabled(true);
              }
              break;
              case cc::target::common::EPARKRecoverIndR2L::PARKREC_PauseCommand:
              {
                m_settingParkIcon.getIcon(PARKING_SUSPEND_PRESS_DEADMAN_SWITCH)->setEnabled(true);

                m_settingParkIcon.getIcon(PARKING_CONFIRM_BTN_OFF)->setEnabled(true);
                m_settingParkIcon.getIcon(PARKING_CONFIRM_BTN)->setEnabled(false);
              }
              break;
              // case PARKREC_Req_Reserved:
              // break;
              case cc::target::common::EPARKRecoverIndR2L::PARKREC_ObjectOnPath:
              {
                m_settingParkIcon.getIcon(PARKING_SUSPEND_OBJ_IN_TRAJ)->setEnabled(true);
                // XLOG_INFO_OS(g_EngineContext) << "Park Guidance active, object in the traj"<< XLOG_ENDL;
              }
              break;
              case PARKREC_SeatBelt:
              break;
              default:
              {
                // XLOG_INFO_OS(g_EngineContext) << "Park Guidance active, parking is on going"<< XLOG_ENDL;
                m_settingParkIcon.getIcon(PARKING_ON_GOING_TEXT)->setEnabled(true);
                if (f_framework->m_parkHmiParkingReqReleaseBtnReceiver.hasData())
                {
                  const cc::daddy::ParkReqReleaseBtnDaddy_t* l_parkreqReleaseBtn = f_framework->m_parkHmiParkingReqReleaseBtnReceiver.getData();
                  bool l_curparkreqReleaseBtn = l_parkreqReleaseBtn->m_Data;
                  if (l_curparkreqReleaseBtn)
                  {
                    m_settingParkIcon.getIcon(PARKING_CONFIRM_BTN)->setEnabled(true);
                    m_settingParkIcon.getIcon(PARKING_CONFIRM_BTN_OFF)->setEnabled(false);
                  }
                }
              }
              break;
            }
          }
          if(f_framework->m_parkHmiParkDriverIndReceiver.hasData())
          {
            const cc::daddy::ParkDriverIndDaddy_t* l_parkDriverInd = f_framework->m_parkHmiParkDriverIndReceiver.getData();
            cc::target::common::EPARKDriverIndR2L l_curparkDriverInd = l_parkDriverInd->m_Data;

            switch (l_curparkDriverInd)
            {
              case PARKDRV_ReleaseBrake:
                m_settingParkIcon.getIcon(PARKING_GUIDANCE_RELEASE_BRAKE)->setEnabled(true);
                m_settingParkIcon.getIcon(PARKING_ON_GOING_TEXT)->setEnabled(false);
                break;
              default:
                break;
            }
          }
        }
      }
      // XLOG_INFO_OS(g_EngineContext) << "Park Guidance active"<< XLOG_ENDL;
      // recoverable suspend
    }
    break;
    case cc::target::common::EPARKStatusR2L::PARK_Terminated:
    {
        if(f_framework->m_parkHmiParkingQuitIndReceiver.hasData())
        {
          const cc::daddy::ParkQuitIndDaddy_t* l_parkquitind = f_framework->m_parkHmiParkingQuitIndReceiver.getData();
          cc::target::common::EPARKQuitIndR2L l_curparkquitind = l_parkquitind->m_Data;
          // XLOG_INFO_OS(g_EngineContext) << "Park terminated, quit ind has data"<< XLOG_ENDL;
          switch (l_curparkquitind)
          {
          // case PARKQUIT_ExternalECUFailure:
          // {
          //   m_settingParkIcon.getIcon(PARKING_QUIT_EXTERNALECUFAILURE)->setEnabled(true);
          //   // XLOG_INFO_OS(g_EngineContext) << "Park terminated, quit due to external ecu failure"<< XLOG_ENDL;
          // }
          //   break;
          // case PARKQUIT_PASFailure:
          // {
          //   m_settingParkIcon.getIcon(PARKING_QUIT_PASFAILURE)->setEnabled(true);
          //   // XLOG_INFO_OS(g_EngineContext) << "Park terminated, quit due to pas failure"<< XLOG_ENDL;
          // }
          //   break;
          // case PARKQUIT_SpeedHigh:
          // {
          //   m_settingParkIcon.getIcon(PARKING_QUIT_SPEEDHIGH)->setEnabled(true);
          //   // XLOG_INFO_OS(g_EngineContext) << "Park terminated, quit due to speed high"<< XLOG_ENDL;
          // }
          //   break;
          // case PARKQUIT_TimingOverflow:
          // {
          //   m_settingParkIcon.getIcon(PARKING_QUIT_TIMINGOVERFLOW)->setEnabled(true);
          //   // XLOG_INFO_OS(g_EngineContext) << "Park terminated, quit due to timing overflow"<< XLOG_ENDL;
          // }
          //   break;
          // case PARKQUIT_MoveTimesOverflow:
          // {
          //   m_settingParkIcon.getIcon(PARKING_QUIT_MOVETIMESOVERFLOW)->setEnabled(true);
          //   // XLOG_INFO_OS(g_EngineContext) << "Park terminated, quit due to move times over flow"<< XLOG_ENDL;
          // }
          //   break;
          // case PARKQUIT_RecoverTimesOverflow:
          // {
          //   m_settingParkIcon.getIcon(PARKING_QUIT_RECORVERTIMESOVERFLOW)->setEnabled(true);
          //   // XLOG_INFO_OS(g_EngineContext) << "Park terminated, quit due to recorvertimes over flow"<< XLOG_ENDL;
          // }
          //   break;
          // case PARKQUIT_GasPedal:
          // {
          //   m_settingParkIcon.getIcon(PARKING_QUIT_GASPEDAL)->setEnabled(true);
          //   // XLOG_INFO_OS(g_EngineContext) << "Park terminated, quit due to recorvertimes over flow"<< XLOG_ENDL;
          // }
          //   break;
          // case PARKQUIT_GearIntervention:
          //   m_settingParkIcon.getIcon(PARKING_QUIT_GEARINTERVENTION)->setEnabled(true);
          //   break;
          // case PARKQUIT_ExternalECUActive:
          //     m_settingParkIcon.getIcon(PARKING_QUIT_EXTERNALECUACTIVE)->setEnabled(true);
          //     break;
          // case PARKQUIT_OtherReason:
          //     m_settingParkIcon.getIcon(PARKING_QUIT_OTHER_REASON)->setEnabled(true);
          //     break;
          // case PARKQUIT_Trajectory:
          //   {
          //     m_settingParkIcon.getIcon(PARKING_QUIT_TRAJECTORY)->setEnabled(true);
          //     // XLOG_INFO_OS(g_EngineContext) << "Park terminated, quit of traj"<< XLOG_ENDL;
          //   }
          //   break;
          // case PARKQUIT_ACC_AEB:
          //   m_settingParkIcon.getIcon(PARKING_QUIT_ACC_AEB)->setEnabled(true);
          //   break;
          // case PARKQUIT_SpaceLimit:
          //   m_settingParkIcon.getIcon(PARKING_QUIT_SPACELIMIT)->setEnabled(true);
          //   break;
          // case PARKQUIT_SeatBeltUnbuckle:
          //   m_settingParkIcon.getIcon(PARKING_QUIT_SEATBELTUNBUCKLE)->setEnabled(true);
          //   break;
          // case PARKQUIT_DriverDoorOpen:
          //   m_settingParkIcon.getIcon(PARKING_QUIT_DRIVERDOOROPEN)->setEnabled(true);
          //   break;
          // case PARKQUIT_EPBApply:
          //   m_settingParkIcon.getIcon(PARKING_QUIT_EPBAPPLY)->setEnabled(true);
          //   break;
          // case PARKQUIT_VehicleBlock:
          //   m_settingParkIcon.getIcon(PARKING_QUIT_VEHICLEBLOCK)->setEnabled(true);
          //   break;
          // case PARKQUIT_GearInterrupt:
          //   m_settingParkIcon.getIcon(PARKING_QUIT_GEARINTERRUPT)->setEnabled(true);
          //   break;
          // case PARKQUIT_TrunkDoorOpen:
          //   m_settingParkIcon.getIcon(PARKING_QUIT_TRUNKDOOROPEN)->setEnabled(true);
          //   break;
          // case PARKQUIT_SteeringWheelHandsOn:
          //   m_settingParkIcon.getIcon(PARKING_QUIT_STEERINGWHEELHANDSON)->setEnabled(true);
          //   break;
          // case PARKQUIT_ExcessiveSlope:
          //   break;
          // case PARKQUIT_SurroundView:
          //   m_settingParkIcon.getIcon(PARKING_QUIT_SURROUNDVIEW)->setEnabled(true);
          //   break;
          // case PARKQUIT_TerminateButtonPressed:
          //   break;
          // case PARKQUIT_DoorLock:
          //   break;
          // case PARKQUIT_SBM:
          //   break;
          // case PARKQUIT_RemoteDevice:
          //   break;
          // case PARKQUIT_RemoteKey:
          //   break;
          // case PARKQUIT_Unsafe_Behavior:
          //   m_settingParkIcon.getIcon(PARKING_QUIT_UNSAFE_BEHAVIOR)->setEnabled(true);
          //   break;
          // case PARKQUIT_TJA_HWA:
          //   m_settingParkIcon.getIcon(PARKING_QUIT_TJA_HWA)->setEnabled(true);
          //   break;
          // case PARKQUIT_Phone_LowBattey:
          //   break;
          default:
            break;
        }
      }
    }
    break;
    case cc::target::common::PARK_Failure:
    {
       m_settingParkIcon.getIcon(PARKING_QUIT_FAILURE)->setEnabled(true);
    }
    break;
    default:
      break;
    }
    }
    }
  }

}

pc::assets::Icon* ParkingIconManager::createIcon(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const osg::Vec2f& f_iconSize) const
{
  pc::assets::Icon* l_icon = new pc::assets::Icon(f_iconPath, true);
  l_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setSize(f_iconSize, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  return l_icon;
}


//!
//! @brief Construct a new ParkingIcon:: ParkingIcon object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
ParkingIcon::ParkingIcon(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays(f_assetId, nullptr)
  , m_customFramework(f_customFramework)
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1);
}


ParkingIcon::~ParkingIcon()
{
}


void ParkingIcon::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    m_manager.update(this, m_customFramework);
  }
  pc::assets::ImageOverlays::traverse(f_nv);
}


} // namespace uielements
} // namespace assets
} // namespace cc