//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  TextSymbols.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/TextSymbols.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "CustomSystemConf.h"

#include "cc/assets/uielements/inc/HmiElementsSettings.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for seeting bar icons
enum class TextSymbolIconType : vfc::uint32_t
{
  WARNSYMBOL_TEXT_VERT,
  WARNSYMBOL_TEXT_VERT_DAY
};

//!
//! @brief Construct a new WarnSymbols Manager:: WarnSymbols Manager object
//!
//! @param f_config
//!
TextSymbolManager::TextSymbolManager()
  : m_lastConfigUpdate(~0u) // PRQA S 4050
  , m_textIcons()
  , m_dayNightTheme(cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT)
  , m_mat_b(false)
{
}



TextSymbolManager::~TextSymbolManager() = default;

void TextSymbolManager::init(pc::assets::ImageOverlays* f_imageOverlays)
{
  // ! init WarnSymbols icons
  m_textIcons.clear(f_imageOverlays);

  m_textIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxSafeNotification,
                                                                            g_uiSettings->m_settingTextBoxSafeNotification_vert.m_iconCenter,
                                                                            g_uiSettings->m_settingTextBoxSafeNotification_vert.m_iconSize,
                                                                            false));
  m_textIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathTextBoxSafeNotificationDay,
                                                                            g_uiSettings->m_settingTextBoxSafeNotification_vert.m_iconCenter,
                                                                            g_uiSettings->m_settingTextBoxSafeNotification_vert.m_iconSize,
                                                                            false));
}


void TextSymbolManager::update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework)
{
  if (f_imageOverlays == nullptr || f_framework == nullptr)
  {
    return; // todo: maybe add more loggings.
  }

  // ! check if config has changed
  if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
  {
    init(f_imageOverlays);
    m_lastConfigUpdate = g_uiSettings->getModifiedCount();
  }

  // ! check the text warning status
  // m_textIcons.getIcon(WARNSYMBOL_TEXT_HORI)->setEnabled(false);
  m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::WARNSYMBOL_TEXT_VERT))->setEnabled(false); // PRQA S 2427


  cc::target::common::EThemeTypeDayNight l_curDayNightTheme = m_dayNightTheme;

  if (f_framework->m_SVSRotateStatusDaddy_Receiver.hasData())
  {
    const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType = f_framework->m_SVSRotateStatusDaddy_Receiver.getData();
    cc::target::common::EThemeTypeHU const l_theme = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 4899

    if (cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT == l_theme)
    {
      if (f_framework->m_dayNightThemeDaddy_Receiver.hasData())
      {
        const auto l_dayNightThemeDaddy = f_framework->m_dayNightThemeDaddy_Receiver.getData();
        const auto l_data = l_dayNightThemeDaddy->m_Data;

        // only update if receive DAY or NIGHT values
        if (l_data == cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT || l_data == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
          l_curDayNightTheme = l_dayNightThemeDaddy->m_Data;
        }
      }

      if (m_dayNightTheme != l_curDayNightTheme)
      {
        m_dayNightTheme = l_curDayNightTheme;

      }

      if (cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT == m_dayNightTheme)
      {
        m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::WARNSYMBOL_TEXT_VERT))->setEnabled(true); // PRQA S 2427
        m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::WARNSYMBOL_TEXT_VERT_DAY))->setEnabled(false); // PRQA S 2427
      }
      else if (cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY == m_dayNightTheme)
      {
        m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::WARNSYMBOL_TEXT_VERT))->setEnabled(false); // PRQA S 2427
        m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::WARNSYMBOL_TEXT_VERT_DAY))->setEnabled(true); // PRQA S 2427
      }
      else
      {
        //do nothing
      }

    }
    else
    {
      // do nothing
    }
  }

}


//!
//! @brief Construct a new TextSymbols:: TextSymbols object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
TextSymbols::TextSymbols(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays(f_assetId, nullptr) // PRQA S 2759 // PRQA S 2323
  , m_customFramework(f_customFramework) // PRQA S 2323
  , m_manager() // PRQA S 2323
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);

  //! render order
  //! Vehicle imposter is 0. Warning symbols are over vehicle, so this render order shall be > 0.
  constexpr vfc::uint32_t l_renderOrder = 10u;
  cc::assets::uielements::CustomImageOverlays::CustomSetRenderOrder(l_renderOrder); // PRQA S 2759

}


TextSymbols::~TextSymbols() = default;


void TextSymbols::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    m_manager.update(this, m_customFramework);
  }
  pc::assets::ImageOverlays::traverse(f_nv);
}


} // namespace uielements
} // namespace assets
} // namespace cc

