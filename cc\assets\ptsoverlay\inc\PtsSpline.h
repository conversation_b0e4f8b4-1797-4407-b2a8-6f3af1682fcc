//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_PTSOVERLAY_PTSSPLINE_H
#define CC_ASSETS_PTSOVERLAY_PTSSPLINE_H

#include "pc/svs/util/math/inc/Polygon2D.h"
#include "pc/svs/util/math/inc/VecMath.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include <osg/Vec4ub>
#include <osg/Geode>

namespace cc
{
namespace assets
{
namespace ptsoverlay
{

static const osg::Vec4ub g_white = osg::Vec4ub(0xFFu, 0xFFu, 0xFFu, 0xFFu);

//!
//! DegradationData
//!
class DegradationData
{
public:

  DegradationData();

  DegradationData(
    const cc::daddy::PtsHmiStateOutput& f_ptsHmiState,
    bool f_flanksEnabled,
    bool f_doorOpenLeft,
    bool f_doorOpenRight,
    bool f_trailerConnected);

  void evaluateState(
    const cc::daddy::PtsHmiStateOutput& f_ptsHmiState,
    bool f_flanksEnabled,
    bool f_doorOpenLeft,
    bool f_doorOpenRight,
    bool f_trailerConnected);

  bool isErrorReactionActive() const;

  bool isDegraded() const;

  bool m_frontDisabled;
  bool m_rearDisabled;

  bool m_flankFrontLeftDisabled;
  bool m_flankFrontRightDisabled;
  bool m_flankRearLeftDisabled;
  bool m_flankRearRightDisabled;

  bool m_errorReactionFront;
  bool m_errorReactionSide;
  bool m_errorReactionRear;
};


//!
//! UpdateVisitor
//!
class PtsUpdateVisitor : public osg::NodeVisitor
{
public:


  typedef std::vector<osg::Vec2f> NormalList;
  typedef std::vector<unsigned char> PasZoneList;

  PtsUpdateVisitor();

  virtual void apply(osg::Node& f_node) override;

  void setData(
    const pc::util::Polygon2D& f_layout,
    const pc::util::FloatList& f_distances,
    const PasZoneList& f_zoneMappings,
    const DegradationData& f_degradationData);

  const pc::util::Polygon2D& getLayout() const
  {
    return m_layout;
  }

  const pc::util::FloatList& getDistances() const
  {
    return m_distances;
  }

  const PasZoneList& getPasZoneMappings() const
  {
    return m_pasZoneMappings;
  }

  const DegradationData& getDegradationData() const
  {
    return m_degradationData;
  }

  const NormalList& getNormals() const
  {
    return m_normals;
  }

private:

  pc::util::Polygon2D m_layout;
  pc::util::FloatList m_distances;
  PasZoneList m_pasZoneMappings;
  DegradationData m_degradationData;
  NormalList m_normals;
};


//!
//! PtsSpline
//!
class PtsSpline : public osg::Geode
{
public:

  PtsSpline(const PtsSpline& f_other, const osg::CopyOp& f_copyOp = osg::CopyOp::SHALLOW_COPY);

  virtual void update(const PtsUpdateVisitor& f_visitor) = 0;

protected:

  PtsSpline();

  virtual ~PtsSpline() = default;

  unsigned int m_numLayoutPoints;

private:

  PtsSpline& operator = (const PtsSpline&) = delete;

};


//!
//! PtsSplineShadow
//!
class PtsSplineShadow : public PtsSpline
{
public:

  enum : unsigned int
  {
    NUM_VERTICES_PER_SEGMENT = 3u
  };

  PtsSplineShadow();
  PtsSplineShadow(const PtsSplineShadow& f_other, const osg::CopyOp& f_copyOp = osg::CopyOp::SHALLOW_COPY);

  META_Node(cc::assets::ptsoverlay, PtsSplineShadow);

  virtual void update(const PtsUpdateVisitor& f_visitor) override;

protected:

  virtual ~PtsSplineShadow() = default;

private:

  PtsSplineShadow& operator = (const PtsSplineShadow&) = delete;

};

//!
//! PtsSpline2D
//!
class PtsSpline2D : public PtsSpline
{
public:

  enum : unsigned int
  {
    NUM_VERTICES_PER_INNER_SEGMENT = 2u,
    NUM_VERTICES_PER_OUTER_SEGMENT = 2u,
    NUM_VERTICES_PER_SEGMENT = NUM_VERTICES_PER_INNER_SEGMENT + NUM_VERTICES_PER_OUTER_SEGMENT
  };

  PtsSpline2D(float f_zPos = 0.0f);
  PtsSpline2D(const PtsSpline2D& f_other, const osg::CopyOp& f_copyOp = osg::CopyOp::SHALLOW_COPY);

  META_Node(cc::assets::ptsoverlay, PtsSpline2D);

  virtual void update(const PtsUpdateVisitor& f_visitor) override;

protected:

  virtual ~PtsSpline2D() = default;

private:

  PtsSpline2D& operator = (const PtsSpline2D&) = delete;

  float m_zPos;

};


//!
//! PtsShield3D
//!
class PtsShield3D : public PtsSpline
{
public:

  enum : unsigned int
  {
    NUM_VERTICES_PER_SHIELD_SEGMENT = 3u,
    NUM_VERTICES_PER_HAIRLINE_SEGMENT = 2u,
    NUM_VERTICES_PER_SEGMENT = NUM_VERTICES_PER_SHIELD_SEGMENT + NUM_VERTICES_PER_HAIRLINE_SEGMENT
  };

  PtsShield3D();
  PtsShield3D(const PtsShield3D& f_other, const osg::CopyOp& f_copyOp = osg::CopyOp::SHALLOW_COPY);

  META_Node(cc::assets::ptsoverlay, PtsShield3D);

  virtual void update(const PtsUpdateVisitor& f_visitor) override;

protected:

  virtual ~PtsShield3D() = default;

private:

  PtsShield3D& operator = (const PtsShield3D&) = delete;
};


//!
//! PtsSpline3D
//!
class PtsSpline3D : public PtsSpline
{
public:

  enum : unsigned int
  {
    NUM_VERTICES_PER_SEGMENT = 2u
  };

  PtsSpline3D(const osg::Vec4ub& f_color = g_white);
  PtsSpline3D(const PtsSpline3D& f_other, const osg::CopyOp& f_copyOp = osg::CopyOp::SHALLOW_COPY);

  META_Node(cc::assets::ptsoverlay, PtsSpline3D);

  float getHeight() const;
  void setHeight(float f_height);

  const osg::Vec4ub getColor() const;
  void setColor(const osg::Vec4ub& f_color);

  virtual void update(const PtsUpdateVisitor& f_visitor) override;

protected:

  virtual ~PtsSpline3D() = default;

  float m_height;
  osg::Vec4ub m_color;

private:

  PtsSpline3D& operator = (const PtsSpline3D&) = delete;
};


//!
//! PtsContour3D
//!
class PtsContour3D : public PtsSpline3D
{
public:

  PtsContour3D();
  PtsContour3D(const PtsContour3D& f_other, const osg::CopyOp& f_copyOp = osg::CopyOp::SHALLOW_COPY);

  META_Node(cc::assets::ptsoverlay, PtsContour3D);

  virtual void update(const PtsUpdateVisitor& f_visitor) override;

protected:

  virtual ~PtsContour3D() = default;

private:

  PtsContour3D& operator = (const PtsContour3D&) = delete;

};


} // namespace ptsoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PTSOVERLAY_PTSSPLINE_H