//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  SettingBar.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/SettingBar.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "CustomSystemConf.h"

#include "cc/assets/uielements/inc/HmiElementsSettings.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for seeting bar icons
enum SettingBarIconType
{
  SETTING_BAR_PP_QUIT,
  SETTING_BAR_PP_DIS,
  SETTING_BAR_PP_ON,
  SETTING_BAR_PP_OFF,
  SETTING_BAR_AUTO_CAM_ON,
  SETTING_BAR_AUTO_CAM_OFF,
  SETTING_BAR_VM_2D,
  SETTING_BAR_VM_2D_OFF,
  SETTING_BAR_VM_3D,
  SETTING_BAR_VM_3D_OFF,
};

//!
//! @brief Construct a new SettingBar Manager:: SettingBar Manager object
//!
//! @param f_config
//!
SettingBarManager::SettingBarManager()
  : m_lastConfigUpdate(~0u)
{
}


SettingBarManager::~SettingBarManager()
{
}

void SettingBarManager::init(pc::assets::ImageOverlays* f_imageOverlays)
{
  // ! init SettingBar icons
  m_settingBarIcons.clear(f_imageOverlays);

  m_settingBarIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathQuit,            g_uiSettings->m_settingQuit.m_iconCenter,          g_uiSettings->m_settingQuit.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathPPDIS,           g_uiSettings->m_settingPPDIS.m_iconCenter,         g_uiSettings->m_settingPPDIS.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathPPON,            g_uiSettings->m_settingPPON.m_iconCenter,          g_uiSettings->m_settingPPON.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathPPOFF,           g_uiSettings->m_settingPPON.m_iconCenter,          g_uiSettings->m_settingPPON.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathAutoCamActivON, g_uiSettings->m_settingAutoCamActiv.m_iconCenter, g_uiSettings->m_settingAutoCamActiv.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathAutoCamActivOFF,g_uiSettings->m_settingAutoCamActiv.m_iconCenter, g_uiSettings->m_settingAutoCamActiv.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathVM2D,            g_uiSettings->m_settingVM2D.m_iconCenter,          g_uiSettings->m_settingVM2D.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathVM2DOFF,         g_uiSettings->m_settingVM2D.m_iconCenter,          g_uiSettings->m_settingVM2D.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathVM3D,            g_uiSettings->m_settingVM3D.m_iconCenter,          g_uiSettings->m_settingVM3D.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathVM3DOFF,         g_uiSettings->m_settingVM3D.m_iconCenter,          g_uiSettings->m_settingVM3D.m_iconSize));

}


void SettingBarManager::update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework)
{
  // ! check if config has changed
  if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
  {
    init(f_imageOverlays);
    m_lastConfigUpdate = g_uiSettings->getModifiedCount();
  }
    // initialization
  m_settingBarIcons.getIcon(SETTING_BAR_VM_2D)->setEnabled(false);
  m_settingBarIcons.getIcon(SETTING_BAR_VM_2D_OFF)->setEnabled(false);
  m_settingBarIcons.getIcon(SETTING_BAR_VM_3D)->setEnabled(false);
  m_settingBarIcons.getIcon(SETTING_BAR_VM_3D_OFF)->setEnabled(false);
  // ! check the Quit status
  // m_settingBarIcons.getIcon(SETTING_BAR_PP_QUIT)->setEnabled(true);

  m_settingBarIcons.getIcon(SETTING_BAR_PP_ON)->setEnabled(true);
  m_settingBarIcons.getIcon(SETTING_BAR_PP_OFF)->setEnabled(false);

  m_settingBarIcons.getIcon(SETTING_BAR_AUTO_CAM_ON)->setEnabled(true);
  m_settingBarIcons.getIcon(SETTING_BAR_AUTO_CAM_OFF)->setEnabled(false);

  // ! check the PP Mute button status
  if (f_framework->m_pasButtonStatus_ReceiverPort.hasData())
  {
    const cc::daddy::PasButtonStatusDaddy_t* l_pasbuttonstatus = f_framework->m_pasButtonStatus_ReceiverPort.getData();
    bool l_pasbutton = l_pasbuttonstatus->m_Data;

    if(l_pasbutton)
    {
      m_settingBarIcons.getIcon(SETTING_BAR_PP_ON)->setEnabled(false);
      m_settingBarIcons.getIcon(SETTING_BAR_PP_OFF)->setEnabled(true);
    }
    else
    {
      m_settingBarIcons.getIcon(SETTING_BAR_PP_ON)->setEnabled(true);
      m_settingBarIcons.getIcon(SETTING_BAR_PP_OFF)->setEnabled(false);
    }
  }

  //! check auto cam activated button status
  if (f_framework->m_AutoCamActivateButtonPressedStsReceiver.hasData())
  {
    const cc::daddy::SettingAutoCamStDaddy_t* l_autoCamActivBtnSts = f_framework->m_AutoCamActivateButtonPressedStsReceiver.getData();
    ESettingSts l_curAutoCamActivBtnSts = l_autoCamActivBtnSts->m_Data;
    if(l_curAutoCamActivBtnSts == ESettingSts_Set_ON)
    {
      m_settingBarIcons.getIcon(SETTING_BAR_AUTO_CAM_ON)->setEnabled(true);
      m_settingBarIcons.getIcon(SETTING_BAR_AUTO_CAM_OFF)->setEnabled(false);
    }
    else if (l_curAutoCamActivBtnSts == ESettingSts_Set_OFF)
    {
      m_settingBarIcons.getIcon(SETTING_BAR_AUTO_CAM_ON)->setEnabled(false);
      m_settingBarIcons.getIcon(SETTING_BAR_AUTO_CAM_OFF)->setEnabled(true);
    }

  }

  if (f_framework->m_displayedView_ReceiverPort.hasData())
  {
    const cc::daddy::SVSDisplayedViewDaddy_t* l_displayid = f_framework->m_displayedView_ReceiverPort.getData();
    EScreenID l_curviewid = l_displayid->m_Data;

    cc::target::common::EPARKStatusR2L   l_curparksts = cc::target::common:::EPARKStatusR2L::PARK_Off;
    pc::daddy::EGear l_curgear    = pc::daddy::GEAR_P;

    if (f_framework->m_parkHmiParkingStatusReceiver.hasData())         // ! view mode setting is disable when parking is active
    {
      const cc::daddy::ParkStatusDaddy_t* l_parksts = f_framework->m_parkHmiParkingStatusReceiver.getData();
      l_curparksts = l_parksts->m_Data;
    }

    if (f_framework->m_gearReceiver.hasData())                         // ! view mode setting is disable when gear in R
    {
      const pc::daddy::GearDaddy* l_gear = f_framework->m_gearReceiver.getData();
      l_curgear = static_cast<pc::daddy::EGear>(l_gear->m_Data);       //PRQA S 3013
    }

    if ( (EScreenID_PERSPECTIVE_FL == l_curviewid)                     // ! View Mode in 3D perspective views
      || (EScreenID_PERSPECTIVE_FR == l_curviewid)
      || (EScreenID_PERSPECTIVE_RL == l_curviewid)
      || (EScreenID_PERSPECTIVE_RR == l_curviewid) )
    {
      if ( (cc::target::common::EPARKStatusR2L::PARK_Searching == l_curparksts)
        || (cc::target::common::EPARKStatusR2L::PARK_Guidance_active == l_curparksts)
        || (cc::target::common::PARK_Guidance_suspend == l_curparksts)
        || (cc::target::common::EPARKStatusR2L::PARK_Completed == l_curparksts)
        || (cc::target::common::EPARKStatusR2L::PARK_AssistStandby == l_curparksts)
        || (pc::daddy::GEAR_R == l_curgear) )
      {
        m_settingBarIcons.getIcon(SETTING_BAR_VM_3D_OFF)->setEnabled(true);
      }
      else
      {
        m_settingBarIcons.getIcon(SETTING_BAR_VM_3D)->setEnabled(true);
      }
    }
    else                                                                // ! View Mode in 2D standard views
    {
      if ( (cc::target::common::EPARKStatusR2L::PARK_Searching == l_curparksts)
        || (cc::target::common::EPARKStatusR2L::PARK_Guidance_active == l_curparksts)
        || (cc::target::common::PARK_Guidance_suspend == l_curparksts)
        || (cc::target::common::EPARKStatusR2L::PARK_Completed == l_curparksts)
        || (cc::target::common::EPARKStatusR2L::PARK_AssistStandby == l_curparksts)
        || (pc::daddy::GEAR_R == l_curgear) )
      {
        m_settingBarIcons.getIcon(SETTING_BAR_VM_2D_OFF)->setEnabled(true);
      }
      else
      {
        m_settingBarIcons.getIcon(SETTING_BAR_VM_2D)->setEnabled(true);
      }
    }

  }

}


pc::assets::Icon* SettingBarManager::createIcon(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const osg::Vec2f& f_iconSize) const
{
  pc::assets::Icon* l_icon = new pc::assets::Icon(f_iconPath, true);
  l_icon->setOrigin(pc::assets::Icon::Origin::TopLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setSize(f_iconSize, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  return l_icon;
}


//!
//! @brief Construct a new SettingBar:: SettingBar object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
SettingBar::SettingBar(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays(f_assetId , nullptr)    // PRQA S 2966
  , m_customFramework(f_customFramework)
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1);
}


SettingBar::~SettingBar()
{
}


void SettingBar::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    m_manager.update(this, m_customFramework);
  }
  pc::assets::ImageOverlays::traverse(f_nv);
}


} // namespace uielements
} // namespace assets
} // namespace cc