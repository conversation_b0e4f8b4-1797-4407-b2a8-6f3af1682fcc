//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Ha Thanh Phong (MS/EDA92-XC)
//  Department: (MS/EDA92-XC)
//=============================================================================
/// @swcomponent SVS BYD
/// @file  DrivablePath.cpp
/// @brief
//=============================================================================

#include "cc/assets/drivablepath/inc/DrivablePath.h"
#include "cc/assets/drivablepath/inc/DrivablePathManager.h"
#include "cc/assets/trajectory/inc/Helper.h"

#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/core/inc/ShaderManager.h"

#include "osg/Array"
#include "osg/Texture2D"
#include "osgDB/ReadFile"
#include "osgDB/WriteFile"
#include "osg/LineWidth"


/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3804
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace trajectory
{

pc::util::coding::Item<StopLineSettings> g_stopLineSettings("StopLine");

// This multiplier is to make the quad stripe bigger to have enough room for the blur on the downsampled mipmaps
constexpr vfc::float32_t g_blurMul = 1.2f; // (1 <= )


/**
 * Calculates the parameter for each track regarding to the f_side it is and pass them to Frame.cpp to generate the vertices for the geometry
 * @param f_side: If it is for the tyres on the right or left side
 * @param f_trajParams: General const parameters for all trackTypes that are given by TrajectoryCodingParams with CodingParamters.xml
 * @param f_numOfVerts: Number of vertices to generate the geometry afterwards
 */
DrivablePath::DrivablePath(
  pc::core::Framework* f_framework,
  vfc::float32_t f_height,
  const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
  vfc::uint32_t f_numOfVerts)
  : GeneralTrajectoryLine(
      f_framework,
      cc::assets::trajectory::commontypes::Middle_enm /* DUMMY */,
      2u,
      f_height,
      f_trajParams,
      true)
  , mc_numOfVerts(f_numOfVerts)
  , m_pathPlanningArr()
  , m_isMovingBackward(true)
  , m_parkingOutEndIndex(60u)
{
  m_geometry->addPrimitiveSet(new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES)));  // PRQA S 3804  // PRQA S 3803

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray());  //PRQA S 3076
  l_colors->setBinding(osg::Array::BIND_PER_VERTEX);

  osg::Vec2ubArray* const l_texCoords = new osg::Vec2ubArray;
  l_texCoords->setNormalize(true);
  l_texCoords->reserve(2u * mc_numOfVerts);
  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < mc_numOfVerts; l_vertexIndex++)
  {
    l_texCoords->push_back(osg::Vec2ub(0u, 127u));
  }
  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < mc_numOfVerts; l_vertexIndex++)
  {
    l_texCoords->push_back(osg::Vec2ub(255u, 127u));
  }
  m_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);

  const vfc::float32_t lc_halfGradientWidth = std::abs(m_trajParams.GradientWidth) * 0.5f;
  constexpr vfc::float32_t lc_halfWholeWidth    =  2.0f * 0.5f; // should base on the whole of trajectory, current value is set as 2 meter
  const vfc::float32_t lc_halfGeometryWidth = (lc_halfWholeWidth + lc_halfGradientWidth) * g_blurMul;
  m_lineGeometryWidth              = lc_halfGeometryWidth * 2.0f;

}


DrivablePath::~DrivablePath() =default;


osg::Image* DrivablePath::create1DTexture() const  // PRQA S 6043
{
  constexpr vfc::uint32_t  lc_imageWidth  = 256u; // Image width in pixels.
  constexpr vfc::uint32_t  lc_imageHeight = 1u;   // Image height in pixels.
  constexpr vfc::uint32_t  lc_imageDepth  = 1u;   // Image depth in pixels, in case of a 3D image.
  constexpr vfc::float32_t         lc_imageGeometryWidth = static_cast<vfc::float32_t>(lc_imageWidth - 1);

  const vfc::float32_t lc_halfGradientWidth = std::abs(m_trajParams.GradientWidth) * 0.5f;
  constexpr vfc::float32_t lc_halfWholeWidth     = 2.0f * 0.5f; // should base on the whole of trajectory, current value is set as 2 meter
  const vfc::float32_t lc_halfGeometryWidth = (lc_halfWholeWidth + lc_halfGradientWidth) * g_blurMul;

  std::array<vfc::float32_t, 4> l_absDistancesFromCenter; // 0..3: From outermost to innermost
  l_absDistancesFromCenter[0u] = lc_halfWholeWidth + lc_halfGradientWidth;
  l_absDistancesFromCenter[1u] = lc_halfWholeWidth - lc_halfGradientWidth;
  l_absDistancesFromCenter[2u] = lc_halfWholeWidth - m_trajParams.DrivablePath_BorderWidth + lc_halfGradientWidth;
  l_absDistancesFromCenter[3u] = lc_halfWholeWidth - m_trajParams.DrivablePath_BorderWidth - lc_halfGradientWidth;

  std::array<vfc::float32_t, 8> l_normalizedPositions;    // 0..7: From left to right
  l_normalizedPositions[0u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[0u]) / m_lineGeometryWidth;
  l_normalizedPositions[1u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[1u]) / m_lineGeometryWidth;
  l_normalizedPositions[2u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[2u]) / m_lineGeometryWidth;
  l_normalizedPositions[3u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[3u]) / m_lineGeometryWidth;
  l_normalizedPositions[4u] = 1.0f - l_normalizedPositions[3u];
  l_normalizedPositions[5u] = 1.0f - l_normalizedPositions[2u];
  l_normalizedPositions[6u] = 1.0f - l_normalizedPositions[1u];
  l_normalizedPositions[7u] = 1.0f - l_normalizedPositions[0u];

  osg::Vec4ub l_lineColor_Inside = osg::Vec4ub(255u, 255u, 255u, 255u);  // set fix value for image's ground
  osg::Vec4ub l_lineColor_BorderLine = osg::Vec4ub(255u, 255u, 255u, 255u); // showing border line

  l_lineColor_Inside = pc::util::osgx::toVec4ub(m_trajParams.DrivablePath_Color);
  l_lineColor_BorderLine = pc::util::osgx::toVec4ub(m_trajParams.DrivablePath_BorderColor);

  osg::Vec4ub l_lineColor_Outside = l_lineColor_BorderLine;
  l_lineColor_Outside.a() = 0u;

  osg::Image* const l_image = new osg::Image;
  l_image->allocateImage(static_cast<vfc::int32_t>(lc_imageWidth), static_cast<vfc::int32_t>(lc_imageHeight), static_cast<vfc::int32_t>(lc_imageDepth), static_cast<GLenum>(GL_RGBA), static_cast<GLenum>(GL_UNSIGNED_BYTE));
  osg::Vec4ub* l_data = reinterpret_cast<osg::Vec4ub*> (l_image->data());  //PRQA S 3030
  for (vfc::uint32_t x = 0u; x < lc_imageWidth; ++x)
  {
    const vfc::float32_t l_x_normalized = static_cast<vfc::float32_t>(x) / lc_imageGeometryWidth;

    if ( (l_x_normalized < l_normalizedPositions[0u])
      || (l_x_normalized > l_normalizedPositions[7u]) )
    {
      // Outside the assist line
      (*l_data) = l_lineColor_Outside;
    }
    else if ( (l_x_normalized > l_normalizedPositions[3u])
           && (l_x_normalized < l_normalizedPositions[4u]) )
    {
      // Middle of the assist line
      (*l_data) = l_lineColor_Inside;
    }
    else if ( (l_x_normalized > l_normalizedPositions[1u])
           && (l_x_normalized < l_normalizedPositions[2u]) )
    {
      // Middle of the left border line
      (*l_data) = l_lineColor_BorderLine;
    }
    else if ( (l_x_normalized > l_normalizedPositions[5u])
           && (l_x_normalized < l_normalizedPositions[6u]) )
    {
      // Middle of the right border line
      (*l_data) = l_lineColor_BorderLine;
    }
    else
    {
      // Gradient
      if (l_x_normalized <= l_normalizedPositions[1u])
      {
        // Left border line, left gradient
        (*l_data) = helper::smoothstep_Vec4ub(
          l_lineColor_Outside, l_lineColor_BorderLine, l_normalizedPositions[0u], l_normalizedPositions[1u], l_x_normalized);
      }
      else if (l_x_normalized >= l_normalizedPositions[6u])
      {
        // Right border line, right gradient
        (*l_data) = helper::smoothstep_Vec4ub(
          l_lineColor_BorderLine, l_lineColor_Outside, l_normalizedPositions[6u], l_normalizedPositions[7u], l_x_normalized);
      }
      else
      {
        // Inner gradient of either border lines
        if (l_x_normalized <= l_normalizedPositions[3u])
        {
          // Left border line, right gradient
          (*l_data) = helper::smoothstep_Vec4ub(
            l_lineColor_BorderLine, l_lineColor_Inside, l_normalizedPositions[2u], l_normalizedPositions[3u], l_x_normalized);
        }
        else //(l_x_normalized >= l_normalizedPositions[4u])
        {
          // Right border line, left gradient
          (*l_data) = helper::smoothstep_Vec4ub(
            l_lineColor_Inside, l_lineColor_BorderLine, l_normalizedPositions[4u], l_normalizedPositions[5u], l_x_normalized);
        }
      }
    }
    ++l_data;
  }

  return l_image;
}


void DrivablePath::generateVertexData()
{
  generateVertexData_usingTexture();
}

void DrivablePath::updateVertexData(const PathPlanning& f_pathPlanning)
{
  m_pathPlanningArr = f_pathPlanning;
}


void DrivablePath::generateVertexData_usingTexture()  // PRQA S 6040  // PRQA S 6041  // PRQA S 6044
{
  // *** 1. Create frame ***
  m_frame.removeAllPoints();

  m_frame.setVertexLineOffset(0u, 0.f);
  m_frame.setVertexLineOffset(1u, 0.f);
  m_frame.setBumperLineAngle(0u, 0);
  m_frame.setBumperLinePos  (0u, 0);

  const osg::Vec4f l_lineColor = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f); // With texturing just the alpha matters in the shader.
  cc::assets::trajectory::commontypes::ControlPoint_st l_controlPoint;

  // Controllpoint left(0),right(1)
  l_controlPoint.Angle = 0.f;
  l_controlPoint.LongitudinalPos = 0;
  l_controlPoint.Color = l_lineColor;
  l_controlPoint.Index = 0u; // Dummy value. Will be calculated later.
  m_frame.addControlPoint(0u, l_controlPoint);
  m_frame.addControlPoint(1u, l_controlPoint);

  // Controllpoint left(0),right(1)
  l_controlPoint.Angle = 0.f;
  l_controlPoint.LongitudinalPos = 10.f;
  l_controlPoint.Color = l_lineColor;
  m_frame.addControlPoint(0u, l_controlPoint);
  m_frame.addControlPoint(1u, l_controlPoint);


  // *** 2. Create vertices (and colors) ***
  // Generate line vertices
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (m_geometry->getVertexArray());  //PRQA S 3076
  l_vertices->clear();

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray());  //PRQA S 3076
  l_colors->clear();

  m_frame.generateDrivablePathVertices(  // PRQA S 3803
      0u, 0u, 1u, m_height,
      l_vertices, l_colors, m_pathPlanningArr.m_numOfValidPoint, m_pathPlanningArr, m_isMovingBackward, m_parkingOutEndIndex);

  m_frame.generateDrivablePathVertices(  // PRQA S 3803
      1u, 0u, 1u, m_height,
      l_vertices, l_colors, m_pathPlanningArr.m_numOfValidPoint, m_pathPlanningArr, m_isMovingBackward, m_parkingOutEndIndex);

  // *** 3. Create indices ***
  osg::DrawElementsUShort* const l_indices = static_cast<osg::DrawElementsUShort*> (m_geometry->getPrimitiveSet(0u));  //PRQA S 3076
  l_indices->clear();
  m_frame.generateIndices(0u, 0u, 1u, 0u, mc_numOfVerts, l_indices);


  l_vertices->dirty();
  l_colors->dirty();
  l_indices->dirty();
  m_geometry->dirtyBound();

  setCull(false);
}


class StopLineCullCallback : public osg::Drawable::CullCallback
{
public:

  explicit StopLineCullCallback(StopLine* f_StopLine)
    : osg::Object()
    , osg::Drawable::CullCallback()
    , m_StopLine(f_StopLine)
  {
  }

  bool cull(osg::NodeVisitor* /* f_nv */, osg::Drawable* /* f_drawable */, osg::RenderInfo* /* f_renderInfo */) const override    // PRQA S 2120
  {
    return m_StopLine->isCulled();
  }

protected:

  StopLine* m_StopLine;

};

//!
//! StopLineHideCallback
//!
class StopLineHideCallback : public StopLineCullCallback
{
public:

  explicit StopLineHideCallback(StopLine* f_StopLine)
    : osg::Object()
    , StopLineCullCallback(f_StopLine)
  {
  }

  bool cull(osg::NodeVisitor* /* f_nv */, osg::Drawable* /* f_drawable */, osg::RenderInfo* /* f_renderInfo */) const override
  {
    if(m_StopLine->isCulled() || m_StopLine->isHidden())
    {
      return true;
    }
    else
    {
      return false;
    }
  }

};


StopLine::StopLine()
:osg::Geode()
,m_cull(false)
,m_hide(false)
,m_rectVertexArray(new osg::Vec3Array(4))
,m_PtrColor(new osg::Vec4Array(1u))
,m_color(g_stopLineSettings->m_stopLineColor)
{

  osg::Vec4Array* const l_Color = new osg::Vec4Array(4u);
  (*l_Color)[0u] = m_color;
  (*l_Color)[1u] = m_color;
  (*l_Color)[2u] = m_color;
  (*l_Color)[3u] = m_color;
  //define vertex array

  (*m_rectVertexArray)[0] = osg::Vec3f(0.f, 0.f, 0.003f);
  (*m_rectVertexArray)[1] = osg::Vec3f(0.f, 0.f, 0.003f);
  (*m_rectVertexArray)[2] = osg::Vec3f(0.f, 0.f, 0.003f);
  (*m_rectVertexArray)[3] = osg::Vec3f(0.f, 0.f, 0.003f);

  osg::Vec3Array* const l_VertexArray = m_rectVertexArray;

  //! create fill geometry
  osg::Geometry* const l_stopLine = pc::util::osgx::createGeometry("DrivablePathStopLine");
  l_VertexArray->dirty();
  l_stopLine->setVertexArray(l_VertexArray);

  osg::DrawElementsUByte* const l_indices = new osg::DrawElementsUByte(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), 6u);
  (*l_indices)[0u] = 1u;
  (*l_indices)[1u] = 0u;
  (*l_indices)[2u] = 2u;
  (*l_indices)[3u] = 2u;
  (*l_indices)[4u] = 0u;
  (*l_indices)[5u] = 3u;
  l_stopLine->addPrimitiveSet(l_indices);    // PRQA S 3803

  l_stopLine->setColorArray(l_Color, osg::Array::BIND_PER_VERTEX);

  osg::StateSet* const l_commonStateSet = getOrCreateStateSet();
  l_commonStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
  l_commonStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
  l_commonStateSet->setRenderBinDetails(1100, "RenderBin");
  l_commonStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143

  l_stopLine->setStateSet(l_commonStateSet);
  l_stopLine->setCullCallback(new StopLineHideCallback(this));

  osg::Geode::addDrawable(l_stopLine); // PRQA S 3803

  // set to none-render by default
  setHide(true);
}

void StopLine::updatePosition(const osg::Vec2f& f_pos, osg::Vec2f f_direction)
{

  if (f_direction.length() < cc::assets::drivablepath::g_drivablePathSettings->m_thresholdDistance*0.9f) // allow for +/- 10 percent deviation
  {
    // there is no fitting direction is found
    setHide(true);
    return;
  }

  f_direction.normalize();  //PRQA S 3804

  osg::Vec2f l_left_offset  = f_direction;
  osg::Vec2f l_right_offset = f_direction;
  osg::Vec2f l_with_offest  = f_direction * (0.1f);

  cc::assets::trajectory::helper::TrajectoryHelper::Rotate2DPoint(l_left_offset, osg::DegreesToRadians(-90.f));
  cc::assets::trajectory::helper::TrajectoryHelper::Rotate2DPoint(l_right_offset, osg::DegreesToRadians(90.f));

  l_left_offset  = l_left_offset  * std::abs(pc::vehicle::g_mechanicalData->m_width) * 0.5f * 0.9f;
  l_right_offset = l_right_offset * std::abs(pc::vehicle::g_mechanicalData->m_width) * 0.5f * 0.9f;

  (*m_rectVertexArray)[0u] = osg::Vec3f(f_pos.x() + l_left_offset.x(),  f_pos.y() + l_left_offset.y(),  0.003f);
  (*m_rectVertexArray)[1u] = osg::Vec3f(f_pos.x() + l_right_offset.x(), f_pos.y() + l_right_offset.y(), 0.003f);
  (*m_rectVertexArray)[2u] = osg::Vec3f((*m_rectVertexArray)[1u].x() + l_with_offest.x(), (*m_rectVertexArray)[1u].y() + l_with_offest.y(), 0.003f);
  (*m_rectVertexArray)[3u] = osg::Vec3f((*m_rectVertexArray)[0u].x() + l_with_offest.x(), (*m_rectVertexArray)[0u].y() + l_with_offest.y(), 0.003f);

  osg::Geometry*  const l_geometry  = getDrawable(0u)->asGeometry();
  l_geometry->getVertexArray()->dirty();
  l_geometry->dirtyBound();
}


StopLine::~StopLine() = default;



} // namespace trajectory
} // namespace assets
} // namespace cc
