//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EXTERNAL Castellane Florian (Altran, CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  Vehicle.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_COMMON_VEHICLE_H
#define CC_ASSETS_COMMON_VEHICLE_H

#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "pc/generic/util/cli/inc/CommandRegistry.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/assets/vehiclemodel/inc/VehicleModel.h"
#include "pc/svs/core/inc/Asset.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"

#include <osg/Vec4i>

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace common
{

//!
//! CustomVehicleModelSettings
//!
class CustomVehicleModelSettings : public pc::util::coding::ISerializable
{
public:
    CustomVehicleModelSettings()
        : m_lightStateFilePath("cc/vehicle_model/lightstate.json")
        , m_doorWarningTrans(0.6f)
        , m_transparency(0.1f)
        , m_transparencyInterior(0.0f)
        , m_halfTransparency(0.1f)
        , m_halfTransparencyInterior(0.0f)
    {
    }

    SERIALIZABLE(CustomVehicleModelSettings)
    {
        ADD_STRING_MEMBER(lightStateFilePath);
        ADD_FLOAT_MEMBER(doorWarningTrans);
        ADD_FLOAT_MEMBER(transparency);
        ADD_FLOAT_MEMBER(transparencyInterior);
        ADD_FLOAT_MEMBER(halfTransparency);
        ADD_FLOAT_MEMBER(halfTransparencyInterior);
    }

    std::string    m_lightStateFilePath;
    vfc::float32_t m_doorWarningTrans;
    vfc::float32_t m_transparency;
    vfc::float32_t m_transparencyInterior;
    vfc::float32_t m_halfTransparency;
    vfc::float32_t m_halfTransparencyInterior;
};

extern pc::util::coding::Item<CustomVehicleModelSettings> g_modelSettings;

//!
//! ColorSettings
//!
class ColorSettings : public pc::util::coding::ISerializable
{
public:
    ColorSettings()
        : m_diffuseColor(73.f, 73.f, 73.f, 255.f)
        , m_specColor1(191.f, 191.f, 191.f, 9.f)
        , m_specShininess1(200.0f)
        , m_specColor2(191.f, 191.f, 191.f, 255.f)
        , m_specShininess2(10.0f)
        , m_reflectionPower(1.0f)
        , m_fresnel(1.5f)
        , m_lightPos(0.0f, 1.0f, 1.0f)
        , m_brightness(1.0f)
        , m_chromeDiffuseColor(127.f, 127.f, 127.f, 255.f)
        , m_chromeSpecColor(127.f, 127.f, 127.f, 0.f)
        , m_chromeSpecShininess(100.0f)
        , m_chromeBrightness(1.0f)
        , m_chrome2Brightness(1.0f)
        , m_chrome3Brightness(1.0f)
        , m_veh2dBrightness(1.0f)
    {
    }

    SERIALIZABLE(ColorSettings)
    {
        ADD_MEMBER(osg::Vec4f, diffuseColor);
        ADD_MEMBER(osg::Vec4f, specColor1);
        ADD_FLOAT_MEMBER(specShininess1);
        ADD_MEMBER(osg::Vec4f, specColor2);
        ADD_FLOAT_MEMBER(specShininess2);
        ADD_FLOAT_MEMBER(reflectionPower);
        ADD_FLOAT_MEMBER(fresnel);
        ADD_MEMBER(osg::Vec3f, lightPos);
        ADD_FLOAT_MEMBER(brightness);
        ADD_MEMBER(osg::Vec4f, chromeDiffuseColor);
        ADD_MEMBER(osg::Vec4f, chromeSpecColor);
        ADD_FLOAT_MEMBER(chromeSpecShininess);
        ADD_FLOAT_MEMBER(chromeBrightness);
        ADD_FLOAT_MEMBER(chrome2Brightness);
        ADD_FLOAT_MEMBER(chrome3Brightness);
        ADD_FLOAT_MEMBER(veh2dBrightness);
    }

    osg::Vec4f     m_diffuseColor;
    osg::Vec4f     m_specColor1;
    vfc::float32_t m_specShininess1;
    osg::Vec4f     m_specColor2;
    vfc::float32_t m_specShininess2;
    vfc::float32_t m_reflectionPower;
    vfc::float32_t m_fresnel;
    osg::Vec3f     m_lightPos;
    vfc::float32_t m_brightness;
    osg::Vec4f     m_chromeDiffuseColor;
    osg::Vec4f     m_chromeSpecColor;
    vfc::float32_t m_chromeSpecShininess;
    vfc::float32_t m_chromeBrightness;
    vfc::float32_t m_chrome2Brightness;
    vfc::float32_t m_chrome3Brightness;
    vfc::float32_t m_veh2dBrightness;
};

//!
//! CustomVehicleColorSettings
//!
class CustomVehicleColorSettings : public pc::util::coding::ISerializable
{
public:
    CustomVehicleColorSettings()
    {
    }

    SERIALIZABLE(CustomVehicleColorSettings)
    {
        ADD_MEMBER(ColorSettings, TimeGray);
        ADD_MEMBER(ColorSettings, MountainAsh);
        ADD_MEMBER(ColorSettings, RedEmperor);
        ADD_MEMBER(ColorSettings, SnowyWhite);
        ADD_MEMBER(ColorSettings, SilverGlazeWhite);
        ADD_MEMBER(ColorSettings, DuDuWhite);
        ADD_MEMBER(ColorSettings, JunWareGray);
        ADD_MEMBER(ColorSettings, SilverSandBlack);
        ADD_MEMBER(ColorSettings, WisdomBlue);
        ADD_MEMBER(ColorSettings, QianshanCui);
        ADD_MEMBER(ColorSettings, Azure);
        ADD_MEMBER(ColorSettings, MushanPink);
        ADD_MEMBER(ColorSettings, Golden);
        ADD_MEMBER(ColorSettings, HermesGreen);
        ADD_MEMBER(ColorSettings, SunriseGolden);
        ADD_MEMBER(ColorSettings, BlackGolden);
    }

    ColorSettings m_TimeGray;
    ColorSettings m_MountainAsh;
    ColorSettings m_RedEmperor;
    ColorSettings m_SnowyWhite;
    ColorSettings m_SilverGlazeWhite;
    ColorSettings m_DuDuWhite;
    ColorSettings m_JunWareGray;
    ColorSettings m_SilverSandBlack;
    ColorSettings m_WisdomBlue;
    ColorSettings m_QianshanCui;
    ColorSettings m_Azure;
    ColorSettings m_MushanPink;
    ColorSettings m_Golden;
    ColorSettings m_HermesGreen;
    ColorSettings m_SunriseGolden;
    ColorSettings m_BlackGolden;
};

extern pc::util::coding::Item<CustomVehicleColorSettings> g_modelColorSettings;

class WipingIndicatorSignal;
extern pc::util::coding::Item<WipingIndicatorSignal> g_wipingIndicatorSettings;

osg::Uniform* getOrCreateUniform(
    osg::StateSet* const               f_stateSet,
    const std::string&                 f_uniformName,
    osg::Uniform::Type                 f_type,
    osg::StateAttribute::OverrideValue f_value);

osg::Camera*  findParentCameraWithAbsoluteReferenceFrame(const osg::NodePath& f_nodePath);
void          updateCameraStateSet(osg::StateSet* const f_stateSet, const osg::Matrix& f_inverseViewMatrix);
ColorSettings getColorSettingFromIndex(vfc::uint8_t f_index, pc::core::Framework* f_framework);

//!
//! CarPaintFinalizer
//!
class CarPaintFinalizer : public pc::vehiclemodel::IFinalizer
{
public:
    enum Uniform : std::size_t
    {
        UNIFORM_DIFFUSE_COLOR,
        UNIFORM_SPEC_COLOR1,
        UNIFORM_SPEC_SHININESS1,
        UNIFORM_SPEC_COLOR2,
        UNIFORM_SPEC_SHININESS2,
        UNIFORM_REFLECTION_POWER,
        UNIFORM_FRESNEL,
        UNIFORM_LIGHTPOS,
        UNIFORM_BRIGHTNESS,
        UNIFORM_CHROME_DIFFUSE_COLOR,
        UNIFORM_CHROME_SPEC_COLOR,
        UNIFORM_CHROME_SPEC_SHININESS,
        UNIFORM_CHROME_BRIGHTNESS,
        UNIFORM_CHROME2_BRIGHTNESS,
        UNIFORM_CHROME3_BRIGHTNESS,
        UNIFORM_VEH2D_BRIGHTNESS,
        NUM_UNIFORMS
    };

    static const char* getUniformName(Uniform f_uniform)
    {
        switch (f_uniform)
        {
        case UNIFORM_DIFFUSE_COLOR:
            return "diffuseColor";
        case UNIFORM_SPEC_COLOR1:
            return "specColor1";
        case UNIFORM_SPEC_SHININESS1:
            return "specShininess1";
        case UNIFORM_SPEC_COLOR2:
            return "specColor2";
        case UNIFORM_SPEC_SHININESS2:
            return "specShininess2";
        case UNIFORM_REFLECTION_POWER:
            return "reflectionPower";
        case UNIFORM_FRESNEL:
            return "fresnel";
        case UNIFORM_LIGHTPOS:
            return "lightPos";
        case UNIFORM_BRIGHTNESS:
            return "brightness";
        case UNIFORM_CHROME_DIFFUSE_COLOR:
            return "chromeDiffuseColor";
        case UNIFORM_CHROME_SPEC_COLOR:
            return "chromeSpecColor";
        case UNIFORM_CHROME_SPEC_SHININESS:
            return "chromeSpecShininess";
        case UNIFORM_CHROME_BRIGHTNESS:
            return "chromeBrightness";
        case UNIFORM_CHROME2_BRIGHTNESS:
            return "chrome2Brightness";
        case UNIFORM_CHROME3_BRIGHTNESS:
            return "chrome3Brightness";
        case UNIFORM_VEH2D_BRIGHTNESS:
            return "veh2dBrightness";
        default:
            return "";
        }
    }

    //!
    //! Vehicle Diffuse Color
    //!
    class VehicleDiffuseColorUpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit VehicleDiffuseColorUpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }
        ~VehicleDiffuseColorUpdateCallback()
        {
        }
        void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv)
        {
            update(f_uniform, f_nv);
        }
        void update(osg::Uniform* uniform, osg::NodeVisitor* /* f_nv */); // PRQA S 6043

    private:
        //! Copy constructor is not permitted.
        VehicleDiffuseColorUpdateCallback(const VehicleDiffuseColorUpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        VehicleDiffuseColorUpdateCallback& operator=(const VehicleDiffuseColorUpdateCallback& other); // = delete
        //::daddy::TLatestReceiverPort < cc::daddy::ColorDaddy >  m_receiver ;
        pc::core::Framework* m_pFramework;
    };

    //!
    //! specColor1 Update
    //!
    class VehicleSpecColor1UpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit VehicleSpecColor1UpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~VehicleSpecColor1UpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        VehicleSpecColor1UpdateCallback(const VehicleSpecColor1UpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        VehicleSpecColor1UpdateCallback& operator=(const VehicleSpecColor1UpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! VehicleSpecShininess1UpdateCallback
    //!
    class VehicleSpecShininess1UpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit VehicleSpecShininess1UpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~VehicleSpecShininess1UpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        VehicleSpecShininess1UpdateCallback(const VehicleSpecShininess1UpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        VehicleSpecShininess1UpdateCallback& operator=(const VehicleSpecShininess1UpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! SpecColor2 Update
    //!
    class VehicleSpecColor2UpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit VehicleSpecColor2UpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~VehicleSpecColor2UpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        VehicleSpecColor2UpdateCallback(const VehicleSpecColor2UpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        VehicleSpecColor2UpdateCallback& operator=(const VehicleSpecColor2UpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! VehicleSpecShininess2UpdateCallback
    //!
    class VehicleSpecShininess2UpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit VehicleSpecShininess2UpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~VehicleSpecShininess2UpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        VehicleSpecShininess2UpdateCallback(const VehicleSpecShininess2UpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        VehicleSpecShininess2UpdateCallback& operator=(const VehicleSpecShininess2UpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! ReflectionUpdateCallback
    //!
    class ReflectionUpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit ReflectionUpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~ReflectionUpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        ReflectionUpdateCallback(const ReflectionUpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        ReflectionUpdateCallback& operator=(const ReflectionUpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! VehicleFresnelUpdateCallback
    //!
    class VehicleFresnelUpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit VehicleFresnelUpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~VehicleFresnelUpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        VehicleFresnelUpdateCallback(const VehicleFresnelUpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        VehicleFresnelUpdateCallback& operator=(const VehicleFresnelUpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! LightPositionUpdateCallback
    //!
    class LightPositionUpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit LightPositionUpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~LightPositionUpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        LightPositionUpdateCallback(const LightPositionUpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        LightPositionUpdateCallback& operator=(const LightPositionUpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! Brightness Update for Day Night mode
    //!
    class BrightnessUpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit BrightnessUpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~BrightnessUpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        BrightnessUpdateCallback(const BrightnessUpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        BrightnessUpdateCallback& operator=(const BrightnessUpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! VehicleChromeDiffuseColorUpdateCallback
    //!
    class VehicleChromeDiffuseColorUpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit VehicleChromeDiffuseColorUpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~VehicleChromeDiffuseColorUpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        VehicleChromeDiffuseColorUpdateCallback(const VehicleChromeDiffuseColorUpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        VehicleChromeDiffuseColorUpdateCallback&
        operator=(const VehicleChromeDiffuseColorUpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! VehicleChromeSpecColorUpdateCallback
    //!
    class VehicleChromeSpecColorUpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit VehicleChromeSpecColorUpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~VehicleChromeSpecColorUpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        VehicleChromeSpecColorUpdateCallback(const VehicleChromeSpecColorUpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        VehicleChromeSpecColorUpdateCallback& operator=(const VehicleChromeSpecColorUpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! VehicleChromeSpecShininessUpdateCallback
    //!
    class VehicleChromeSpecShininessUpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit VehicleChromeSpecShininessUpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~VehicleChromeSpecShininessUpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        VehicleChromeSpecShininessUpdateCallback(const VehicleChromeSpecShininessUpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        VehicleChromeSpecShininessUpdateCallback&
        operator=(const VehicleChromeSpecShininessUpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! VehicleChromeBrightnessUpdateCallback
    //!
    class VehicleChromeBrightnessUpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit VehicleChromeBrightnessUpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~VehicleChromeBrightnessUpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        VehicleChromeBrightnessUpdateCallback(const VehicleChromeBrightnessUpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        VehicleChromeBrightnessUpdateCallback&
        operator=(const VehicleChromeBrightnessUpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! VehicleChrome2BrightnessUpdateCallback
    //!
    class VehicleChrome2BrightnessUpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit VehicleChrome2BrightnessUpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~VehicleChrome2BrightnessUpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        VehicleChrome2BrightnessUpdateCallback(const VehicleChrome2BrightnessUpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        VehicleChrome2BrightnessUpdateCallback&
        operator=(const VehicleChrome2BrightnessUpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! VehicleChrome3BrightnessUpdateCallback
    //!
    class VehicleChrome3BrightnessUpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit VehicleChrome3BrightnessUpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~VehicleChrome3BrightnessUpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        VehicleChrome3BrightnessUpdateCallback(const VehicleChrome3BrightnessUpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        VehicleChrome3BrightnessUpdateCallback&
        operator=(const VehicleChrome3BrightnessUpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! VehicleVeh2dBrightnessUpdateCallback
    //!
    class VehicleVeh2dBrightnessUpdateCallback : public osg::Uniform::Callback
    {
    public:
        explicit VehicleVeh2dBrightnessUpdateCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~VehicleVeh2dBrightnessUpdateCallback()
        {
        }

        virtual void operator()(osg::Uniform* f_uniform, osg::NodeVisitor* f_nv) override
        {
            update(f_uniform, f_nv);
        }

        void update(osg::Uniform* uniform, osg::NodeVisitor*);

    private:
        //! Copy constructor is not permitted.
        VehicleVeh2dBrightnessUpdateCallback(const VehicleVeh2dBrightnessUpdateCallback& other); // = delete
        //! Copy assignment operator is not permitted.
        VehicleVeh2dBrightnessUpdateCallback& operator=(const VehicleVeh2dBrightnessUpdateCallback& other); // = delete

        pc::core::Framework* m_pFramework;
    };

    //!
    //! UpdateCallbackInstaller
    //!
    class UpdateCallbackInstaller : public osg::NodeVisitor
    {
    public:
        UpdateCallbackInstaller(pc::core::Framework* f_framework)
            : osg::NodeVisitor(osg::NodeVisitor::TRAVERSE_ALL_CHILDREN)
            , m_framework(f_framework)
            , m_done(false)
        {
        }

        virtual void apply(osg::Node& f_node) override // PRQA S 2120
        {
            osg::StateSet* l_stateSet = f_node.getStateSet();
            if (l_stateSet)
            {
                checkStateSet(l_stateSet);
            }
            if (!m_done)
            {
                traverse(f_node);
            }
        }

        virtual void apply(osg::Geode& f_geode) override
        {
            const vfc::uint32_t l_numDrawables = f_geode.getNumDrawables();
            for (vfc::uint32_t i = 0u; (i < l_numDrawables) && !m_done; ++i)
            {
                osg::Drawable* l_drawable = f_geode.getDrawable(i);
                osg::StateSet* l_stateSet = l_drawable->getStateSet();
                if (l_stateSet)
                {
                    checkStateSet(l_stateSet);
                }
            }
            if (!m_done)
            {
                apply(static_cast<osg::Node&>(f_geode)); // PRQA S 3820
            }
        }

    private:
        // First find and create all uniform and put in a array
        void checkStateSet(osg::StateSet* f_stateSet) // PRQA S 6040
        {
            std::array<osg::ref_ptr<osg::Uniform>, NUM_UNIFORMS> l_uniformArray;
            for (std::size_t i = 0u; i < NUM_UNIFORMS; ++i)
            {
                osg::Uniform* l_uniform = nullptr;
                switch (i)
                {
                case UNIFORM_DIFFUSE_COLOR:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT_VEC4); // PRQA S 3013
                    break;
                case UNIFORM_SPEC_COLOR1:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT_VEC4); // PRQA S 3013
                    break;
                case UNIFORM_SPEC_SHININESS1:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT); // PRQA S 3013
                    break;
                case UNIFORM_SPEC_COLOR2:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT_VEC4); // PRQA S 3013
                    break;
                case UNIFORM_SPEC_SHININESS2:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT); // PRQA S 3013
                    break;
                case UNIFORM_REFLECTION_POWER:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT); // PRQA S 3013
                    break;
                case UNIFORM_FRESNEL:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT); // PRQA S 3013
                    break;
                case UNIFORM_LIGHTPOS:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT_VEC3); // PRQA S 3013
                    break;
                case UNIFORM_BRIGHTNESS:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT); // PRQA S 3013
                    break;
                case UNIFORM_CHROME_DIFFUSE_COLOR:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT_VEC4); // PRQA S 3013
                    break;
                case UNIFORM_CHROME_SPEC_COLOR:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT_VEC4); // PRQA S 3013
                    break;
                case UNIFORM_CHROME_SPEC_SHININESS:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT); // PRQA S 3013
                    break;
                case UNIFORM_CHROME_BRIGHTNESS:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT); // PRQA S 3013
                    break;
                case UNIFORM_CHROME2_BRIGHTNESS:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT); // PRQA S 3013
                    break;
                case UNIFORM_CHROME3_BRIGHTNESS:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT); // PRQA S 3013
                    break;
                case UNIFORM_VEH2D_BRIGHTNESS:
                    l_uniform = f_stateSet->getOrCreateUniform(
                        getUniformName(static_cast<Uniform>(i)), osg::Uniform::FLOAT); // PRQA S 3013
                    break;
                default:
                    l_uniform = f_stateSet->getUniform(getUniformName(static_cast<Uniform>(i))); // PRQA S 3013
                    break;
                }
                if (l_uniform)
                {
                    l_uniformArray[i] = l_uniform;
                }
                else
                {
                    break;
                }
            }

            // And add a callback function to each uniform
            const bool l_allUniformsFound = std::all_of(
                l_uniformArray.begin(),
                l_uniformArray.end(),
                [](const osg::ref_ptr<osg::Uniform>& f_uniform) { return f_uniform.valid(); });

            if (l_allUniformsFound)
            {
                for (std::size_t i = 0u; i < NUM_UNIFORMS; ++i)
                {
                    osg::Uniform* l_uniform = l_uniformArray[i].get();
                    switch (i)
                    {
                    case UNIFORM_DIFFUSE_COLOR:
                        l_uniform->setUpdateCallback(new VehicleDiffuseColorUpdateCallback(m_framework));
                        break;
                    case UNIFORM_SPEC_COLOR1:
                        l_uniform->setUpdateCallback(new VehicleSpecColor1UpdateCallback(m_framework));
                        break;
                    case UNIFORM_SPEC_SHININESS1:
                        l_uniform->setUpdateCallback(new VehicleSpecShininess1UpdateCallback(m_framework));
                        break;
                    case UNIFORM_SPEC_COLOR2:
                        l_uniform->setUpdateCallback(new VehicleSpecColor2UpdateCallback(m_framework));
                        break;
                    case UNIFORM_SPEC_SHININESS2:
                        l_uniform->setUpdateCallback(new VehicleSpecShininess2UpdateCallback(m_framework));
                        break;
                    case UNIFORM_REFLECTION_POWER:
                        l_uniform->setUpdateCallback(new ReflectionUpdateCallback(m_framework));
                        break;
                    case UNIFORM_FRESNEL:
                        l_uniform->setUpdateCallback(new VehicleFresnelUpdateCallback(m_framework));
                        break;
                    case UNIFORM_LIGHTPOS:
                        l_uniform->setUpdateCallback(new LightPositionUpdateCallback(m_framework));
                        break;
                    case UNIFORM_BRIGHTNESS:
                        l_uniform->setUpdateCallback(new BrightnessUpdateCallback(m_framework));
                        break;
                    case UNIFORM_CHROME_DIFFUSE_COLOR:
                        l_uniform->setUpdateCallback(new VehicleChromeDiffuseColorUpdateCallback(m_framework));
                        break;
                    case UNIFORM_CHROME_SPEC_COLOR:
                        l_uniform->setUpdateCallback(new VehicleChromeSpecColorUpdateCallback(m_framework));
                        break;
                    case UNIFORM_CHROME_SPEC_SHININESS:
                        l_uniform->setUpdateCallback(new VehicleChromeSpecShininessUpdateCallback(m_framework));
                        break;
                    case UNIFORM_CHROME_BRIGHTNESS:
                        l_uniform->setUpdateCallback(new VehicleChromeBrightnessUpdateCallback(m_framework));
                        break;
                    case UNIFORM_CHROME2_BRIGHTNESS:
                        l_uniform->setUpdateCallback(new VehicleChrome2BrightnessUpdateCallback(m_framework));
                        break;
                    case UNIFORM_CHROME3_BRIGHTNESS:
                        l_uniform->setUpdateCallback(new VehicleChrome3BrightnessUpdateCallback(m_framework));
                        break;
                    case UNIFORM_VEH2D_BRIGHTNESS:
                        l_uniform->setUpdateCallback(new VehicleVeh2dBrightnessUpdateCallback(m_framework));
                        break;
                    default:
                        // l_uniform->setUpdateCallback(new UnifromUpdateCallback(static_cast<Uniform> (i)));    // PRQA
                        // S 3013
                        break;
                    }
                }
                m_done = true;
            }
        }

        pc::core::Framework* m_framework;
        bool                 m_done;
    };
    CarPaintFinalizer(pc::core::Framework* f_framework)
        : m_framework(f_framework)
    {
    }

    virtual void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override
    {
        UpdateCallbackInstaller l_uci(m_framework);
        f_vehicleModel->accept(l_uci);
    }

protected:
    virtual ~CarPaintFinalizer()
    {
    }

    CarPaintFinalizer(const CarPaintFinalizer&)            = delete;
    CarPaintFinalizer& operator=(const CarPaintFinalizer&) = delete;

private:
    pc::core::Framework* m_framework;
};

// !
// ! VehicleTransparencyUpdateCallback
// !
class VehicleTransparencyUpdateCallback : public osg::NodeCallback
{
public:
    explicit VehicleTransparencyUpdateCallback(pc::core::Framework* pFramework, osg::Node* f_CrystalNode, osg::Node* f_OpaqueNode)
        : m_pFramework(pFramework), m_VehicleCrystalBodyNode(f_CrystalNode), m_VehicleMainBodyNode(f_OpaqueNode)
    {
    }

    virtual ~VehicleTransparencyUpdateCallback() = default;

    void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
    {
        update(f_node, f_nv);
        traverse(f_node, f_nv);
    }

    void update(osg::Node* f_node, osg::NodeVisitor* /* f_nv */);

    static void setTransparencyLevel(float f_transparent)
    {
        sm_transparencyLevel = f_transparent;
    }

    static float getTransparencyLevel()
    {
        return sm_transparencyLevel;
    }

private:
    VehicleTransparencyUpdateCallback(const VehicleTransparencyUpdateCallback& other) = delete;

    VehicleTransparencyUpdateCallback& operator=(const VehicleTransparencyUpdateCallback& other) = delete;

    pc::core::Framework* m_pFramework;

private:
    static float sm_transparencyLevel;
    float        m_transparencyLevel = 1.0f;

    osg::Node* m_VehicleCrystalBodyNode = nullptr;
    osg::Node* m_VehicleMainBodyNode = nullptr;
};

//!
//! VehicleTransparencyFinalizer
//!
class VehicleTransparencyFinalizer : public pc::vehiclemodel::IFinalizer
{
public:
    VehicleTransparencyFinalizer(pc::core::Framework* f_framework)
        : m_framework(f_framework)
    {
    }
    ~VehicleTransparencyFinalizer()
    {
    }

    virtual void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel);

protected:
    VehicleTransparencyFinalizer(const VehicleTransparencyFinalizer&)            = delete;
    VehicleTransparencyFinalizer& operator=(const VehicleTransparencyFinalizer&) = delete;

private:
    pc::core::Framework* m_framework;

    const std::string m_vehicleCrystalBodyNodeName = "vehicle_crystal_body_node";
    const std::string m_vehicleMainBodyNodeName = "vehicle_main_body_node";
};

//!
//! VehicleSeatFinalizer
//!
class VehicleSeatFinalizer : public pc::vehiclemodel::IFinalizer
{
public:
    VehicleSeatFinalizer(pc::core::Framework* f_framework)
        : m_framework(f_framework)
    {
    }
    ~VehicleSeatFinalizer()
    {
    }

    virtual void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel);

    static void
    setNodeMask(const std::string& f_name, pc::vehiclemodel::VehicleModel* f_vehicleModel, bool f_enable = true);

protected:
    VehicleSeatFinalizer(const VehicleSeatFinalizer&)            = delete;
    VehicleSeatFinalizer& operator=(const VehicleSeatFinalizer&) = delete;

private:
    pc::core::Framework* m_framework;
};

//!
//! class CustomVehicleModel
//!
class CustomVehicleModel : public pc::vehiclemodel::VehicleModel
{
public:
    // enum CustomComponent
    // {
    //     FRONT_LEFT_DOOR_INTERIOR,
    //     FRONT_RIGHT_DOOR_INTERIOR,
    //     FRONT_LEFT_WHEEL_4CARBODY,
    //     FRONT_RIGHT_WHEEL_4CARBODY,
    //     REAR_LEFT_WHEEL_4CARBODY,
    //     REAR_RIGHT_WHEEL_4CARBODY,
    //     LOW_BEAM,
    //     LOW_BEAM_BRAKE,
    //     REAR_LIGHT,
    //     BRAKE_LIGHT,
    //     THIRD_BRAKE_LIGHT,
    //     TURN_SIGNAL_LEFT,
    //     TURN_SIGNAL_RIGHT,
    //     TURN_SIGNAL_LEFT_MIRROR,
    //     TURN_SIGNAL_RIGHT_MIRROR,
    //     TURN_SIGNAL_LEFT_REAR,
    //     TURN_SIGNAL_RIGHT_REAR,
    //     TURN_SIGNAL_LEFT_TRUNK,
    //     TURN_SIGNAL_RIGHT_TRUNK,
    //     DAYTIME_LIGHT_LEFT,
    //     DAYTIME_LIGHT_RIGHT,
    //     REVERSE_LAMP,
    //     NUM_CUSTOM_COMPONENTS //! Keep this the last entry!
    // };

    explicit CustomVehicleModel(pc::core::Framework* f_framework = nullptr);

    CustomVehicleModel(const CustomVehicleModel& f_other, const osg::CopyOp& f_copyOp);

    META_Node(cc::assets::common, CustomVehicleModel); // PRQA S 2504

    virtual void reset() override;

    virtual void traverse(osg::NodeVisitor& f_nv) override;

    // osg::Node* getComponent(CustomComponent f_id, bool f_suppressErrorMessage = false);

private:
    // std::array<osg::ref_ptr<osg::Node>, NUM_CUSTOM_COMPONENTS> m_customComponents;
    std::array<osg::ref_ptr<osg::Node>, pc::vehiclemodel::VehicleModel::NUM_COMPONENTS> m_customComponents;

    osg::Node::NodeMask            m_floorplateNodeMask;
    cc::daddy::SolidBasePlateState m_prevBaseplateState;
    vfc::uint32_t                  m_newRefViewID;

    cc::core::CustomFramework* m_customFramework;
};

//======================================================
// Vehicle
//------------------------------------------------------
/// Vehicle asset
/// This class is responsible for comprising all vehicle model features
/// into a single vehicle asset which can be attached to the scene and
/// rendered as a part thereof.
/// <AUTHOR> Castellane Florian (Altran, CC-DA/EAV3)
/// @ingroup vehicle
//======================================================
class Vehicle : public pc::core::Asset
{
public:
    Vehicle(
        cc::core::AssetId    f_assetId,
        pc::core::Framework* f_framework,
        osg::Group*          f_frontWheelsGroup,
        osg::Group*          f_allWheelsGroup,
        osg::Group*          f_vehicleDoorsGroup);

    Vehicle(const Vehicle&) = delete;
    Vehicle& operator=(const Vehicle&) = delete;

    ~Vehicle() override = default;
};

class VehicleReadCommand;
extern pc::util::cli::Command<VehicleReadCommand> g_vehicleReadCommand;

} // namespace common
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_COMMON_VEHICLE_H
