//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: VUJ1LR Vujicic Milica (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  OverlayCallback.cpp
/// @brief
//=============================================================================

#include "pc/generic/util/coding/inc/CodingManager.h"

#include "cc/assets/overlaycallback/inc/OverlayCallback.h"
#include "cc/sm/viewmode/inc/ViewModeStateMachine.h"
#include "cc/core/inc/CustomMechanicalData.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "cc/util/logging/inc/LoggingContexts.h"

#include "osg/ValueObject"

// #define OVERLAY_LOG(component_name)  (if (g_overlayLogSettings->m_log##component_name) XLOG_INFO_OS(g_OverlayContext))

namespace cc
{
namespace assets
{
namespace overlaycallback
{
using cc::util::logging::g_OverlayContext;

pc::util::coding::Item<OverlayData> g_overlayData("OverlayData");

pc::util::coding::Item<OverlayLogSettings> g_overlayLogSettings("OverlayLogSettings");


// To obtain enum names for easier debugging.
static const char* g_OverlayTypeNames[] =  //PRQA S 2428
{
  "OVERLAY_NONE",
  "VEHICLE_WHEEL_TRACK",
  "VEHICLE_DRIVING_TUBE",
  "OBSTACLE_DISTANCE",
  "HITCH",
  "TRAILER_DRIVING_TUBE",
  "DISTANCE_TO_STOP",
  "DEGRADATION_OVERLAY"
};

OverlayCallback::OverlayCallback(pc::core::Framework* f_framework, OverlayType f_overlayType) :
    m_framework(f_framework),
    m_overlayStatus(0),
    m_movementDirection(false),
    m_powerModePrevious(0),
    m_powerModeOverlayStatus(0),
    m_viewModeCamDegraded(0 ),
    m_viewModeDoorsDegraded(0),
    m_overlayType(f_overlayType),
    m_rearCamPosTailgate(false),
    m_trailerDegraded(false),
    m_timer(),
    m_tick()
{
}

OverlayCallback::~OverlayCallback() = default;

void OverlayCallback::operator() (osg::Node* f_node, osg::NodeVisitor* f_nv)  // PRQA S 6041
{
  if (f_node == nullptr || f_nv == nullptr)
  {
    return;
  }
  if ((osg::NodeVisitor::CULL_VISITOR == f_nv->getVisitorType()))
  {
    checkOverlayStatus();
    checkMovementDirection();
    checkPowerMode();
    checkViewModeCamDegradation();
    checkViewModeDoorDegradation();

    //check trailer status onlx in case of trailer trajectories
    if (TRAILER_DRIVING_TUBE == m_overlayType)
    {
      checkTrailerStatus();
    }
    else
    {
      m_trailerDegraded = false;
    }

    // check the overlay status
    if(1 == m_overlayStatus)
    {
      traverse(f_node, f_nv);
    }
  }
  // OVERLAY_LOG( OverlayType            ) << "m_overlayType: "              << g_OverlayTypeNames[ static_cast<vfc::int32_t>(m_overlayType) ] << XLOG_ENDL;
  // OVERLAY_LOG( OverlayStatus          ) << "m_overlayStatus: "            << static_cast<vfc::int32_t> (m_overlayStatus)                    << XLOG_ENDL;
  // OVERLAY_LOG( MovementDirection      ) << "m_movementDirection: "        << static_cast<vfc::int32_t> (m_movementDirection)                << XLOG_ENDL;
  // OVERLAY_LOG( PowerModeOverlayStatus ) << "m_powerModeOverlayStatus: "   << static_cast<vfc::int32_t> (m_powerModeOverlayStatus)           << XLOG_ENDL;
  // OVERLAY_LOG( ViewModeCamDegraded    ) << "m_viewModeCamDegraded: "      << static_cast<vfc::int32_t> (m_viewModeCamDegraded)              << XLOG_ENDL;
  // OVERLAY_LOG( ViewModeDoorsDegraded  ) << "m_viewModeDoorsDegraded: "    << static_cast<vfc::int32_t> (m_viewModeDoorsDegraded)            << XLOG_ENDL;
  // OVERLAY_LOG( TrailerDegraded        ) << "m_trailerDegraded: "          << static_cast<vfc::int32_t> (m_trailerDegraded)                  << XLOG_ENDL;
}

void OverlayCallback::checkOverlayStatus()
{
  m_overlayStatus = 1;
}

void OverlayCallback::checkMovementDirection()
{
    const cc::daddy::NFSM_MovementDirectionUpdate_t* const l_pData = m_framework->asCustomFramework()->m_movementDirection_ReceiverPort.getData();
    if (nullptr != l_pData)
    {
      const vfc::uint32_t l_movementDirection = l_pData->m_Data.m_MovementDirection_u8;
      //1-forward, 2-reverse, 3-static unsecured
      if ((1u == l_movementDirection) || (2u == l_movementDirection) || (3u == l_movementDirection))
      {
        m_movementDirection = true;
      }
      else
      {
        m_movementDirection = false;
      }
    }
}

void OverlayCallback::checkPowerMode()  // PRQA S 6043
{
  //powerModeOverlayStatus = true if:
  //1.powermode signal >= 7 OR
  //2.powermode transitions from powerMode 7 to powerMode 6 and stays in this mode for ICE_STALL_TIMEOUT [s]
  const cc::daddy::PowerModeDaddy_t* const l_pData = m_framework->asCustomFramework()->m_powerMode_ReceiverPort.getData();

  if (nullptr != l_pData)
  {
    const vfc::uint32_t l_currentMode = l_pData->m_Data;
    if (cc::daddy::RUNNING <= l_currentMode)
    {
      m_powerModeOverlayStatus = 1;
    }
    else
    {
      m_powerModeOverlayStatus = 0;
    }

    if ((cc::daddy::IGNITION_ON == l_currentMode))
    {
      if (static_cast<vfc::int32_t>(cc::daddy::RUNNING) == m_powerModePrevious)
      {
        m_timer.setStartTick();
        m_powerModeOverlayStatus = 1;
      }
      else if (static_cast<vfc::int32_t>(cc::daddy::IGNITION_ON) == m_powerModePrevious)
      {
        const vfc::float32_t l_delta = static_cast<vfc::float32_t>(m_timer.time_s());

        if (l_delta < g_overlayData->m_ICE_stallTimeout)
        {
          m_powerModeOverlayStatus = 1;
        }
      }
      else
      {
        //Do nothing
      }
    }
    m_powerModePrevious = static_cast<vfc::int32_t>(l_currentMode);
  }
}

void OverlayCallback::checkViewModeCamDegradation()
{
    //check camera degradation for:
    //TRAILER HITCH and TRAILER TRAJECTORIES (they are connected to their specific views, so no view mode check is needed)
    //WHEEL TRACKS depending on view

    const pc::daddy::CameraDegradationMaskDaddy* const l_pDegContainer = m_framework->m_degradationMaskReceiver.getData();

    if (nullptr != l_pDegContainer)
    {
      vfc::uint32_t l_deg  = l_pDegContainer->m_Data;
      const vfc::uint32_t l_viewMode = m_framework->getCurrentScreenId();
      //0-Front, 1-Right, 2-Rear, 3-Left

      //check if hitch  and trailer overlays should be degraded
      if(pc::daddy::isBitSet(2, l_deg) && ((HITCH == m_overlayType) || (TRAILER_DRIVING_TUBE == m_overlayType)))
      {
        m_viewModeCamDegraded = 1;
      }
      //check wheel track degradation
      else if (pc::daddy::isBitSet(2, l_deg) && (VEHICLE_WHEEL_TRACK == m_overlayType) &&
      ((EScreenID_SINGLE_REAR_NORMAL_OFF_ROAD == static_cast<vfc::int32_t>(l_viewMode)) || (EScreenID_SINGLE_REAR_NORMAL_ON_ROAD == static_cast<vfc::int32_t>(l_viewMode))
      || (EScreenID_PARK_ASSIST_REAR == static_cast<vfc::int32_t>(l_viewMode)) || (EScreenID_SINGLE_REAR_TRAILER == static_cast<vfc::int32_t>(l_viewMode))))
      {
        m_viewModeCamDegraded = 1;
      }
      else if (pc::daddy::isBitSet(0, l_deg) && (VEHICLE_WHEEL_TRACK == m_overlayType) &&
      ((EScreenID_SINGLE_FRONT_NORMAL == static_cast<vfc::int32_t>(l_viewMode)) || (EScreenID_SINGLE_STB == static_cast<vfc::int32_t>(l_viewMode)) || (EScreenID_PARK_ASSIST_FRONT == static_cast<vfc::int32_t>(l_viewMode))))
      {
        m_viewModeCamDegraded = 1;
      }
      else
      {
        m_viewModeCamDegraded = 0;
      }
    }
}

void OverlayCallback::checkViewModeDoorDegradation()
{
    //check if trunk is open and degrade overlays if true
    const pc::daddy::DoorStateDaddy* const l_pDoorState = m_framework->m_doorStateReceiver.getData();

    if (nullptr != l_pDoorState)
    {
      m_rearCamPosTailgate = g_overlayData->m_SATCAM_rear_tailgatePosition;
      const vfc::uint32_t l_viewMode = m_framework->getCurrentScreenId();
      //0-Front, 1-Right, 2-Rear, 3-Left
      //check hitch and trailer trajectory overlays
      if((pc::daddy::CARDOORSTATE_OPEN == l_pDoorState->m_Data[pc::daddy::CARDOOR_TRUNK] && !m_rearCamPosTailgate)
          && ((HITCH == m_overlayType) || (TRAILER_DRIVING_TUBE == m_overlayType)))
      {
        m_viewModeDoorsDegraded = 1;
      }
      //check wheel track overlays
      else if ((pc::daddy::CARDOORSTATE_OPEN == l_pDoorState->m_Data[pc::daddy::CARDOOR_TRUNK] && !m_rearCamPosTailgate)
            && ( VEHICLE_WHEEL_TRACK == m_overlayType) &&
        ((EScreenID_SINGLE_REAR_NORMAL_OFF_ROAD == static_cast<vfc::int32_t>(l_viewMode)) || (EScreenID_SINGLE_REAR_NORMAL_ON_ROAD == static_cast<vfc::int32_t>(l_viewMode))
        || (EScreenID_PARK_ASSIST_REAR == static_cast<vfc::int32_t>(l_viewMode)) || (EScreenID_SINGLE_REAR_TRAILER == static_cast<vfc::int32_t>(l_viewMode))))
      {
        m_viewModeDoorsDegraded = 1;
      }
      else
      {
        m_viewModeDoorsDegraded = 0;
      }
    }
}

void OverlayCallback::checkTrailerStatus()
{
  //show overlays only in case trailer is connected and calibrated and the function is running
  const pc::daddy::TrailerConnectedDaddy* const l_pTrailerConnected = m_framework->m_trailerConnectionReceiver.getData();
  const cc::daddy::TrailerSvsDaddy*       const l_pTrailerSVS       = m_framework->asCustomFramework()->m_trailerSvsReceiver.getData();
  if ((nullptr != l_pTrailerConnected) && (nullptr != l_pTrailerSVS))
  {
    const bool l_trailerConnected = (pc::daddy::TRAILER_CONNECTED == l_pTrailerConnected->m_Data);
    const vfc::uint32_t l_trailerStatus = l_pTrailerSVS->m_Data.m_isTrailerCalibrated;
    const vfc::uint32_t l_isFunctionRunning = l_pTrailerSVS->m_Data.m_isFunctionRunning;
    if (l_trailerConnected && (1u == l_trailerStatus) && (1u == l_isFunctionRunning))
    {
      m_trailerDegraded = false;
    }
    else
    {
      m_trailerDegraded = true;
    }
  }
}

TopViewOverlayCullCallback::TopViewOverlayCullCallback(pc::core::Framework* f_framework, OverlayType f_overlayType)
    : OverlayCallback(f_framework, f_overlayType)
{
}

TopViewOverlayCullCallback::~TopViewOverlayCullCallback() = default;

void TopViewOverlayCullCallback::operator() (osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  osgUtil::CullVisitor* const l_cv = dynamic_cast<osgUtil::CullVisitor*> (f_nv); // PRQA S 3077  // PRQA S 3400
  if (l_cv != nullptr)
  {
    //check if the current camera has a "trajectoryOverlayAlwaysOn" user value set, and then check
    //if this user value is set to true
    bool l_trajectoryOverlayAlwaysOn = false;
    l_cv->getCurrentCamera()->getUserValue("trajectoryOverlayAlwaysOn", l_trajectoryOverlayAlwaysOn);  // PRQA S 3803  // PRQA S 3804
    if (true == l_trajectoryOverlayAlwaysOn)
    {
      traverse(f_node, f_nv);
    }
    else
    {
      OverlayCallback::operator()(f_node, f_nv);
    }
  }
}

} // namespace overlaycallback
} // namespace assets
} // namespace cc

