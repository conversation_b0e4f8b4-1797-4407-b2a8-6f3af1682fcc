
//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/button/inc/StartPauseConfirmButton.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/imgui/inc/imgui_manager.h"



namespace cc
{
namespace assets
{
namespace button
{
namespace confirmbutton
{

class StartPauseConfirmButtonSettings : public pc::util::coding::ISerializable
{
public:

    SERIALIZABLE(StartPauseConfirmButtonSettings) // PRQA S 2428
    {
        ADD_MEMBER(ButtonTextureSettings, parkinTexture);
        ADD_MEMBER(ButtonTextureSettings, parkoutTexture);
        ADD_MEMBER(ButtonTextureSettings, suspendTexture);
        ADD_MEMBER(ButtonTextureSettings, terminateTexture);
        ADD_MEMBER(osg::Vec2f, horiPos);
        ADD_MEMBER(osg::Vec2f, vertPos);
    }

    ButtonTextureSettings m_parkinTexture;
    ButtonTextureSettings m_parkoutTexture;
    ButtonTextureSettings m_suspendTexture;
    ButtonTextureSettings m_terminateTexture;

    osg::Vec2f m_horiPos{0.0f, 100.0f};
    osg::Vec2f m_vertPos{100.0f, 0.0f};

};

static pc::util::coding::Item<StartPauseConfirmButtonSettings> g_startPauseConfirmButtonSettings("StartPauseConfirmButton");


StartPauseConfirmButton::StartPauseConfirmButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView)
: Button(f_assetId, f_referenceView) // PRQA S 2759 // PRQA S 2323
, m_framework(f_framework) // PRQA S 2323
{
    setIconAtMiddle(true);
    setPositionHori(g_startPauseConfirmButtonSettings->m_horiPos);
    setPositionVert(g_startPauseConfirmButtonSettings->m_vertPos);
}


void StartPauseConfirmButton::onInvalid()
{
    setIconEnable(false);
}


void StartPauseConfirmButton::onUnavailable()
{
    setIconEnable(true);
    if      (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY   && m_buttonMode == ButtonMode::PARK_IN  ) { setTexturePath(g_startPauseConfirmButtonSettings->m_parkinTexture.m_day.m_UnavailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT && m_buttonMode == ButtonMode::PARK_IN  ) { setTexturePath(g_startPauseConfirmButtonSettings->m_parkinTexture.m_night.m_UnavailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY   && m_buttonMode == ButtonMode::PARK_OUT ) { setTexturePath(g_startPauseConfirmButtonSettings->m_parkoutTexture.m_day.m_UnavailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT && m_buttonMode == ButtonMode::PARK_OUT ) { setTexturePath(g_startPauseConfirmButtonSettings->m_parkoutTexture.m_night.m_UnavailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY   && m_buttonMode == ButtonMode::GUIDANCE ) { setTexturePath(g_startPauseConfirmButtonSettings->m_suspendTexture.m_day.m_UnavailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT && m_buttonMode == ButtonMode::GUIDANCE ) { setTexturePath(g_startPauseConfirmButtonSettings->m_suspendTexture.m_night.m_UnavailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY   && m_buttonMode == ButtonMode::TERMINATE) { setTexturePath(g_startPauseConfirmButtonSettings->m_terminateTexture.m_day.m_UnavailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT && m_buttonMode == ButtonMode::TERMINATE) { setTexturePath(g_startPauseConfirmButtonSettings->m_terminateTexture.m_night.m_UnavailableTexturePath); }
    else
    {
        setTexturePath(g_startPauseConfirmButtonSettings->m_parkinTexture.m_night.m_UnavailableTexturePath);
    }
}


void StartPauseConfirmButton::onAvailable()
{
    setIconEnable(true);
    if      (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY   && m_buttonMode == ButtonMode::PARK_IN  ) { setTexturePath(g_startPauseConfirmButtonSettings->m_parkinTexture.m_day.m_AvailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT && m_buttonMode == ButtonMode::PARK_IN  ) { setTexturePath(g_startPauseConfirmButtonSettings->m_parkinTexture.m_night.m_AvailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY   && m_buttonMode == ButtonMode::PARK_OUT ) { setTexturePath(g_startPauseConfirmButtonSettings->m_parkoutTexture.m_day.m_AvailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT && m_buttonMode == ButtonMode::PARK_OUT ) { setTexturePath(g_startPauseConfirmButtonSettings->m_parkoutTexture.m_night.m_AvailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY   && m_buttonMode == ButtonMode::GUIDANCE ) { setTexturePath(g_startPauseConfirmButtonSettings->m_suspendTexture.m_day.m_AvailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT && m_buttonMode == ButtonMode::GUIDANCE ) { setTexturePath(g_startPauseConfirmButtonSettings->m_suspendTexture.m_night.m_AvailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY   && m_buttonMode == ButtonMode::TERMINATE) { setTexturePath(g_startPauseConfirmButtonSettings->m_terminateTexture.m_day.m_AvailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT && m_buttonMode == ButtonMode::TERMINATE) { setTexturePath(g_startPauseConfirmButtonSettings->m_terminateTexture.m_night.m_AvailableTexturePath); }
    else
    {
        setTexturePath(g_startPauseConfirmButtonSettings->m_parkinTexture.m_night.m_AvailableTexturePath);
    }
}


void StartPauseConfirmButton::onPressed()
{
    setIconEnable(true);
    if      (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY   && m_buttonMode == ButtonMode::PARK_IN  ) { setTexturePath(g_startPauseConfirmButtonSettings->m_parkinTexture.m_day.m_PressedTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT && m_buttonMode == ButtonMode::PARK_IN  ) { setTexturePath(g_startPauseConfirmButtonSettings->m_parkinTexture.m_night.m_PressedTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY   && m_buttonMode == ButtonMode::PARK_OUT ) { setTexturePath(g_startPauseConfirmButtonSettings->m_parkoutTexture.m_day.m_PressedTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT && m_buttonMode == ButtonMode::PARK_OUT ) { setTexturePath(g_startPauseConfirmButtonSettings->m_parkoutTexture.m_night.m_PressedTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY   && m_buttonMode == ButtonMode::GUIDANCE ) { setTexturePath(g_startPauseConfirmButtonSettings->m_suspendTexture.m_day.m_PressedTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT && m_buttonMode == ButtonMode::GUIDANCE ) { setTexturePath(g_startPauseConfirmButtonSettings->m_suspendTexture.m_night.m_PressedTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY   && m_buttonMode == ButtonMode::TERMINATE) { setTexturePath(g_startPauseConfirmButtonSettings->m_terminateTexture.m_day.m_PressedTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT && m_buttonMode == ButtonMode::TERMINATE) { setTexturePath(g_startPauseConfirmButtonSettings->m_terminateTexture.m_night.m_PressedTexturePath); }
    else
    {
        setTexturePath(g_startPauseConfirmButtonSettings->m_parkinTexture.m_night.m_PressedTexturePath);
    }
}


void StartPauseConfirmButton::onReleased()
{
    if (checkTouchInsideResponseArea()) // PRQA S 2759
    {
        IMGUI_LOG("Buttons", "StartPauseConfirmButton", "Pressed");
        switch (m_buttonMode)
        {
            case ButtonMode::PARK_IN:
            case ButtonMode::PARK_OUT:
            {
                if (cc::daddy::CustomDaddyPorts::sm_StartPauseConfirmButtonPressDaddy_SenderPort.isConnected())
                {
                    auto& container = cc::daddy::CustomDaddyPorts::sm_StartPauseConfirmButtonPressDaddy_SenderPort.reserve();
                    container.m_Data = true;
                    cc::daddy::CustomDaddyPorts::sm_StartPauseConfirmButtonPressDaddy_SenderPort.deliver();
                }
                break;
            }
            case ButtonMode::GUIDANCE:
            case ButtonMode::TERMINATE:
            {
                if (cc::daddy::CustomDaddyPorts::sm_SuspendButtonPressDaddy_SenderPort.isConnected())
                {
                    auto& container = cc::daddy::CustomDaddyPorts::sm_SuspendButtonPressDaddy_SenderPort.reserve();
                    container.m_Data = true;
                    cc::daddy::CustomDaddyPorts::sm_SuspendButtonPressDaddy_SenderPort.deliver();
                }
                break;
            }
            default:
            {
                break;
            }
        }
    }
}


void StartPauseConfirmButton::update()
{
    setSettingModifiedCount(g_startPauseConfirmButtonSettings->getModifiedCount());

    bool allPortsHaveData = true;
    GET_PORT_DATA(touchStatusContainer,       m_framework->asCustomFramework()->m_HUTouchTypeReceiver, allPortsHaveData)
    GET_PORT_DATA(huRotateStatusContainer,    m_framework->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver, allPortsHaveData)
    GET_PORT_DATA(hmiDataContainer,           m_framework->asCustomFramework()->m_hmiDataReceiver, allPortsHaveData)
    GET_PORT_DATA(dayNightThemeContainer,     m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, allPortsHaveData)
    GET_PORT_DATA(parkStatusContainer,        m_framework->asCustomFramework()->m_parkHmiParkingStatusReceiver, allPortsHaveData)
    GET_PORT_DATA(freeparkingActiveContainer, m_framework->asCustomFramework()->m_freeparkingActiveReceiver, allPortsHaveData)
    GET_PORT_DATA(driverIndContainer,         m_framework->asCustomFramework()->m_parkHmiParkDriverIndReceiver, allPortsHaveData)
    GET_PORT_DATA(driverIndExtContainer,      m_framework->asCustomFramework()->m_parkHmiParkDriverIndExtReceiver, allPortsHaveData)
    GET_PORT_DATA(apaParkModeContainer,       m_framework->asCustomFramework()->m_parkHmiParkAPAPARKMODEReceiver, allPortsHaveData)
    GET_PORT_DATA(parkTypeContainer,          m_framework->asCustomFramework()->m_parkHmiParkParkngTypeSeldReceiver, allPortsHaveData)
    GET_PORT_DATA(recoverIndContainer,        m_framework->asCustomFramework()->m_parkHmiParkingRecoverIndReceiver, allPortsHaveData)
    GET_PORT_DATA(driverSearchContainer,      m_framework->asCustomFramework()->m_ParkDriverIndSearchReceiver, allPortsHaveData)
    GET_PORT_DATA(quitIndContainer,           m_framework->asCustomFramework()->m_parkHmiParkingQuitIndReceiver, allPortsHaveData)
    GET_PORT_DATA(quitIndExtContainer,        m_framework->asCustomFramework()->m_parkHmiParkingQuitIndExtReceiver, allPortsHaveData)
    GET_PORT_DATA(brakeReleasedContainer,     m_framework->asCustomFramework()->m_parkHmiParkBreakPedalBeenReleasedBfReceiver, allPortsHaveData)
    GET_PORT_DATA(brakeAppliedContainer,      m_framework->asCustomFramework()->m_parkHmiParkParkbrkPedlAppldReceiver, allPortsHaveData)
    GET_PORT_DATA(parkableStatusContainer,    m_framework->asCustomFramework()->m_FreeParkingParkable_ReceiverPort, allPortsHaveData)

    if (!allPortsHaveData)
    {
        return;
    }

    const bool touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
    setDayNightTheme(dayNightThemeContainer->m_Data);
    setRotateTheme(static_cast<cc::target::common::EThemeTypeHU>(huRotateStatusContainer->m_Data));
    setPositionHori(g_startPauseConfirmButtonSettings->m_horiPos);
    setPositionVert(g_startPauseConfirmButtonSettings->m_vertPos);
    setHuX(hmiDataContainer->m_Data.m_huX);
    setHuY(hmiDataContainer->m_Data.m_huY);
    setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    m_parkStatus = parkStatusContainer->m_Data;

    const auto l_driverIndExt = driverIndExtContainer->m_Data;
    const auto l_driverInd = driverIndContainer->m_Data;
    const auto l_recoverInd = recoverIndContainer->m_Data;
    const auto l_apaParkMode = apaParkModeContainer->m_Data;
    const auto l_parkType = parkTypeContainer->m_Data;
    const auto l_driverSearch = driverSearchContainer->m_Data;
    const auto l_freeparkingActive = freeparkingActiveContainer->m_Data;
    const auto l_quitInd = quitIndContainer->m_Data;
    const auto l_quitIndExt = quitIndExtContainer->m_Data;
    const auto l_brakeReleasedBefore = brakeReleasedContainer->m_Data;
    const auto l_brakeApplied = brakeAppliedContainer->m_Data;
    const auto l_parkable = parkableStatusContainer->m_Data;

    ButtonState currentState = ButtonState::INVALID;
    const auto l_prevButtonMode = m_buttonMode;
    //* ParkOut
    // Req_398: ParkOutDeactivate Searching, PARKDRVEXT_ConfirmTheParkingDirection, PARKING_OUT, APAPARKMODE_APA
    if (m_parkStatus == ParkStatusType::PARK_Searching &&
        l_driverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_ConfirmTheParkingDirection &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_OUT &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA)
    {
        m_buttonMode = PARK_OUT; currentState = UNAVAILABLE;
    }
    // Req_463: ParkOutActive AssistStandby, PARKDRV_Confirm_Press_DM, APAPARKMODE_APA, PARKING_OUT
    else if (
        m_parkStatus == ParkStatusType::PARK_AssistStandby &&
        l_driverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_OUT)
    {
        m_buttonMode = PARK_OUT; currentState = AVAILABLE;
    }
    // Req_406
    else if (
        l_driverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_OUT)
    {
        m_buttonMode = PARK_OUT; currentState = UNAVAILABLE;
    }
    // Req_405
    else if (
        m_parkStatus == ParkStatusType::PARK_AssistStandby &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_OUT &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA &&
        l_driverSearch == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForDriverOperateGear)
    {
        // note: notice rolling is disabled so no need to check for it
        m_buttonMode = PARK_OUT;
        currentState = (l_brakeApplied == cc::target::common::EBrkPedlAppldFlg::BrkPedl_Applied) ? AVAILABLE : UNAVAILABLE;
    }
    // Req_813
    else if (
        m_parkStatus == ParkStatusType::PARK_Searching &&
        l_driverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_ConfirmTheParkingDirection &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_OUT)
    {
        m_buttonMode = PARK_OUT; currentState = AVAILABLE;
    }
    //* ParkIn
    // Req_447 ParkInActive Searching, PARKING_IN, PARKREC_NoPrompt, PARKDRVSEARCH_NoRequest, freeParkingActive
    else if (
        m_parkStatus == ParkStatusType::PARK_Searching &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_IN &&
        l_driverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_NoRequest &&
        l_driverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_NoRequest &&
        l_driverSearch == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_NoRequest &&
        l_freeparkingActive)
    {
        m_buttonMode = PARK_IN; currentState = UNAVAILABLE;
    }
    // Req_774 freeparking AssistStandby PARKING_IN, APAPARKMODE_APA, PARKDRVSEARCH_WaitForDriverOperateGear, !noticeRolling
    else if (
        m_parkStatus == ParkStatusType::PARK_AssistStandby &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_IN &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA &&
        l_driverSearch == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForDriverOperateGear &&
        l_freeparkingActive)
    {
        // note: notice rolling is disabled so no need to check for it
        m_buttonMode = PARK_IN; currentState = UNAVAILABLE;
    }
    // Req_449 ParkIn unavailable Seearching freeparkingActive, APA, PARKDRV_Request_Stop, PARKING_IN
    else if (
        m_parkStatus == ParkStatusType::PARK_Searching &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_IN &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA &&
        l_driverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop &&
        l_freeparkingActive)
    {
        m_buttonMode = PARK_IN; currentState = UNAVAILABLE;
    }
    // Req_? ParkInActive AssistStandby, PARKING_IN, PARKREC_NoPrompt, PARKDRV_Confirm_Press_DM, freeParkingActive
    else if (
        m_parkStatus == ParkStatusType::PARK_AssistStandby &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_IN &&
        l_recoverInd == cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt &&
        l_driverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM &&
        l_freeparkingActive)
    {
        m_buttonMode = PARK_IN; currentState = (l_parkable) ? AVAILABLE : UNAVAILABLE;
    }
    // Req 389 ParkInDeactivate AssistStandby PARKING_IN, APAPARKMODE_APA, PARKDRVSEARCH_WaitForDriverOperateGear, !noticeRolling
    else if (
        m_parkStatus == ParkStatusType::PARK_AssistStandby &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_IN &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA &&
        l_driverSearch == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForDriverOperateGear)
    {
        // note: notice rolling is disabled so no need to check for it
        m_buttonMode = PARK_IN;
        currentState = (l_brakeApplied == cc::target::common::EBrkPedlAppldFlg::BrkPedl_Applied) ? AVAILABLE : UNAVAILABLE;
    }
    // Req_369, Req_377, Req_378, Req_382 ParkInActive AssistStandby, PARKING_IN, APAPARKMODE_APA, PARKDRV_Confirm_Press_DM
    else if (
        m_parkStatus == ParkStatusType::PARK_AssistStandby &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_IN &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA &&
        l_driverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM)
    {
        m_buttonMode = PARK_IN; currentState = AVAILABLE;
    }
    // Req_371 ParkInActive AssistStandby, PARKING_IN, APAPARKMODE_APA, PARKDRV_SmallParkSlot
    else if (
        m_parkStatus == ParkStatusType::PARK_AssistStandby &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_IN &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA &&
        l_driverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_SmallParkSlot)
    {
        m_buttonMode = PARK_IN; currentState = AVAILABLE;
    }
    // Req 391 GuidanceActive ParkIn
    else if (
        m_parkStatus == ParkStatusType::PARK_Guidance_active &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_IN &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA &&
        l_driverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_PayAttentionToSurrounding)
    {
        // Req_712
        if (l_brakeReleasedBefore && l_brakeApplied == cc::target::common::EBrkPedlAppldFlg::BrkPedl_Applied)
        {
            m_buttonMode = GUIDANCE; currentState = AVAILABLE;
        }
    }
    // Req 397 GuidanceActive ParkOut
    else if (
        m_parkStatus == ParkStatusType::PARK_Guidance_active &&
        l_parkType == cc::target::common::EParkngTypeSeld::PARKING_OUT &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA &&
        l_driverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_PayAttentionToSurrounding)
    {
        // Req_713
        if (l_brakeReleasedBefore && l_brakeApplied == cc::target::common::EBrkPedlAppldFlg::BrkPedl_Applied)
        {
            m_buttonMode = GUIDANCE; currentState = AVAILABLE;
        }
    }
    // Req_781, Req_782
    else if (
        m_parkStatus == ParkStatusType::PARK_Searching &&
        cc::target::common::EAPAPARKMODE_::APAPARKMODE_APA == l_apaParkMode &&
        (cc::target::common::EParkngTypeSeld_::PARKING_OUT == l_parkType || cc::target::common::EParkngTypeSeld::PARKING_IN == l_parkType) &&
        cc::target::common::EPARKDriverIndR2L::PARKDRV_FunctionOff == l_driverInd &&
        cc::target::common::EPARKQuitIndR2L::PARKQUIT_ExternalECUFailure == l_quitInd)
    {
        m_buttonMode = TERMINATE; currentState = AVAILABLE;
    }
    // Req_502
    else if (
        m_parkStatus == ParkStatusType::PARK_Searching &&
        l_driverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_FunctionOff &&
        (l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA || l_quitIndExt == cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_Nofault))
    {
        m_buttonMode = TERMINATE; currentState = AVAILABLE;
    }
    // Req_502
    else if (
        m_parkStatus == ParkStatusType::PARK_AssistStandby &&
        l_driverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_FunctionOff &&
        (l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA || l_quitIndExt == cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_Nofault))
    {
        m_buttonMode = TERMINATE; currentState = AVAILABLE;
    }
    // Req_596
    else if (
        m_parkStatus == ParkStatusType::PARK_Searching &&
        l_driverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_FunctionOff &&
        l_quitIndExt == cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_DriveModeUnsuitable &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA)
    {
        m_buttonMode = TERMINATE; currentState = AVAILABLE;
    }
    // Req_594
    else if (
        m_parkStatus == ParkStatusType::PARK_AssistStandby &&
        l_driverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_FunctionOff &&
        l_quitIndExt == cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_DriveModeUnsuitable &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA)
    {
        m_buttonMode = TERMINATE; currentState = AVAILABLE;
    }
    // PARK TERMINATED
    else if (
        m_parkStatus == ParkStatusType::PARK_Terminated &&
        (l_parkType == cc::target::common::EParkngTypeSeld::PARKING_IN || l_parkType == cc::target::common::EParkngTypeSeld::PARKING_OUT) &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA)
    {
        if (l_driverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_HeavyRain)
        {
            m_buttonMode = TERMINATE; currentState = AVAILABLE;
        }
        switch (l_quitInd) // PRQA S 4018
        {
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_None:
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_UnsafeBehavior:
            // case cc::target::common::EPARKQuitIndR2L::PARKQUIT_DoorOpen:
            // case cc::target::common::EPARKQuitIndR2L::PARKQUIT_OtherReason:
            // case cc::target::common::EPARKQuitIndR2L::PARKQUIT_SeatBeltUnbuckle: //Req_419
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_ExcessiveSlope: //Req_420
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_DriverOverride: //Req_418
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_RoutePlanningFailure: //Req_414
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_VehicleSpeedOverthreshold: //Req_415
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_APSTimeout: //Req_417
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_CurrentStepNumberOverThreshold: //Req_421
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_SpaceIsLimitedInParkOutMode: //Req_423
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_EPBFailure: //Req_422
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_SCUFailure: //Req_427
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_APAFailure: //Req_428
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_InvalidVehicleSpeed: //Req_410
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_ExternalECUFailure: //Req_410
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_SurroundView:
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_ABSTCSESPACCAEBActive: //Req_424
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_ESCFailure: //Req_425
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_EPSFailure: //Req_429
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_VehicleBlock: //Req_509
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_InterruptNumberOverThreshold: //Req_507
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_RadarDirty: //Req_508
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_EPBActive: //Req_506
            case cc::target::common::EPARKQuitIndR2L::PARKQUIT_ChargingGunActive:
            {
                m_buttonMode = TERMINATE; currentState = AVAILABLE;
                break;
            }
            default: 
            {
                break;
            }
        }

        switch (l_quitIndExt) // PRQA S 4018
        {
            // case cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_TrailerHitchConnected:  //Req_591
            case cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_DriveModeUnsuitable:  //Req_592
            case cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_TemperatureSensorFault:
            case cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_LowerTirePressure:
            case cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_CollisionHappened:
            {
                m_buttonMode = TERMINATE; currentState = AVAILABLE; break;
            }
            default:
            {
                break;
            }
        }
    }
    // PARK COMPLETED Req_394, Req_395
    else if (
        m_parkStatus == ParkStatusType::PARK_Completed &&
        (l_parkType == cc::target::common::EParkngTypeSeld::PARKING_IN || l_parkType == cc::target::common::EParkngTypeSeld::PARKING_OUT) &&
        (l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) &&
        (l_driverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_ParkingHasBeenCompleted) )
    {
        m_buttonMode = TERMINATE; currentState = AVAILABLE;
    }
    else
    {
        m_buttonMode = INVALID;
    }

    if (m_buttonMode == INVALID)
    {
        setState(ButtonState::INVALID);
    }
    else
    {
        if (checkTouchInsideResponseArea() && (touchStatus() == TOUCH_DOWN || touchStatus() == TOUCH_MOVE) && currentState == AVAILABLE) // PRQA S 2759
        {
            currentState = PRESSED;
        }
        if (touchStatusChanged && touchStatus() == TOUCH_UP && getState() == PRESSED)
        {
            currentState = RELEASED;
        }
        if (l_prevButtonMode != m_buttonMode)
        {
            // hack to update the texture
            setState(ButtonState::INVALID);
        }
        setState(currentState);
    }

    if (currentState != RELEASED)
    {
        if (cc::daddy::CustomDaddyPorts::sm_StartPauseConfirmButtonPressDaddy_SenderPort.isConnected())
        {
            auto& container = cc::daddy::CustomDaddyPorts::sm_StartPauseConfirmButtonPressDaddy_SenderPort.reserve();
            container.m_Data = false;
            cc::daddy::CustomDaddyPorts::sm_StartPauseConfirmButtonPressDaddy_SenderPort.deliver();
        }
        if (cc::daddy::CustomDaddyPorts::sm_SuspendButtonPressDaddy_SenderPort.isConnected())
        {
            auto& container = cc::daddy::CustomDaddyPorts::sm_SuspendButtonPressDaddy_SenderPort.reserve();
            container.m_Data = false;
            cc::daddy::CustomDaddyPorts::sm_SuspendButtonPressDaddy_SenderPort.deliver();
        }
    }
}

} // namespace confirmbutton
} // namespace button
} // namespace assets
} // namespace cc
