//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/augmentedview/inc/TransitionWaveAnimation.h"
#include "cc/assets/augmentedview/inc/PeriodicScanAnimation.h"
#include "cc/assets/augmentedview/inc/AugmentedViewTransition.h"

#include <limits>
#include <algorithm>
#include <vector>
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace augmentedview
{

PeriodicScanAnimation::PeriodicScanAnimation(vfc::float32_t f_animationDuration, vfc::float32_t f_maxWaveRadius, vfc::float32_t f_interBurstTime, vfc::uint32_t f_burstSize,  vfc::float32_t f_intraBurstTime)
  : m_activeWaves() // PRQA S 2323
  , m_lastWaveEmissionTime() // PRQA S 2323
  , m_currentBurstCount() // PRQA S 2323
  , m_burstSize() // PRQA S 2323
  , m_interBurstTime() // PRQA S 2323
  , m_intraBurstTime() // PRQA S 2323
  , m_animationDuration() // PRQA S 2323
  , m_maxWaveRadius() // PRQA S 2323
  , m_waveLength() // PRQA S 2323
  , m_stopAnimation() // PRQA S 2323
  , m_scanWaveAnimations()
{
  reset(f_animationDuration, f_maxWaveRadius, f_interBurstTime, f_burstSize, f_intraBurstTime);
}

PeriodicScanAnimation::PeriodicScanAnimation()
  : m_activeWaves() // PRQA S 2323
  , m_lastWaveEmissionTime() // PRQA S 2323
  , m_currentBurstCount() // PRQA S 2323
  , m_burstSize() // PRQA S 2323
  , m_interBurstTime() // PRQA S 2323
  , m_intraBurstTime() // PRQA S 2323
  , m_animationDuration() // PRQA S 2323
  , m_maxWaveRadius() // PRQA S 2323
  , m_waveLength() // PRQA S 2323
  , m_stopAnimation() // PRQA S 2323
  , m_scanWaveAnimations()
{
  reset(3.0f, 40.0f, 4.0f, 1u, 1.f);
}

void PeriodicScanAnimation::reset(vfc::float32_t f_animationDuration, vfc::float32_t f_maxWaveRadius, vfc::float32_t f_interBurstTime, vfc::uint32_t f_burstSize,  vfc::float32_t f_intraBurstTime)
{
  assert(f_animationDuration > 0.0f);
  assert(f_burstSize > 0u);
  assert(f_interBurstTime >= 0.0f);
  assert(f_intraBurstTime >= 0.0f);
  assert((f_intraBurstTime > 0.0f && f_burstSize > 1u) || f_interBurstTime > 0.0f);
  f_burstSize = std::max(f_burstSize, 1u);

  m_animationDuration = f_animationDuration;
  m_maxWaveRadius = f_maxWaveRadius;
  m_stopAnimation = false;

  // Calculate the max. number of scan waves visible at a time
  const vfc::float32_t totalBurstTime = f_intraBurstTime*(static_cast<vfc::float32_t>(f_burstSize)-1.0f)+f_interBurstTime;
  const vfc::uint32_t fullConcurrentBurstWaves = static_cast<vfc::uint32_t>(std::floor(f_animationDuration/totalBurstTime));  //PRQA S 3016
  const vfc::float32_t partialBurstTime = f_animationDuration - totalBurstTime*static_cast<vfc::float32_t>(fullConcurrentBurstWaves);
  vfc::uint32_t partialBurstWaves = (f_burstSize > 1u) ? static_cast<vfc::uint32_t>(std::ceil(partialBurstTime/f_intraBurstTime)) : static_cast<vfc::uint32_t>(std::ceil(partialBurstTime)); // PRQA S 3016
  partialBurstWaves = std::min(f_burstSize, partialBurstWaves); // PRQA S 3803

  // std::size_t numConcurrentAnimations = fullConcurrentBurstWaves + partialBurstWaves;

  m_scanWaveAnimations.resize(1);
  for (std::size_t i = 0u; i < m_scanWaveAnimations.size(); ++i) // PRQA S 4297 // PRQA S 4687
  {
    m_scanWaveAnimations[i].reset(f_animationDuration, f_maxWaveRadius, true);
  }

  // Allocate "numConcurrentAnimations" ring instances w/o alpha blending
  m_lastWaveEmissionTime = -std::numeric_limits<vfc::float32_t>::max();

  m_activeWaves = 0u;
  m_currentBurstCount = 0u;
  m_burstSize = f_burstSize;
  m_interBurstTime = f_interBurstTime;
  m_intraBurstTime = f_intraBurstTime;
}

std::size_t PeriodicScanAnimation::getMaxConcurrentWaves() const
{
  return m_scanWaveAnimations.size();
}

std::size_t PeriodicScanAnimation::getActiveWaves() const
{
  return m_activeWaves;
}

vfc::float32_t PeriodicScanAnimation::getWaveRadius(std::size_t f_index) const
{
  assert(f_index < m_activeWaves);
  return getActiveWave(f_index).getWaveRadius();
}

vfc::float32_t PeriodicScanAnimation::getWaveAlphaFade(std::size_t f_index) const
{
  assert(f_index < m_activeWaves);
  return getActiveWave(f_index).getWaveAlphaFade();
}

vfc::float32_t PeriodicScanAnimation::getCameraFadeFactor(std::size_t f_index) const
{
  return getActiveWave(f_index).getCameraFadeFactor();
}

// this will stop the re-start of the animation
void PeriodicScanAnimation::setStopAnimation(bool f_stopAnimation)
{
  m_stopAnimation = f_stopAnimation;
}

void PeriodicScanAnimation::update(vfc::float32_t f_currentTime)
{
  m_activeWaves = 0u;
  for (std::size_t i = 0u; i < m_scanWaveAnimations.size(); ++i) // PRQA S 4297 // PRQA S 4687
  {
    auto& ta = m_scanWaveAnimations[i];
    ta.update(f_currentTime);
    if (ta.isRunning())
    {
      ++m_activeWaves;
    }
  }

  const vfc::float32_t elapsedTime = f_currentTime - m_lastWaveEmissionTime;

  const bool isBursting = m_currentBurstCount > 0u;

  const vfc::float32_t emissionDelay = isBursting ? m_intraBurstTime : m_interBurstTime;

  if (elapsedTime >= emissionDelay)
  {
    // Find unused animation controller
    for (std::size_t i = 0u; i < m_scanWaveAnimations.size(); ++i) // PRQA S 4297 // PRQA S 4687
    {
      auto& ta = m_scanWaveAnimations[i];
      //! do not restart animation if stop animation has been requested
      if (!ta.isRunning() && !m_stopAnimation)
      {
        m_lastWaveEmissionTime = f_currentTime;
        ta.start(m_animationDuration, m_maxWaveRadius, true);
        ta.update(f_currentTime);
        ++m_activeWaves;

        m_currentBurstCount = (m_currentBurstCount + 1u)%m_burstSize;
        break;
      }
    }
  }
}

const TransitionWaveAnimation& PeriodicScanAnimation::getActiveWave(std::size_t f_index) const
{
  assert(f_index < m_activeWaves);
  std::size_t i = 0u;
  for (std::size_t j = 0u; j < m_scanWaveAnimations.size(); ++j) // PRQA S 4687
  {
    const auto& ta = m_scanWaveAnimations[j];
    if (ta.isRunning())
    {
      if (i == f_index)
      {
        return ta;
      }
      ++i;
    }
  }
  XLOG_ERROR(g_AppContext, "getActiveWave error!");
  //assert(false);
  return m_scanWaveAnimations.front();
}

} // namespace augmentedview
} // namespace asset
} // namespace cc
