//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EXTERNAL Castellane Florian (Altran, CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  PanoramaView.cpp
/// @brief
//=============================================================================


#include "cc/views/panoramaview/inc/PanoramaView.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/core/inc/VideoTexture.h"
#include "pc/svs/core/inc/View.h"
#include "pc/svs/c2w/inc/CylDistCorr.h"
#include "pc/svs/factory/inc/SV3DAlgorithm.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"

#include "pc/svs/util/cli/inc/BaseCommands.h"
#include "pc/generic/util/cli/inc/CommandRegistry.h"
#include "pc/generic/util/cli/inc/CommandLineInterface.h"

#include "osg/Vec4i" // PRQA S 1060

using pc::util::logging::g_EngineContext;

namespace cc
{
namespace views
{
namespace panoramaview
{

pc::util::coding::Item<PanoramaViewData> g_panoViewDataFront("PanoramaViewFront");
pc::util::coding::Item<PanoramaViewData> g_panoViewDataRear("PanoramaViewRear");


//! const for cam frame rotation offset
static constexpr vfc::float32_t CAM_FRAME_OFFSETS[pc::core::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS] =
{
    0.f, // front
  -90.f, // right
  180.f, // rear
   90.f  // left
};

pc::core::sysconf::Cameras getCamId(PanoramaView::Direction f_direction)
{
  if (PanoramaView::PANORAMAVIEW_FRONT == f_direction)
  {
    return pc::core::sysconf::FRONT_CAMERA;
  }
  else
  {
    return pc::core::sysconf::REAR_CAMERA;
  }
}

const PanoramaViewData& getViewData(PanoramaView::Direction f_direction)
{
  if (PanoramaView::PANORAMAVIEW_FRONT == f_direction)
  {
    return g_panoViewDataFront.data();
  }
  else
  {
    return g_panoViewDataRear.data();
  }
}

//!
//! PanoramaView
//!
PanoramaView::PanoramaView(pc::core::Framework* f_framework, Direction f_direction, // PRQA S 2988
  const std::string& f_name,
  const pc::core::Viewport& f_viewport)
  : pc::core::View(f_name, f_viewport) // PRQA S 2323
  , m_projObj{new pc::util::osgx::ProjectionObjects}
  , m_greyGeode{new osg::Geode()}
  , m_framework(f_framework) // PRQA S 2323
  , m_camId(getCamId(f_direction)) // PRQA S 2323
  , m_CylDistCorr(getViewData(f_direction).m_cylinder) // PRQA S 2323
  , m_pVertexArray(nullptr) // PRQA S 2323
  , m_pTexCoord(nullptr) // PRQA S 2323
  , m_resH(0u) // PRQA S 2323
  , m_resW(0u) // PRQA S 2323
{
  const PanoramaViewData& l_pData = getViewData(f_direction);

  setRenderOrder(osg::Camera::POST_RENDER);
  setViewMatrix(osg::Matrixf::identity());
  setProjectionMatrixAsOrtho2D(
    l_pData.m_horizontalStart,
    l_pData.m_horizontalEnd,
    l_pData.m_verticalStart,
    l_pData.m_verticalEnd);

  // panorama wall dimensions
  m_projObj->setName("ProjectionObjects for Panorama view objects");

  // the rear and front panorama view are treated differently so two projection walls are needed
  m_projObj->addWall(osg::Vec3f(0.f, 0.f, 0.f),
      osg::Vec3f(l_pData.m_planeWidth, 0.f, 0.f),
      osg::Vec3f(0.f, l_pData.m_planeHeight, 0.f),
      l_pData.m_resolutionHorizontal,
      l_pData.m_resolutionVertical);

  // geode for grey Texture
  m_greyGeode->setName("Grey Texture Geode");
  osg::Geometry* const l_quad = osg::createTexturedQuadGeometry(
    osg::Vec3f(0.0f, 0.0f, 0.0f), // corner
    osg::Vec3f(l_pData.m_planeWidth, 0.0f, 0.0f), // width
    osg::Vec3f(0.0f, l_pData.m_planeHeight, 0.0f)); // height

  osg::Vec4Array* const colours = new osg::Vec4Array(1u);
  (*colours)[0u].set(0.227f,0.227f,0.227f,1.0f); // Hex 3a3a3a
  l_quad->setColorArray(colours, osg::Array::BIND_OVERALL);
  l_quad->setUseDisplayList(false);
  l_quad->setUseVertexBufferObjects(true);

  m_greyGeode->addDrawable(l_quad); // PRQA S 3803

  osg::StateSet* const l_pStateSet = m_greyGeode->getOrCreateStateSet();
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicColor");
  l_basicTexShader.apply(l_pStateSet); // PRQA S 3803
  l_pStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
  l_pStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);

  osg::Group::addChild(m_greyGeode); // PRQA S 3803
  osg::Group::addChild(m_projObj); // PRQA S 3803
  m_projObj->setNodeMask(~0u);
  m_greyGeode->setNodeMask(0u);

  //retrieve the wall vertex array
  osg::Geode* const l_pGeodeWall = m_projObj->getChild(0u)->asGeode();
  osg::Geometry* const l_pGeometryWall = l_pGeodeWall->getDrawable(0u)->asGeometry();

  if( nullptr == l_pGeometryWall )
  {
    XLOG_ERROR(g_EngineContext, "could not access the panorama wall geometry");
  }
  else
  {
    m_pVertexArray  = static_cast<osg::Vec3Array*>(l_pGeometryWall->getVertexArray()); // PRQA S 3076
    m_pTexCoord     = static_cast<osg::Vec2Array*>(l_pGeometryWall->getTexCoordArray(0u)); // PRQA S 3076
    m_resH          = l_pData.m_resolutionVertical;
    m_resW          = l_pData.m_resolutionHorizontal;

    assert(m_pVertexArray); //sanity check before dereferencing
    //calculate the cylindric texture coordinates including horizontal stretching/squeezing
    computeCylinderTextureCoordinates();

    addUpdateCallback( new PanoramaViewUpdateCallback( m_framework ));
  }

  osg::StateSet* const l_stateSet = m_projObj->getOrCreateStateSet();
  l_stateSet->setMode(GL_BLEND, osg::StateAttribute::OFF);
  l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
  l_stateSet->setTextureAttribute(0u, m_framework->getVideoTexture()->getCameraTexture(static_cast<vfc::uint32_t>(m_camId)));
  pc::core::setCameraTextureShader(l_stateSet, m_camId); // PRQA S 3000

  if (PANORAMAVIEW_REAR != f_direction)
  {
    osg::Matrixf l_mirrorTheWorld;
    l_mirrorTheWorld(0, 0) = -1.f;
    setProjectionMatrix(getProjectionMatrix() * l_mirrorTheWorld);
  }
}


PanoramaView::~PanoramaView() = default;

void PanoramaView::updateUsedTexture() // PRQA S 4211
{
  // read degradation and deactivation mask
  vfc::uint32_t l_degradationMaskTarget = 0u;
  const pc::daddy::CameraDegradationMaskDaddy* const l_degradationMaskDaddy = m_framework->m_degradationMaskReceiver.getData();
  if (l_degradationMaskDaddy != nullptr)
  {
      l_degradationMaskTarget = l_degradationMaskDaddy->m_Data;
  }
  vfc::uint32_t l_deactivationMaskTarget = 0u;
  const pc::daddy::CameraDeactivationMaskDaddy* const l_deactivationMaskDaddy = m_framework->m_deactivationMaskReceiver.getData();
  if (l_deactivationMaskDaddy != nullptr)
  {
      l_deactivationMaskTarget = l_deactivationMaskDaddy->m_Data;
  }
  const vfc::uint32_t l_camId_u = static_cast<vfc::uint32_t>(m_camId);
  // activate and deactivate the panoramer view geode according to the masks
  if(static_cast<bool>(l_degradationMaskTarget & (static_cast<vfc::uint32_t>(1u) << l_camId_u)))
  {
    // SHADER_CAM_OFF
    m_projObj->setNodeMask(0u);
    m_greyGeode->setNodeMask(~0u);
  }
  else
  {
    if(static_cast<bool>(l_deactivationMaskTarget & (static_cast<vfc::uint32_t>(1u) << l_camId_u)))
    {
      // SHADER_CAM_DISABLED
      m_projObj->setNodeMask(0u);
      m_greyGeode->setNodeMask(~0u);
    }
    else
    {
      // SHADER_CAM_ON
      m_projObj->setNodeMask(~0u);
      m_greyGeode->setNodeMask(0u);
    }
  }
}

void PanoramaView::computeCylinderTextureCoordinates() // PRQA S 4211
{
  assert(m_pTexCoord);
  assert(m_pVertexArray);

  if (true == m_framework->m_cameraCalibrationReceiver.isConnected())
  {
    const pc::daddy::CameraCalibrationDaddy* const l_pCalibDaddy = m_framework->m_cameraCalibrationReceiver.getData();
    if (nullptr != l_pCalibDaddy)
    {
      const vfc::uint32_t l_camId_u = static_cast<vfc::uint32_t>(m_camId);
      const pc::c2w::SatCam& l_satCam = l_pCalibDaddy->m_Data[l_camId_u];

      const vfc::uint32_t l_arraySize = static_cast<vfc::uint32_t>(m_pVertexArray->size());
      osg::ref_ptr<osg::Vec3Array> const l_cylDistArray = new osg::Vec3Array(l_arraySize);

      //Remove the vertical and horizontal stretching for now
      //vertical stretching
      //m_CylDistCorr.verticalStretch(l_camId_u, l_cylDistArray.get(), f_resH, f_resW);

      //horizontal stretch
      //m_CylDistCorr.horizontalStretch(l_camId_u, l_cylDistArray.get(), f_resH, f_resW);

      //create cylinder coordinates
      m_CylDistCorr.createCylinderPoints(l_cylDistArray, m_resH, m_resW);


      //! calculate texture coordinates
      osg::Vec3f l_rotate = l_satCam.getExtrinsicCalibration().getRotation();
      osg::Matrixf l_refFrameRotation;
      l_refFrameRotation.makeRotate(osg::inDegrees(-l_rotate.x() + CAM_FRAME_OFFSETS[l_camId_u]), osg::Z_AXIS,
                                    osg::inDegrees(-l_rotate.y()), osg::Y_AXIS,
                                    osg::inDegrees(-l_rotate.z()), osg::X_AXIS);

      for (vfc::uint32_t i = 0u; i < l_arraySize; ++i)
      {
        osg::Vec3f const l_cylinder = osg::Vec3f( (*l_cylDistArray)[i].z(), -((*l_cylDistArray)[i].x()), -(*l_cylDistArray)[i].y());
        const osg::Vec3f l_vecInCam = l_cylinder * l_refFrameRotation;
        (*m_pTexCoord)[i] = pc::factory::normalizeImageCoord(l_satCam.cam2img(l_vecInCam));
      }
      m_pTexCoord->dirty();
    }
  }
}

bool PanoramaView::updateCalibration()
{
  if(nullptr != m_pVertexArray && nullptr != m_pTexCoord)
  {
    //calculate the cylindric texture coordinates including horizontal stretching/squeezing
    computeCylinderTextureCoordinates();
    return true;
  }
  else
  {
    return false;
  }
}

void PanoramaView::traverse(osg::NodeVisitor& f_nv)
{
  if ((osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType()))
  {
    updateUsedTexture();
  }
  pc::core::View::traverse(f_nv);
}

//!
//! PanoReadCommand
//!
class PanoReadCommand : public pc::util::cli::CommandCallback
{
public:

  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface& f_cli) override
  {
    vfc::int32_t l_texId = -1;
    f_input >> l_texId;

    if( f_input.fail())
    {
      return true;
    }

    pc::core::Framework* l_framework = nullptr;
    bool const l_res = f_cli.getUserData().getValue<pc::core::Framework*>(l_framework);
    assert(l_res && (nullptr != l_framework));
    cc::core::CustomScene* const l_scene = static_cast<cc::core::CustomScene*> (l_framework->getScene()); // PRQA S 3076

    pc::core::View *l_pView = nullptr;
    if( 0 == l_texId)
    {
      l_pView = l_scene->getView(cc::core::CustomViews::REAR_JUNCTION_VIEW);
    }
    else
    {
      l_pView = l_scene->getView(cc::core::CustomViews::FRONT_JUNCTION_VIEW);
    }

    if( nullptr == l_pView )
    {
      return true;
    }

    cc::views::panoramaview::PanoramaView * const l_pPanoView = dynamic_cast<cc::views::panoramaview::PanoramaView*>(l_pView); // PRQA S 3400

    if( nullptr == l_pPanoView )
    {
      return true;
    }

    //! retrieve the wall vertex array
    osg::Geode* const l_pGeodeWall = l_pPanoView->m_projObj->getChild(0u)->asGeode();
    osg::Geometry* const l_pGeometryWall = l_pGeodeWall->getDrawable(0u)->asGeometry();

    osg::Vec3Array* const l_pVertexArray = static_cast<osg::Vec3Array*>(l_pGeometryWall->getVertexArray()); // PRQA S 3076
    osg::Vec2Array* const l_pTexCoord = static_cast<osg::Vec2Array*>(l_pGeometryWall->getTexCoordArray(0u)); // PRQA S 3076
    if( (l_pVertexArray != nullptr) && (l_pTexCoord != nullptr) && ( l_pVertexArray->size() == l_pTexCoord->size()) )
    {
      for (vfc::uint32_t i = 0u; i < l_pTexCoord->size(); ++i)
      {
        f_output << l_pVertexArray->at(i).x() << ", "
                 << l_pVertexArray->at(i).y() << ", "
                 << l_pVertexArray->at(i).z() << ", "
                 << l_pTexCoord->at(i).x()    << ", "
                 << l_pTexCoord->at(i).y()    << "; ";
      }
    }
    f_output << newline;

    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Get a list of texture coordinates in pano mesh" << newline;
  }

  void getHelp(std::ostream& f_output) const override
  {
    f_output << "[Front/Rear]" << newline;
  }

};

static pc::util::cli::Command<PanoReadCommand> g_panoReadCommand("readpano");


//!
//! PanoramaViewUpdateCallback
//!
PanoramaViewUpdateCallback::PanoramaViewUpdateCallback( pc::core::Framework* f_pFramework)
    : m_pFramework(f_pFramework) // PRQA S 2323 // PRQA S 4052
    , m_seqCtr(-1) // PRQA S 2323

{
}

void PanoramaViewUpdateCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  if (true == m_pFramework->m_cameraCalibrationReceiver.isConnected())
  {
    const pc::daddy::CameraCalibrationDaddy* const l_pCalibDaddy = m_pFramework->m_cameraCalibrationReceiver.getData();

    if ( (nullptr != l_pCalibDaddy) && (static_cast<vfc::int32_t>(l_pCalibDaddy->m_sequenceNumber) != m_seqCtr) )
    {
      cc::views::panoramaview::PanoramaView* const l_pPanoView = dynamic_cast<cc::views::panoramaview::PanoramaView*>(f_node); // PRQA S 3400
      if(nullptr != l_pPanoView)
      {
        XLOG_INFO(g_EngineContext, "PanoramaViewUpdateCallback : Calling update of camera calibration");
        if(true == l_pPanoView->updateCalibration())
        {
          m_seqCtr = static_cast<vfc::int32_t>(l_pCalibDaddy->m_sequenceNumber);
        }
      }
    }

    traverse(f_node, f_nv);
  }
}

} // namespace panoramaview
} // namespace views
} // namespace cc
