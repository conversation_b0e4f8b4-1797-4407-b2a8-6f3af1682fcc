//-------------------------------------------------------------------------------
// Copyright (c) 2021 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_PDC_PDCHELPER_H
#define CC_ASSETS_PDC_PDCHELPER_H

#include "pc/generic/util/coding/inc/CodingManager.h"

#include <osg/Vec2>
#include <osg/Vec4ub>
#include <cstdint>
#include <limits>
#include <vector>
#include <array>  


namespace cc
{
namespace assets
{
namespace pdc
{

class Color
{
public:

  // 1 byte would be sufficient, however using a signed type with sufficient range
  // simplifies computations without lots of cast operation
  typedef std::int16_t value_type;

  Color()
    : m_r(0)
    , m_g(0)
    , m_b(0)
  {
  }

  Color(value_type f_r, value_type f_g, value_type f_b)
    : m_r(f_r)
    , m_g(f_g)
    , m_b(f_b)
  {
  }

  explicit Color(const std::string& f_string)
  {
    fromString(f_string);
  }

  void fromString(const std::string& f_str);

  std::string toString() const;

  // required for interpolation
  inline Color operator * (float f_mul) const
  {
    return Color(
      static_cast<value_type> (m_r * f_mul),
      static_cast<value_type> (m_g * f_mul),
      static_cast<value_type> (m_b * f_mul));
  }

  inline Color operator + (const Color& f_add) const
  {
    return Color(m_r + f_add.m_r, m_g + f_add.m_g, m_b + f_add.m_b);
  }

  inline Color operator - (const Color& f_sub) const
  {
    return Color(m_r - f_sub.m_r, m_g - f_sub.m_g, m_b - f_sub.m_b);
  }

  value_type m_r;
  value_type m_g;
  value_type m_b;

};


inline pc::util::coding::InputStream& operator >> (pc::util::coding::InputStream& f_is, Color& f_color)
{
  f_is.beginArray();
  f_is >> f_color.m_r >> f_color.m_g >> f_color.m_b;  // PRQA S 3803
  f_is.endArray();
  return f_is;
}


inline pc::util::coding::OutputStream& operator << (pc::util::coding::OutputStream& f_os, const Color& f_color)
{
  f_os.beginArray();
  f_os <<  f_color.m_r << f_color.m_g << f_color.m_b;  // PRQA S 3803
  f_os.endArray();
  return f_os;
}



///
/// ColorsValues
///
class ColorValues : public pc::util::coding::ISerializable
{
public:

  enum : std::size_t { NUM_COLORS = 3 };

  ColorValues() = default;

  ColorValues(
    const Color& f_col1,
    const Color& f_col2,
    const Color& f_col3)
    : m_col1(f_col1)
    , m_col2(f_col2)
    , m_col3(f_col3)
  {
  }

  SERIALIZABLE(ColorValues)
  {
    ADD_MEMBER(Color, col1);
    ADD_MEMBER(Color, col2);
    ADD_MEMBER(Color, col3);
  }

  const Color& getColor(std::size_t f_index) const
  {
    switch (f_index)
    {
      default:
      case 0:
        return m_col1;
      case 1:
        return m_col2;
      case 2:
        return m_col3;
    }
  }

  Color m_col1;
  Color m_col2;
  Color m_col3;
};


///
/// DistanceValues
///
class DistanceValues : public pc::util::coding::ISerializable
{
public:

  DistanceValues() = default;

  DistanceValues(float f_dist1, float f_dist2, float f_dist3)
    : m_dist1(f_dist1)
    , m_dist2(f_dist2)
    , m_dist3(f_dist3)
  {
  }

  SERIALIZABLE(DistanceValues)
  {
    ADD_FLOAT_MEMBER(dist1);
    ADD_FLOAT_MEMBER(dist2);
    ADD_FLOAT_MEMBER(dist3);
  }

  float getDistance(std::size_t f_index) const
  {
    switch (f_index)
    {
      default:
      case 0:
        return m_dist1;
      case 1:
        return m_dist2;
      case 2:
        return m_dist3;
    }
  }

  inline float getNear() const
  {
    return m_dist1;
  }

  inline float getFar() const
  {
    return m_dist3;
  }

  ///
  /// @brief maps a distance value to a normalized range x <= near = 0, x >= default = 1
  ///
  /// @param f_value distance value
  /// @return float normalized value
  ///
  float normalize(float f_value) const
  {
    const float l_result = (f_value - getNear()) / (getFar() - getNear());
    return std::min(1.0f, std::max(0.0f, l_result)); // clamp
  }

  ///
  /// @brief maps a normalized input value to the valid distance range, 0 = near, 1 = far
  ///
  /// @param f_value Expected to be in the range from 0 to 1
  /// @return float mapped value
  ///
  float denormalize(float f_value) const
  {
    return (f_value * (getFar() - getNear())) + getNear();
  }

  float m_dist1;
  float m_dist2;
  float m_dist3;
};



///
/// Opacity
///
class Opacity
{
public:

  // 1 byte would be sufficient, however using a signed type with sufficient range
  // simplifies computations without lots of cast operation
  typedef std::int16_t value_type;

  Opacity()
    : m_value(0)
  {
  }

  Opacity(value_type f_value)
    : m_value(clamp(f_value))
  {
  }

  static constexpr value_type clamp(value_type f_value)
  {
    return std::min(std::max(f_value, min()), max());
  }

  static constexpr value_type min()
  {
    return 0;
  }

  static constexpr value_type max()
  {
    return 100; // percentage
  }

  template <typename T>
  static constexpr T toAlpha(value_type f_value)
  {
    return static_cast<T> (
      (f_value * static_cast<value_type> (std::numeric_limits<T>::max())) / max());
  }

  inline value_type getValue() const
  {
    return m_value;
  }

  inline void setValue(value_type f_value)
  {
    m_value = clamp(f_value);
  }

  inline std::uint8_t getAlpha() const
  {
    return toAlpha<std::uint8_t>(m_value);
  }

  inline float getAlphaF() const
  {
    return m_value / static_cast<float> (max());
  }

private:

  value_type m_value;
};


inline pc::util::coding::InputStream& operator >> (pc::util::coding::InputStream& f_is, Opacity& f_opacity)
{
  Opacity::value_type l_value = 0;
  f_is >> l_value;
  f_opacity.setValue(l_value);
  return f_is;
}


inline pc::util::coding::OutputStream& operator << (pc::util::coding::OutputStream& f_os, const Opacity& f_opacity)
{
  f_os << f_opacity.getValue();
  return f_os;
}


inline osg::Vec4ub toVec4ub(const Color& f_color, osg::Vec4ub::value_type f_alpha)
{
  return osg::Vec4ub(
    f_color.m_r,
    f_color.m_g,
    f_color.m_b,
    f_alpha);
}


//!
//! @brief linear interpolation
//!
//! @tparam T type
//! @param f_a point or scalar a
//! @param f_b point or scalar b
//! @param f_t amount of inter interpolation assumed to be in range 0 - 1
//!
template <typename T>
T lerp(const T& f_a, const T& f_b, float f_t)
{
  return f_a + ((f_b - f_a) * f_t);
}


typedef std::array<osg::Vec2, 4> ControlPoints;
typedef std::array<float, 4> KnotSequence;

template <class ControlPointIter>
KnotSequence computeKnotSequence(ControlPointIter f_cp, float f_alpha = 0.5f)
{
  auto l_getT = [] (const osg::Vec2& f_p0, const osg::Vec2& f_p1, float f_t, float f_alpha)
  {
    const osg::Vec2 l_diff = f_p1 - f_p0;
    const float l_dot = l_diff * l_diff; // dot prodoct
    const float l_b = std::pow(l_dot, f_alpha * 0.5f);
    return l_b + f_t;
  };
  const float l_t0 = 0.0f;
  const float l_t1 = l_getT(f_cp[0], f_cp[1], l_t0, f_alpha);
  const float l_t2 = l_getT(f_cp[1], f_cp[2], l_t1, f_alpha);
  const float l_t3 = l_getT(f_cp[2], f_cp[3], l_t2, f_alpha);
  return {{ l_t0, l_t1, l_t2, l_t3 }};
}


///
/// @brief Catmull-Rom spline interpolation
///
/// Computes an interpolated spline point based on a curve defined by four control points p0, p1, p2, p3
/// lying on the curve which is going through the points p1 and p2 (f_ts = 0.0 -> f_ts = 1.0)
///
/// @param f_cp Array of four control points
/// @param f_t Corresponding array of knot sequence
/// @param f_ts Current knot paramter (0.0 - 1.0)
///
template <class ControlPointIterator>
osg::Vec2 interpolateCatmullRom(
  ControlPointIterator f_cp,
  const KnotSequence& f_ks,
  float f_t)
{
  f_t = lerp(f_ks[1], f_ks[2], f_t);
  const osg::Vec2 l_a1 = f_cp[0] * (f_ks[1] - f_t) / (f_ks[1] - f_ks[0]) + f_cp[1] * (f_t - f_ks[0]) / (f_ks[1] - f_ks[0]);
  const osg::Vec2 l_a2 = f_cp[1] * (f_ks[2] - f_t) / (f_ks[2] - f_ks[1]) + f_cp[2] * (f_t - f_ks[1]) / (f_ks[2] - f_ks[1]);
  const osg::Vec2 l_a3 = f_cp[2] * (f_ks[3] - f_t) / (f_ks[3] - f_ks[2]) + f_cp[3] * (f_t - f_ks[2]) / (f_ks[3] - f_ks[2]);
  const osg::Vec2 l_b1 = l_a1 * (f_ks[2] - f_t) / (f_ks[2] - f_ks[0]) + l_a2 * (f_t - f_ks[0]) / (f_ks[2] - f_ks[0]);
  const osg::Vec2 l_b2 = l_a2 * (f_ks[3] - f_t) / (f_ks[3] - f_ks[1]) + l_a3 * (f_t - f_ks[1]) / (f_ks[3] - f_ks[1]);
  return l_b1 * (f_ks[2] - f_t) / (f_ks[2] - f_ks[1]) + l_b2 * (f_t - f_ks[1]) / (f_ks[2] - f_ks[1]);
}


template <class ControlPointArray, class OutputIterator>
OutputIterator computeSpline(
  const ControlPointArray& f_controlPoints,
  OutputIterator f_destination,
  std::size_t f_numPoints,
  float f_alpha = 0.5f)
{
  const std::size_t l_numControlPoints = f_controlPoints.size();
  if ((l_numControlPoints < 4) || (f_numPoints < 2))
  {
    return f_destination; // we need at least four control points and a minimum of two points to generated
  }
  const std::size_t l_numIterations = l_numControlPoints - 3;
  const float l_tDiff = l_numIterations / static_cast<float> (f_numPoints - 1);
  std::size_t l_segment = std::numeric_limits<std::size_t>::max();
  KnotSequence l_knots;
  for (std::size_t i = 0; i < f_numPoints - 1; ++i)
  {
    const float l_t = i * l_tDiff; // global parameter t among all curve segments
    const std::size_t l_s = static_cast<std::size_t> (l_t); // truncate to get int part only which indicates on which segment we are
    if (l_s != l_segment)
    {
      // update knot sequence if a new segment has been reached
      l_segment = l_s;
      l_knots = computeKnotSequence(std::next(f_controlPoints.begin(), l_segment), f_alpha);
    }
    (*f_destination) = interpolateCatmullRom(
      std::next(f_controlPoints.begin(), l_segment),
      l_knots,
      l_t - static_cast<float> (l_s)); // localized parameter t
    std::advance(f_destination, 1);
  }
  // add last point which is equal to p2 of last segment (interpolation is going through p1 and p2, with t=1)
  (*f_destination) = f_controlPoints[l_segment+2];
  return std::next(f_destination);
}


template <class VecType>
VecType computeOrthogonal(const VecType& f_p0, const VecType& f_p1)
{
  VecType l_s = f_p1 - f_p0;
  l_s = VecType(-l_s.y(), l_s.x()); // rotate by 90 degree
  l_s.normalize(); // PRQA S 3803
  return l_s;
}


///
/// SvgWriter
///
class SvgWriter
{
public:

  SvgWriter(std::ostream& f_os, float f_viewBoxMinX, float f_viewBoxMinY, float f_viewBoxMaxX, float f_viewBoxMaxY);
  SvgWriter(std::ostream& f_os, const osg::Vec2& f_viewBoxMin, const osg::Vec2& f_viewBoxMax);

  ~SvgWriter();

  template <class Point>
  void addCircle(const Point& f_point, float f_radius);

  template <class PointArray>
  void addCircles(const PointArray& f_points, float f_radius);

  template <class PointArray>
  void addPolygon(const PointArray& f_points);

  template <class PointIterator>
  void addPolygon(PointIterator f_begin, PointIterator f_end);

  template <class PointArray>
  void addPolyline(const PointArray& f_points);

  template <class PointIterator>
  void addPolyline(PointIterator f_begin, PointIterator f_end);

private:

  std::ostream& m_os;
};


template <class Point>
void SvgWriter::addCircle(const Point& f_point, float f_radius)
{
  m_os << "  <circle cx=\"" << f_point.x() << "\" cy=\"" << f_point.y() << "\" r=\"" << f_radius << "\" fill=\"red\" />" << std::endl;
}


template <class PointArray>
void SvgWriter::addCircles(const PointArray& f_points, float f_radius)
{
  for (const auto& l_p : f_points)
  {
    addCircle(l_p, f_radius);
  }
}


template <class PointArray>
void SvgWriter::addPolygon(const PointArray& f_points)
{
  addPolygon(f_points.begin(), f_points.end());
}


template <class PointIterator>
void SvgWriter::addPolygon(PointIterator f_begin, PointIterator f_end)
{
  m_os << "  <polygon stroke=\"black\" fill=\"none\" points=\"";
  for (PointIterator l_itr = f_begin; l_itr != f_end; ++l_itr)
  {
    m_os << l_itr->x() << "," << l_itr->y() << " ";
  }
  m_os << "\"/>" << std::endl;
}


template <class PointArray>
void SvgWriter::addPolyline(const PointArray& f_points)
{
  addPolyline(f_points.begin(), f_points.end());
}


template <class PointIterator>
void SvgWriter::addPolyline(PointIterator f_begin, PointIterator f_end)
{
  m_os << "  <polyline stroke=\"black\" fill=\"none\" points=\"";
  for (auto l_itr = f_begin; l_itr != f_end; ++l_itr)
  {
    m_os << l_itr->x() << "," << l_itr->y() << " ";
  }
  m_os << "\"/>" << std::endl;
}


void testCatmullRom();


} // namespace pdc
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PDC_PDCHELPER_H