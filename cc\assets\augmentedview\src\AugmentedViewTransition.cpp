//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/augmentedview/inc/AugmentedViewTransition.h"
#include "cc/assets/augmentedview/inc/PeriodicScanAnimation.h"
#include "cc/assets/augmentedview/inc/TransitionWaveAnimation.h"
#include "cc/assets/augmentedview/inc/ParkViewRenderManager.h"

#include "cc/assets/common/inc/Floor.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"

#include "pc/generic/util/coding/inc/ISerializable.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/core/inc/View.h"
#include "pc/svs/util/math/inc/Box2D.h"
#include "pc/svs/util/osgx/inc/Utils.h"

#include "osg/Array"
#include "osg/StateAttribute"
#include "osg/BlendFunc" // PRQA S 1060
#include "osg/Depth"
#include "osg/Node"
#include "osg/Texture2D"
#include "osgDB/ReadFile"


using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace augmentedview
{

pc::util::coding::Item<AugmentedViewTransitionData> g_augmentedSettings("AugmentedViewTransition");

class OuterRingBoundingBoxCallback : public osg::Drawable::ComputeBoundingBoxCallback
{
public:
  osg::BoundingBox computeBound(const osg::Drawable& drawable) const override
  {
    osg::BoundingBox bbox;
    const vfc::float32_t displacement = g_augmentedSettings->m_transitionWaveWidth * 0.5f + g_augmentedSettings->m_scanWaveMaxRadius;
    const osg::Vec3Array* const vertices = dynamic_cast<const osg::Vec3Array*>(drawable.asGeometry()->getVertexArray()); // PRQA S 3400
    if (vertices != nullptr)
    {
      for (osg::Vec3Array::const_iterator it = vertices->begin(); it != vertices->end(); ++it) // PRQA S 4687
      {
        const osg::Vec3 vertex = *it;
        bbox.expandBy(vertex * displacement);
      }
    }
    return bbox;
  }
};

//!
//! AugmentedViewTransition
//!
AugmentedViewTransition::AugmentedViewTransition(pc::core::Framework* f_framework, cc::target::common::EThemeTypeHU f_themeType)
  : osg::MatrixTransform() // PRQA S 2323
  , m_backgroundQuad() // PRQA S 2323
  , m_framework(f_framework) // PRQA S 2323
  , m_inAugmentedViewState(false) // PRQA S 2323
  , m_scanWaveAllowed(false) // PRQA S 2323
  , m_activeAnimation(0u) // PRQA S 2323
  , m_animation() // PRQA S 2323
  , m_scanAnimation() // PRQA S 2323
  , m_scanWavesSwitch() // PRQA S 2323
  , m_scanWaveGeodes()
  , m_waveRadiusUniform() // PRQA S 2323
  , m_waveAlphaUniform() // PRQA S 2323
  , m_radarFrontGeometry() // PRQA S 2323
  , m_additiveWavefrontGeode() // PRQA S 2323
  , m_innerRingGeode() // PRQA S 2323
  , m_rootNode() // PRQA S 2323
  , m_waveTexture() // PRQA S 2323
  , m_view() // PRQA S 2323
  , m_streetOverlayStateSet() // PRQA S 2323
  , m_themeType(f_themeType) // PRQA S 2323
  , m_scanWaveMaxRadius(5.0f) // PRQA S 2323
  , m_scanWaveWidth(0.3f) // PRQA S 2323
{
  setName("AugmentedViewTransition");
  setNumChildrenRequiringUpdateTraversal(1u);

  m_animation[0].reset(new TransitionWaveAnimation);
  m_animation[1].reset(new TransitionWaveAnimation);
  m_animation[0]->reset(g_augmentedSettings->m_transitionWaveAnimationDuration, g_augmentedSettings->m_transitionWaveMaxRadius, true);

  // 0.9 = (720/1080) / (720/972) = 972/1080  image size transfer from horizontal view to vertical view
  if (m_themeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT )
  {
    m_scanWaveMaxRadius = g_augmentedSettings->m_scanWaveMaxRadius*0.9f;
    m_scanWaveWidth = g_augmentedSettings->m_scanWaveWidth*0.9f;
  }
  else
  {
    m_scanWaveMaxRadius = g_augmentedSettings->m_scanWaveMaxRadius;
    m_scanWaveWidth = g_augmentedSettings->m_scanWaveWidth;
  }

  m_scanAnimation.reset(new PeriodicScanAnimation(
      g_augmentedSettings->m_scanWaveAnimationDuration,
      m_scanWaveMaxRadius,
      g_augmentedSettings->m_scanWaveInterBurstTime,
      static_cast<vfc::uint32_t>(g_augmentedSettings->m_scanWaveBurstSize), // PRQA S 3016
      g_augmentedSettings->m_scanWaveIntraBurstTime
    )
  );
}


void AugmentedViewTransition::transitionToAugmentedView()
{
  const vfc::uint32_t queueIndex = m_animation[m_activeAnimation]->isRunning() ? 1u-m_activeAnimation : m_activeAnimation;
  m_animation[queueIndex]->start(g_augmentedSettings->m_transitionWaveAnimationDuration, g_augmentedSettings->m_transitionWaveMaxRadius, true);

  m_scanAnimation->reset(
    g_augmentedSettings->m_scanWaveAnimationDuration,
    m_scanWaveMaxRadius,
    g_augmentedSettings->m_scanWaveInterBurstTime,
    static_cast<vfc::uint32_t>(g_augmentedSettings->m_scanWaveBurstSize), // PRQA S 3016
    g_augmentedSettings->m_scanWaveIntraBurstTime
  );
  m_inAugmentedViewState = false;
  prepareAnimationState();
}


void AugmentedViewTransition::transitionFromAugmentedView()  //PRQA S 1724
{
  const vfc::uint32_t queueIndex = m_animation[m_activeAnimation]->isRunning() ? 1u-m_activeAnimation : m_activeAnimation;
  m_animation[queueIndex]->start(g_augmentedSettings->m_transitionWaveAnimationDuration, g_augmentedSettings->m_transitionWaveMaxRadius, false);
  m_inAugmentedViewState = true;
  prepareAnimationState();
}


void AugmentedViewTransition::switchToAugmentedView()  //PRQA S 1724
{
  m_scanAnimation->reset(
      g_augmentedSettings->m_scanWaveAnimationDuration,
      m_scanWaveMaxRadius,
      g_augmentedSettings->m_scanWaveInterBurstTime,
      static_cast<vfc::uint32_t>(g_augmentedSettings->m_scanWaveBurstSize), // PRQA S 3016
      g_augmentedSettings->m_scanWaveIntraBurstTime
  );
  finalizeAugmentedViewState();
}


void AugmentedViewTransition::switchFromAugmentedView()  //PRQA S 1724
{
  finalizeSurroundViewState();
}


bool AugmentedViewTransition::prepareAugmentedViewAnimation()
{
  if (!m_radarFrontGeometry)
  {
    return false;
  }
  if (!m_scanWavesSwitch)
  {
    m_scanWavesSwitch = new osg::Switch();
    osg::StateSet* const l_stateSet = m_scanWavesSwitch->getOrCreateStateSet();
    setupAdditiveWavefrontState(l_stateSet);
    l_stateSet->setRenderBinDetails(pc::core::g_renderOrder->m_overlays - 1, "RenderBin");

    m_rootNode->addChild(m_scanWavesSwitch);    // PRQA S 3803
  }
  const vfc::uint32_t l_existingWaveCount = m_scanWavesSwitch->getNumChildren();
  if (m_scanAnimation->getMaxConcurrentWaves() > l_existingWaveCount)
  {
    for (vfc::uint32_t i = l_existingWaveCount; i < m_scanAnimation->getMaxConcurrentWaves(); ++i)
    {
      osg::Geode* const l_scanWaveGeode = new osg::Geode();
      l_scanWaveGeode->addDrawable(m_radarFrontGeometry);    // PRQA S 3803
      l_scanWaveGeode->setCullingActive(false);
      m_scanWavesSwitch->addChild(l_scanWaveGeode, false);    // PRQA S 3803
    }
  }
  return true;
}


osg::Node* AugmentedViewTransition::createBackgroundNode()
{
  osg::Camera* const l_backgroundCam = new osg::Camera();
  l_backgroundCam->setClearMask(0u);
  l_backgroundCam->setReferenceFrame(osg::Transform::ABSOLUTE_RF);
  l_backgroundCam->setViewMatrix(osg::Matrix::identity());
  l_backgroundCam->setRenderOrder(osg::Camera::NESTED_RENDER);
  l_backgroundCam->setProjectionMatrixAsOrtho2D(0.0f, 1.0f, 0.0f, 1.0f);

  //! seems to be a little be more efficient on the Mali GPU to split the geometry in order to fill the entire background
  osg::Geometry* const l_backgroundGeometryTop = pc::util::osgx::createTexturePlane(
    osg::Vec3f(0.0f, 0.5f, 0.0f),
    osg::X_AXIS,
    osg::Vec3f(0.0f, 0.5f, 0.0f),
    2u, 1u, 0.0f, 1.0f, 0.5f, 1.0f);

  osg::Geometry* const l_backgroundGeometryBottom = pc::util::osgx::createTexturePlane(
    osg::Vec3f(0.0f, 0.0f, 0.0f),
    osg::X_AXIS,
    osg::Vec3f(0.0f, 0.5f, 0.0f),
    2u, 1u, 0.0f, 1.0f, 0.0f, 0.5f);

  osg::Geode* const l_backgroundGeode = new osg::Geode();
  l_backgroundGeode->addDrawable(l_backgroundGeometryTop);    // PRQA S 3803
  l_backgroundGeode->addDrawable(l_backgroundGeometryBottom);    // PRQA S 3803

  osg::StateSet* const l_stateSet = l_backgroundCam->getOrCreateStateSet();
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
  l_basicTexShader.apply(l_stateSet);    // PRQA S 3803
  l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);  // PRQA S 3143
  l_stateSet->setRenderBinDetails(pc::core::g_renderOrder->m_carOpaque - 1, "RenderBin");

  osg::Image* const l_image = osgDB::readImageFile(g_augmentedSettings->m_backgroundTextureFilename);
  osg::Texture2D* const l_texture = new osg::Texture2D(l_image);
  l_texture->setDataVariance(osg::Object::STATIC);
  l_texture->setUnRefImageDataAfterApply(true);
  l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
  l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
  l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::REPEAT);
  l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::REPEAT);
  l_stateSet->setTextureAttribute(0u, l_texture);

  osg::Depth* const l_depth = new osg::Depth;
  l_depth->setWriteMask(false);
  l_stateSet->setAttribute(l_depth);

  osg::Stencil* const l_stencil = new osg::Stencil;
  l_stencil->setFunction(osg::Stencil::EQUAL, 240, ~0u);
  l_stencil->setOperation(osg::Stencil::KEEP, osg::Stencil::KEEP, osg::Stencil::KEEP);
  l_stateSet->setAttributeAndModes(l_stencil, osg::StateAttribute::ON);  // PRQA S 3143

  l_backgroundCam->addChild(l_backgroundGeode);    // PRQA S 3803
  return l_backgroundCam;
}


bool AugmentedViewTransition::initializeAugmentedViewTransition(const vfc::uint32_t f_tesselationCount)
{
  if (f_tesselationCount < 3u)
  {
    //assert(f_tesselationCount >= 3u);
    XLOG_ERROR(g_AppContext, "tesselationCount is less than 3");
    return false;
  }

  const vfc::uint32_t l_outerVertexCount   = f_tesselationCount * 2u;
  const vfc::uint32_t l_outerTriangleCount = f_tesselationCount * 2u;

  osg::Vec3Array* const l_vertices = new osg::Vec3Array(l_outerVertexCount + 1u);
  osg::Vec4Array* const l_colors =  new osg::Vec4Array(l_outerVertexCount + 1u);
  osg::Vec2usArray* const l_texCoords = new osg::Vec2usArray(l_outerVertexCount + 1u);
  l_texCoords->setNormalize(true);
  static constexpr osg::Vec2us::value_type s_usMax = std::numeric_limits<osg::Vec2us::value_type>::max();
  {

    //vfc::uint32_t l_ReduceDelta = 0u;
    vfc::float32_t l_angle = static_cast<vfc::float32_t> (0 * osg::PI); //! radian
    const vfc::float32_t l_stepSize = static_cast<vfc::float32_t> (2.0 * osg::PI) / static_cast<vfc::float32_t>(f_tesselationCount); //! radian
    //const vfc::float32_t l_stepSize = static_cast<vfc::float32_t> (osg::PI) / static_cast<vfc::float32_t>(16.0); //! radian
    // step angle = 360/64 = 5.625 degree or 0.03125 radian
    //l_angle += static_cast<vfc::float32_t>(l_ReduceDelta) * l_stepSize;

    constexpr vfc::float32_t xFactorInnner = 0.0f;
    constexpr vfc::float32_t yFactorInnner = 0.0f;
    constexpr vfc::float32_t xFactorOuter  = 1.0f;
    constexpr vfc::float32_t yFactorOuter  = 1.0f;

    for (vfc::uint32_t i = 0u; i < 32u; ++i) // use only one quarter
    {
      osg::Vec2f l_v(std::cos(l_angle), std::sin(l_angle));
      (*l_vertices) [i*2u]      = osg::Vec3f(l_v.x() * xFactorInnner, l_v.y() * yFactorInnner, -1.0f); //! Inner ring
      (*l_vertices) [i*2u + 1u] = osg::Vec3f(l_v.x() * xFactorOuter,  l_v.y() * yFactorOuter,   1.0f); //! Outer ring
      (*l_colors) [i*2u]      = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f); //! Inner ring
      (*l_colors) [i*2u + 1u] = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f); //! Outer ring
      (*l_texCoords)[i*2u]      = osg::Vec2us(0u, s_usMax / 2u);
      (*l_texCoords)[i*2u + 1u] = osg::Vec2us(s_usMax, s_usMax / 2u);
      l_angle += l_stepSize;
    }

    //l_angle = static_cast<vfc::float32_t>(osg::PI) + static_cast<vfc::float32_t>(l_ReduceDelta) * l_stepSize;

    for (vfc::uint32_t i = 32u; i < 64u; ++i) // another half, the vertex has to be discrete
    {
      osg::Vec2f l_v(std::cos(l_angle), std::sin(l_angle));
      (*l_vertices) [i*2u]      = osg::Vec3f(l_v.x() * xFactorInnner, l_v.y() * yFactorInnner, -1.0f); //! Inner ring
      (*l_vertices) [i*2u + 1u] = osg::Vec3f(l_v.x() * xFactorOuter,  l_v.y() * yFactorOuter,   1.0f); //! Outer ring
      (*l_colors) [i*2u]      = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f); //! Inner ring
      (*l_colors) [i*2u + 1u] = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f); //! Outer ring
      (*l_texCoords)[i*2u]      = osg::Vec2us(0u, s_usMax / 2u);
      (*l_texCoords)[i*2u + 1u] = osg::Vec2us(s_usMax, s_usMax / 2u);
      l_angle += l_stepSize;
    }
  }

  (*l_vertices)[l_outerVertexCount] = osg::Vec3f(0.f, 0.f, 0.f);
  (*l_colors)[l_outerVertexCount] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_texCoords)[l_outerVertexCount] = osg::Vec2us(0u, 0u);



  //front opening angle
  (*l_colors)[116] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.9f);
  (*l_colors)[117] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.8f);
  (*l_colors)[118] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.7f);
  (*l_colors)[119] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.6f);
  (*l_colors)[120] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.5f);
  (*l_colors)[121] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.3f);
  (*l_colors)[122] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.1f);
  (*l_colors)[123] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[124] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[125] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[126] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[127] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[0]   = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[1]   = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[2]   = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[3]   = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[4]   = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[5]   = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[6]   = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.1f);
  (*l_colors)[7]   = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.3f);
  (*l_colors)[8]   = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.5f);
  (*l_colors)[9]   = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.6f);
  (*l_colors)[10]  = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.7f);
  (*l_colors)[11]  = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.8f);
  (*l_colors)[12]  = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.9f);

  //rear part opening angle
  (*l_colors)[52] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.9f);
  (*l_colors)[53] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.8f);
  (*l_colors)[54] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.7f);
  (*l_colors)[55] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.6f);
  (*l_colors)[56] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.5f);
  (*l_colors)[57] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.3f);
  (*l_colors)[58] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.1f);
  (*l_colors)[59] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[60] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[61] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[62] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[63] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[64] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[65] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[66] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[67] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[68] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[69] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
  (*l_colors)[70] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.1f);
  (*l_colors)[71] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.3f);
  (*l_colors)[72] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.5f);
  (*l_colors)[73] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.6f);
  (*l_colors)[74] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.7f);
  (*l_colors)[75] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.8f);
  (*l_colors)[76] = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.9f);

  //! Triangulate vertices
  osg::DrawElements* const l_outerIndices = pc::util::osgx::createDrawElements(static_cast<vfc::uint32_t>(l_vertices->size()), GL_TRIANGLES, l_outerTriangleCount * 3u);    // PRQA S 3143
  for (vfc::uint32_t i = 0u; i < 32U; ++i) // use one quarter
  {
    l_outerIndices->setElement(i*6u + 0u,  i * 2u + 0u);
    l_outerIndices->setElement(i*6u + 1u,  i * 2u + 1u);
    l_outerIndices->setElement(i*6u + 2u, (i * 2u + 2u) % l_outerVertexCount);
    l_outerIndices->setElement(i*6u + 3u, (i * 2u + 2u) % l_outerVertexCount);
    l_outerIndices->setElement(i*6u + 4u,  i * 2u + 1u);
    l_outerIndices->setElement(i*6u + 5u, (i * 2u + 3u) % l_outerVertexCount);
  }

  for (vfc::uint32_t i = 32u; i < 64U; ++i) // another half, the vertex has to be discrete
  {
    l_outerIndices->setElement(i*6u + 0u,  i * 2u + 0u);
    l_outerIndices->setElement(i*6u + 1u,  i * 2u + 1u);
    l_outerIndices->setElement(i*6u + 2u, (i * 2u + 2u) % l_outerVertexCount);
    l_outerIndices->setElement(i*6u + 3u, (i * 2u + 2u) % l_outerVertexCount);
    l_outerIndices->setElement(i*6u + 4u,  i * 2u + 1u);
    l_outerIndices->setElement(i*6u + 5u, (i * 2u + 3u) % l_outerVertexCount);
  }

  const vfc::uint32_t l_innerTriangleCount = f_tesselationCount;

  // TODO: we could use a triangle fan instead
  osg::DrawElements* const l_innerIndices = pc::util::osgx::createDrawElements(static_cast<vfc::uint32_t>(l_vertices->size()), GL_TRIANGLES, l_innerTriangleCount * 3u);    // PRQA S 3143
  for (vfc::uint32_t i = 0u; i < l_innerTriangleCount; ++i)
  {
    l_innerIndices->setElement(i * 3u + 0u, i * 2u + 0u);
    l_innerIndices->setElement(i * 3u + 1u, (i * 2u + 2u) % l_outerVertexCount);
    l_innerIndices->setElement(i * 3u + 2u, l_outerVertexCount);
  }

  m_rootNode = new osg::Switch();
  m_rootNode->setName("AugViewTransiRootNode");
  setupCommonState(m_rootNode->getOrCreateStateSet());

  // Create radar wave-front geometry (for the leading wave)
  m_radarFrontGeometry = pc::util::osgx::createGeometry();
  m_radarFrontGeometry->setVertexArray(l_vertices);
  m_radarFrontGeometry->setColorArray(l_colors);
  m_radarFrontGeometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);
  m_radarFrontGeometry->addPrimitiveSet(l_outerIndices);    // PRQA S 3803
  m_radarFrontGeometry->setName("radarFrontGeo");

 //osg::Vec4Array* l_v_colors = static_cast<osg::Vec4Array*> (m_radarFrontGeometry->getColorArray());  //PRQA S 3076
  //l_v_colors->setBinding(osg::Array::BIND_PER_VERTEX);

  const osg::ref_ptr<OuterRingBoundingBoxCallback> boundingBoxCallback = new OuterRingBoundingBoxCallback();
  m_radarFrontGeometry->setComputeBoundingBoxCallback(boundingBoxCallback.get());

  m_additiveWavefrontGeode = new osg::Geode();
  m_additiveWavefrontGeode ->addDrawable(m_radarFrontGeometry);    // PRQA S 3803
  m_additiveWavefrontGeode ->setCullingActive(false);
  m_additiveWavefrontGeode->setName("additiveWavefront");
  setupAdditiveWavefrontState(m_additiveWavefrontGeode->getOrCreateStateSet());
  m_rootNode->addChild(m_additiveWavefrontGeode, false);    // PRQA S 3803

  // The inner ring is used for setting up the stencil buffer
  osg::Geometry* const l_innerRingGeometry = pc::util::osgx::createGeometry();
  l_innerRingGeometry->setVertexArray(l_vertices);
  l_innerRingGeometry->setColorArray(l_colors);
  l_innerRingGeometry->addPrimitiveSet(l_innerIndices);    // PRQA S 3803

  osg::Vec4Array* const l_inner_v_colors = static_cast<osg::Vec4Array*> (l_innerRingGeometry->getColorArray());  //PRQA S 3076
  l_inner_v_colors->setBinding(osg::Array::BIND_PER_VERTEX);

  m_innerRingGeode = new osg::Geode();
  m_innerRingGeode ->addDrawable(l_innerRingGeometry);    // PRQA S 3803
  m_innerRingGeode->setCullingActive(false);
  setupInnerDiskState(m_innerRingGeode->getOrCreateStateSet());
  m_rootNode->addChild(m_innerRingGeode, false);    // PRQA S 3803

  osg::StateSet* const l_rootStateSet = m_rootNode->getOrCreateStateSet();

  osg::Image* const l_image = osgDB::readImageFile(CONCAT_PATH(g_augmentedSettings->m_waveTextureFilename));

  if (l_image != nullptr)
  {
    m_waveTexture = new osg::Texture2D(l_image);
    m_waveTexture->setName("AugmentedViewTransition Wave Texture");
    m_waveTexture->setResizeNonPowerOfTwoHint(false);
    m_waveTexture->setUnRefImageDataAfterApply(true);
    l_rootStateSet->setTextureAttribute(0u, m_waveTexture, osg::StateAttribute::ON);
    l_rootStateSet->getOrCreateUniform("s_waveTexture", osg::Uniform::SAMPLER_2D)->set(0); // PRQA S 3803
    l_rootStateSet->getOrCreateUniform("u_trueColor", osg::Uniform::FLOAT_VEC4)->set(g_augmentedSettings->m_trueColor); // PRQA S 3803
  }
  else
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "AugmentedViewTransition Wave Texture could not be loaded!");
  }

  m_backgroundQuad = createBackgroundNode();
  m_rootNode->addChild(m_backgroundQuad, false);    // PRQA S 3803

  addChild(m_rootNode);    // PRQA S 3803

  return true;
}


void AugmentedViewTransition::setupCommonState(osg::StateSet* f_stateSet)
{
  if (nullptr == f_stateSet)
  {
    return;
  }
  f_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);

  // Initialize uniforms
  m_waveRadiusUniform = f_stateSet->getOrCreateUniform("u_wavefrontRadius", osg::Uniform::FLOAT);
  m_waveAlphaUniform = f_stateSet->getOrCreateUniform("u_wavefrontFade", osg::Uniform::FLOAT);

  f_stateSet->getOrCreateUniform("u_wavefrontCenter", osg::Uniform::FLOAT_VEC3)->set(osg::Vec3f(getWavefrontCenter(), 0.0f)); // PRQA S 3803
  f_stateSet->getOrCreateUniform("u_wavefrontWidth", osg::Uniform::FLOAT)->set(g_augmentedSettings->m_transitionWaveWidth * 0.5f); // PRQA S 3803
}


void AugmentedViewTransition::setupInnerDiskState(osg::StateSet* f_stateSet)
{
  if (nullptr == f_stateSet)
  {
    return;
  }
  f_stateSet->setRenderBinDetails(pc::core::g_renderOrder->m_carOpaque + 1, "RenderBin");
  f_stateSet->setMode(GL_BLEND, osg::StateAttribute::OFF);  // PRQA S 3143
  f_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);  // PRQA S 3143

  //! setup the stencil operation to replace the stencil buffer contents only on Z pass
  osg::Stencil* const l_stencil = new osg::Stencil;
  l_stencil->setFunction(osg::Stencil::ALWAYS, 240, ~0u);
  l_stencil->setOperation(osg::Stencil::KEEP, osg::Stencil::KEEP, osg::Stencil::REPLACE);
  f_stateSet->setAttributeAndModes(l_stencil, osg::StateAttribute::ON);  // PRQA S 3143

  //! switch off writing to colors
  osg::ColorMask* const l_colorMask = new osg::ColorMask;
  l_colorMask->setMask(false, false, false, false);
  f_stateSet->setAttribute(l_colorMask);

  osg::Depth* const l_depth = new osg::Depth;
  l_depth->setWriteMask(false);
  f_stateSet->setAttribute(l_depth);

  pc::core::BasicShaderProgramDescriptor l_augmentedViewInnerTransitionShader("augmentedViewInnerTransition");
  l_augmentedViewInnerTransitionShader.apply(f_stateSet);    // PRQA S 3803
}


void AugmentedViewTransition::setupAdditiveWavefrontState(osg::StateSet* f_stateSet)
{
  if (nullptr == f_stateSet)
  {
    return;
  }
  f_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);

  pc::core::BasicShaderProgramDescriptor l_augmentedViewWavefrontAdditiveShader("augmentedViewWavefront",
    "augmentedViewWavefrontAdditive");
  l_augmentedViewWavefrontAdditiveShader.apply(f_stateSet);    // PRQA S 3803
  // disable depth write mask
  f_stateSet->setAttribute(new osg::Depth(osg::Depth::LESS, 0.0, 1.0, false));

  f_stateSet->setRenderBinDetails(pc::core::g_renderOrder->m_baseplate + 1, "RenderBin");
}

void AugmentedViewTransition::registerWithView(pc::core::View* f_view)
{
  if (!initializeAugmentedViewTransition( /*tesselationcount*/ 64u ))
  {
    //assert(static_cast<bool>(0 && "could not init augmented view transition")); //PRQA S 3145
    XLOG_ERROR(g_AppContext, "could not init augmented view transition");
    return;
  }
  m_view = f_view;
}


void AugmentedViewTransition::updateAugmentedViewAnimationState(vfc::float32_t f_currentTime)
{
  assert(m_inAugmentedViewState);
  if (!prepareAugmentedViewAnimation())
  {
    return;
  }

  m_scanWaveAllowed = true;

  //if (true == m_scanWaveAllowed)
  //{
  m_scanAnimation->setStopAnimation(false);
  //}
  //else   // This code is unreachable.
  //{
  //  m_scanAnimation->setStopAnimation(true);
  //}

  m_scanAnimation->update(f_currentTime);

  osg::StateSet* const l_scanWaveStateSet = m_scanWavesSwitch->getOrCreateStateSet();
  l_scanWaveStateSet->getOrCreateUniform("u_wavefrontWidth", osg::Uniform::FLOAT)->set(m_scanWaveWidth * 0.5f);    // PRQA S 3803

  for (vfc::uint32_t i = 0u; i < m_scanWavesSwitch->getNumChildren(); ++i)
  {
    if (i < m_scanAnimation->getActiveWaves())
    {
      osg::Node* const scanWaveGeode = m_scanWavesSwitch->getChild(i);

      m_scanWavesSwitch->setValue(i, true);
      osg::StateSet* const stateSet = scanWaveGeode->getOrCreateStateSet();
      stateSet->getOrCreateUniform("u_wavefrontRadius", osg::Uniform::FLOAT)->set(m_scanAnimation->getWaveRadius(i));    // PRQA S 3803
      stateSet->getOrCreateUniform("u_wavefrontFade", osg::Uniform::FLOAT)->set(m_scanAnimation->getWaveAlphaFade(i));    // PRQA S 3803

    }
    else
    {
      m_scanWavesSwitch->setValue(i, false);
    }
  }
}


void AugmentedViewTransition::updateTransitionState(vfc::float32_t f_currentTime)
{
  TransitionWaveAnimation& animation = *m_animation[m_activeAnimation];    // PRQA S 2504

  //m_rootNode->setChildValue(m_innerRingGeode.get(), animation.isRunning());

  if (!animation.isRunning())
  {
    if (m_inAugmentedViewState)
    {
      updateAugmentedViewAnimationState(f_currentTime);
    }
    return;
  }

  if (m_scanWavesSwitch != nullptr)
  {
    m_scanWavesSwitch->setAllChildrenOff();    // PRQA S 3803
  }

  animation.update(f_currentTime);
  m_waveRadiusUniform->set(animation.getWaveRadius());    // PRQA S 3803
  m_waveAlphaUniform->set(animation.getWaveAlphaFade());    // PRQA S 3803

  // if (isBasePlateCovered(animation.getWaveRadius() + m_scanWaveWidth*0.5f))
  // {
  //   m_view->setAssetValue(core::AssetId::EASSETS_BASEPLATE, false);
  // }
  // else
  // {
  //   // CAVEAT: the blurred baseplate currently does not support fading, fortunately it isn't terribly obvious due to the fast transition.
  //   m_view->setAssetValue(core::AssetId::EASSETS_BASEPLATE, true);
  // }

  const bool toAugmented = animation.isTransitioningToAugmentedView();

  if (toAugmented && animation.getCameraFadeFactor() > 0.0f)
  {
    fadeCamerasOut();
  }
  if (!toAugmented && animation.getCameraFadeFactor() < 1.0f)
  {
    fadeCamerasIn();
  }

  if (animation.hasFinished())
  {
    toAugmented ? finalizeAugmentedViewState() : finalizeSurroundViewState();

    // Swap animation queue, so that next update will proceed with the queued animation
    m_activeAnimation = 1u - m_activeAnimation;

    // Discard animation if we're already in the target state
    if (m_animation[m_activeAnimation]->isRunning() &&
        m_animation[m_activeAnimation]->isTransitioningToAugmentedView() == m_inAugmentedViewState)
    {
      m_animation[m_activeAnimation]->reset();
    }
  }
}


void AugmentedViewTransition::fadeCamerasOut()
{
  ParkViewRenderManager* const l_pvrm = dynamic_cast<ParkViewRenderManager*> (pc::factory::RenderManager::get(m_view.get())); // PRQA S 3077  // PRQA S 3400
  if (l_pvrm != nullptr)
  {
    l_pvrm->hideEnvironment();
  }
}


void AugmentedViewTransition::fadeCamerasIn()
{
  ParkViewRenderManager* const l_pvrm = dynamic_cast<ParkViewRenderManager*> (pc::factory::RenderManager::get(m_view.get())); // PRQA S 3077  // PRQA S 3400
  if (l_pvrm != nullptr)
  {
    l_pvrm->showEnvironment();
  }
}


void AugmentedViewTransition::prepareAnimationState()
{
  //using namespace cc::core;

  //! Enable transition geometry
  m_rootNode->setChildValue(m_additiveWavefrontGeode.get(), true);
  m_rootNode->setChildValue(m_innerRingGeode.get(),         true);
  m_rootNode->setChildValue(m_backgroundQuad.get(),         true);
  osg::StateSet* const l_backgroundStateSet = m_backgroundQuad->getOrCreateStateSet();
  l_backgroundStateSet->setMode(GL_STENCIL_TEST, osg::StateAttribute::ON);    // PRQA S 3143
  //! put background node in front of vehicle and surrounding, stencil test will also ensure correct depth testing
  const vfc::int32_t l_renderOrderEnv = std::max(pc::core::g_renderOrder->m_floor, pc::core::g_renderOrder->m_wall);
  l_backgroundStateSet->setRenderBinDetails(l_renderOrderEnv + 1, "RenderBin");

  // Assets not shown during transition
  //m_view->setAssetValue(AssetId::EASSETS_PARKINGSPOTS,               false);    // PRQA S 3803
  // m_view->setAssetValue(AssetId::EASSETS_TV2D_IMPOSPARKSELECT,       false);
  //m_view->setAssetValue(AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES, false);    // PRQA S 3803
  // m_view->setAssetValue(AssetId::EASSETS_TRAJECTORY_WHEEL_TRACKS,    false);
  // m_view->setAssetValue(AssetId::EASSETS_MINI_TOP_VIEW,              false);

  // Enable Street overlays and stencil test
  //m_view->setAssetValue(AssetId::EASSETS_STREETOVERLAY,   true);    // PRQA S 3803
  // if (m_streetOverlayStateSet.valid())
  // {
  //   m_streetOverlayStateSet->setMode(GL_STENCIL_TEST, osg::StateAttribute::ON);
  // }

  if (m_inAugmentedViewState)
  {
    fadeCamerasOut();
    // We're about to switch from augmented to camera...
    // m_view->setAssetValue(AssetId::EASSETS_BOWL_STATIC,     true);
    // m_view->setAssetValue(AssetId::EASSETS_BOWL_DYNAMIC,    true);
    m_view->setAssetValue(cc::core::AssetId::EASSETS_FLOOR,           true);    // PRQA S 3803
  }
}


void AugmentedViewTransition::finalizeAugmentedViewState()
{
  //using namespace cc::core;

  //m_view->setAssetValue(AssetId::EASSETS_PARKINGSPOTS,               true);    // PRQA S 3803
  // m_view->setAssetValue(AssetId::EASSETS_STREETOVERLAY,              true);    // PRQA S 3803
  // m_view->setAssetValue(AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES, false);    // PRQA S 3803
  // m_view->setAssetValue(AssetId::EASSETS_FLOOR,                      false);    // PRQA S 3803
  //m_view->setAssetValue(AssetId::EASSETS_BACKGROUND,                 true);     // PRQA S 3803
  //m_view->setAssetValue(AssetId::EASSETS_UI_QUIT_BUTTON,              true);     // PRQA S 3803
  //m_view->setAssetValue(AssetId::EASSETS_UI_SETTINGBAR_ICON,         true);     // PRQA S 3803
  // m_view->setAssetValue(AssetId::EASSETS_PARKINGTYPECONFIRM,         true);     // PRQA S 3803
  //m_view->setAssetValue(AssetId::EASSETS_UI_PARKING_BACKGROUND,         true);     // PRQA S 3803


  // Just to be safe
  fadeCamerasIn();

  //! disable stencil test for street overlay
  // if (m_streetOverlayStateSet.valid())
  // {
  //   m_streetOverlayStateSet->setMode(GL_STENCIL_TEST, osg::StateAttribute::OFF);
  // }

  // Disable transition geometry
  m_rootNode->setChildValue(m_additiveWavefrontGeode.get(), false);
  m_rootNode->setChildValue(m_innerRingGeode.get(),         false);
  // Enable black background and disable stencil testing
  m_rootNode->setChildValue(m_backgroundQuad.get(),         false);
  osg::StateSet* const l_backgroundStateSet = m_backgroundQuad->getOrCreateStateSet();
  l_backgroundStateSet->setMode(GL_STENCIL_TEST, osg::StateAttribute::OFF);  // PRQA S 3143
  // //! put background behind vehicle model since stencil testing is disabled now
  l_backgroundStateSet->setRenderBinDetails(pc::core::g_renderOrder->m_carOpaque - 1, "RenderBin");

  m_inAugmentedViewState = true;
  //per default the scan waves are allowed
  m_scanWaveAllowed = true;
}


void AugmentedViewTransition::finalizeSurroundViewState()
{
  // Disable transition geometry
  m_rootNode->setChildValue(m_innerRingGeode.get(),         false);
  m_rootNode->setChildValue(m_additiveWavefrontGeode.get(), false);
  // Disable black background
  m_rootNode->setChildValue(m_backgroundQuad.get(),         false);

  //using namespace cc::core;
  //m_view->setAssetValue(AssetId::EASSETS_PARKINGSPOTS,               false);    // PRQA S 3803
  // m_view->setAssetValue(AssetId::EASSETS_TV2D_IMPOSPARKSELECT,       false);
  //m_view->setAssetValue(AssetId::EASSETS_STREETOVERLAY,              false);    // PRQA S 3803
  // m_view->setAssetValue(AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES, true);    // PRQA S 3803
  // m_view->setAssetValue(AssetId::EASSETS_TRAJECTORY_WHEEL_TRACKS,    true);
  // m_view->setAssetValue(AssetId::EASSETS_BOWL_STATIC,                true);
  // m_view->setAssetValue(AssetId::EASSETS_BOWL_DYNAMIC,               true);
  // m_view->setAssetValue(AssetId::EASSETS_FLOOR,                      true);    // PRQA S 3803
  // m_view->setAssetValue(AssetId::EASSETS_BASEPLATE,                  true);
  // m_view->setAssetValue(AssetId::EASSETS_MINI_TOP_VIEW,              true);
  //m_view->setAssetValue(AssetId::EASSETS_BACKGROUND,                 false);     // PRQA S 3803
  //m_view->setAssetValue(AssetId::EASSETS_UI_QUIT_BUTTON,              false);     // PRQA S 3803
  //m_view->setAssetValue(AssetId::EASSETS_UI_SETTINGBAR_ICON,         false);     // PRQA S 3803
  // m_view->setAssetValue(AssetId::EASSETS_PARKINGTYPECONFIRM,         false);     // PRQA S 3803
  //m_view->setAssetValue(AssetId::EASSETS_UI_PARKING_BACKGROUND,         false);     // PRQA S 3803

  if (m_scanWavesSwitch != nullptr)
  {
    m_scanWavesSwitch->setAllChildrenOff();    // PRQA S 3803
  }

  // Just to be safe
  fadeCamerasIn();

  m_inAugmentedViewState = false;
}


void AugmentedViewTransition::traverse(osg::NodeVisitor& f_nv)    // PRQA S 6043
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    // if (!m_streetOverlayStateSet.valid())
    // {
    //   // Setup stencil operation on Street overlay
    //   pc::core::Asset* l_asset = m_view->getAsset(cc::core::AssetId::EASSETS_STREETOVERLAY);
    //   if (l_asset)
    //   {
    //     osg::Node* l_streetOverlay = l_asset->getAsset();
    //     if (l_streetOverlay)
    //     {
    //       m_streetOverlayStateSet = l_streetOverlay->getOrCreateStateSet();
    //       //check if stencil is already existing,
    //       osg::Stencil* l_stencil = dynamic_cast<osg::Stencil*>(m_streetOverlayStateSet->getAttribute(osg::StateAttribute::STENCIL));
    //       if (!l_stencil)
    //       {
    //         //if not create a new one
    //         l_stencil = new osg::Stencil;
    //       }
    //       l_stencil->setFunction(osg::Stencil::EQUAL, 240u, ~0u);
    //       l_stencil->setOperation(osg::Stencil::KEEP, osg::Stencil::KEEP, osg::Stencil::KEEP);
    //       m_streetOverlayStateSet->setAttributeAndModes(l_stencil, osg::StateAttribute::OFF);
    //     }
    //   }
    // }
    if (m_modifiedCount != g_augmentedSettings->getModifiedCount())
    {
      m_modifiedCount = g_augmentedSettings->getModifiedCount();
      osg::MatrixTransform::setMatrix(osg::Matrix::translate(g_augmentedSettings->m_position));
    }
    updateTransitionState(static_cast<vfc::float32_t>(f_nv.getFrameStamp()->getReferenceTime()));
  }
  osg::Group::traverse(f_nv);
}


bool AugmentedViewTransition::isTransitioning() const  //PRQA S 1724
{
  return m_animation[m_activeAnimation]->isRunning();
}


bool AugmentedViewTransition::isTransitioningToAugmentedView() const  //PRQA S 1724
{
  return m_animation[m_activeAnimation]->isRunning() && m_animation[m_activeAnimation]->isTransitioningToAugmentedView();
}


bool AugmentedViewTransition::isTransitioningFromAugmentedView() const  //PRQA S 1724
{
  return m_animation[m_activeAnimation]->isRunning() && !m_animation[m_activeAnimation]->isTransitioningToAugmentedView();
}


bool AugmentedViewTransition::isInAugmentedView() const  //PRQA S 1724
{
  return m_inAugmentedViewState;
}


pc::animation::Animation* AugmentedViewTransition::createTransitionToAugmentedViewAnimation(bool f_instantSwitch)
{
  return new AugmentedViewTransitionAnimation(true, f_instantSwitch, this);
}


pc::animation::Animation* AugmentedViewTransition::createTransitionFromAugmentedViewAnimation(bool f_instantSwitch)
{
  return new AugmentedViewTransitionAnimation(false, f_instantSwitch, this);
}

osg::Vec2f AugmentedViewTransition::getWavefrontCenter() const
{
  osg::Vec2f l_center_pos;

  if (m_themeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT)
  {
    l_center_pos = osg::Vec2f(g_augmentedSettings->m_scanWaveCenterXVert, 0);
  }
  else
  {
    l_center_pos = osg::Vec2f(g_augmentedSettings->m_scanWaveCenterXVert, 0);
  }

  return l_center_pos;
}


// bool AugmentedViewTransition::isBasePlateCovered(vfc::float32_t f_radius) const
// {
//   osg::Vec2f l_min, l_max;
//   if (!getBasePlateExtent(l_min, l_max))
//   {
//     return true;
//   }
//   osg::Vec2f l_center = getWavefrontCenter();

//   pc::util::Box2D l_box(l_min, l_max);
//   vfc::float32_t l_radius2 = f_radius*f_radius;

//   // Iterate over 4 corners of the baseplate AABB
//   for (std::size_t i = 0; i < 4; i++)
//   {
//     // and check if any are outside the initial wavefront
//     if ((l_box[i] - l_center).length2() > l_radius2)
//     {
//       return false;
//     }
//   }
//   return true;
// }

// bool AugmentedViewTransition::getBasePlateExtent(osg::Vec2f& f_min, osg::Vec2f& f_max) const
// {
//   const vfc::float32_t l_m = std::numeric_limits<vfc::float32_t>::max();
//   f_min =  osg::Vec2f(l_m, l_m);
//   f_max = -osg::Vec2f(l_m, l_m);
//   if (nullptr == m_view)
//   {
//     return false;
//   }
//   cc::assets::common::Floor* l_floor = dynamic_cast<cc::assets::common::Floor*>(m_view->getAsset(cc::core::EASSETS_FLOOR));
//   if (nullptr == l_floor)
//   {
//     return false;
//   }
//   return l_floor->getNoCamExtent(f_min, f_max);
// }


} // namespace augmentedview
} // namespace assets
} // namespace cc
