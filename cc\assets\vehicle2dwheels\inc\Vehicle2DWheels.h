//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC BYD_DENZA&MR
/// @file  Vehicle2DWheels.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_VEHICLE2DWHEELS_VEHICLE2DWHEELS_H
#define CC_ASSETS_VEHICLE2DWHEELS_VEHICLE2DWHEELS_H

#include "cc/daddy/inc/CustomDaddyTypes.h"

#include <osg/Group>
#include <osg/MatrixTransform>
#include "cc/target/common/inc/commonInterface.h"

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace vehicle2dwheels
{

//!
//! Vehicle2DWheelsSetting
//!
class Vehicle2DWheelsSetting : public pc::util::coding::ISerializable
{
public:

    Vehicle2DWheelsSetting()
    : m_wheelSize(0.7f, 0.3f)
    , m_wheelPlanViewPos(215.0f, 355.0f)
    , m_wheelParkingPlanViewPos(854.0f, 355.0f)
    , m_wheel(CONCAT_PATH("cc/resources/icons/wheel.png"))
    , m_groundLevel(0.2f)
    , m_rearWheelAngleFactor(2.0f)
    {
    }

    SERIALIZABLE(Vehicle2DWheelsSetting)
    {
        ADD_MEMBER(osg::Vec2f, wheelSize);
        ADD_MEMBER(osg::Vec2f, wheelPlanViewPos);
        ADD_MEMBER(osg::Vec2f, wheelParkingPlanViewPos);
        ADD_FLOAT_MEMBER(groundLevel);
        ADD_FLOAT_MEMBER(rearWheelAngleFactor);
    }

    osg::Vec2 m_wheelSize;
    osg::Vec2 m_wheelPlanViewPos;
    osg::Vec2 m_wheelParkingPlanViewPos;
    vfc::float32_t m_rearWheelAngleFactor;
    std::string m_wheel;
    vfc::float32_t m_groundLevel;
};

extern pc::util::coding::Item<Vehicle2DWheelsSetting> g_vehicle2DWheelsSettings;

class Vehicle2DWheelsUpdateCallback: public osg::NodeCallback
{

public:
    Vehicle2DWheelsUpdateCallback(
        pc::core::Framework* f_pFramework,
        const bool f_isLeft
    );

    virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

protected:
    virtual ~Vehicle2DWheelsUpdateCallback() = default;

private:
    //! Copy constructor is not permitted.
    Vehicle2DWheelsUpdateCallback (const Vehicle2DWheelsUpdateCallback& other); // = delete
    //! Copy assignment operator is not permitted.
    Vehicle2DWheelsUpdateCallback& operator=(const Vehicle2DWheelsUpdateCallback& other); // = delete

    osg::ref_ptr<osg::Geode> m_geodeWheelFL;
    osg::ref_ptr<osg::Geode> m_geodeWheelFR;
    osg::ref_ptr<osg::Geode> m_geodeWheelRL;
    osg::ref_ptr<osg::Geode> m_geodeWheelRR;
    pc::core::Framework* m_pFramework;
};

//!
//! vehicle2dwheels
//!
class Vehicle2DWheels : public osg::MatrixTransform
{
public:

    Vehicle2DWheels(pc::core::Framework* f_framework);

    bool getAnimXDirection()
    {
        return (m_animXDirection);
    }
    void setAnimXDirection(bool f_dirX)
    {
        m_animXDirection = f_dirX;
    }

    void setupCommonState();

    void updateWheelSteering();

    virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:

    void init();

    void updateCrabWheelAngle();

    virtual ~Vehicle2DWheels() = default;

    pc::core::Framework* m_framework;
    osg::ref_ptr<osg::MatrixTransform> m_vehicle2DWheelsFL;
    osg::ref_ptr<osg::MatrixTransform> m_vehicle2DWheelsFR;
    osg::ref_ptr<osg::MatrixTransform> m_vehicle2DWheelsRL;
    osg::ref_ptr<osg::MatrixTransform> m_vehicle2DWheelsRR;
    osg::Vec2 m_position;
    osg::Vec2 m_size;
    vfc::float32_t m_steeringWheelAngleFront;
    vfc::float32_t m_steeringWheelAngleRear;
    vfc::uint32_t m_settingsModifiedCount;
    vfc::float32_t m_lastUpdateTime;
    vfc::float32_t m_drivenDistance;
    bool m_animXDirection;

private:
  //! Copy constructor is not permitted.
  Vehicle2DWheels (const Vehicle2DWheels& other); // = delete
  //! Copy assignment operator is not permitted.
  Vehicle2DWheels& operator=(const Vehicle2DWheels& other); // = delete

    void update();
    bool isViewIncludeVehicle2DWheels();

};

osg::Texture2D* loadTexturInVehicle2DWheel(const std::string& f_filename);

} // namespace vehicle2dwheels
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_VEHICLE2DWHEELS_VEHICLE2DWHEELS_H
