//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EXTERNAL Castellane Florian (Altran, CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  PanoramaView.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_PANORAMAVIEW_PANORAMAVIEW_H
#define CC_ASSETS_PANORAMAVIEW_PANORAMAVIEW_H

#include <osg/Array>
#include <osg/Camera>

#include "pc/svs/core/inc/View.h"
#include "pc/svs/core/inc/SystemConf.h"
#include "pc/svs/c2w/inc/CylDistCorr.h"
#include "pc/svs/util/osgx/inc/ProjectionObjects.h"



namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace views
{
namespace panoramaview
{

//!
//! PanoramaViewData
//!
class PanoramaViewData : public pc::util::coding::ISerializable
{
public:

  PanoramaViewData()
    : m_horizontalStart(0.0f)
    , m_horizontalEnd(8.0f)
    , m_verticalStart(0.0f)
    , m_verticalEnd(6.0f)
    , m_resolutionHorizontal(20u)
    , m_resolutionVertical(10u)
    , m_planeWidth(8.0f)
    , m_planeHeight(8.0f)
    , m_noOfPoints1(30u)
    , m_noOfPoints2(40u)
    , m_noOfPoints3(30u)
    , m_widthMiddlePart(0.5f)
    , m_widthOuterPart(0.5f)
    , m_height(1.0f)
    , m_depth (0.4f)
    , m_verticalOffset(-0.6f)
    , m_fov(180.0f)
  {
  }

  SERIALIZABLE(PanoramaViewData)
  {
    ADD_FLOAT_MEMBER(horizontalStart);
    ADD_FLOAT_MEMBER(horizontalEnd);
    ADD_FLOAT_MEMBER(verticalStart);
    ADD_FLOAT_MEMBER(verticalEnd);
    ADD_UINT32_MEMBER(resolutionHorizontal);
    ADD_UINT32_MEMBER(resolutionVertical);
    ADD_FLOAT_MEMBER(planeWidth);
    ADD_FLOAT_MEMBER(planeHeight);
    ADD_UINT32_MEMBER(noOfPoints1);
    ADD_UINT32_MEMBER(noOfPoints2);
    ADD_UINT32_MEMBER(noOfPoints3);
    ADD_FLOAT_MEMBER(widthMiddlePart);
    ADD_FLOAT_MEMBER(widthOuterPart);
    ADD_FLOAT_MEMBER(height);
    ADD_FLOAT_MEMBER(depth);
    ADD_FLOAT_MEMBER(verticalOffset);
    ADD_FLOAT_MEMBER(fov);
    ADD_MEMBER(pc::c2w::CylinderData, cylinder);
  }

  vfc::float32_t m_horizontalStart;
  vfc::float32_t m_horizontalEnd;
  vfc::float32_t m_verticalStart;
  vfc::float32_t m_verticalEnd;
  vfc::uint32_t m_resolutionHorizontal;
  vfc::uint32_t m_resolutionVertical;
  vfc::float32_t m_planeWidth;
  vfc::float32_t m_planeHeight;
  vfc::uint32_t m_noOfPoints1;
  vfc::uint32_t m_noOfPoints2;
  vfc::uint32_t m_noOfPoints3;
  vfc::float32_t m_widthMiddlePart;
  vfc::float32_t m_widthOuterPart;
  vfc::float32_t m_height;
  vfc::float32_t m_depth;
  vfc::float32_t m_verticalOffset;
  vfc::float32_t m_fov;
  pc::c2w::CylinderData m_cylinder;
};

extern pc::util::coding::Item<PanoramaViewData> g_panoViewDataFront;
extern pc::util::coding::Item<PanoramaViewData> g_panoViewDataRear;

//======================================================
// PanoramaView
//------------------------------------------------------
/// Generate and render the sorround view background.
/// Use a geode 3D object and fit the sorround images
/// as textures.
/// <AUTHOR> Castellane Florian (Altran, CC-DA/EAV3)
//======================================================
class PanoramaView : public pc::core::View
{
public:

  enum Direction
  {
    PANORAMAVIEW_FRONT = 0,
    PANORAMAVIEW_REAR  = 1
  };

  PanoramaView(pc::core::Framework* f_framework, Direction f_direction,
    const std::string& f_name,
    const pc::core::Viewport& f_viewport);

  void updateUsedTexture();

  bool updateCalibration();     // Function trigger to update the Texture Coords based on the new Extrinsic Calib values.

  virtual void traverse(osg::NodeVisitor& f_nv);

  osg::ref_ptr<pc::util::osgx::ProjectionObjects> m_projObj;
  osg::ref_ptr<osg::Geode> m_greyGeode;


protected:

  virtual ~PanoramaView();

private:

  //! Copy constructor is not permitted.
  PanoramaView (const PanoramaView& other); // = delete
  //! Copy assignment operator is not permitted.
  PanoramaView& operator=(const PanoramaView& other); // = delete

  void computeCylinderTextureCoordinates();

  pc::core::Framework* m_framework;

  pc::core::sysconf::Cameras m_camId;

  pc::c2w::Cylinder m_CylDistCorr;

  osg::ref_ptr<osg::Vec3Array> m_pVertexArray;
  osg::ref_ptr<osg::Vec2Array> m_pTexCoord;
  vfc::uint32_t    m_resH;
  vfc::uint32_t    m_resW;

};


//======================================================
// PanoramaViewUpdateCallback
//------------------------------------------------------
/// Updates the texture coordinates for the Panorama View
/// based on Calibration updates
///
//======================================================
class PanoramaViewUpdateCallback: public osg::NodeCallback
{
public:
  PanoramaViewUpdateCallback( pc::core::Framework* f_pFramework);

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

private:
  pc::core::Framework* m_pFramework;
  vfc::int32_t m_seqCtr;                     // Assigning a sequence counter per instance for tracking new DADDY updates
};

pc::core::sysconf::Cameras getCamId(PanoramaView::Direction f_direction);
const PanoramaViewData& getViewData(PanoramaView::Direction f_direction);

} // namespace panoramaview
} // namespace views
} // namespace cc


#endif // CC_ASSETS_PANORAMAVIEW_PANORAMAVIEW_H
