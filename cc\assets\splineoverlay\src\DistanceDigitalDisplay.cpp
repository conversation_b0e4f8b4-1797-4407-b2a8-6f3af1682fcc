//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  DistanceDigitalDisplay.cpp
/// @brief
//=============================================================================

#include "cc/assets/splineoverlay/inc/DistanceDigitalDisplay.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"

#include "osg/Depth"
#include "osg/Geode"
#include "osg/Geometry"
#include "osg/MatrixTransform"
#include "osg/Texture2D"
#include "osgDB/ReadFile"

#include <string>

using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{
namespace distancedigitaldisplay
{

pc::util::coding::Item<DistanceDigitalDisplaySettings> g_displaySettings("DistanceDigitalDisplay");

inline osg::Texture2D* loadTexture(const std::string& f_filename)
{
  // static std::map< std::string, osg::ref_ptr<osg::Texture2D> > l_textureCache;
  // std::map< std::string, osg::ref_ptr<osg::Texture2D> >::const_iterator l_result = l_textureCache.find(f_filename);
  // if (l_result != l_textureCache.end())
  // {
  //   return l_result->second.get();
  // }
  const osg::ref_ptr<osg::Image> l_image = osgDB::readImageFile(f_filename);
  if (l_image.valid())
  {
    osg::Texture2D* const l_texture = new osg::Texture2D(l_image);
    l_texture->setDataVariance(osg::Object::STATIC);
    l_texture->setUnRefImageDataAfterApply(true);
    l_texture->setResizeNonPowerOfTwoHint(true);
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP);
    l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP);
    // l_textureCache[f_filename] = l_texture;
    return l_texture;
  }
  else
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "DistanceDigitalDisplay::loadTexture(): Could not load " << f_filename);  //PRQA S 4060
  }
  return nullptr;
}


//!
//! RctaOverlay
//!
DistanceDigitalDisplay::DistanceDigitalDisplay(pc::core::Framework* f_framework)
  : m_framework(f_framework) // PRQA S 4050
  , m_settingsModifiedCount(~0u)
  , m_screenID(EScreenID_SINGLE_FRONT_NORMAL)
  , m_stopTextFrontPositionShow(g_displaySettings->m_stopTextFrontPosition)
  , m_stopTextRearPositionShow(g_displaySettings->m_stopTextRearPosition)
{
  setName("DistanceDigitalDisplay");
  setNumChildrenRequiringUpdateTraversal(1u);
}


DistanceDigitalDisplay::~DistanceDigitalDisplay() = default;


void DistanceDigitalDisplay::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    if (updateNeeded() || g_displaySettings->getModifiedCount() != m_settingsModifiedCount)
    {
      init();
      removeUpdateCallback(getUpdateCallback());
      addUpdateCallback(
        new DistanceDigitalDisplayUpdateCallback(
          m_FrontSectorDisGeode,
          m_RearSectorDisGeode,
          m_FrontSectorWarnImageGeode,
          m_RearSectorWarnImageGeode,
          m_framework
        )
      );
      m_settingsModifiedCount = g_displaySettings->getModifiedCount();
    }
  }
  osg::Group::traverse(f_nv);
}


void DistanceDigitalDisplay::init()
{
  removeChildren(0u, getNumChildren());    // PRQA S 3803
  m_FrontSectorDisGeode.release();    // PRQA S 3804
  m_RearSectorDisGeode.release();    // PRQA S 3804
  m_FrontSectorWarnImageGeode.release();    // PRQA S 3804
  m_RearSectorWarnImageGeode.release();    // PRQA S 3804

  vfc::float32_t fontSize = g_displaySettings->m_fontSizeNormal;

  if (
    m_screenID == EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__LEFT_SIDE || m_screenID == EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__RIGHT_SIDE ||
    m_screenID == EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__LEFT_SIDE   || m_screenID == EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__RIGHT_SIDE)
  {
    fontSize = g_displaySettings->m_fontSizeNormalEnlarge;
  }

  if (m_screenID == EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE || m_screenID == EScreenID_VERT_FULLSCREEN_REAR_ENLARGE)
  {
    fontSize = g_displaySettings->m_fontSizeFullscreenEnlarge;
  }
  if (m_screenID == EScreenID_VERT_FULLSCREEN)
  {
    fontSize = g_displaySettings->m_fontSizeFullscreen;
  }

  //! FRONT SECTOR TEXT *********************************************************************
  {
    m_FrontSectorDisGeode = new osg::Geode;
    const osg::ref_ptr<osgText::Text> l_FrontShortestDis = new osgText::Text;
    osg::StateSet* const l_textStateSet = l_FrontShortestDis->getOrCreateStateSet();
    l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    l_basicTexShader.apply(l_textStateSet);  // PRQA S 3803
    l_FrontShortestDis->setFont(g_displaySettings->m_fontType);
    l_FrontShortestDis->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
    l_FrontShortestDis->setDrawMode(osgText::TextBase::TEXT ); // PRQA S 3143
    l_FrontShortestDis->setCharacterSize(fontSize);
    l_FrontShortestDis->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
    l_FrontShortestDis->setAxisAlignment(osgText::Text::XY_PLANE);
    l_FrontShortestDis->setAlignment(osgText::Text::CENTER_TOP);
    m_FrontSectorDisGeode->addDrawable(l_FrontShortestDis);
    addChild(m_FrontSectorDisGeode);    // PRQA S 3803
  }

  //! REAR SECTOR TEXT **********************************************************************
  {
    m_RearSectorDisGeode = new osg::Geode;
    const osg::ref_ptr<osgText::Text> l_RearShortestDis = new osgText::Text;
    osg::StateSet* const l_textStateSet = l_RearShortestDis->getOrCreateStateSet();
    l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    l_basicTexShader.apply(l_textStateSet);  // PRQA S 3803
    l_RearShortestDis->setFont(g_displaySettings->m_fontType);
    l_RearShortestDis->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
    l_RearShortestDis->setDrawMode(osgText::TextBase::TEXT ); // PRQA S 3143
    l_RearShortestDis->setCharacterSize(fontSize);
    l_RearShortestDis->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
    l_RearShortestDis->setAxisAlignment(osgText::Text::XY_PLANE);
    l_RearShortestDis->setAlignment(osgText::Text::CENTER_TOP);
    m_RearSectorDisGeode->addDrawable(l_RearShortestDis);
    addChild(m_RearSectorDisGeode);    // PRQA S 3803
  }

  //! FRONT SECTOR IMAGE
  {
    m_FrontSectorWarnImageGeode = new osg::Geode;
    osg::Texture2D* const l_stopTextTexture = loadTexture(CONCAT_PATH(g_displaySettings->m_stopTextTexture));
    const osg::Image* const l_image = l_stopTextTexture->getImage();
    // assert(l_image);
    osg::Vec2i l_textureSize{l_image->s(), l_image->t()}; // 200 x 50

    const osg::Vec3f l_front  = osg::Vec3f(m_stopTextFrontPositionShow.x(),
                                     - static_cast<vfc::float32_t>(l_textureSize.x()) / (g_displaySettings->m_stopScaleFactor_Hori * 2.0f),  //?
                                     0.0f);

    const osg::Vec3f l_width  = osg::Vec3f(0.0f,
                                     static_cast<vfc::float32_t>(l_textureSize.x()) / g_displaySettings->m_stopScaleFactor_Hori,
                                     0.0f);

    const osg::Vec3f l_height = osg::Vec3f(static_cast<vfc::float32_t>(l_textureSize.y()) / g_displaySettings->m_stopScaleFactor_Hori,
                                     0.0f,
                                     0.0f);

    osg::Geometry* const l_stopText = pc::util::osgx::createTexturePlane(l_front, l_width, l_height, 1, 1, 1.0f, 0.0f, 0.0f, 1.0f);
    osg::StateSet* const l_stopTextStateSet = l_stopText->getOrCreateStateSet();
    l_stopTextStateSet->setTextureAttribute(0u, l_stopTextTexture);
    l_stopTextStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    l_stopTextStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
    l_stopTextStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stopTextStateSet);
    m_FrontSectorWarnImageGeode->addDrawable(l_stopText);
    m_FrontSectorWarnImageGeode->setNodeMask(0u);
    addChild(m_FrontSectorWarnImageGeode);
  }

  //! REAR SECTOR IMAGE
  {
    m_RearSectorWarnImageGeode = new osg::Geode;
    osg::Texture2D* const l_stopTextTexture = loadTexture(CONCAT_PATH(g_displaySettings->m_stopTextTexture));
    const osg::Image* const l_image = l_stopTextTexture->getImage();
    // assert(l_image);
    osg::Vec2i l_textureSize{l_image->s(), l_image->t()};

    const osg::Vec3f l_rear  = osg::Vec3f(m_stopTextRearPositionShow.x(),
                                    - static_cast<vfc::float32_t>(l_textureSize.x()) / (g_displaySettings->m_stopScaleFactor_Hori * 2.0f),
                                    0.0f);

    const osg::Vec3f l_width  = osg::Vec3f(0.0f,
                                    static_cast<vfc::float32_t>(l_textureSize.x()) / g_displaySettings->m_stopScaleFactor_Hori,
                                    0.0f);

    const osg::Vec3f l_height = osg::Vec3f(static_cast<vfc::float32_t>(l_textureSize.y()) / g_displaySettings->m_stopScaleFactor_Hori,
                                    0.0f,
                                    0.0f);

    osg::Geometry* const l_stopText = pc::util::osgx::createTexturePlane(l_rear, l_width, l_height, 1, 1, 1.0f, 0.0f, 0.0f, 1.0f);
    osg::StateSet* const l_stopTextStateSet = l_stopText->getOrCreateStateSet();
    l_stopTextStateSet->setTextureAttribute(0u, l_stopTextTexture);
    l_stopTextStateSet->setMode(GL_DEPTH_TEST, static_cast<osg::StateAttribute::Values>(osg::StateAttribute::OFF));
    l_stopTextStateSet->setMode(GL_BLEND, static_cast<osg::StateAttribute::Values>(osg::StateAttribute::ON));
    l_stopTextStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stopTextStateSet);
    m_RearSectorWarnImageGeode->addDrawable(l_stopText);
    m_RearSectorWarnImageGeode->setNodeMask(0u);
    addChild(m_RearSectorWarnImageGeode);
  }
}

bool DistanceDigitalDisplay::updateNeeded()
{
  bool l_isChanged = false;
  EScreenID l_currentScreenID   = m_screenID;
  cc::target::common::EThemeTypeHU l_currentTheme   = cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI;

  if (true == m_framework->asCustomFramework()->m_displayedView_ReceiverPort.isConnected())
  {
    const cc::daddy::SVSDisplayedViewDaddy_t* const l_pDataDaddy = m_framework->asCustomFramework()->m_displayedView_ReceiverPort.getData();
    if (nullptr != l_pDataDaddy)
    {
      l_currentScreenID = l_pDataDaddy->m_Data;
    }
  }

  if (true == m_framework->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver.isConnected())
  {
    const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType = m_framework->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver.getData();
    if (nullptr != l_themeType)
    {
      l_currentTheme = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data);
    }
  }

  if ((l_currentScreenID != m_screenID) &&
      (l_currentTheme ==  cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT))  // change to vert parking view
  {
    m_stopTextFrontPositionShow = g_displaySettings->m_stopTextFrontPosition_vert_parking;
    m_stopTextRearPositionShow = g_displaySettings->m_stopTextRearPosition_vert_parking;
    l_isChanged = true;
  }
  else if ((l_currentScreenID != m_screenID) &&
           (l_currentTheme ==  cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI))  // change from vert parking view
  {
    m_stopTextFrontPositionShow = g_displaySettings->m_stopTextFrontPosition;
    m_stopTextRearPositionShow = g_displaySettings->m_stopTextRearPosition;
    l_isChanged = true;
  }
  else
  {
    // do nothing
  }

  m_screenID = l_currentScreenID;

  return l_isChanged;
}


DistanceDigitalDisplayUpdateCallback::DistanceDigitalDisplayUpdateCallback(
  osg::ref_ptr<osg::Geode> f_FrontSectorDisGeode,
  osg::ref_ptr<osg::Geode> f_RearSectorDisGeode,
  osg::ref_ptr<osg::Geode> f_FrontSectorWarnImageGeode,
  osg::ref_ptr<osg::Geode> f_RearSectorWarnImageGeode,
  pc::core::Framework* f_pFramework)
  : Object()
  , NodeCallback()
  , m_FrontSectorDisGeode(f_FrontSectorDisGeode)
  , m_RearSectorDisGeode(f_RearSectorDisGeode)
  , m_FrontSectorWarnImageGeode(f_FrontSectorWarnImageGeode)
  , m_RearSectorWarnImageGeode(f_RearSectorWarnImageGeode)
  , m_IsObjMovingL1_Sector_0(false)
  , m_IsObjMovingL1_Sector_1(false)
  , m_IsObjMovingL1_Sector_14(false)
  , m_IsObjMovingL1_Sector_15(false)
  , m_IsObjMovingL1_Sector_6(false)
  , m_IsObjMovingL1_Sector_7(false)
  , m_IsObjMovingL1_Sector_8(false)
  , m_IsObjMovingL1_Sector_9(false)
  , m_previousDistanceL1_Sector_0(g_defaultDistance)
  , m_previousDistanceL1_Sector_1(g_defaultDistance)
  , m_previousDistanceL1_Sector_14(g_defaultDistance)
  , m_previousDistanceL1_Sector_15(g_defaultDistance)
  , m_previousDistanceL1_Sector_6(g_defaultDistance)
  , m_previousDistanceL1_Sector_7(g_defaultDistance)
  , m_previousDistanceL1_Sector_8(g_defaultDistance)
  , m_previousDistanceL1_Sector_9(g_defaultDistance)
  , m_IsObjMovingL3_Sector_0(false)
  , m_IsObjMovingL3_Sector_1(false)
  , m_IsObjMovingL3_Sector_14(false)
  , m_IsObjMovingL3_Sector_15(false)
  , m_IsObjMovingL3_Sector_6(false)
  , m_IsObjMovingL3_Sector_7(false)
  , m_IsObjMovingL3_Sector_8(false)
  , m_IsObjMovingL3_Sector_9(false)
  , m_previousDistanceL3_Sector_0(g_defaultDistance)
  , m_previousDistanceL3_Sector_1(g_defaultDistance)
  , m_previousDistanceL3_Sector_14(g_defaultDistance)
  , m_previousDistanceL3_Sector_15(g_defaultDistance)
  , m_previousDistanceL3_Sector_6(g_defaultDistance)
  , m_previousDistanceL3_Sector_7(g_defaultDistance)
  , m_previousDistanceL3_Sector_8(g_defaultDistance)
  , m_previousDistanceL3_Sector_9(g_defaultDistance)
  , m_IsObjMovingL2_Sector_0(false)
  , m_IsObjMovingL2_Sector_15(false)
  , m_IsObjMovingL2_Sector_7(false)
  , m_IsObjMovingL2_Sector_8(false)
  , m_previousDistanceL2_Sector_0(g_defaultDistance)
  , m_previousDistanceL2_Sector_15(g_defaultDistance)
  , m_previousDistanceL2_Sector_7(g_defaultDistance)
  , m_previousDistanceL2_Sector_8(g_defaultDistance)
  , m_frontSectorShortestDis(g_defaultDistance)
  , m_frontSectorShortestDisIndex(g_defaultIndex)
  , m_rearSectorShortestDis(g_defaultDistance)
  , m_rearSectorShortestDisIndex(g_defaultIndex)
  , m_initialized(false)
  , m_textPositions()
  , m_ussZoneNum(static_cast<vfc::uint32_t>(cc::target::sysconf::E_ULTRASONIC_NUM_ZONES))
  , m_pFramework(f_pFramework)
{
  m_textPositions = std::vector<DistanceDigitalDisplayProperties>(m_ussZoneNum);
}


/**
 * @brief find Best Fit Line Coefficient y = B_0 + B_1 * x
 *
 * @param f_interpolatePoints size refer to tilespline NUM_INTERPOLATED_POINTS
 * @param f_orientation orientation of displayed text
 */
void findBestFitLineCoefficient(const cc::assets::tileoverlay::TileUpdateVisitor::PointArray& f_interpolatePoints, vfc::float32_t &f_orientation, vfc::uint32_t f_index, vfc::float32_t& f_x, vfc::float32_t& f_y)
{
  const size_t SIZE = f_interpolatePoints.size();
  vfc::float32_t x_average = 0.0f;
  vfc::float32_t y_average = 0.0f;
  vfc::float32_t xy = 0.0f;
  vfc::float32_t xx = 0.0f;
  vfc::float32_t SS_xy = 0.0f;
  vfc::float32_t SS_xx = 0.0f;
  vfc::float32_t b_1 = 0.0f;
  // vfc::float32_t b_0 = 0.0f;

  for (vfc::uint32_t i = 0u; i < SIZE; i++)
  {
    x_average += f_interpolatePoints[i].x() / static_cast<vfc::float32_t>(SIZE);
    y_average += f_interpolatePoints[i].y() / static_cast<vfc::float32_t>(SIZE);
    xy += f_interpolatePoints[i].x() * f_interpolatePoints[i].y();
    xx += f_interpolatePoints[i].x() * f_interpolatePoints[i].x();
  }
  SS_xx = xx - static_cast<vfc::float32_t>(SIZE) * x_average * x_average;
  SS_xy = xy - static_cast<vfc::float32_t>(SIZE) * y_average * x_average;
  b_1 = SS_xy / SS_xx;

  f_x = x_average;
  f_y = y_average;
  switch (f_index)
  {
    //! FRONT
    case FRONT_MIDDLE_RIGHT:
    {
      f_orientation = pc::util::polarAngle(osg::Vec2f{-1.0f, -b_1}) + osg::DegreesToRadians(-90.0f);
      break;
    }
    case FRONT_MIDDLE_LEFT:
    {
      f_orientation = pc::util::polarAngle(osg::Vec2f{-1.0f, -b_1}) + osg::DegreesToRadians(90.0f);
      break;
    }
    case FRONT_LEFT:
    {
      f_orientation = pc::util::polarAngle(osg::Vec2f{  b_1, -1.0f});
      break;
    }
    case FRONT_RIGHT:
    {
      f_orientation = pc::util::polarAngle(osg::Vec2f{ -b_1,  1.0f});
      break;
    }

    //! REAR
    case REAR_MIDDLE_RIGHT:
    {
      f_orientation = pc::util::polarAngle(osg::Vec2f{ -b_1, 1.0f}) + osg::DegreesToRadians(180.0f);
      break;
    }
    case REAR_MIDDLE_LEFT:
    {
      f_orientation = pc::util::polarAngle(osg::Vec2f{ -b_1, 1.0f});
      break;
    }
    case REAR_LEFT:
    {
      f_orientation = pc::util::polarAngle(osg::Vec2f{ b_1, -1.0f}) + osg::DegreesToRadians(180.0f);
      break;
    }
    case REAR_RIGHT:
    {
      f_orientation = pc::util::polarAngle(osg::Vec2f{ b_1, -1.0f});
      break;
    }
    default:
    {
      f_orientation = 0.0f;
      break;
    }
  }
}


bool DistanceDigitalDisplayUpdateCallback::updateOrientation()
{
  if (!m_pFramework->asCustomFramework()->m_tileSplineInterpolateArrayReceiver.isConnected())
  {
    return false; // UPDATE NOT SUCCESS AS RECEIVER PORT NOT CONNECTED
  }
  else
  {
    const cc::daddy::TileSplineInterpolateArrayDaddy* const l_pointArrayDaddy = m_pFramework->asCustomFramework()->m_tileSplineInterpolateArrayReceiver.getData();
    if (l_pointArrayDaddy == nullptr)
    {
      return false; // INIT NOT SUCCESS AS DATA IS NOT AVAILABLE
    }
    else
    {
      const cc::assets::tileoverlay::TileUpdateVisitor::PointArrayZone l_pointArray = l_pointArrayDaddy->m_Data;
      for (vfc::uint32_t i = 0u; i < m_ussZoneNum; i++)
      {
        findBestFitLineCoefficient(l_pointArray[i], m_textPositions[i].m_orientation, i,
          m_textPositions[i].m_x,
          m_textPositions[i].m_y);
      }
      return true; // UPDATE SUCCESS
    }
  }
}

bool checkCornerSector(const vfc::uint32_t& f_index)
{
  switch(f_index)
  {
    case FRONT_LEFT:
    case FRONT_RIGHT:
    case REAR_LEFT:
    case REAR_RIGHT:
    {
      return true;
    }
    default:
    {
      return false;
    }
  }
}


//! OUT OF RANGE, NO DISPLAY */
bool DistanceDigitalDisplayUpdateCallback::checkDistanceThreshL0(const vfc::float32_t& f_distance, const vfc::uint32_t& f_index)
{
  const bool l_isMoving = getMovingStatusByIndex(f_index);
  const vfc::float32_t l_maxDistanceThresh = getMaxDistanceByIndex(f_index);
  const vfc::float32_t l_hysteresisDistanceThresh = cc::assets::tileoverlay::g_tileSettings->m_HysteresisDistanceThresh;
  if (l_isMoving)
  {
    return isGreater(f_distance, l_maxDistanceThresh + l_hysteresisDistanceThresh);
  }
  else
  {
    return isGreater(f_distance, l_maxDistanceThresh);
  }
}


//! DISPLAY MODE 3, DANGER TEXT FIXED POSITION AT TOP */
bool DistanceDigitalDisplayUpdateCallback::checkDistanceThreshL1(const vfc::float32_t& f_distance, const vfc::uint32_t& f_index) const
{
  const bool l_isMoving = getMovingStatusByIndex(f_index);
  if (l_isMoving)
  {
    return isLessEqual(f_distance,
      cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1 + cc::assets::tileoverlay::g_tileSettings->m_HysteresisDistanceThresh);
  }
  else
  {
    return isLessEqual(f_distance, cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1);
  }
}


//! DISPLAY MODE 2, ROTATING TEXT AROUND TILE */ // 30 -> 70 cm
bool DistanceDigitalDisplayUpdateCallback::checkDistanceThreshL2(const vfc::float32_t& f_distance, const vfc::uint32_t& f_index) const
{
  vfc::float32_t l_upperThresh = cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL2; // default is not corner
  const vfc::float32_t l_lowerThresh = cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1;
  // bool l_isCorner = checkCornerSector(f_index); // corner is not needed
  const bool l_isMoving = getMovingStatusByIndex(f_index);
  const vfc::float32_t l_hysteresisDistanceThresh = cc::assets::tileoverlay::g_tileSettings->m_HysteresisDistanceThresh;
  if (l_isMoving) { l_upperThresh += l_hysteresisDistanceThresh; }
  return(
    isLessEqual(f_distance, l_upperThresh) &&
    isGreater  (f_distance, l_lowerThresh)
  );
}


//! DISPLAY MODE 1, DISTANCE SHOW FIXED POSITION AT TOP */ // 70 -> 120 cm
bool DistanceDigitalDisplayUpdateCallback::checkDistanceThreshL3(const vfc::float32_t& f_distance, const vfc::uint32_t& f_index) const
{
  const vfc::float32_t l_upperThresh = cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL3; // default is not corner
  const vfc::float32_t l_lowerThresh = cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL2;
  const bool l_isMoving = getMovingStatusByIndex(f_index);
  const bool l_isCorner = checkCornerSector(f_index);
  if (l_isCorner) {
    return false; // Corner limit is L2
  }
  if (l_isMoving)
  {
    const vfc::float32_t l_hysteresisDistanceThresh = cc::assets::tileoverlay::g_tileSettings->m_HysteresisDistanceThresh;
    return (
      isLessEqual(f_distance, l_upperThresh + l_hysteresisDistanceThresh) &&
      isGreater  (f_distance, l_lowerThresh + l_hysteresisDistanceThresh)
    );
  }
  else
  {
    return (
      isLessEqual(f_distance, l_upperThresh) &&
      isGreater  (f_distance, l_lowerThresh)
    );
  }
}


void DistanceDigitalDisplayUpdateCallback::updatefrontsector(const osg::NodeVisitor& /*f_nv*/, osg::ref_ptr<osg::Geode> f_Geode, osg::ref_ptr<osg::Geode> f_imgGeode)
{
  getFrontSectorShortestDis();  // PRQA S 3804
  f_Geode->setNodeMask(0u);
  f_imgGeode->setNodeMask(0u);
  if (checkDistanceThreshL0(m_frontSectorShortestDis, m_frontSectorShortestDisIndex))
  {
    return;
  }
  else
  {
    if (checkDistanceThreshL1(m_frontSectorShortestDis,m_frontSectorShortestDisIndex))
    {
      f_imgGeode->setNodeMask(~0u);
      return;
    }
    else
    {
      f_Geode->setNodeMask(~0u);
      osgText::Text* const l_FrontShortestDis = static_cast<osgText::Text*>(f_Geode->getDrawable(0u));
      const std::string l_textFrontString = std::to_string(pc::util::round2uInt(
                                      (m_frontSectorShortestDis > cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL3 ?
                                      cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL3 : m_frontSectorShortestDis)
                                      * 100.0f)) + "cm";
      l_FrontShortestDis->setText(l_textFrontString.c_str());

      if (checkDistanceThreshL2(m_frontSectorShortestDis, m_frontSectorShortestDisIndex))
      {
        const vfc::float32_t orientation = (m_textPositions[m_frontSectorShortestDisIndex].m_orientation);
        const vfc::float32_t x           = (m_textPositions[m_frontSectorShortestDisIndex].m_x);
        const vfc::float32_t y           = (m_textPositions[m_frontSectorShortestDisIndex].m_y);
        const vfc::float32_t dx          = (-std::cos(orientation)) * g_displaySettings->m_textOffsetPercentageFront;
        const vfc::float32_t dy          = (-std::sin(orientation)) * g_displaySettings->m_textOffsetPercentageFront;
        const osg::Vec3f l_frontPOS = osg::Vec3f {x + dx, y + dy, g_displaySettings->m_heightOverGround}; // Z AXIS is 0
        l_FrontShortestDis->setPosition(l_frontPOS);
        l_FrontShortestDis->setRotation(osg::Quat(orientation + osg::DegreesToRadians(90.0f), osg::Z_AXIS));
      }
      else if (checkDistanceThreshL3(m_frontSectorShortestDis, m_frontSectorShortestDisIndex))
      {
        l_FrontShortestDis->setPosition(osg::Vec3f{isVerticalParking() ? g_displaySettings->m_fixedFrontPosition_vert_parking : g_displaySettings->m_fixedFrontPosition, 0.0f, g_displaySettings->m_heightOverGround});
        l_FrontShortestDis->setRotation(osg::Quat(osg::DegreesToRadians(-90.0f), osg::Z_AXIS));
      }
      else
      {
        //do nothing
      }
      return;
    }
  }
}

void DistanceDigitalDisplayUpdateCallback::updaterearsector(const osg::NodeVisitor& /*f_nv*/, osg::ref_ptr<osg::Geode> f_Geode, osg::ref_ptr<osg::Geode> f_imgGeode)
{
  getRearSectorShortestDis();  // PRQA S 3804
  f_Geode->setNodeMask(0u);
  f_imgGeode->setNodeMask(0u);
  // if (isGreater(m_rearSectorShortestDis, cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL3))
  if (checkDistanceThreshL0(m_rearSectorShortestDis, m_rearSectorShortestDisIndex))
  {
    return;
  }
  else
  {
    // if (isLessEqual(m_rearSectorShortestDis, cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1))
    if (checkDistanceThreshL1(m_rearSectorShortestDis, m_rearSectorShortestDisIndex))
    {
      f_imgGeode->setNodeMask(~0u);
    }
    else
    {
      osgText::Text* const l_RearShortestDis = static_cast<osgText::Text*>(f_Geode->getDrawable(0u));
      const std::string l_textRearString = std::to_string(pc::util::round2uInt(
                                      (m_rearSectorShortestDis > cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL3 ?
                                      cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL3 : m_rearSectorShortestDis)
                                      * 100.0f)) + "cm";
      l_RearShortestDis->setText(l_textRearString.c_str());


      if (checkDistanceThreshL2(m_rearSectorShortestDis, m_rearSectorShortestDisIndex))
      {
        f_Geode->setNodeMask(~0u);
        const vfc::float32_t orientation = (m_textPositions[m_rearSectorShortestDisIndex].m_orientation);
        const vfc::float32_t x           = (m_textPositions[m_rearSectorShortestDisIndex].m_x);
        const vfc::float32_t y           = (m_textPositions[m_rearSectorShortestDisIndex].m_y);
        const vfc::float32_t dx          = std::cos(orientation) * g_displaySettings->m_textOffsetPercentageRear;
        const vfc::float32_t dy          = std::sin(orientation) * g_displaySettings->m_textOffsetPercentageRear;
        const osg::Vec3f l_rearPOS = osg::Vec3f {x + dx, y + dy, g_displaySettings->m_heightOverGround}; // Z AXIS is 0
        l_RearShortestDis->setPosition(l_rearPOS);
        l_RearShortestDis->setRotation(osg::Quat(orientation + osg::DegreesToRadians(90.0f), osg::Z_AXIS));
      }
      else if (checkDistanceThreshL3(m_rearSectorShortestDis, m_rearSectorShortestDisIndex))
      {
        f_Geode->setNodeMask(~0u);
        l_RearShortestDis->setPosition(osg::Vec3f{isVerticalParking() ? g_displaySettings->m_fixedRearPosition_vert_parking : g_displaySettings->m_fixedRearPosition, 0.0f, g_displaySettings->m_heightOverGround});
        l_RearShortestDis->setRotation(osg::Quat(osg::DegreesToRadians(-90.0f), osg::Z_AXIS));
      }
      else
      {
        //do nothing
      }
      return;
    }
  }
}


void DistanceDigitalDisplayUpdateCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  if ((f_node == nullptr) || (f_nv == nullptr))
    {
        return;
    }
  if (updateOrientation())
  {
    // Not update when orientation is not latest
    updatefrontsector(*f_nv, m_FrontSectorDisGeode, m_FrontSectorWarnImageGeode);
    updaterearsector(*f_nv, m_RearSectorDisGeode, m_RearSectorWarnImageGeode);
  }
  traverse(f_node, f_nv);
}


void DistanceDigitalDisplayUpdateCallback::checkSectorMovingStatus(bool& f_sector, vfc::float32_t& f_previousSector, vfc::float32_t f_distance, const vfc::float32_t& f_upperThresh, const vfc::float32_t& f_lowerThresh)
{
  const vfc::float32_t l_hysteresisDistanceThresh = cc::assets::tileoverlay::g_tileSettings->m_HysteresisDistanceThresh;
  if (f_sector != true &&
    isLessEqual(f_previousSector, f_upperThresh) &&
    isGreater  (f_previousSector, f_lowerThresh) &&
    isGreater  (f_distance, f_upperThresh) &&
    isLessEqual(f_distance , f_upperThresh + l_hysteresisDistanceThresh))
  {
    f_sector = true;
  }
  else if ( (f_sector == true) && (
    isLess(f_distance, f_upperThresh) ||
    isGreater(f_distance,f_upperThresh + l_hysteresisDistanceThresh )))
  {
    f_sector = false;
  } else { /*do nothing*/ }

  if(isNotEqual(f_previousSector, f_distance))
  {
    f_previousSector = f_distance;
  }
}


bool DistanceDigitalDisplayUpdateCallback::getMovingStatusByIndex(const vfc::uint32_t f_index) const
{
  switch(f_index)
  {
    case FRONT_LEFT:
    {
      return m_IsObjMovingL1_Sector_1 || m_IsObjMovingL3_Sector_1;
    }
    case FRONT_MIDDLE_LEFT:
    {
      return m_IsObjMovingL1_Sector_0 || m_IsObjMovingL3_Sector_0 || m_IsObjMovingL2_Sector_0;
    }
    case FRONT_MIDDLE_RIGHT:
    {
      return m_IsObjMovingL1_Sector_15 || m_IsObjMovingL3_Sector_15 || m_IsObjMovingL2_Sector_15;
    }
    case FRONT_RIGHT:
    {
      return m_IsObjMovingL1_Sector_14 || m_IsObjMovingL3_Sector_14;
    }
    case REAR_LEFT:
    {
      return m_IsObjMovingL1_Sector_6 || m_IsObjMovingL3_Sector_6;
    }
    case REAR_MIDDLE_LEFT:
    {
      return m_IsObjMovingL1_Sector_7 || m_IsObjMovingL3_Sector_7 || m_IsObjMovingL2_Sector_7;
    }
    case REAR_MIDDLE_RIGHT:
    {
      return m_IsObjMovingL1_Sector_8 || m_IsObjMovingL3_Sector_8 || m_IsObjMovingL2_Sector_8;
    }
    case REAR_RIGHT:
    {
      return m_IsObjMovingL1_Sector_9 || m_IsObjMovingL3_Sector_9;
    }
    default:
    {
      return false;
    }
  }
}

vfc::float32_t DistanceDigitalDisplayUpdateCallback::getMaxDistanceByIndex(const vfc::uint32_t f_index)
{
  vfc::float32_t l_distanceThresh = 0.0f;
  switch(f_index)
  {
    case REAR_LEFT:
    case REAR_RIGHT:
    case FRONT_LEFT:
    case FRONT_RIGHT:
    {
      l_distanceThresh = cc::assets::tileoverlay::g_tileSettings->m_distanceThreshCorner;
      break;
    }
    default:
    {
      l_distanceThresh = cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL3;
      break;
    }
  }
  return l_distanceThresh;
}


void DistanceDigitalDisplayUpdateCallback::compareFrontDistance(const vfc::float32_t& f_distance, const bool& f_isMoving, const vfc::uint32_t f_index)
{
  if (isGreater(f_distance, m_frontSectorShortestDis))
  {
    return; // distance larger than smallest distance so not require update
  }
  else
  {
    const vfc::float32_t l_distanceThresh = getMaxDistanceByIndex(f_index);
    const vfc::float32_t l_hysteresisDistanceThresh = cc::assets::tileoverlay::g_tileSettings->m_HysteresisDistanceThresh;
    {
      if (f_distance > l_distanceThresh + l_hysteresisDistanceThresh)
      {
        return; // out of range, ignore
      }
      else
      {
        if (
          isGreater  (f_distance, l_distanceThresh) &&
          isLessEqual(f_distance, l_distanceThresh + l_hysteresisDistanceThresh))
        {
          if (f_index == FRONT_LEFT)
          {
            if (f_isMoving && isGreater(m_frontSectorShortestDis, f_distance))
            {
              m_frontSectorShortestDis = f_distance;
              m_frontSectorShortestDisIndex = f_index;
            }
          }
          else
          {
            if (f_isMoving)
            {
              m_frontSectorShortestDis = f_distance;
              m_frontSectorShortestDisIndex = f_index;
            }
          }
        }
        else
        {
          if (isGreater(m_frontSectorShortestDis, f_distance))
          {
            m_frontSectorShortestDis = f_distance;
            m_frontSectorShortestDisIndex = f_index;
          }
        }
      }
    }
  }
}


void DistanceDigitalDisplayUpdateCallback::compareRearDistance(const vfc::float32_t& f_distance, const bool& f_isMoving, const vfc::uint32_t f_index)
{
  if (isGreater(f_distance, m_rearSectorShortestDis))
  {
    return; // distance larger than smallest distance so not require update
  }
  else
  {
    const vfc::float32_t l_distanceThresh = getMaxDistanceByIndex(f_index);
    const vfc::float32_t l_hysteresisDistanceThresh = cc::assets::tileoverlay::g_tileSettings->m_HysteresisDistanceThresh;
    {
      if (f_distance > l_distanceThresh + l_hysteresisDistanceThresh)
      {
        return; // out of range, ignore
      }
      else
      {
        if (
          isGreater  (f_distance, l_distanceThresh) &&
          isLessEqual(f_distance, l_distanceThresh + l_hysteresisDistanceThresh))
        {
          if (f_index == REAR_LEFT)
          {
            if (f_isMoving && isGreater(m_rearSectorShortestDis, f_distance))
            {
              m_rearSectorShortestDis = f_distance;
              m_rearSectorShortestDisIndex = f_index;
            }
          }
          else
          {
            if (f_isMoving)
            {
              m_rearSectorShortestDis = f_distance;
              m_rearSectorShortestDisIndex = f_index;
            }
          }
        }
        else
        {
          if (isGreater(m_rearSectorShortestDis, f_distance))
          {
            m_rearSectorShortestDis = f_distance;
            m_rearSectorShortestDisIndex = f_index;
          }
        }
      }
    }
  }
}


vfc::float32_t DistanceDigitalDisplayUpdateCallback::getFrontSectorShortestDis()  // PRQA S 6040  // PRQA S 6041  // PRQA S 6043
{
  // Reset shortest distance to max => ensure update
  m_frontSectorShortestDis = g_defaultDistance; // PRQA S 3803
  if (true == m_pFramework->asCustomFramework()->m_customUltrasonicDataReceiver.isConnected())
  {
    const pc::daddy::UltrasonicDataDaddy* const l_pDataDaddy = m_pFramework->asCustomFramework()->m_customUltrasonicDataReceiver.getData();
    if (nullptr != l_pDataDaddy)
    {
      pc::vehicle::UltrasonicData l_ultrasonicData = l_pDataDaddy->m_Data;

      //* UPDATE MOVING STATUS */
      checkSectorMovingStatus(m_IsObjMovingL3_Sector_14, m_previousDistanceL3_Sector_14, l_ultrasonicData[FRONT_RIGHT       ].getDistance(), getMaxDistanceByIndex(FRONT_RIGHT       ), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1);
      checkSectorMovingStatus(m_IsObjMovingL3_Sector_15, m_previousDistanceL3_Sector_15, l_ultrasonicData[FRONT_MIDDLE_RIGHT].getDistance(), getMaxDistanceByIndex(FRONT_MIDDLE_RIGHT), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL2);
      checkSectorMovingStatus(m_IsObjMovingL3_Sector_0,  m_previousDistanceL3_Sector_0,  l_ultrasonicData[FRONT_MIDDLE_LEFT ].getDistance(), getMaxDistanceByIndex(FRONT_MIDDLE_LEFT ), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL2);
      checkSectorMovingStatus(m_IsObjMovingL3_Sector_1,  m_previousDistanceL3_Sector_1,  l_ultrasonicData[FRONT_LEFT        ].getDistance(), getMaxDistanceByIndex(FRONT_LEFT        ), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1);

      checkSectorMovingStatus(m_IsObjMovingL2_Sector_15, m_previousDistanceL2_Sector_15, l_ultrasonicData[FRONT_MIDDLE_RIGHT].getDistance(), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL2, cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1);
      checkSectorMovingStatus(m_IsObjMovingL2_Sector_0,  m_previousDistanceL2_Sector_0,  l_ultrasonicData[FRONT_MIDDLE_LEFT ].getDistance(), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL2, cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1);

      checkSectorMovingStatus(m_IsObjMovingL1_Sector_14, m_previousDistanceL1_Sector_14, l_ultrasonicData[FRONT_RIGHT       ].getDistance(), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1, 0.0f);
      checkSectorMovingStatus(m_IsObjMovingL1_Sector_15, m_previousDistanceL1_Sector_15, l_ultrasonicData[FRONT_MIDDLE_RIGHT].getDistance(), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1, 0.0f);
      checkSectorMovingStatus(m_IsObjMovingL1_Sector_0,  m_previousDistanceL1_Sector_0,  l_ultrasonicData[FRONT_MIDDLE_LEFT ].getDistance(), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1, 0.0f);
      checkSectorMovingStatus(m_IsObjMovingL1_Sector_1,  m_previousDistanceL1_Sector_1,  l_ultrasonicData[FRONT_LEFT        ].getDistance(), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1, 0.0f);

      // ObjMovingL3 : coner:30-60; middle: 70-120
      // ObjMovingL2 : only for middle: 30-70
      // ObjMovingL1 : all 0-30

      //* UPDATE SHORTEST DISTANCE */
      //! DO NOT CHANGE COMPARE ORDER
      compareFrontDistance(l_ultrasonicData[FRONT_RIGHT       ].getDistance(), m_IsObjMovingL1_Sector_14, FRONT_RIGHT       ); // right has most priority
      compareFrontDistance(l_ultrasonicData[FRONT_MIDDLE_RIGHT].getDistance(), m_IsObjMovingL1_Sector_15, FRONT_MIDDLE_RIGHT);
      compareFrontDistance(l_ultrasonicData[FRONT_MIDDLE_LEFT ].getDistance(), m_IsObjMovingL1_Sector_0,  FRONT_MIDDLE_LEFT );
      compareFrontDistance(l_ultrasonicData[FRONT_LEFT        ].getDistance(), m_IsObjMovingL1_Sector_1,  FRONT_LEFT        ); // left has less priority

      compareFrontDistance(l_ultrasonicData[FRONT_RIGHT       ].getDistance(), m_IsObjMovingL3_Sector_14, FRONT_RIGHT       ); // right has most priority
      compareFrontDistance(l_ultrasonicData[FRONT_MIDDLE_RIGHT].getDistance(), m_IsObjMovingL2_Sector_15, FRONT_MIDDLE_RIGHT);
      compareFrontDistance(l_ultrasonicData[FRONT_MIDDLE_LEFT ].getDistance(), m_IsObjMovingL2_Sector_0,  FRONT_MIDDLE_LEFT );
      compareFrontDistance(l_ultrasonicData[FRONT_LEFT        ].getDistance(), m_IsObjMovingL3_Sector_1,  FRONT_LEFT        ); // left has less priority

      compareFrontDistance(l_ultrasonicData[FRONT_MIDDLE_RIGHT].getDistance(), m_IsObjMovingL3_Sector_15, FRONT_MIDDLE_RIGHT);
      compareFrontDistance(l_ultrasonicData[FRONT_MIDDLE_LEFT ].getDistance(), m_IsObjMovingL3_Sector_0,  FRONT_MIDDLE_LEFT );

    }
  }
  return 100.0f * m_frontSectorShortestDis;
}


vfc::float32_t DistanceDigitalDisplayUpdateCallback::getRearSectorShortestDis()  // PRQA S 6040  // PRQA S 6041  // PRQA S 6043
{
  m_rearSectorShortestDis = g_defaultDistance; // PRQA S 3803
  if (true == m_pFramework->asCustomFramework()->m_customUltrasonicDataReceiver.isConnected())
  {
    const pc::daddy::UltrasonicDataDaddy* const l_pDataDaddy = m_pFramework->asCustomFramework()->m_customUltrasonicDataReceiver.getData();
    if (nullptr != l_pDataDaddy)
    {
      pc::vehicle::UltrasonicData l_ultrasonicData = l_pDataDaddy->m_Data;

      //* UPDATE MOVING STATUS */
      checkSectorMovingStatus(m_IsObjMovingL3_Sector_6, m_previousDistanceL3_Sector_6, l_ultrasonicData[REAR_LEFT        ].getDistance(), getMaxDistanceByIndex(REAR_LEFT        ), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1);
      checkSectorMovingStatus(m_IsObjMovingL3_Sector_7, m_previousDistanceL3_Sector_7, l_ultrasonicData[REAR_MIDDLE_LEFT ].getDistance(), getMaxDistanceByIndex(REAR_MIDDLE_LEFT ), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL2);
      checkSectorMovingStatus(m_IsObjMovingL3_Sector_8, m_previousDistanceL3_Sector_8, l_ultrasonicData[REAR_MIDDLE_RIGHT].getDistance(), getMaxDistanceByIndex(REAR_MIDDLE_RIGHT), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL2);
      checkSectorMovingStatus(m_IsObjMovingL3_Sector_9, m_previousDistanceL3_Sector_9, l_ultrasonicData[REAR_RIGHT       ].getDistance(), getMaxDistanceByIndex(REAR_RIGHT       ), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1);

      checkSectorMovingStatus(m_IsObjMovingL2_Sector_7, m_previousDistanceL2_Sector_7, l_ultrasonicData[REAR_MIDDLE_LEFT ].getDistance(), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL2, cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1);
      checkSectorMovingStatus(m_IsObjMovingL2_Sector_8, m_previousDistanceL2_Sector_8, l_ultrasonicData[REAR_MIDDLE_RIGHT].getDistance(), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL2, cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1);

      checkSectorMovingStatus(m_IsObjMovingL1_Sector_6, m_previousDistanceL1_Sector_6, l_ultrasonicData[REAR_LEFT        ].getDistance(), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1, 0.0f);
      checkSectorMovingStatus(m_IsObjMovingL1_Sector_7, m_previousDistanceL1_Sector_7, l_ultrasonicData[REAR_MIDDLE_LEFT ].getDistance(), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1, 0.0f);
      checkSectorMovingStatus(m_IsObjMovingL1_Sector_8, m_previousDistanceL1_Sector_8, l_ultrasonicData[REAR_MIDDLE_RIGHT].getDistance(), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1, 0.0f);
      checkSectorMovingStatus(m_IsObjMovingL1_Sector_9, m_previousDistanceL1_Sector_9, l_ultrasonicData[REAR_RIGHT       ].getDistance(), cc::assets::tileoverlay::g_tileSettings->m_distanceThreshL1, 0.0f);

      // ObjMovingL3 : coner:30-60; middle: 70-120
      // ObjMovingL2 : only for middle: 30-70
      // ObjMovingL1 : all 0-30

      //* UPDATE SHORTEST DISTANCE */
      //! DO NOT CHANGE COMPARE ORDER
      compareRearDistance(l_ultrasonicData[REAR_RIGHT       ].getDistance(), m_IsObjMovingL1_Sector_9, REAR_RIGHT       ); // right has most priority
      compareRearDistance(l_ultrasonicData[REAR_MIDDLE_RIGHT].getDistance(), m_IsObjMovingL1_Sector_8, REAR_MIDDLE_RIGHT);
      compareRearDistance(l_ultrasonicData[REAR_MIDDLE_LEFT ].getDistance(), m_IsObjMovingL1_Sector_7, REAR_MIDDLE_LEFT );
      compareRearDistance(l_ultrasonicData[REAR_LEFT        ].getDistance(), m_IsObjMovingL1_Sector_6, REAR_LEFT        ); // left has less priority

      compareRearDistance(l_ultrasonicData[REAR_RIGHT       ].getDistance(), m_IsObjMovingL3_Sector_9, REAR_RIGHT       ); // right has most priority
      compareRearDistance(l_ultrasonicData[REAR_MIDDLE_RIGHT].getDistance(), m_IsObjMovingL2_Sector_8, REAR_MIDDLE_RIGHT);
      compareRearDistance(l_ultrasonicData[REAR_MIDDLE_LEFT ].getDistance(), m_IsObjMovingL2_Sector_7, REAR_MIDDLE_LEFT );
      compareRearDistance(l_ultrasonicData[REAR_LEFT        ].getDistance(), m_IsObjMovingL3_Sector_6, REAR_LEFT        ); // left has less priority

      compareRearDistance(l_ultrasonicData[REAR_MIDDLE_RIGHT].getDistance(), m_IsObjMovingL3_Sector_8, REAR_MIDDLE_RIGHT);
      compareRearDistance(l_ultrasonicData[REAR_MIDDLE_LEFT ].getDistance(), m_IsObjMovingL3_Sector_7, REAR_MIDDLE_LEFT );
    }
  }
  return 100.0f * m_rearSectorShortestDis;
}

bool DistanceDigitalDisplayUpdateCallback::isVerticalParking()  // PRQA S 6040  // PRQA S 6041  // PRQA S 6043
{

  bool l_isVerticalParking = false;
  if (true == m_pFramework->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver.isConnected())
  {
    const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType = m_pFramework->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver.getData();
    if (nullptr != l_themeType)
    {
      if (static_cast<vfc::uint8_t>(cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT) == l_themeType->m_Data)
      {
        l_isVerticalParking = true;
      }
    }
  }
  return l_isVerticalParking;
}

} // namespace rctaoverlay
} // namespace assets
} // namespace cc
