//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/ptsoverlay/inc/PtsSpline.h"
#include "cc/assets/ptsoverlay/inc/PtsOverlay.h"
#include "cc/assets/ptsoverlay/inc/PtsSettings.h"
#include "cc/assets/ptsoverlay/inc/PtsUtils.h"
#include "cc/assets/ptsoverlay/inc/PtsLookupTexture.h"
#include "cc/util/logging/inc/LoggingContexts.h"

#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/util/math/inc/Interpolator.h"
#include "pc/svs/util/osgx/inc/Utils.h"

#include <osg/Geometry>
#include <osg/Math>
#include <array>



/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3803
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace ptsoverlay
{
using cc::util::logging::g_PtsContext;

namespace
{

typedef pc::util::math::LinearInterpolator<float> SplineWidthInterpolator; // PRQA S 2446
SplineWidthInterpolator g_splineWidthInterpolator;

void updateInterpolators()
{
  static unsigned int s_sequenceNumber = ~0u; // PRQA S 2427
  if (g_ptsSettings->getModifiedCount() != s_sequenceNumber)
  {
    s_sequenceNumber = g_ptsSettings->getModifiedCount();

    //! Spline width interpolator
    g_splineWidthInterpolator.clear();
    g_splineWidthInterpolator.addSample(g_ptsSettings->getDistances().getNear(), g_ptsSettings->m_spline2DWidthInnerNear);
    g_splineWidthInterpolator.addSample(g_ptsSettings->getDistances().getFar(), g_ptsSettings->m_spline2DWidthInnerFar);
    g_splineWidthInterpolator.init();
  }
}


osg::Geometry* createGeometry(const std::string& f_name)
{
  //! create VBO manually in order to set the usage flag
  osg::Vec3Array* const l_vertices = new osg::Vec3Array;
  osg::VertexBufferObject* const l_vbo = new osg::VertexBufferObject;
  l_vbo->setUsage(GL_DYNAMIC_DRAW_ARB);
  l_vertices->setVertexBufferObject(l_vbo);

  osg::Geometry* const l_geometry = pc::util::osgx::createGeometry(f_name);
  l_geometry->setVertexArray(l_vertices);

  osg::Vec4ubArray* const l_colors = new osg::Vec4ubArray;
  l_colors->setNormalize(true);
  l_geometry->setColorArray(l_colors, osg::Array::BIND_PER_VERTEX);

  LookupTexture::TexCoordArray* const l_texCoords = new LookupTexture::TexCoordArray;
  l_texCoords->setNormalize(true);
  l_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);

  l_geometry->setUserData(new osg::UByteArray); // mapping table which vertex belongs to which PAS zone area

  l_geometry->setDataVariance(osg::Object::DYNAMIC);
  return l_geometry;
}


osg::DrawElements* createSurface(
  unsigned int f_numLayoutPoints, // PRQA S 2427
  unsigned int f_numSurfacePoints, // PRQA S 2427
  unsigned int f_vertexOffset) // PRQA S 2427
{
  const unsigned int l_numVertices = f_numLayoutPoints * f_numSurfacePoints; // PRQA S 2427
  const unsigned int l_numIndices = f_numLayoutPoints * (f_numSurfacePoints - 1u) * 6u; // PRQA S 2427
  osg::DrawElementsUShort* const l_indices = new osg::DrawElementsUShort(osg::PrimitiveSet::TRIANGLES, l_numIndices);

  unsigned int l_indexCounter = 0u; // PRQA S 2427
  for(unsigned int i = 0u; i < f_numLayoutPoints; ++i) // PRQA S 2427
  {
    const unsigned int l_vo =  i * f_numSurfacePoints; // PRQA S 2427
    for (unsigned int j = 0u; j < f_numSurfacePoints - 1u; ++j) // PRQA S 2427
    {
      typedef osg::DrawElementsUShort::value_type UShort;
      // 1st triangle
      (*l_indices)[l_indexCounter]      = static_cast<UShort> (f_vertexOffset + ((l_vo + j) % l_numVertices));
      (*l_indices)[l_indexCounter + 1u] = static_cast<UShort> (f_vertexOffset + ((l_vo + j + 1u) % l_numVertices));
      (*l_indices)[l_indexCounter + 2u] = static_cast<UShort> (f_vertexOffset + ((l_vo + j + f_numSurfacePoints) % l_numVertices));
      // 2nd triangle
      (*l_indices)[l_indexCounter + 3u] = static_cast<UShort> (f_vertexOffset + ((l_vo + j + 1u) % l_numVertices));
      (*l_indices)[l_indexCounter + 4u] = static_cast<UShort> (f_vertexOffset + ((l_vo + j + 1u + f_numSurfacePoints) % l_numVertices));
      (*l_indices)[l_indexCounter + 5u] = static_cast<UShort> (f_vertexOffset + ((l_vo + j + f_numSurfacePoints) % l_numVertices));
      l_indexCounter += 6u;
    }
  }
  return l_indices;
}

} // namespace


//!
//! DegradationData
//!
DegradationData::DegradationData()
  : m_frontDisabled(false) // PRQA S 2323
  , m_rearDisabled(false) // PRQA S 2323
  , m_flankFrontLeftDisabled(false) // PRQA S 2323
  , m_flankFrontRightDisabled(false) // PRQA S 2323
  , m_flankRearLeftDisabled(false) // PRQA S 2323
  , m_flankRearRightDisabled(false) // PRQA S 2323
  , m_errorReactionFront(false) // PRQA S 2323
  , m_errorReactionSide(false) // PRQA S 2323
  , m_errorReactionRear(false) // PRQA S 2323
{
}


DegradationData::DegradationData(
  const cc::daddy::PtsHmiStateOutput& f_ptsHmiState,
  bool f_flanksEnabled,
  bool f_doorOpenLeft,
  bool f_doorOpenRight,
  bool f_trailerConnected)
  : m_frontDisabled(false) // PRQA S 2323
  , m_rearDisabled(false) // PRQA S 2323
  , m_flankFrontLeftDisabled(false) // PRQA S 2323
  , m_flankFrontRightDisabled(false) // PRQA S 2323
  , m_flankRearLeftDisabled(false) // PRQA S 2323
  , m_flankRearRightDisabled(false) // PRQA S 2323
  , m_errorReactionFront(false) // PRQA S 2323
  , m_errorReactionSide(false) // PRQA S 2323
  , m_errorReactionRear(false) // PRQA S 2323
{
  evaluateState( f_ptsHmiState,  f_flanksEnabled, f_doorOpenLeft, f_doorOpenRight, f_trailerConnected);
}


void DegradationData::evaluateState(
  const cc::daddy::PtsHmiStateOutput& f_ptsHmiState,
  bool f_flanksEnabled,
  bool f_doorOpenLeft,
  bool f_doorOpenRight,
  bool f_trailerConnected)
{
  if (isHidden(f_ptsHmiState))
  {
    // avoid visual glitches if PTS is comming from a hidden state by disabling all parts
    m_frontDisabled = true;
    m_rearDisabled = true;
    m_flankFrontLeftDisabled = true;
    m_flankFrontRightDisabled = true;
    m_flankRearLeftDisabled = true;
    m_flankRearRightDisabled = true;
  }
  else
  {
    // evaluate error reactions
    if (f_ptsHmiState.m_errorReaction)
    {
      m_errorReactionFront = true;
      m_errorReactionSide = true;
      m_errorReactionRear = true;
    }
    if (f_ptsHmiState.m_disturbanceReactionFront)
    {
      m_errorReactionFront = true;
      m_errorReactionSide = true;
    }
    else
    {
      if (static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_DISTURBANCE_FRONT) == f_ptsHmiState.m_pashmiState) // PRQA S 2427
      {
        // if we have a disturbance front and the error reaction is over, the front part shall be hidden
        m_frontDisabled = true;
      }
    }
    if (f_ptsHmiState.m_disturbanceReactionRear)
    {
      m_errorReactionRear = true;
      m_errorReactionSide = true;
    }
    else
    {
      if (static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_DISTURBANCE_REAR) == f_ptsHmiState.m_pashmiState) // PRQA S 2427
      {
        // analog disturbance front, see above
        m_rearDisabled = true;
      }
    }

    if (f_trailerConnected)
    {
      m_rearDisabled = true;
    }

    // evaluate flank visibility
    unsigned int l_flanksActivationMask = f_flanksEnabled ? f_ptsHmiState.m_sideSegmentActivation.m_activationMask : 0u; // PRQA S 2427
    if (f_doorOpenLeft)
    {
      l_flanksActivationMask &= ~(
        static_cast<vfc::uint32_t>(cc::daddy::PtsSideSegmentActivation::SIDE_SEGMENT_BIT_FRONT_LEFT) |
        static_cast<vfc::uint32_t>(cc::daddy::PtsSideSegmentActivation::SIDE_SEGMENT_BIT_REAR_LEFT));
    }
    if (f_doorOpenRight)
    {
      l_flanksActivationMask &= ~(
        static_cast<vfc::uint32_t>(cc::daddy::PtsSideSegmentActivation::SIDE_SEGMENT_BIT_FRONT_RIGHT) |
        static_cast<vfc::uint32_t>(cc::daddy::PtsSideSegmentActivation::SIDE_SEGMENT_BIT_REAR_RIGHT));
    }
    if (isErrorReactionActive())
    {
      switch (f_ptsHmiState.m_pashmiState)
      {
        case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_ERROR): // PRQA S 2427
        case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_ERROR_FEEDBACK): // PRQA S 2427
        case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_DISTURBANCE_FRONT_REAR): // PRQA S 2427
        case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_WARNING_ACTIVE): // PRQA S 2427
        {
          // override visibility of front and rear in case of error reaction -> show spline
          m_frontDisabled = false;
          m_rearDisabled = false;
          l_flanksActivationMask = cc::daddy::PtsSideSegmentActivation::ALL_SEGMENTS_MASK;
        } break;
        case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_DISTURBANCE_FRONT): // PRQA S 2427
        {
          m_frontDisabled = false;
          l_flanksActivationMask = cc::daddy::PtsSideSegmentActivation::ALL_SEGMENTS_MASK;
        } break;
        case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_DISTURBANCE_REAR): // PRQA S 2427
        {
          m_rearDisabled = false;
          l_flanksActivationMask = cc::daddy::PtsSideSegmentActivation::ALL_SEGMENTS_MASK;
        } break;
        default:
        {
            break;
        }
      }
    }
    //! translate flank activation bit masks to individual booleans
    m_flankFrontLeftDisabled  = (0u == (static_cast<vfc::int32_t>(cc::daddy::PtsSideSegmentActivation::SIDE_SEGMENT_BIT_FRONT_LEFT)  & l_flanksActivationMask));
    m_flankFrontRightDisabled = (0u == (static_cast<vfc::int32_t>(cc::daddy::PtsSideSegmentActivation::SIDE_SEGMENT_BIT_FRONT_RIGHT) & l_flanksActivationMask));
    m_flankRearLeftDisabled   = (0u == (static_cast<vfc::int32_t>(cc::daddy::PtsSideSegmentActivation::SIDE_SEGMENT_BIT_REAR_LEFT)   & l_flanksActivationMask));
    m_flankRearRightDisabled  = (0u == (static_cast<vfc::int32_t>(cc::daddy::PtsSideSegmentActivation::SIDE_SEGMENT_BIT_REAR_RIGHT)  & l_flanksActivationMask));
  }
}


bool DegradationData::isErrorReactionActive() const
{
  return (m_errorReactionFront || m_errorReactionSide || m_errorReactionRear);
}


bool DegradationData::isDegraded() const
{
  return isErrorReactionActive() ||
    (m_frontDisabled || m_rearDisabled || m_flankFrontLeftDisabled || m_flankFrontRightDisabled || m_flankRearLeftDisabled || m_flankRearRightDisabled);
}


namespace
{

//!
//! @brief Empty degradation functor which defines the interface for modifying vertex color an tex coord attributes
//!
//!
struct DegradationFunctor
{
  virtual void onHide(osg::Vec4ubArray& /* f_colors */, LookupTexture::TexCoordArray& /* f_texCoords */, unsigned int /* f_index */) const // PRQA S 2427
  {
  }

  virtual void onError(osg::Vec4ubArray& /* f_colors */, LookupTexture::TexCoordArray& /* f_texCoords */, unsigned int /* f_index */) const // PRQA S 2427
  {
  }
};


template <class T>
void handleDegradation(
  const DegradationData& f_data,
  osg::Vec4ubArray* f_colors,
  LookupTexture::TexCoordArray* f_texCoords,
  const osg::UByteArray* f_pasZoneAreas,
  const T& f_degradationFunctor)
{

  if (f_colors == nullptr || f_texCoords == nullptr || f_pasZoneAreas == nullptr){
    XLOG_ERROR(g_PtsContext, "handleDegradation nullptr error");
    return;
  }

  if ((f_colors->size() != f_texCoords->size()) || (f_colors->size() != f_pasZoneAreas->size()))
  {
    XLOG_ERROR(g_PtsContext, "handleDegradation() failed, input array sizes do not have equal sizes");
    return;
  }

  if (f_data.isDegraded())
  {
    const unsigned int l_numCoords = static_cast<unsigned int> (f_texCoords->size()); // PRQA S 2427
    for (unsigned int i = 0u; i < l_numCoords; ++i) // PRQA S 2427
    {
      bool l_isHidden = false;
      bool l_inErrorState = false;
      switch ((*f_pasZoneAreas)[i])
      {
      case PAS_ZONE_FRONT:
      {
          l_isHidden     = f_data.m_frontDisabled;
          l_inErrorState = f_data.m_errorReactionFront;
          break;
      }
      case PAS_ZONE_REAR:
      {
          l_isHidden     = f_data.m_rearDisabled;
          l_inErrorState = f_data.m_errorReactionRear;
          break;
      }
      case PAS_ZONE_LEFT_FRONT:
      {
          l_isHidden     = f_data.m_flankFrontLeftDisabled;
          l_inErrorState = f_data.m_errorReactionSide;
          break;
      }
      case PAS_ZONE_LEFT_REAR:
      {
          l_isHidden     = f_data.m_flankRearLeftDisabled;
          l_inErrorState = f_data.m_errorReactionSide;
          break;
      }
      case PAS_ZONE_RIGHT_FRONT:
      {
          l_isHidden     = f_data.m_flankFrontRightDisabled;
          l_inErrorState = f_data.m_errorReactionSide;
          break;
      }
      case PAS_ZONE_RIGHT_REAR:
      {
          l_isHidden     = f_data.m_flankRearRightDisabled;
          l_inErrorState = f_data.m_errorReactionSide;
          break;
      }
      default:
      {
          break;
      }
      }
      if (l_isHidden)
      {
        f_degradationFunctor.onHide(*f_colors, *f_texCoords, i);
      }
      if (l_inErrorState)
      {
        f_degradationFunctor.onError(*f_colors, *f_texCoords, i);
      }
    }
  }
}

}  //namespace


//!
//! PtsUpdateVisitor
//!
PtsUpdateVisitor::PtsUpdateVisitor() // PRQA S 2628
  : osg::NodeVisitor(osg::NodeVisitor::TRAVERSE_ALL_CHILDREN) // PRQA S 2323 // PRQA S 4052
  , m_layout{}
  , m_distances{}
  , m_pasZoneMappings{}
  , m_degradationData{}
  , m_normals{}
{
}


void PtsUpdateVisitor::apply(osg::Node& f_node)
{
  PtsSpline* const l_ptsSpline = dynamic_cast<PtsSpline*> (&f_node); // PRQA S 3400
  if (l_ptsSpline != nullptr)
  {
    l_ptsSpline->update(*this);
  }
  traverse(f_node);
}


void PtsUpdateVisitor::setData(
  const pc::util::Polygon2D& f_layout,
  const pc::util::FloatList& f_distances,
  const PasZoneList& f_zoneMappings,
  const DegradationData& f_degradationData)
{
  m_layout = f_layout;
  m_distances = f_distances;
  m_pasZoneMappings = f_zoneMappings;
  m_degradationData = f_degradationData;
  m_normals = pc::util::computeNormals(f_layout);
}


//!
//! PtsSpline
//!
PtsSpline::PtsSpline()
  : m_numLayoutPoints(0u) // PRQA S 2323 // PRQA S 4052
{
  setStateSet(getOrCreateTexturingStateSet());
}


PtsSpline::PtsSpline(const PtsSpline& f_other, const osg::CopyOp& f_copyOp)
  : osg::Geode(f_other, f_copyOp) // PRQA S 2323
  , m_numLayoutPoints(f_other.m_numLayoutPoints) // PRQA S 2323
{
}


//!
//! PtsSplineShadowDegradationFunctor
//!
class PtsSplineShadowDegradationFunctor : public DegradationFunctor
{
public:

  void onHide(osg::Vec4ubArray& f_colors, LookupTexture::TexCoordArray& /* f_texCoord */, unsigned int f_index) const override // PRQA S 2427
  {
    f_colors[f_index].a() = static_cast<osg::Vec4ub::value_type> (0u);
  }

  // the shadow color shall remain untouched in case of an error reaction so we stick to the empty base implementation
};


//!
//! PtsSplineShadow
//!
PtsSplineShadow::PtsSplineShadow() // PRQA S 4054
{
  // addDrawable is virtual, so explicitly call base class implementation when called from constructor
  osg::Geode::addDrawable(createGeometry("PtsSplineShadow")); // PRQA S 3803
}


PtsSplineShadow::PtsSplineShadow(const PtsSplineShadow& f_other, const osg::CopyOp& f_copyOp)
  : PtsSpline(f_other, f_copyOp) // PRQA S 2323
{
}


void PtsSplineShadow::update(const PtsUpdateVisitor& f_visitor)
{
  osg::Geometry* const l_geometry  = getDrawable(0u)->asGeometry();
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (l_geometry->getVertexArray()); // PRQA S 3076
  osg::UByteArray* const l_pasZoneAreas = static_cast<osg::UByteArray*> (l_geometry->getUserData()); // PRQA S 3076
  osg::Vec4ubArray* const l_colors = static_cast<osg::Vec4ubArray*> (l_geometry->getColorArray()); // PRQA S 3076
  LookupTexture::TexCoordArray* const l_texCoords = static_cast<LookupTexture::TexCoordArray*> (l_geometry->getTexCoordArray(0u)); // PRQA S 3076

  const pc::util::Polygon2D& l_layout = f_visitor.getLayout();
  const unsigned int l_numLayoutPoints = static_cast<unsigned int> (l_layout.size()); // PRQA S 2427
  if (l_numLayoutPoints != m_numLayoutPoints)
  {
    m_numLayoutPoints = l_numLayoutPoints;
    const unsigned int l_numVertices = l_numLayoutPoints * static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SEGMENT); // PRQA S 2427
    l_vertices->resize(l_numVertices);
    l_pasZoneAreas->resize(l_numVertices);
    l_colors->resize(l_numVertices);
    l_texCoords->resize(l_numVertices);
    // clear primitive sets
    l_geometry->removePrimitiveSet(0u, l_geometry->getNumPrimitiveSets()); // PRQA S 3803
    l_geometry->addPrimitiveSet(createSurface(l_numLayoutPoints, NUM_VERTICES_PER_SEGMENT, 0u)); // PRQA S 3803
    // build PAS zone area look-up table
    const PtsUpdateVisitor::PasZoneList& l_pasZoneMappings = f_visitor.getPasZoneMappings();
    unsigned int l_vi = 0u; // PRQA S 2427
    for (unsigned int i = 0u; i < l_numLayoutPoints; ++i) // PRQA S 2427
    {
      const osg::UByteArray::value_type l_pasZoneArea = getPasZoneArea(l_pasZoneMappings[i], l_layout[i]);
      (*l_pasZoneAreas)[l_vi]      = l_pasZoneArea;
      (*l_pasZoneAreas)[l_vi + 1u] = l_pasZoneArea;
      (*l_pasZoneAreas)[l_vi + 2u] = l_pasZoneArea;
      l_vi += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SEGMENT);
    }
  }

  const PtsUpdateVisitor::NormalList& l_normals = f_visitor.getNormals();
  const osg::Vec4ub l_color = osg::Vec4ub(g_white.r(), g_white.g(), g_white.b(), int2UByte(g_ptsSettings->m_colorShadow.a()));
  const LookupTexture::TexCoord l_colorCoordInner = LookupTexture::s_shadowColor.get(1.0f, 0.5f);
  const LookupTexture::TexCoord l_colorCoordOuter = LookupTexture::s_shadowColor.get(0.0f, 0.5f);
  unsigned int l_vi = 0u; // PRQA S 2427
  for (unsigned int i = 0u; i < l_numLayoutPoints; ++i) // PRQA S 2427
  {
    //! vertices
    const osg::Vec2f l_vns = l_normals[i] * g_ptsSettings->m_shadowRadius;
    (*l_vertices)[l_vi]      = osg::Vec3f(l_layout[i] - l_vns, 0.0f);
    (*l_vertices)[l_vi + 1u] = osg::Vec3f(l_layout[i], 0.0f);
    (*l_vertices)[l_vi + 2u] = osg::Vec3f(l_layout[i] + l_vns, 0.0f);
    //! colors
    (*l_colors)[l_vi]      = l_color;
    (*l_colors)[l_vi + 1u] = l_color;
    (*l_colors)[l_vi + 2u] = l_color;
    //! texture coordinates
    (*l_texCoords)[l_vi]      = l_colorCoordOuter;
    (*l_texCoords)[l_vi + 1u] = l_colorCoordInner;
    (*l_texCoords)[l_vi + 2u] = l_colorCoordOuter;
    l_vi += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SEGMENT);
  }

  handleDegradation(f_visitor.getDegradationData(), l_colors, l_texCoords, l_pasZoneAreas, PtsSplineShadowDegradationFunctor());

  l_vertices->dirty();
  l_colors->dirty();
  l_texCoords->dirty();
  l_geometry->dirtyBound();
}


//!
//! PtsSpline2DDegradationFunctor
//!
class PtsSpline2DDegradationFunctor : public DegradationFunctor
{
public:

  explicit PtsSpline2DDegradationFunctor(unsigned int f_vertexOffsetOuterContour) // PRQA S 2427
    : m_vertexOffsetOuterContour(f_vertexOffsetOuterContour) // PRQA S 2323 // PRQA S 4052
    , m_errorColorCoord(LookupTexture::s_distanceColor.get(0.0f)) // PRQA S 2323
  {
  }

  void onHide(osg::Vec4ubArray& f_colors, LookupTexture::TexCoordArray& /* f_texCoord */, unsigned int f_index) const override // PRQA S 2427
  {
    f_colors[f_index].a() = static_cast<osg::Vec4ub::value_type> (0u);
  }

  void onError(osg::Vec4ubArray& /* f_color */, LookupTexture::TexCoordArray& f_texCoords, unsigned int f_index) const override // PRQA S 2427
  {
    if (f_index < m_vertexOffsetOuterContour) // we only want to modify the inner contour
    {
      f_texCoords[f_index].y() = m_errorColorCoord;
    }
  }

  static float getSplineWidth(const DegradationData& f_degradationData, unsigned int f_pasZoneArea, float f_distance) // PRQA S 2446 // PRQA S 2427
  {
    if ((f_degradationData.m_errorReactionFront && (PAS_ZONE_FRONT == f_pasZoneArea)) ||
        (f_degradationData.m_errorReactionSide  && ((PAS_ZONE_LEFT_FRONT <= f_pasZoneArea) && (f_pasZoneArea <= PAS_ZONE_RIGHT_REAR))) ||
        (f_degradationData.m_errorReactionRear  && (PAS_ZONE_REAR == f_pasZoneArea)))
    {
      return g_splineWidthInterpolator.getValue(0.0f);
    }
    else
    {
      return g_splineWidthInterpolator.getValue(f_distance);
    }
  }

  const unsigned int m_vertexOffsetOuterContour; // PRQA S 2427
  LookupTexture::TexCoord::value_type m_errorColorCoord;
};


//!
//! PtsSpline2D
//!
PtsSpline2D::PtsSpline2D(float f_zPos) // PRQA S 2446
  : m_zPos(f_zPos) // PRQA S 2323 // PRQA S 4052
{
  osg::Geode::addDrawable(createGeometry("PtsSpline2D")); // PRQA S 3803
}


PtsSpline2D::PtsSpline2D(const PtsSpline2D& f_other, const osg::CopyOp& f_copyOp)
  : PtsSpline(f_other, f_copyOp) // PRQA S 2323
  , m_zPos(f_other.m_zPos) // PRQA S 2323
{
}


void PtsSpline2D::update(const PtsUpdateVisitor& f_visitor)
{
  updateInterpolators();

  osg::Geometry* const l_geometry  = getDrawable(0u)->asGeometry();
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (l_geometry->getVertexArray()); // PRQA S 3076
  osg::UByteArray* const l_pasZoneAreas = static_cast<osg::UByteArray*> (l_geometry->getUserData()); // PRQA S 3076
  osg::Vec4ubArray* const l_colors = static_cast<osg::Vec4ubArray*> (l_geometry->getColorArray()); // PRQA S 3076
  LookupTexture::TexCoordArray* const l_texCoords = static_cast<LookupTexture::TexCoordArray*> (l_geometry->getTexCoordArray(0u)); // PRQA S 3076

  const pc::util::Polygon2D& l_layout = f_visitor.getLayout();
  const PtsUpdateVisitor::PasZoneList& l_pasZoneMappings = f_visitor.getPasZoneMappings();
  const unsigned int l_numLayoutPoints = static_cast<unsigned int> (l_layout.size()); // PRQA S 2427
  const unsigned int l_vertexOffsetOuterContour = l_numLayoutPoints * static_cast<vfc::uint32_t>(NUM_VERTICES_PER_INNER_SEGMENT); // PRQA S 2427
  if (l_numLayoutPoints != m_numLayoutPoints)
  {
    m_numLayoutPoints = l_numLayoutPoints;
    const unsigned int l_numVertices = l_numLayoutPoints * static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SEGMENT); // PRQA S 2427
    l_vertices->resize(l_numVertices);
    l_pasZoneAreas->resize(l_numVertices);
    l_colors->resize(l_numVertices);
    l_texCoords->resize(l_numVertices);
    //! clear primitive sets
    l_geometry->removePrimitiveSet(0u, l_geometry->getNumPrimitiveSets()); // PRQA S 3803
    //! inner contour
    l_geometry->addPrimitiveSet(createSurface(l_numLayoutPoints, NUM_VERTICES_PER_INNER_SEGMENT, 0u)); // PRQA S 3803
    //! outer contour
    l_geometry->addPrimitiveSet(createSurface(l_numLayoutPoints, NUM_VERTICES_PER_OUTER_SEGMENT, l_vertexOffsetOuterContour)); // PRQA S 3803

    // build PAS zone area look-up table
    unsigned int l_vi = 0u; // PRQA S 2427
    unsigned int l_vo = l_vertexOffsetOuterContour; // PRQA S 2427
    for (unsigned int i = 0u; i < l_numLayoutPoints; ++i) // PRQA S 2427
    {
      const osg::UByteArray::value_type l_pasZoneArea = getPasZoneArea(l_pasZoneMappings[i], l_layout[i]);
      (*l_pasZoneAreas)[l_vi]      = l_pasZoneArea;
      (*l_pasZoneAreas)[l_vi + 1u] = l_pasZoneArea;
      (*l_pasZoneAreas)[l_vo]      = l_pasZoneArea;
      (*l_pasZoneAreas)[l_vo + 1u] = l_pasZoneArea;
      l_vi += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_INNER_SEGMENT);
      l_vo += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_OUTER_SEGMENT);
    }
  }

  const pc::util::FloatList& l_distances = f_visitor.getDistances();
  const PtsUpdateVisitor::NormalList& l_normals = f_visitor.getNormals();
  unsigned int l_vi = 0u; // PRQA S 2427
  unsigned int l_vo = l_vertexOffsetOuterContour; // PRQA S 2427
  const LookupTexture::TexCoord l_outerContourInner = LookupTexture::s_outlineColor.get(0.0f, 0.5f);
  const LookupTexture::TexCoord l_outerContourOuter = LookupTexture::s_outlineColor.get(1.0f, 0.5f);
  for (unsigned int i = 0u; i < l_numLayoutPoints; ++i) // PRQA S 2427
  {
    //! vertices inner contour
    float l_splineWidthInner = PtsSpline2DDegradationFunctor::getSplineWidth(f_visitor.getDegradationData(), l_pasZoneMappings[i], l_distances[i]); // PRQA S 2446
    l_splineWidthInner = l_splineWidthInner * (1.0f + g_ptsSettings->m_smoothingGradientSize * 2.0f);
    const osg::Vec2f l_innerContourOuterVertex = l_layout[i] + (l_normals[i] * l_splineWidthInner);
    (*l_vertices)[l_vi]      = osg::Vec3f(l_layout[i], m_zPos);
    (*l_vertices)[l_vi + 1u] = osg::Vec3f(l_innerContourOuterVertex, m_zPos);
    //! vertices outer contour
    float const l_splineWidthOuter = g_ptsSettings->m_spline2DWidthOuter * (1.0f + g_ptsSettings->m_smoothingGradientSize * 2.0f); // PRQA S 2446
    const osg::Vec2f l_outerContourInnerVertex = l_innerContourOuterVertex + (l_normals[i] * (-1.0f * l_splineWidthOuter * g_ptsSettings->m_smoothingGradientSize));
    const osg::Vec2f l_outerContourOuterVertex = l_outerContourInnerVertex + (l_normals[i] * l_splineWidthOuter);
    (*l_vertices)[l_vo]      = osg::Vec3f(l_outerContourInnerVertex, m_zPos);
    (*l_vertices)[l_vo + 1u] = osg::Vec3f(l_outerContourOuterVertex, m_zPos);
    //! colors
    (*l_colors)[l_vi]      = g_white;
    (*l_colors)[l_vi + 1u] = g_white;
    (*l_colors)[l_vo]      = g_white;
    (*l_colors)[l_vo + 1u] = g_white;
    //! tex coords
    const LookupTexture::TexCoord::value_type l_colorCoord = LookupTexture::s_distanceColor.get(g_ptsSettings->getDistances().normalize(l_distances[i]));
    (*l_texCoords)[l_vi]     = LookupTexture::TexCoord(0, l_colorCoord);
    (*l_texCoords)[l_vi + 1u] = LookupTexture::TexCoord(LookupTexture::s_maxTexCoord, l_colorCoord);
    (*l_texCoords)[l_vo]     = l_outerContourInner;
    (*l_texCoords)[l_vo + 1u] = l_outerContourOuter;
    l_vi += static_cast<vfc::int32_t>(NUM_VERTICES_PER_INNER_SEGMENT);
    l_vo += static_cast<vfc::int32_t>(NUM_VERTICES_PER_OUTER_SEGMENT);
  }

  handleDegradation(f_visitor.getDegradationData(), l_colors, l_texCoords, l_pasZoneAreas, PtsSpline2DDegradationFunctor(l_vertexOffsetOuterContour));

  l_vertices->dirty();
  l_colors->dirty();
  l_texCoords->dirty();
  l_geometry->dirtyBound();
}


//!
//! PtsShield3DDegradationFunctor
//!
class PtsShield3DDegradationFunctor : public DegradationFunctor
{
public:

  PtsShield3DDegradationFunctor()
    : m_errorColorCoord(LookupTexture::s_distanceColor.get(0.0f)) // PRQA S 2323 // PRQA S 4052
  {
  }

  void onHide(osg::Vec4ubArray& /* f_colors */, LookupTexture::TexCoordArray& f_texCoord, unsigned int f_index) const override // PRQA S 2427
  {
    //f_colors[f_index].a() = 0x00u;
    f_texCoord[f_index].x() = static_cast<LookupTexture::TexCoord::value_type> (0u);
  }

  void onError(osg::Vec4ubArray& /* f_color */, LookupTexture::TexCoordArray& f_texCoords, unsigned int f_index) const override // PRQA S 2427
  {
    f_texCoords[f_index].y() = m_errorColorCoord;
  }

  LookupTexture::TexCoord::value_type m_errorColorCoord;
};


//!
//! PtsShield3D
//!
PtsShield3D::PtsShield3D() // PRQA S 4054
{
  osg::Geode::addDrawable(createGeometry("PtsShield3D")); // PRQA S 3803
}


PtsShield3D::PtsShield3D(const PtsShield3D& f_other, const osg::CopyOp& f_copyOp)
  : PtsSpline(f_other, f_copyOp) // PRQA S 2323
{
}


void PtsShield3D::update(const PtsUpdateVisitor& f_visitor)
{
  osg::Geometry* const l_geometry  = getDrawable(0u)->asGeometry();
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (l_geometry->getVertexArray()); // PRQA S 3076
  osg::UByteArray* const l_pasZoneAreas = static_cast<osg::UByteArray*> (l_geometry->getUserData()); // PRQA S 3076
  osg::Vec4ubArray* const l_colors = static_cast<osg::Vec4ubArray*> (l_geometry->getColorArray()); // PRQA S 3076
  LookupTexture::TexCoordArray* const l_texCoords = static_cast<LookupTexture::TexCoordArray*> (l_geometry->getTexCoordArray(0u)); // PRQA S 3076

  const pc::util::Polygon2D& l_layout = f_visitor.getLayout();
  const unsigned int l_numLayoutPoints = static_cast<unsigned int> (l_layout.size()); // PRQA S 2427
  const unsigned int l_vertexOffsetHairlines = l_numLayoutPoints * static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT); // PRQA S 2427
  if (l_numLayoutPoints != m_numLayoutPoints)
  {
    m_numLayoutPoints = l_numLayoutPoints;
    const unsigned int l_numVertices = l_numLayoutPoints * static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SEGMENT); // PRQA S 2427
    l_vertices->resize(l_numVertices);
    l_pasZoneAreas->resize(l_numVertices);
    l_colors->resize(l_numVertices);
    l_texCoords->resize(l_numVertices);
    //! clear primitive sets
    l_geometry->removePrimitiveSet(0u, l_geometry->getNumPrimitiveSets()); // PRQA S 3803
    l_geometry->addPrimitiveSet(createSurface(l_numLayoutPoints, NUM_VERTICES_PER_SHIELD_SEGMENT, 0u)); // PRQA S 3803
    const unsigned int l_numVerticesHairlines = l_numLayoutPoints * static_cast<vfc::uint32_t>(NUM_VERTICES_PER_HAIRLINE_SEGMENT); // PRQA S 2427
    l_geometry->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::LINES, l_vertexOffsetHairlines, l_numVerticesHairlines)); // PRQA S 3803 // PRQA S 3000

    const PtsUpdateVisitor::PasZoneList& l_pasZoneMappings = f_visitor.getPasZoneMappings();
    unsigned int l_vs = 0u; // PRQA S 2427
    unsigned int l_vh = l_vertexOffsetHairlines; // PRQA S 2427
    for (unsigned int i = 0u; i < l_numLayoutPoints; ++i) // PRQA S 2427
    {
      //! PAS zone area look-up
      const osg::UByteArray::value_type l_pasZoneArea = getPasZoneArea(l_pasZoneMappings[i], l_layout[i]);
      (*l_pasZoneAreas)[l_vs]      = l_pasZoneArea;
      (*l_pasZoneAreas)[l_vs + 1u] = l_pasZoneArea;
      (*l_pasZoneAreas)[l_vs + 2u] = l_pasZoneArea;
      (*l_pasZoneAreas)[l_vh]      = l_pasZoneArea;
      (*l_pasZoneAreas)[l_vh + 1u] = l_pasZoneArea;
      l_vs += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT);
      l_vh += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_HAIRLINE_SEGMENT);
    }
  }

  const osg::Vec4ub::value_type l_shieldBrightness = float2UByte(1.0f - g_ptsSettings->m_hairlineBlooming);
  const osg::Vec4ub l_colorShieldInner = osg::Vec4ub(l_shieldBrightness, l_shieldBrightness, l_shieldBrightness, float2UByte(g_ptsSettings->m_shieldAlphaInner));
  const osg::Vec4ub l_colorShieldOuter = osg::Vec4ub(l_shieldBrightness, l_shieldBrightness, l_shieldBrightness, float2UByte(g_ptsSettings->m_shieldAlphaOuter));
  const osg::Vec4ub l_colorHairline = osg::Vec4ub(g_white.r(), g_white.g(), g_white.b(), float2UByte(g_ptsSettings->m_hairlineAlpha));
  const pc::util::FloatList& l_distances = f_visitor.getDistances();
  unsigned int l_vs = 0u; // PRQA S 2427
  unsigned int l_vh = l_vertexOffsetHairlines; // PRQA S 2427
  for (unsigned int i = 0u; i < l_numLayoutPoints; ++i) // PRQA S 2427
  {
    //! shield vertices
    (*l_vertices)[l_vs]      = osg::Vec3f(l_layout[i], g_ptsSettings->m_spline3DHeight);
    (*l_vertices)[l_vs + 1u] = osg::Vec3f(l_layout[i], g_ptsSettings->m_spline3DHeight + (g_ptsSettings->m_shieldHeight * 0.5f));
    (*l_vertices)[l_vs + 2u] = osg::Vec3f(l_layout[i], g_ptsSettings->m_spline3DHeight + g_ptsSettings->m_shieldHeight);
    //! hairline vertices
    (*l_vertices)[l_vh]      = osg::Vec3f(l_layout[i], g_ptsSettings->m_spline3DHeight);
    (*l_vertices)[l_vh + 1u] = osg::Vec3f(l_layout[i], g_ptsSettings->m_spline3DHeight + g_ptsSettings->m_shieldHeight);
    //! colors shield
    (*l_colors)[l_vs]      = l_colorShieldOuter;
    (*l_colors)[l_vs + 1u] = l_colorShieldInner;
    (*l_colors)[l_vs + 2u] = l_colorShieldOuter;
    //! colors hairlines
    (*l_colors)[l_vh]      = l_colorHairline;
    (*l_colors)[l_vh + 1u] = l_colorHairline;
    //! tex coords shield
    const LookupTexture::TexCoord l_colorCoord = LookupTexture::s_distanceColor.get(0.5f, g_ptsSettings->getDistances().normalize(l_distances[i]));
    (*l_texCoords)[l_vs]      = l_colorCoord;
    (*l_texCoords)[l_vs + 1u] = l_colorCoord;
    (*l_texCoords)[l_vs + 2u] = l_colorCoord;
    //! tex coords hairlines
    (*l_texCoords)[l_vh]      = l_colorCoord;
    (*l_texCoords)[l_vh + 1u] = l_colorCoord;
    l_vs += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SHIELD_SEGMENT);
    l_vh += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_HAIRLINE_SEGMENT);
  }

  handleDegradation(f_visitor.getDegradationData(), l_colors, l_texCoords, l_pasZoneAreas, PtsShield3DDegradationFunctor());

  l_vertices->dirty();
  l_colors->dirty();
  l_texCoords->dirty();
  l_geometry->dirtyBound();
}


//!
//! PtsSpline3DDegradationFunctor
//!
class PtsSpline3DDegradationFunctor : public DegradationFunctor
{
public:

  PtsSpline3DDegradationFunctor()
    : m_errorColorCoord(LookupTexture::s_distanceColor.get(0.0f)) // PRQA S 2323 // PRQA S 4052
  {
  }

  void onHide(osg::Vec4ubArray& f_colors, LookupTexture::TexCoordArray& /* f_texCoord */, unsigned int f_index) const override // PRQA S 2427
  {
    f_colors[f_index].a() = static_cast<osg::Vec4ub::value_type> (0u);
    //f_texCoord[f_index].x() = 0u;
  }

  void onError(osg::Vec4ubArray& /* f_colors */, LookupTexture::TexCoordArray& f_texCoords, unsigned int f_index) const override // PRQA S 2427
  {
    f_texCoords[f_index].y() = m_errorColorCoord;
  }

  LookupTexture::TexCoord::value_type m_errorColorCoord;
};


//!
//! PtsSpline3D
//!
PtsSpline3D::PtsSpline3D(const osg::Vec4ub& f_color)
  : m_height(1.0f) // PRQA S 2323 // PRQA S 4052
  , m_color(f_color) // PRQA S 2323
{
  osg::Geode::addDrawable(createGeometry("PtsSpline3D")); // PRQA S 3803
}


PtsSpline3D::PtsSpline3D(const PtsSpline3D& f_other, const osg::CopyOp& f_copyOp)
  : PtsSpline(f_other, f_copyOp) // PRQA S 2323
  , m_height(f_other.m_height) // PRQA S 2323
  , m_color(f_other.m_color) // PRQA S 2323
{
}


float PtsSpline3D::getHeight() const // PRQA S 2446
{
  return m_height;
}


void PtsSpline3D::setHeight(float f_height) // PRQA S 2446
{
  m_height = f_height;
}


const osg::Vec4ub PtsSpline3D::getColor() const
{
  return m_color;
}


void PtsSpline3D::setColor(const osg::Vec4ub& f_color)
{
  m_color = f_color;
}


void PtsSpline3D::update(const PtsUpdateVisitor& f_visitor)
{
  osg::Geometry* const l_geometry  = getDrawable(0u)->asGeometry();
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (l_geometry->getVertexArray()); // PRQA S 3076
  osg::UByteArray* const l_pasZoneAreas = static_cast<osg::UByteArray*> (l_geometry->getUserData()); // PRQA S 3076
  osg::Vec4ubArray* const l_colors = static_cast<osg::Vec4ubArray*> (l_geometry->getColorArray()); // PRQA S 3076
  LookupTexture::TexCoordArray* const l_texCoords = static_cast<LookupTexture::TexCoordArray*> (l_geometry->getTexCoordArray(0u)); // PRQA S 3076

  const pc::util::Polygon2D& l_layout = f_visitor.getLayout();
  const unsigned int l_numLayoutPoints = static_cast<unsigned int> (l_layout.size()); // PRQA S 2427
  if (l_numLayoutPoints != m_numLayoutPoints)
  {
    m_numLayoutPoints = l_numLayoutPoints;
    const unsigned int l_numVertices = l_numLayoutPoints * static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SEGMENT); // PRQA S 2427
    l_vertices->resize(l_numVertices);
    l_pasZoneAreas->resize(l_numVertices);
    l_colors->resize(l_numVertices);
    l_texCoords->resize(l_numVertices);
    //! clear primitive sets
    l_geometry->removePrimitiveSet(0u, l_geometry->getNumPrimitiveSets()); // PRQA S 3803
    l_geometry->addPrimitiveSet(createSurface(l_numLayoutPoints, NUM_VERTICES_PER_SEGMENT, 0u)); // PRQA S 3803
    // build PAS zone area look-up table
    const PtsUpdateVisitor::PasZoneList& l_pasZoneMappings = f_visitor.getPasZoneMappings();
    unsigned int l_v = 0u; // PRQA S 2427
    for (unsigned int i = 0u; i < l_numLayoutPoints; ++i) // PRQA S 2427
    {
      const osg::UByteArray::value_type l_pasZoneArea = getPasZoneArea(l_pasZoneMappings[i], l_layout[i]);
      (*l_pasZoneAreas)[l_v]      = l_pasZoneArea;
      (*l_pasZoneAreas)[l_v + 1u] = l_pasZoneArea;
      l_v += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SEGMENT);
    }
  }

  const pc::util::FloatList& l_distances = f_visitor.getDistances();
  const float l_gradientSize = m_height * (g_ptsSettings->m_smoothingGradientSize); // PRQA S 2446
  unsigned int l_v = 0u; // PRQA S 2427
  for (unsigned int i = 0u; i < l_numLayoutPoints; ++i) // PRQA S 2427
  {
    //! vertices
    (*l_vertices)[l_v]      = osg::Vec3f(l_layout[i], 0.0f - l_gradientSize);
    (*l_vertices)[l_v + 1u] = osg::Vec3f(l_layout[i], m_height + l_gradientSize);
    //! colors
    (*l_colors)[l_v]      = m_color;
    (*l_colors)[l_v + 1u] = m_color;
    //! tex coords
    const LookupTexture::TexCoord::value_type l_colorCoord = LookupTexture::s_distanceColor.get(g_ptsSettings->getDistances().normalize(l_distances[i]));
    (*l_texCoords)[l_v]      = LookupTexture::TexCoord(0, l_colorCoord);
    (*l_texCoords)[l_v + 1u] = LookupTexture::TexCoord(LookupTexture::s_maxTexCoord, l_colorCoord);
    l_v += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SEGMENT);
  }

  handleDegradation(f_visitor.getDegradationData(), l_colors, l_texCoords, l_pasZoneAreas, PtsSpline3DDegradationFunctor());

  l_vertices->dirty();
  l_colors->dirty();
  l_texCoords->dirty();
  l_geometry->dirtyBound();
}


//!
//! PtsContour3DDegradationFunctor
//!
class PtsContour3DDegradationFunctor : public DegradationFunctor
{
public:

  void onHide(osg::Vec4ubArray& f_colors, LookupTexture::TexCoordArray& /* f_texCoord */, unsigned int f_index) const override // PRQA S 2427
  {
    f_colors[f_index].a() = static_cast<osg::Vec4ub::value_type> (0u);
  }

  //! contour has fixed color, so no re-coloring in case of an error reaction

};



//!
//! PtsContour3D
//!
PtsContour3D::PtsContour3D() // PRQA S 2628
  : PtsSpline3D(osg::Vec4ub(g_white.r(), g_white.g(), g_white.b(), float2UByte(g_ptsSettings->m_contourAlpha))) // PRQA S 2323
{
}


PtsContour3D::PtsContour3D(const PtsContour3D& f_other, const osg::CopyOp& f_copyOp)
  : PtsSpline3D(f_other, f_copyOp) // PRQA S 2323
{
}


void PtsContour3D::update(const PtsUpdateVisitor& f_visitor)
{
  osg::Geometry* const l_geometry  = getDrawable(0u)->asGeometry();
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (l_geometry->getVertexArray()); // PRQA S 3076
  osg::UByteArray* const l_pasZoneAreas = static_cast<osg::UByteArray*> (l_geometry->getUserData()); // PRQA S 3076
  osg::Vec4ubArray* const l_colors = static_cast<osg::Vec4ubArray*> (l_geometry->getColorArray()); // PRQA S 3076
  LookupTexture::TexCoordArray* const l_texCoords = static_cast<LookupTexture::TexCoordArray*> (l_geometry->getTexCoordArray(0u)); // PRQA S 3076

  const pc::util::Polygon2D& l_layout = f_visitor.getLayout();
  const unsigned int l_numLayoutPoints = static_cast<unsigned int> (l_layout.size()); // PRQA S 2427
  if (l_numLayoutPoints != m_numLayoutPoints)
  {
    m_numLayoutPoints = l_numLayoutPoints;
    const unsigned int l_numVertices = l_numLayoutPoints * static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SEGMENT); // PRQA S 2427
    l_vertices->resize(l_numVertices);
    l_pasZoneAreas->resize(l_numVertices);
    l_colors->resize(l_numVertices);
    l_texCoords->resize(l_numVertices);
    //! clear primitive sets
    l_geometry->removePrimitiveSet(0u, l_geometry->getNumPrimitiveSets()); // PRQA S 3803
    l_geometry->addPrimitiveSet(createSurface(l_numLayoutPoints, NUM_VERTICES_PER_SEGMENT, 0u)); // PRQA S 3803
    //! texture coords will not change dynamically, so on initialize them only once
    const PtsUpdateVisitor::PasZoneList& l_pasZoneMappings = f_visitor.getPasZoneMappings();
    unsigned int l_v = 0u; // PRQA S 2427
    for (unsigned int i = 0u; i < l_numLayoutPoints; ++i) // PRQA S 2427
    {
      const osg::UByteArray::value_type l_pasZoneArea = getPasZoneArea(l_pasZoneMappings[i], l_layout[i]);
      (*l_pasZoneAreas)[l_v]      = l_pasZoneArea;
      (*l_pasZoneAreas)[l_v + 1u] = l_pasZoneArea;
      l_v += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SEGMENT);
    }
  }

  const LookupTexture::TexCoord::value_type l_colorCoord = LookupTexture::s_outlineColor.get(0.5f);
  unsigned int l_v = 0u; // PRQA S 2427
  for (unsigned int i = 0u; i < l_numLayoutPoints; ++i) // PRQA S 2427
  {
    (*l_vertices)[l_v]     = osg::Vec3f(l_layout[i], 0.0f);
    (*l_vertices)[l_v + 1u] = osg::Vec3f(l_layout[i], m_height);
    (*l_colors)[l_v]      = m_color;
    (*l_colors)[l_v + 1u] = m_color;
    (*l_texCoords)[l_v]      = LookupTexture::TexCoord(0, l_colorCoord);
    (*l_texCoords)[l_v + 1u] = LookupTexture::TexCoord(LookupTexture::s_maxTexCoord, l_colorCoord);
    l_v += static_cast<vfc::uint32_t>(NUM_VERTICES_PER_SEGMENT);
  }

  handleDegradation(f_visitor.getDegradationData(), l_colors, l_texCoords, l_pasZoneAreas, PtsContour3DDegradationFunctor());

  l_vertices->dirty();
  l_colors->dirty();
  l_texCoords->dirty();
  l_geometry->dirtyBound();
}


} // namespace ptsoverlay
} // namespace assets
} // namespace cc
