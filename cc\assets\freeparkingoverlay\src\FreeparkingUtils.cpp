//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/freeparkingoverlay/inc/FreeparkingUtils.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingManager.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/sm/viewmode/inc/ViewModeStateMachine.h"
#include "pc/generic/util/cli/inc/CommandCallback.h"
#include "pc/generic/util/cli/inc/CommandLineInterface.h"
#include "pc/generic/util/cli/inc/CommandRegistry.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/c2w/inc/Utils.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/Scene.h"
#include "pc/svs/core/inc/View.h"
#include "pc/svs/util/cli/inc/BaseCommands.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/virtcam/inc/CameraFirstPersonManipulator.h"
#include "util/logging/inc/LoggingContexts.h"
#include <algorithm>

using pc::util::logging::g_AppContext;
using pc::util::logging::g_EngineContext;

/// @deviation NRCS2_076
/// Rule QACPP-4.3.0-3803
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

bool isSlotHeadingUp(vfc::float32_t f_angle)
{
    clampAngleRad(f_angle);
    if (f_angle < osg::PI_2 || f_angle > osg::PI_2 * 3.0)
    {
        return true;
    }
    return false;
}

bool isSlotHeadingDown(vfc::float32_t f_angle)
{
    clampAngleRad(f_angle);
    if (f_angle > osg::PI_2 && f_angle <= osg::PI_2 * 3.0)
    {
        return true;
    }
    return false;
}

bool isSlotHeadingLeft(vfc::float32_t f_angle)
{
    clampAngleRad(f_angle);
    if (f_angle > 0.0 && f_angle < osg::PI )
    {
        return true;
    }
    return false;
}

bool isSlotHeadingRight(vfc::float32_t f_angle)
{
    clampAngleRad(f_angle);
    if (f_angle > osg::PI && f_angle < osg::PI * 2.0)
    {
        return true;
    }
    return false;
}

bool isInsideViewport(const cc::daddy::HmiData* f_hmiData, const pc::core::Viewport* f_viewport, bool f_hori)
{
    if (f_hmiData == nullptr || f_viewport == nullptr)
    {
        return false;
    }
    vfc::int16_t huX = 0;
    vfc::int16_t huY = 0;
    vfc::int16_t huX1 = 0;
    vfc::int16_t huY1 = 0;
    vfc::int16_t huX2 = 0;
    vfc::int16_t huY2 = 0;
    const vfc::int16_t originX = static_cast<vfc::int16_t>(f_viewport->m_origin.x());
    const vfc::int16_t originY = static_cast<vfc::int16_t>(f_viewport->m_origin.y());
    const vfc::int16_t sizeX   = static_cast<vfc::int16_t>(f_viewport->m_size.x());
    const vfc::int16_t sizeY   = static_cast<vfc::int16_t>(f_viewport->m_size.y());
    if (f_hori)
    {
        huX  = static_cast<vfc::float32_t>(f_hmiData->m_huX);
        huY  = (pc::core::g_systemConf->m_mainViewport.m_size.y()) - static_cast<vfc::float32_t>(f_hmiData->m_huY);
        huX1 = static_cast<vfc::float32_t>(f_hmiData->m_huTwoFinger1X);
        huY1 = (pc::core::g_systemConf->m_mainViewport.m_size.y()) -
               static_cast<vfc::float32_t>(f_hmiData->m_huTwoFinger1Y);
        huX2 = static_cast<vfc::float32_t>(f_hmiData->m_huTwoFinger2X);
        huY2 = (pc::core::g_systemConf->m_mainViewport.m_size.y()) -
               static_cast<vfc::float32_t>(f_hmiData->m_huTwoFinger2Y);
    }
    else
    {
        huX  = static_cast<vfc::float32_t>(f_hmiData->m_huY);
        huY  = static_cast<vfc::float32_t>(f_hmiData->m_huX);
        huX1 = static_cast<vfc::float32_t>(f_hmiData->m_huTwoFinger1X);
        huY1 = static_cast<vfc::float32_t>(f_hmiData->m_huTwoFinger1Y);
        huX2 = static_cast<vfc::float32_t>(f_hmiData->m_huTwoFinger2X);
        huY2 = static_cast<vfc::float32_t>(f_hmiData->m_huTwoFinger2Y);
    }
    const bool l_insideX  = (huX <= (originX + sizeX)) && (huX >= originX);
    const bool l_insideY  = (huY <= (originY + sizeY)) && (huY >= originY);
    const bool l_insideX1 = (huX1 <= (originX + sizeX)) && (huX1 >= originX);
    const bool l_insideY1 = (huY1 <= (originY + sizeY)) && (huY1 >= originY);
    const bool l_insideX2 = (huX2 <= (originX + sizeX)) && (huX2 >= originX);
    const bool l_insideY2 = (huY2 <= (originY + sizeY)) && (huY2 >= originY);
    return l_insideX && l_insideY && l_insideX1 && l_insideY1 && l_insideX2 && l_insideY2;
}

//* @brief : if start point locate inside center circle region, it is a translate action
bool isInTranslationArea(const osg::Vec3f& f_Center_vertex_screen, APSlitherStartEndPos& f_SlitherPos)
{
    osg::Vec3f l_rotateStartPos = osg::Vec3f(0.f, 0.f, 0.f);
    l_rotateStartPos.x()        = f_SlitherPos.StartPos.x();
    l_rotateStartPos.y()        = f_SlitherPos.StartPos.y();
    return pc::util::isInsideCircle(f_Center_vertex_screen, 200.0f, l_rotateStartPos);
}

bool isInTranslationArea(
    const osg::Vec3f&           f_FrontLeft_vertex_screen,
    const osg::Vec3f&           f_FrontRight_vertex_screen,
    const osg::Vec3f&           f_RearLeft_vertex_screen,
    const osg::Vec3f&           f_RearRight_vertex_screen,
    const APSlitherStartEndPos& f_SlitherPos)
{
    const vfc::float32_t minX = std::min(
        std::min(f_FrontLeft_vertex_screen.x(), f_FrontRight_vertex_screen.x()),
        std::min(f_RearLeft_vertex_screen.x(), f_RearRight_vertex_screen.x()));
    const vfc::float32_t maxX = std::max(
        std::max(f_FrontLeft_vertex_screen.x(), f_FrontRight_vertex_screen.x()),
        std::max(f_RearLeft_vertex_screen.x(), f_RearRight_vertex_screen.x()));
    const vfc::float32_t minY = std::min(
        std::min(f_FrontLeft_vertex_screen.y(), f_FrontRight_vertex_screen.y()),
        std::min(f_RearLeft_vertex_screen.y(), f_RearRight_vertex_screen.y()));
    const vfc::float32_t maxY = std::max(
        std::max(f_FrontLeft_vertex_screen.y(), f_FrontRight_vertex_screen.y()),
        std::max(f_RearLeft_vertex_screen.y(), f_RearRight_vertex_screen.y()));
    // XLOG_INFO(g_AppContext, "isInTranslationArea: minX: "<< minX << ", maxX:" << maxX << ".  minY: "<<minY << ", maxY:" << maxY);

    osg::Vec3f startPoint = {f_SlitherPos.StartPos, 0.0f};
    // XLOG_INFO(g_AppContext, "isInTranslationArea: f_SlitherPos.StartPos, x(): "<< f_SlitherPos.StartPos.x() << ", y():" << f_SlitherPos.StartPos.y());

    if (startPoint.x() < minX || startPoint.x() > maxX || startPoint.y() < minY || startPoint.y() > maxY)
    {
        return false;
    }

    const osg::Vec3f vp1 = startPoint - f_FrontLeft_vertex_screen;
    const osg::Vec3f vp2 = startPoint - f_FrontRight_vertex_screen;
    const osg::Vec3f vp3 = startPoint - f_RearRight_vertex_screen;
    const osg::Vec3f vp4 = startPoint - f_RearLeft_vertex_screen;

    const vfc::float32_t theta12 = acosf((vp1 * vp2) / (vp1.length() * vp2.length()));
    const vfc::float32_t theta23 = acosf((vp2 * vp3) / (vp2.length() * vp3.length()));
    const vfc::float32_t theta34 = acosf((vp3 * vp4) / (vp3.length() * vp4.length()));
    const vfc::float32_t theta41 = acosf((vp4 * vp1) / (vp4.length() * vp1.length()));

    const vfc::float32_t totalDegree = theta12 + theta23 + theta34 + theta41;
    // XLOG_INFO(g_AppContext, "isInTranslationArea: totalDegree: "<< totalDegree );

    return isEqual(totalDegree, static_cast<vfc::float32_t>(osg::PI * 2.0));
}

bool isInTranslationArea(
    const osg::Vec3f& f_FrontLeft_vertex_screen,
    const osg::Vec3f& f_FrontRight_vertex_screen,
    const osg::Vec3f& f_RearLeft_vertex_screen,
    const osg::Vec3f& f_RearRight_vertex_screen,
    const osg::Vec2f& f_Pos0,
    const osg::Vec2f& f_Pos1)
{
    const vfc::float32_t minX = std::min(
        std::min(f_FrontLeft_vertex_screen.x(), f_FrontRight_vertex_screen.x()),
        std::min(f_RearLeft_vertex_screen.x(), f_RearRight_vertex_screen.x()));
    const vfc::float32_t maxX = std::max(
        std::max(f_FrontLeft_vertex_screen.x(), f_FrontRight_vertex_screen.x()),
        std::max(f_RearLeft_vertex_screen.x(), f_RearRight_vertex_screen.x()));
    const vfc::float32_t minY = std::min(
        std::min(f_FrontLeft_vertex_screen.y(), f_FrontRight_vertex_screen.y()),
        std::min(f_RearLeft_vertex_screen.y(), f_RearRight_vertex_screen.y()));
    const vfc::float32_t maxY = std::max(
        std::max(f_FrontLeft_vertex_screen.y(), f_FrontRight_vertex_screen.y()),
        std::max(f_RearLeft_vertex_screen.y(), f_RearRight_vertex_screen.y()));

    osg::Vec3f startPoint0 = {f_Pos0, 0.0f};
    osg::Vec3f startPoint1 = {f_Pos1, 0.0f};
    // XLOG_INFO(g_AppContext, "twofingerisInTranslationArea: minX: "<< minX << ", maxX:" << maxX << ".  minY: "<<minY << ", maxY:" << maxY);
    // XLOG_INFO(g_AppContext, "twofingerisInTranslationArea: startPoint0, x(): "<< startPoint0.x() << ", y():" << startPoint0.y() << ".  startPoint1, x(): "<<startPoint1.x() << ", y():" << startPoint1.y());

    if (startPoint0.x() < minX || startPoint0.x() > maxX || startPoint0.y() < minY || startPoint0.y() > maxY ||
        startPoint1.x() < minX || startPoint1.x() > maxX || startPoint1.y() < minY || startPoint1.y() > maxY)
    {
        return false;
    }

    const osg::Vec3f vp1_0 = startPoint0 - f_FrontLeft_vertex_screen;
    const osg::Vec3f vp2_0 = startPoint0 - f_FrontRight_vertex_screen;
    const osg::Vec3f vp3_0 = startPoint0 - f_RearRight_vertex_screen;
    const osg::Vec3f vp4_0 = startPoint0 - f_RearLeft_vertex_screen;

    const osg::Vec3f vp1_1 = startPoint1 - f_FrontLeft_vertex_screen;
    const osg::Vec3f vp2_1 = startPoint1 - f_FrontRight_vertex_screen;
    const osg::Vec3f vp3_1 = startPoint1 - f_RearRight_vertex_screen;
    const osg::Vec3f vp4_1 = startPoint1 - f_RearLeft_vertex_screen;

    const vfc::float32_t theta12_0 = acosf((vp1_0 * vp2_0) / (vp1_0.length() * vp2_0.length()));
    const vfc::float32_t theta23_0 = acosf((vp2_0 * vp3_0) / (vp2_0.length() * vp3_0.length()));
    const vfc::float32_t theta34_0 = acosf((vp3_0 * vp4_0) / (vp3_0.length() * vp4_0.length()));
    const vfc::float32_t theta41_0 = acosf((vp4_0 * vp1_0) / (vp4_0.length() * vp1_0.length()));

    const vfc::float32_t theta12_1 = acosf((vp1_1 * vp2_1) / (vp1_1.length() * vp2_1.length()));
    const vfc::float32_t theta23_1 = acosf((vp2_1 * vp3_1) / (vp2_1.length() * vp3_1.length()));
    const vfc::float32_t theta34_1 = acosf((vp3_1 * vp4_1) / (vp3_1.length() * vp4_1.length()));
    const vfc::float32_t theta41_1 = acosf((vp4_1 * vp1_1) / (vp4_1.length() * vp1_1.length()));

    const vfc::float32_t totalDegree_0 = theta12_0 + theta23_0 + theta34_0 + theta41_0;
    const vfc::float32_t totalDegree_1 = theta12_1 + theta23_1 + theta34_1 + theta41_1;
    // XLOG_INFO(g_AppContext, "twofingerisInTranslationArea: totalDegree_0: "<< totalDegree_0 << ", totalDegree_1:" << totalDegree_1 );

    return isEqual(totalDegree_0, static_cast<vfc::float32_t>(osg::PI * 2.0)) &&
           isEqual(totalDegree_1, static_cast<vfc::float32_t>(osg::PI * 2.0));
}

//* @brief : if start point locate inside four corner circle region, it is a rotate action
bool isInRotationArea(
    const osg::Vec3f&     f_FrontLeft_vertex_screen,
    const osg::Vec3f&     f_FrontRight_vertex_sreen,
    const osg::Vec3f&     f_RearLeft_vertex_screen,
    const osg::Vec3f&     f_RearRight_vertex_screen,
    APSlitherStartEndPos& f_SlitherPos)
{
    osg::Vec3f l_rotateStartPos = osg::Vec3f(0.f, 0.f, 0.f);
    l_rotateStartPos.x()        = f_SlitherPos.StartPos.x();
    l_rotateStartPos.y()        = f_SlitherPos.StartPos.y();

    if (pc::util::isInsideCircle(f_FrontLeft_vertex_screen, 50.0f, l_rotateStartPos) ||
        pc::util::isInsideCircle(f_FrontRight_vertex_sreen, 50.0f, l_rotateStartPos) ||
        pc::util::isInsideCircle(f_RearLeft_vertex_screen, 50.0f, l_rotateStartPos) ||
        pc::util::isInsideCircle(f_RearRight_vertex_screen, 50.0f, l_rotateStartPos))
    {
        return true;
    }
    else
    {
        return false;
    }
}

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc
