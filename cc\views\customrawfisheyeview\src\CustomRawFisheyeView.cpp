//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: VUJ1LR Vujicic Milica (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomRawFisheyeView.cpp
/// @brief
//=============================================================================
#include "cc/views/customrawfisheyeview/inc/CustomRawFisheyeView.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/virtcam/inc/VirtualCam.h"
#include "pc/svs/core/inc/VideoTexture.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "CustomSystemConf.h" // PRQA S 1060
#include "osg/MatrixTransform"

namespace
{
using pc::core::sysconf::Cameras;

const std::string g_name = "RawFisheyeView";
const std::string g_nameFront = g_name + "Front";
const std::string g_nameRight = g_name + "Right";
const std::string g_nameRear = g_name + "Rear";
const std::string g_nameLeft = g_name + "Left";

std::string getNameForCamera(Cameras f_cam)
{
  switch (f_cam)
  {
    case Cameras::FRONT_CAMERA:
    {
      return g_nameFront;
    }
    case Cameras::RIGHT_CAMERA:
    {
      return g_nameRight;
    }
    case Cameras::REAR_CAMERA:
    {
      return g_nameRear;
    }
    case Cameras::LEFT_CAMERA:
    {
      return g_nameLeft;
    }
    default:
    {
      return g_name;
    }
  }
}
}

namespace cc
{
namespace views
{
namespace CustomRawFisheyeView
{

//!
//! CustomRawFisheyeViewSettings
//!
class CustomRawFisheyeViewSettings : public pc::util::coding::ISerializable
{
public:
  CustomRawFisheyeViewSettings()
    : m_viewport(0, 0, cc::target::sysconf::E_IMAGE_FORMAT_TEXTURE_WIDTH, cc::target::sysconf::E_IMAGE_FORMAT_TEXTURE_HEIGHT)
  {
  }

  SERIALIZABLE(CustomRawFisheyeViewSettings)  //PRQA S 2428
  {
    ADD_MEMBER(pc::core::Viewport, viewport);
  }

  pc::core::Viewport m_viewport;
};

pc::util::coding::Item<CustomRawFisheyeViewSettings> g_settings("CustomRawFisheyeView");

//!
//! CustomRawFisheyeView
//!

CustomRawFisheyeView::CustomRawFisheyeView(vfc::uint32_t f_camID, bool f_quadViews, pc::core::Framework* f_framework, const pc::core::Viewport& f_viewport)
  : pc::core::View("CustomRawFisheyeView", f_viewport)
{
  if (false == f_quadViews)
  {
    osg::ref_ptr<pc::views::rawfisheyeview::RawFisheyeView> const l_rawFisheyeView = new pc::views::rawfisheyeview::RawFisheyeView(f_framework);
    setProjectionMatrixAsOrtho2D(0.0f, 1.0f, 0.0, 1.0f);

    osg::Group::addChild(reinterpret_cast<osg::Node*>(l_rawFisheyeView->createRawViewQuads(f_camID, f_framework)));  // PRQA S 3803
  }
  else
  {
    // Adaptation for calibration view
    osg::ref_ptr<CustomMonoRawFisheyeView> const l_rawCustomMonoFisheyeView = new CustomMonoRawFisheyeView(f_framework);
    setProjectionMatrixAsOrtho2D(0.0f, 1.0f, 0.0, 1.0f);

    osg::ref_ptr<osg::MatrixTransform> const l_topLeft = new osg::MatrixTransform(osg::Matrix::translate(0.0f, 1.0f, 0.0f));
    l_topLeft->addChild(reinterpret_cast<osg::Node*>(l_rawCustomMonoFisheyeView->createRawViewQuads(static_cast<vfc::uint32_t>(pc::core::sysconf::FRONT_CAMERA), f_framework)));  // PRQA S 3803

    osg::ref_ptr<osg::MatrixTransform>  const l_topRight = new osg::MatrixTransform(osg::Matrix::translate(1.0f, 1.0f, 0.0f));
    l_topRight->addChild(reinterpret_cast<osg::Node*>(l_rawCustomMonoFisheyeView->createRawViewQuads(static_cast<vfc::uint32_t>(pc::core::sysconf::REAR_CAMERA), f_framework)));  // PRQA S 3803

    osg::ref_ptr<osg::MatrixTransform> const l_bottomLeft = new osg::MatrixTransform(osg::Matrix::translate(0.0f, 0.0f, 0.0f));
    l_bottomLeft->addChild(reinterpret_cast<osg::Node*>(l_rawCustomMonoFisheyeView->createRawViewQuads(static_cast<vfc::uint32_t>(pc::core::sysconf::LEFT_CAMERA), f_framework)));  // PRQA S 3803

    osg::ref_ptr<osg::MatrixTransform> const l_bottomRight = new osg::MatrixTransform(osg::Matrix::translate(1.0f, 0.0f, 0.0f));
    l_bottomRight->addChild(reinterpret_cast<osg::Node*>(l_rawCustomMonoFisheyeView->createRawViewQuads(static_cast<vfc::uint32_t>(pc::core::sysconf::RIGHT_CAMERA), f_framework)));  // PRQA S 3803

    osg::ref_ptr<osg::MatrixTransform> const l_scaleTransform = new osg::MatrixTransform(osg::Matrix::scale(0.5f, 0.5f, 1.0f));

    l_scaleTransform->addChild(l_topLeft);  // PRQA S 3803
    l_scaleTransform->addChild(l_topRight);  // PRQA S 3803
    l_scaleTransform->addChild(l_bottomLeft);  // PRQA S 3803
    l_scaleTransform->addChild(l_bottomRight);  // PRQA S 3803

    osg::Group::addChild(l_scaleTransform);  // PRQA S 3803
  }
}

CustomRawFisheyeView::~CustomRawFisheyeView() = default;


//!
//! CustomMonoRawFisheyeView
//!
CustomMonoRawFisheyeView::CustomMonoRawFisheyeView(pc::core::Framework* f_framework, Cameras f_camera)
  : pc::core::View(getNameForCamera(f_camera), pc::views::rawfisheyeview::RawFisheyeViewSettings::getSettingsForCamera(f_camera)->m_viewport)
  , m_framework(f_framework)
  , m_settings(pc::views::rawfisheyeview::RawFisheyeViewSettings::getSettingsForCamera(f_camera))
{
  setNumChildrenRequiringUpdateTraversal(1u);
  setRenderOrder(osg::Camera::POST_RENDER, m_settings->m_renderOrder);
  setProjectionMatrixAsOrtho2D(0.0f, 1.0f, 0.0, 1.0f);
}


CustomMonoRawFisheyeView::~CustomMonoRawFisheyeView() = default;


osg::Geode* CustomMonoRawFisheyeView::createRawViewQuads(vfc::uint32_t f_camID, pc::core::Framework* f_framework)
{
    const bool l_flipHorizontally = m_settings->flipHorizontally(f_camID);
    const vfc::float32_t l_left  = l_flipHorizontally ? 1.0f : 0.0f;
    const vfc::float32_t l_right = l_flipHorizontally ? 0.0f : 1.0f;
    bool l_flipVertically = m_settings->flipVertically(f_camID); // PRQA S 3803

    if (static_cast<vfc::int32_t>(f_camID) == pc::core::sysconf::FRONT_CAMERA || static_cast<vfc::int32_t>(f_camID) == pc::core::sysconf::REAR_CAMERA)
    {
      l_flipVertically = true;
    }
    const vfc::float32_t l_bottom = l_flipVertically ? 1.0f : 0.0f;
    const vfc::float32_t l_top    = l_flipVertically ? 0.0f : 1.0f;

    //! Quad geometry
    osg::Geometry* const l_quad = osg::createTexturedQuadGeometry(
      osg::Vec3f(0.0f, 0.0f, 0.0f), //! corner
      osg::Vec3f(1.0f, 0.0f, 0.0f), //! width
      osg::Vec3f(0.0f, 1.0f, 0.0f), //! height
      l_left, l_bottom, l_right, l_top);
    l_quad->setUseDisplayList(false);
    l_quad->setUseVertexBufferObjects(true);

    //! Create geode and set StateSets
    osg::Geode* const l_geode = new osg::Geode;
    l_geode->addDrawable(l_quad);  // PRQA S 3803

    osg::StateSet* const l_stateSet = l_geode->getOrCreateStateSet();
    if (f_framework != nullptr)
    {
      l_stateSet->setTextureAttribute(0u, f_framework->getVideoTexture()->getCameraTexture(f_camID));
    }
    else
    {
       //
    }

    pc::core::setCameraTextureShader(l_stateSet, f_camID);
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
    return l_geode;
}


} // namespace CustomRawFisheyeView
} // namespace views
} // namespace cc

