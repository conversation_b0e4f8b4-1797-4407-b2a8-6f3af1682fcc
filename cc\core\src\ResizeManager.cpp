//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "ResizeManager.h"

#include "cc/core/src/ViewModeToggle.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "pc/svs/animation/inc/Action.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace core
{


// TODO: Proper render number
// #define ANIMATION_RENDER_NUM (9001)
namespace animationRender {
    constexpr int ANIMATION_RENDER_NUM = 9001;
}

ResizeManager::ResizeManager(
    pc::core::View*           f_view,
    const pc::core::Viewport& f_normalViewport,
    const pc::core::Viewport& f_fullViewport,
    float                     f_animationDuration,
    bool (*f_shouldBeFullscreen)(vfc::int32_t))
    : m_baseView(f_view)
    , m_resizeHandlers()
    , m_animationDuration(f_animationDuration)
    , shouldBeFullscreen(f_shouldBeFullscreen)
{
    m_resizeHandlers.push_back(
        std::make_unique<ResizeHandler>(f_view, f_normalViewport, f_fullViewport, animationRender::ANIMATION_RENDER_NUM, true));
}

ResizeManager::ResizeManager(
    std::initializer_list<pc::core::View*> f_views,
    const pc::core::Viewport&              f_normalViewport,
    const pc::core::Viewport&              f_fullViewport,
    float                                  f_animationDuration,
    bool (*f_shouldBeFullscreen)(vfc::int32_t))
    : m_resizeHandlers()
    , m_animationDuration(f_animationDuration)
    , shouldBeFullscreen(f_shouldBeFullscreen)
{
    m_baseView = *f_views.begin();

    int i = 0;
    for (auto* l_view : f_views)
    {
        m_resizeHandlers.push_back(std::make_unique<ResizeHandler>(
            l_view, f_normalViewport, f_fullViewport, animationRender::ANIMATION_RENDER_NUM + i, (i == 0 ? true : false)));
        i++;
    }
}

void ResizeManager::setEnableCameraUpdater(bool f_enableCameraUpdater)
{
    if (m_enableCameraUpdater != f_enableCameraUpdater)
    {
        m_enableCameraUpdater = f_enableCameraUpdater;
        for (auto& l_resizeHandler : m_resizeHandlers)
        {
            l_resizeHandler->setEnableCameraUpdater(f_enableCameraUpdater);
        }
    }
}

pc::animation::Animation* ResizeManager::getAnimation(const pc::daddy::ViewMode& f_mode, bool f_instantResize)
{
    pc::animation::Animation* l_animation = nullptr;

    vfc::float32_t l_animationDuration = m_animationDuration;
    if (EScreenID_PLANETARY_VIEW == f_mode.m_curr || EScreenID_PLANETARY_VIEW == f_mode.m_prev)
    {
        l_animationDuration = 0.0f;
    }

    if (isImageInImageView(f_mode.m_prev) && (f_mode.m_curr == EscreenID_FULL_SCREEN_3D))
    {
        l_animationDuration = 0.0f;
    }

    if (f_mode.m_curr == EscreenID_FULL_SCREEN_3D)
    {
        if (isImageInImageView(f_mode.m_prev))
        {
            l_animationDuration = 0.0f;
        }
        else
        {
            // l_animationDuration = 1.0f;
        }
    }

    if (f_instantResize)
    {
        l_animationDuration = 0.0f;
    }

    if (shouldBeFullscreen(f_mode.m_curr))
    {
        if (!m_isFullScreen)
        {
            // Not in full scren but should be

            l_animation = animateFullSize(isEnabled() ? l_animationDuration : 0);

            m_isFullScreen = true;
        }
    }
    else
    {
        if (m_isFullScreen)
        {
            // In full screen but shouldn't be

            l_animation = animateNormalSize(isEnabled() ? l_animationDuration : 0);

            m_isFullScreen = false;
        }
    }

    return l_animation;
}

pc::animation::Animation* ResizeManager::animateNormalSize(float f_animationDuration)
{
    pc::animation::Animation* l_animation = nullptr;

    for (auto& l_resizeHandler : m_resizeHandlers)
    {
        if (l_animation != nullptr)
        {
            l_animation = pc::animation::parallel(l_animation, l_resizeHandler->animateNormalSize(f_animationDuration));
        }
        else
        {
            l_animation = l_resizeHandler->animateNormalSize(f_animationDuration);
        }
    }

    return l_animation;
}

pc::animation::Animation* ResizeManager::animateFullSize(float f_animationDuration)
{
    pc::animation::Animation* l_animation = nullptr;

    for (auto& l_resizeHandler : m_resizeHandlers)
    {
        if (l_animation != nullptr)
        {
            l_animation = pc::animation::parallel(l_animation, l_resizeHandler->animateFullSize(f_animationDuration));
        }
        else
        {
            l_animation = l_resizeHandler->animateFullSize(f_animationDuration);
        }
    }

    return l_animation;
}

bool ResizeManager::isEnabled()
{
    return m_baseView->getNodeMask() != 0;
}

} // namespace core
} // namespace cc
