//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ViewModeStateTransitionManager.h
/// @brief
//=============================================================================

#ifndef CC_CORE_VIEWMODESTATETRANSITIONMANAGER_H
#define CC_CORE_VIEWMODESTATETRANSITIONMANAGER_H

#include "ResizeManager.h"

#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/virtcam/inc/CameraPositions.h"
#include "pc/svs/animation/inc/AnimationQueue.h"
#include "pc/svs/worker/bowlshaping/inc/BowlShaperTask.h"

#include <osg/Referenced>
#include <osg/ref_ptr>
#include <osgGA/GUIEventHandler>

#include <list>
#include <mutex>

namespace osgGA
{
class GUIEventAdapter;
class GUIActionAdapter;
} // namespace osgGA

namespace pc
{
namespace core
{
class Framework;
class Scene;
} // namespace core
namespace daddy
{
struct ViewMode;
} // namespace daddy
} // namespace pc

namespace cc
{
namespace core
{

extern std::atomic<bool> g_enableFadeAnimation;

struct ScreenIdStruct
{
    cc::daddy::SideViewEnableStatus     m_sideEnableStatus{cc::daddy::SIDEVIEW_DISABLE};
    cc::daddy::TopViewEnableStatus      m_topEnableStatus{cc::daddy::TOPVIEW_DISABLE};
    cc::daddy::WheelViewEnableStatus    m_wheelEnableStatus{cc::daddy::WHEELVIEW_DISABLE};

    bool operator==(const ScreenIdStruct& f_other) const
    {
        return (m_sideEnableStatus == f_other.m_sideEnableStatus) && (m_topEnableStatus == f_other.m_topEnableStatus) &&
               (m_wheelEnableStatus == f_other.m_wheelEnableStatus);
    }

    bool operator!=(const ScreenIdStruct& f_other) const
    {
        return !operator==(f_other);
    }
};

//======================================================
// ViewModeStateTransitionManager
//------------------------------------------------------
/// Main class for viewmode transition management.
/// Responsible for managing view transitions, jumps and all related actions.
/// <AUTHOR> David (CC-DA/EAV3)
/// @ingroup ViewModeStateTransition
//======================================================
class ViewModeStateTransitionManager : public osgGA::GUIEventHandler
{
public:
    explicit ViewModeStateTransitionManager(pc::core::Framework* f_framework);
    // If an animation is running, the transition will be delayed and triggered at the end of the animation.
    void checkDelayedTransition();

protected:
    virtual ~ViewModeStateTransitionManager();

private:
    //! Copy constructor is not permitted.
    ViewModeStateTransitionManager(const ViewModeStateTransitionManager& other); // = delete
    //! Copy assignment operator is not permitted.
    ViewModeStateTransitionManager& operator=(const ViewModeStateTransitionManager& other); // = delete

    pc::animation::Animation* createViewTransition(const pc::daddy::ViewMode& f_mode) const;
    pc::animation::Animation* createViewAction(const pc::daddy::ViewMode& f_mode, vfc::float32_t& f_animDuration) const;
    pc::animation::Animation* createCameraJumpAction(const pc::daddy::ViewMode& f_mode) const;
    pc::animation::Animation* createBowlJumpPreAction(const pc::daddy::ViewMode& f_mode) const;
    pc::animation::Animation* createBowlAnimation(const pc::daddy::ViewMode& f_mode, vfc::float32_t f_duration) const;

    pc::animation::Animation*
    createToFishEyeTransition(pc::core::Framework* f_framework, const pc::daddy::ViewMode& f_mode);
    pc::animation::Animation*
    createFromFishEyeTransition(pc::core::Framework* f_framework, const pc::daddy::ViewMode& f_mode);

    bool handle(const osgGA::GUIEventAdapter& f_ea, osgGA::GUIActionAdapter& f_aa);
    void handleViewModeStateChange(const pc::daddy::ViewMode& f_mode);
    void handleViewportSize();
    void executeViewportSize();

private:
    vfc::uint32_t                      m_sequenceNumber;
    pc::core::Framework*               m_framework;
    pc::core::Scene*                   m_scene;
    bool                               m_isStandStillChanged;
    cc::target::common::EPARKStatusR2L m_lastParkingState;
    cc::target::common::EVehMoveDir    m_parkingStandStill;
    pc::animation::AnimationQueue*     m_transitionQueue;
    bool                               m_distortionOn         = true;
    bool                               m_distortionOnPrevious = true;
    pc::daddy::ViewMode                m_lastViewMode;
    pc::daddy::ViewMode                m_lastAnimatedViewMode;
    vfc::uint32_t                      m_previousScreenId;
    vfc::uint32_t                      m_currentScreenId;
    vfc::uint32_t                      m_lastScreenId; //Only used to compare with current view id
    ScreenIdStruct                     m_lastScreenIdStruct;
    ScreenIdStruct                     m_currentScreenIdStruct;
    std::unique_ptr<ResizeManager>     m_surroundViewResizeManager = nullptr;
    std::unique_ptr<ResizeManager>     m_planViewResizeManager     = nullptr;
    std::unique_ptr<ResizeManager>     m_singleViewResizeManager     = nullptr;
    bool                               m_isTransitionDelayed       = false;
    bool                               m_instantToggle{false};
    std::mutex                         m_mutex;
    vfc::uint32_t                      m_frameCount{0u};
};

enum EViewBowl
{
    EDEFAULT_BIG_BOWL = 0,
    EMEDIUM_BOWL      = 1,
    ESMALL_BOWL       = 2
};

EViewBowl                               getViewBowl(int f_mode);
pc::worker::bowlshaping::BowlShaperData getBowlData(EViewBowl f_bowl);

} // namespace core
} // namespace cc

#endif // CC_CORE_VIEWMODESTATETRANSITIONMANAGER_H
