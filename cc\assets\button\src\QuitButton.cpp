//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/button/inc/QuitButton.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/imgui/inc/imgui_manager.h"


namespace cc
{
namespace assets
{
namespace button
{
namespace quitbutton
{
class QuitButtonSettings : public pc::util::coding::ISerializable
{
public:

    SERIALIZABLE(QuitButtonSettings) // PRQA S 2428
    {
        ADD_MEMBER(ButtonTextureSettings, quitTexture);
        ADD_MEMBER(osg::Vec2f, horiPos);
        ADD_MEMBER(osg::Vec2f, vertPos);
    }

    ButtonTextureSettings m_quitTexture;

    osg::Vec2f m_horiPos{0.0f, 100.0f};
    osg::Vec2f m_vertPos{100.0f, 0.0f};

};

static pc::util::coding::Item<QuitButtonSettings> g_quitButtonSettings("QuitButton");


QuitButton::QuitButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView)
: Button(f_assetId, f_referenceView) // PRQA S 2759 // PRQA S 2323
, m_framework(f_framework) // PRQA S 2323
{
    setIconAtMiddle(false);
    setPositionHori(g_quitButtonSettings->m_horiPos);
    setPositionVert(g_quitButtonSettings->m_vertPos);
}


void QuitButton::onInvalid()
{
    setIconEnable(false);
}


void QuitButton::onUnavailable()
{
    setIconEnable(true);
    if      (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY  ) { setTexturePath(g_quitButtonSettings->m_quitTexture.m_day.m_UnavailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT) { setTexturePath(g_quitButtonSettings->m_quitTexture.m_night.m_UnavailableTexturePath); }
    else
    {
        setTexturePath(g_quitButtonSettings->m_quitTexture.m_night.m_UnavailableTexturePath);
    }
}


void QuitButton::onAvailable()
{
    setIconEnable(true);
    if      (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY  ) { setTexturePath(g_quitButtonSettings->m_quitTexture.m_day.m_AvailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT) { setTexturePath(g_quitButtonSettings->m_quitTexture.m_night.m_AvailableTexturePath); }
    else
    {
        setTexturePath(g_quitButtonSettings->m_quitTexture.m_night.m_AvailableTexturePath);
    }
}


void QuitButton::onPressed()
{
    setIconEnable(true);
    if      (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY  ) { setTexturePath(g_quitButtonSettings->m_quitTexture.m_day.m_PressedTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT) { setTexturePath(g_quitButtonSettings->m_quitTexture.m_night.m_PressedTexturePath); }
    else
    {
        setTexturePath(g_quitButtonSettings->m_quitTexture.m_night.m_PressedTexturePath);
    }
}


void QuitButton::onReleased()
{
    if (checkTouchInsideResponseArea()) // PRQA S 2759
    {
        IMGUI_LOG("Buttons", "QuitButton", "Pressed");
        if (cc::daddy::CustomDaddyPorts::sm_QuitButtonPressDaddy_SenderPort.isConnected())
        {
            auto& container = cc::daddy::CustomDaddyPorts::sm_QuitButtonPressDaddy_SenderPort.reserve();
            container.m_Data = true;
            cc::daddy::CustomDaddyPorts::sm_QuitButtonPressDaddy_SenderPort.deliver();
        }
    }
}


void QuitButton::update()
{
    setSettingModifiedCount(g_quitButtonSettings->getModifiedCount());

    bool allPortsHaveData = true;
    GET_PORT_DATA(touchStatusContainer,       m_framework->asCustomFramework()->m_HUTouchTypeReceiver, allPortsHaveData)
    GET_PORT_DATA(huRotateStatusContainer,    m_framework->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver, allPortsHaveData)
    GET_PORT_DATA(hmiDataContainer,           m_framework->asCustomFramework()->m_hmiDataReceiver, allPortsHaveData)
    GET_PORT_DATA(dayNightThemeContainer,     m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, allPortsHaveData)
    GET_PORT_DATA(parkStatusContainer,        m_framework->asCustomFramework()->m_parkHmiParkingStatusReceiver, allPortsHaveData)
    // GET_PORT_DATA(freeparkingActiveContainer, m_framework->asCustomFramework()->m_freeparkingActiveReceiver, allPortsHaveData)
    GET_PORT_DATA(driverIndContainer,         m_framework->asCustomFramework()->m_parkHmiParkDriverIndReceiver, allPortsHaveData)
    // GET_PORT_DATA(driverIndExtContainer,      m_framework->asCustomFramework()->m_parkHmiParkDriverIndExtReceiver, allPortsHaveData)
    GET_PORT_DATA(apaParkModeContainer,       m_framework->asCustomFramework()->m_parkHmiParkAPAPARKMODEReceiver, allPortsHaveData)
    GET_PORT_DATA(parkTypeContainer,          m_framework->asCustomFramework()->m_parkHmiParkParkngTypeSeldReceiver, allPortsHaveData)
    GET_PORT_DATA(recoverIndContainer,        m_framework->asCustomFramework()->m_parkHmiParkingRecoverIndReceiver, allPortsHaveData)
    // GET_PORT_DATA(driverSearchContainer,      m_framework->asCustomFramework()->m_ParkDriverIndSearchReceiver, allPortsHaveData)
    // GET_PORT_DATA(brakeContainer,             m_framework->asCustomFramework()->m_brakeLightStateReceiver, allPortsHaveData)

    if (!allPortsHaveData)
    {
        return;
    }
    const bool touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
    setDayNightTheme(dayNightThemeContainer->m_Data);
    setRotateTheme(static_cast<cc::target::common::EThemeTypeHU>(huRotateStatusContainer->m_Data));
    setPositionHori(g_quitButtonSettings->m_horiPos);
    setPositionVert(g_quitButtonSettings->m_vertPos);
    setHuX(hmiDataContainer->m_Data.m_huX);
    setHuY(hmiDataContainer->m_Data.m_huY);
    setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    m_parkStatus = parkStatusContainer->m_Data;

    // auto l_driverIndExt = driverIndExtContainer->m_Data;
    const auto l_driverInd = driverIndContainer->m_Data;
    const auto l_recoverInd = recoverIndContainer->m_Data;
    const auto l_apaParkMode = apaParkModeContainer->m_Data;
    const auto l_parkType = parkTypeContainer->m_Data;
    // auto l_driverSearch = driverSearchContainer->m_Data;
    // auto l_freeparkingActive = freeparkingActiveContainer->m_Data;
    // auto l_brakeStatus = brakeContainer->m_Data;

    ButtonState currentState = ButtonState::INVALID;
    if (
        m_parkStatus == ParkStatusType::PARK_Guidance_suspend &&
        (l_parkType == cc::target::common::EParkngTypeSeld::PARKING_IN || l_parkType == cc::target::common::EParkngTypeSeld::PARKING_OUT) &&
        l_apaParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA
    )
    {
        switch (l_recoverInd) // PRQA S 4018
        {
            case cc::target::common::EPARKRecoverIndR2L::PARKREC_PauseCommand:
            case cc::target::common::EPARKRecoverIndR2L::PARKREC_ObjectOnPath: //Req_437
            case cc::target::common::EPARKRecoverIndR2L::PARKREC_DoorOpen: //Req_441
            case cc::target::common::EPARKRecoverIndR2L::PARKREC_MirrorFold: //Req_440
            case cc::target::common::EPARKRecoverIndR2L::PARKREC_SeatBelt:
            case cc::target::common::EPARKRecoverIndR2L::PARKREC_HoodOpen: //Req_442
            case cc::target::common::EPARKRecoverIndR2L::PARKREC_TrunkOpen: //Req_439 and //Req_498
            case cc::target::common::EPARKRecoverIndR2L::PARKREC_BrakePadal: // Req 444
            case cc::target::common::EPARKRecoverIndR2L::PARKREC_RearSideCommingCar:
            case cc::target::common::EPARKRecoverIndR2L::PARKREC_FrontSideCommingCar:
            {
                currentState = AVAILABLE;
                break;
            }
            default:
            {
                break;
            }
        }
        if (l_driverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM)
        {
            currentState = AVAILABLE;
        }
    }

    if (currentState == ButtonState::INVALID)
    {
        setState(ButtonState::INVALID);
    }
    else
    {
        if (checkTouchInsideResponseArea() && (touchStatus() == TOUCH_DOWN || touchStatus() == TOUCH_MOVE) && currentState == AVAILABLE) // PRQA S 2759
        {
            currentState = PRESSED;
        }
        if (touchStatusChanged && touchStatus() == TOUCH_UP && getState() == PRESSED)
        {
            currentState = RELEASED;
        }
        setState(currentState);
    }

    if (currentState != RELEASED)
    {
        if (cc::daddy::CustomDaddyPorts::sm_QuitButtonPressDaddy_SenderPort.isConnected())
        {
            auto& container = cc::daddy::CustomDaddyPorts::sm_QuitButtonPressDaddy_SenderPort.reserve();
            container.m_Data = false;
            cc::daddy::CustomDaddyPorts::sm_QuitButtonPressDaddy_SenderPort.deliver();
        }
    }
}

} // namespace quitbutton
} // namespace button
} // namespace assets
} // namespace cc
