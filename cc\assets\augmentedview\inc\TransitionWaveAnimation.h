//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_AUGMENTEDVIEWTRANSITION_SRC_TRANSITIONWAVEANIMATION_H_
#define CC_ASSETS_AUGMENTEDVIEWTRANSITION_SRC_TRANSITIONWAVEANIMATION_H_

#include "cc/target/common/inc/commonInterface.h"
#include <cassert>
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/math/inc/VecMath.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "vfc/core/vfc_types.hpp"
namespace cc
{
namespace assets
{
namespace augmentedview
{

//!
//! AugmentedViewTransitionData
//!
class AugmentedViewTransitionData : public pc::util::coding::ISerializable
{
public:
  AugmentedViewTransitionData()
  : m_backgroundTextureFilename(CONCAT_PATH("cc/resources/ui/day/131_black_pixel.png"))
  , m_waveTextureFilename(CONCAT_PATH("cc/resources/waveTexture.png"))
  , m_transitionWaveAnimationDuration(0.5f)
  , m_transitionWaveMaxRadius(5.0f)
  , m_transitionWaveWidth(0.4f)
  , m_scanWaveAnimationDuration(4.0f)
  , m_scanWaveMaxRadius(2.5f)
  , m_scanWaveWidth(0.15f)
  , m_scanWaveBurstSize(1.0f)
  , m_scanWaveInterBurstTime(1.9f)
  , m_scanWaveIntraBurstTime(1.0f)
  , m_scanWaveTravelStartingPoint(0.5f)
  , m_scanWaveCenterXVert(0.5f)
  , m_trueColor(0.0f, 1.0f, 0.0f, 1.0f)
  , m_position(0.0f, 0.0f, 0.0f)
  {
  }

  SERIALIZABLE(AugmentedViewTransitionData)
  {
    ADD_STRING_MEMBER(backgroundTextureFilename);
    ADD_STRING_MEMBER(waveTextureFilename);
    ADD_FLOAT_MEMBER(transitionWaveAnimationDuration);
    ADD_FLOAT_MEMBER(transitionWaveMaxRadius);
    ADD_FLOAT_MEMBER(transitionWaveWidth);
    ADD_FLOAT_MEMBER(scanWaveAnimationDuration);
    ADD_FLOAT_MEMBER(scanWaveMaxRadius);
    ADD_FLOAT_MEMBER(scanWaveBurstSize);
    ADD_FLOAT_MEMBER(scanWaveWidth);
    ADD_FLOAT_MEMBER(scanWaveInterBurstTime);
    ADD_FLOAT_MEMBER(scanWaveIntraBurstTime);
    ADD_FLOAT_MEMBER(scanWaveTravelStartingPoint);
    ADD_FLOAT_MEMBER(scanWaveCenterXVert);
    ADD_MEMBER(osg::Vec4f, trueColor);
    ADD_MEMBER(osg::Vec3f, position);
  }

  std::string m_backgroundTextureFilename;
  std::string m_waveTextureFilename;

  vfc::float32_t m_transitionWaveAnimationDuration;
  vfc::float32_t m_transitionWaveMaxRadius;   //!< Max. radius of final expansion
  vfc::float32_t m_transitionWaveWidth;       //!< The "width" of the radar wave

  vfc::float32_t m_scanWaveAnimationDuration; //!< Duration of a scan-wave (periodically emitted wave in augmented view)
  vfc::float32_t m_scanWaveMaxRadius;         //!< Max. radius of final expansion
  vfc::float32_t m_scanWaveWidth;             //!< The "width" of each scan-wave (in meters)
  vfc::float32_t m_scanWaveBurstSize;         //!< Number of waves emitted in direct succession
  vfc::float32_t m_scanWaveInterBurstTime;    //!< Delay between two bursts
  vfc::float32_t m_scanWaveIntraBurstTime;    //!< Delay between two bursts
  vfc::float32_t m_scanWaveTravelStartingPoint;  //!< percentage of travel distance
  vfc::float32_t m_scanWaveCenterXVert;            //!< percentage of travel distance
  osg::Vec4f m_trueColor;
  osg::Vec3f m_position;
};

extern pc::util::coding::Item<AugmentedViewTransitionData> g_augmentedSettings;

//!
//! TransitionWaveAnimation
//!
class TransitionWaveAnimation {

public:
  TransitionWaveAnimation();
  TransitionWaveAnimation(vfc::float32_t f_animationDuration, vfc::float32_t f_maxRadius, bool f_transitionToAugmentedView);

  void reset(vfc::float32_t f_animationDuration, vfc::float32_t f_maxRadius, bool f_transitionToAugmentedView); // Reset animation to non-running state
  void reset(); // Reset animation to non-running state
  void start(vfc::float32_t f_animationDuration, vfc::float32_t f_maxRadius, bool f_transitionToAugmentedView);
  void start();
  void update(vfc::float32_t f_currentTime);
  bool isRunning() const;
  bool hasFinished() const;
  vfc::float32_t getWaveRadius() const;
  vfc::float32_t getWaveAlphaFade() const;
  vfc::float32_t getCameraFadeFactor() const;
  bool isTransitioningToAugmentedView() const;

private:
  bool  m_running;
  vfc::float32_t m_elapsedTime;

  // Animated values
  vfc::float32_t m_waveRadius;                 //!< Current wave radius
  vfc::float32_t m_waveAlpha;                  //!< Fade-factor for wave
  vfc::float32_t m_cameraFadeFactor;           //!< 0 == fully textured, 1 == fully black

  // Animation parameters
  vfc::float32_t m_lastTime;                   //!< Time of last update
  vfc::float32_t m_maxWaveRadius;              //!< Final radius at end of animation
  vfc::float32_t m_animationDuration;          //!< Total duration of animation
  vfc::float32_t m_cameraFadeStartTime;        //!< Start of camera fade
  vfc::float32_t m_cameraFadeEndTime;          //!< End of camera fade
  vfc::float32_t m_waveAlphaFadeStartDistance; //!< Start distance of additive radar wave fade
  vfc::float32_t m_waveAlphaFadeEndDistance;   //!< End distance of additive radar wave fade
  bool  m_transitionToAugmentedView;  //!< True iff transitioning to augmented view, false when transitioning to camera view
};

} // namespace augmentedview
} // namespace assets
} // namespace cc

#endif

