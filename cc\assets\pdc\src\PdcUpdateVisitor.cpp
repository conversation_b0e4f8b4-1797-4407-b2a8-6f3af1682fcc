//-------------------------------------------------------------------------------
// Copyright (c) 2021 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/pdc/inc/PdcUpdateVisitor.h"
#include "cc/assets/pdc/inc/PdcOverlay.h"
#include "cc/assets/pdc/inc/PdcSettings.h"

#include "cc/core/inc/CustomFramework.h"
#include "pc/svs/core/inc/Asset.h"

namespace cc
{
namespace assets
{
namespace pdc
{
///
/// PdcUpdateVisitor
///
PdcUpdateVisitor::PdcUpdateVisitor(const cc::core::CustomZoneLayout& f_zoneLayout, cc::core::CustomFramework* f_framework)
  : m_model(new PdcModel(f_zoneLayout)) // PRQA S 2323 // PRQA S 4052
  , m_framework (f_framework) // PRQA S 2323
{
}

PdcUpdateVisitor* PdcUpdateVisitor::create(const cc::core::CustomZoneLayout& f_zoneLayout, pc::core::Framework* f_framework)
{
  if (f_framework != nullptr)
  {
    return new PdcUpdateVisitor(f_zoneLayout, f_framework->asCustomFramework());
  }
  return nullptr;
}

void PdcUpdateVisitor::apply(osg::Geode& f_geode)
{
  const auto l_pdcOverlay = dynamic_cast<PdcOverlay*> (&f_geode); // PRQA S 3400
  if (l_pdcOverlay != nullptr)
  {
    l_pdcOverlay->update((*m_model.get()));
  }
}

void PdcUpdateVisitor::reset()
{
  m_model->update(*this);
}

const pc::vehicle::UltrasonicData* PdcUpdateVisitor::getUltrasonicData() const
{
  const pc::daddy::UltrasonicDataDaddy* const l_ussData = m_framework->m_ultrasonicDataReceiver.getData();
  if (l_ussData != nullptr)
  {
    return &l_ussData->m_Data;
  }
  return nullptr;
}

///
/// PdcUpdateCallback
///
PdcUpdateCallback::PdcUpdateCallback(PdcUpdateVisitor* f_updateVisitor)
  : m_updateVisitor(f_updateVisitor) // PRQA S 2323 // PRQA S 4052
  , m_lastUpdate(0u) // PRQA S 2323
{
}

PdcUpdateCallback::PdcUpdateCallback(const PdcUpdateCallback& f_other, const osg::CopyOp& f_copyOp)
  : osg::Object(f_other, f_copyOp) // PRQA S 2323
  , osg::NodeCallback(f_other, f_copyOp) // PRQA S 2323
  , m_updateVisitor(f_other.m_updateVisitor) // PRQA S 2323
  , m_lastUpdate(f_other.m_lastUpdate) // PRQA S 2323
{
}

void PdcUpdateCallback::operator() (osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  if (!m_updateVisitor.valid() || (f_node == nullptr) || (f_nv == nullptr))
  {
    return;
  }

  /// Parent Asset UpdateCallback() was set by EventCallback() on OverlayActivationCallback class (activation matrix)
  constexpr bool l_updateCallbackIsActive = true;
  const pc::core::Asset* const l_currentNodeParentAsset = dynamic_cast<pc::core::Asset*>(f_node->getParent(0u));  // PRQA S 3077 // PRQA S 3400

  if ((m_lastUpdate != f_nv->getTraversalNumber()))
  {
    if (l_currentNodeParentAsset != nullptr)
    {
      // only recompute PDC model once per frame
      // only recompute if PDC splines are even visible/enabled
      m_updateVisitor->reset();
    }
    m_lastUpdate = f_nv->getTraversalNumber();
  }

  f_node->accept(*m_updateVisitor.get());
  // no further traversal required when attached to PdcOverlay, since it is a osg::Geode
}

} // namespace pdc
} // namespace assets
} // namespace cc
