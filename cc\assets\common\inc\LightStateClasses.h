#ifndef LIGHT_STATE_CLASSES_H
#define LIGHT_STATE_CLASSES_H

#include "vfc/core/vfc_types.hpp"
#include "pc/generic/util/coding/inc/ISerializable.h"
#include "pc/generic/util/coding/inc/CodingManager.h"

#include <osg/Group>
#include <osg/NodeCallback>

#include <limits>
#include <map>
#include <memory>
#include <string>
#include <vector>



namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace common
{
namespace lightstate
{

class BlinkingIndicatorSettings;
extern pc::util::coding::Item<BlinkingIndicatorSettings> g_lightSettings;

///
/// SignalWrapper
///
class SignalWrapper
{
public:

  static constexpr vfc::uint32_t INVALID_VALUE = std::numeric_limits<vfc::uint32_t>::max();

  SignalWrapper(vfc::uint32_t f_value = INVALID_VALUE);

  virtual ~SignalWrapper() = default;

  vfc::uint32_t getValue() const
  {
    return m_value;
  }

  bool update(pc::core::Framework* f_framework);

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const = 0;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const = 0;

private:

  vfc::uint32_t m_value;
};


template<class Map>
vfc::uint32_t findOrInvalid(const Map& f_map, const std::string& f_name)
{
  const auto l_res = f_map.find(f_name);
  if (l_res != f_map.end())
  {
    return static_cast<vfc::uint32_t> (l_res->second);
  }
  return SignalWrapper::INVALID_VALUE;
}


///
/// HeadlightWrapper
///
class HeadlightWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    None      = 0,
    HighBeam  = 1,
    LowBeam   = 2,
    Both      = 3
  };

  HeadlightWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;
};

///
/// DayWrapper
///
class DayWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    Off      = 0,
    On       = 1,
    DayLight_L = 2,
    DayLight_R = 3
  };

  DayWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;
};

///
/// RearPosWrapper
///
class RearPosWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    Off      = 0,
    On       = 1
  };

  RearPosWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;
};


///
/// BrakeLightWrapper
///
class BrakeLightWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    Off = 0,
    On  = 1
  };

  BrakeLightWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;

};


///
/// ReverseGearWrapper
///
class ReverseGearWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    Off = 0,
    On  = 1
  };

  ReverseGearWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;

};


///
/// IndicatorWrapper
///
class IndicatorWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    Idle  = 0,
    Left  = 1,
    Right = 2,
    Warn  = 3
  };

  IndicatorWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;

};


///
/// SideIndicatorWrapper
///
class SideIndicatorWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    Idle  = 0,
    Left  = 1,
    Right = 2,
    Warn  = 3
  };

  SideIndicatorWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;

};

///
/// SmallLightWrapper
///
class SmallLightWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    Off = 0,
    On  = 1
  };

  SmallLightWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;
};

///
/// FogLightWrapper
///
class FogLightWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    Off = 0,
    On  = 1
  };

  FogLightWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;
};


///
/// FrontClearanceLampWrapper
///
class FrontClearanceLampWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    Off = 0,
    On  = 1
  };

  FrontClearanceLampWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;
};


///
/// RearClearanceLampWrapper
///
class RearClearanceLampWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    Off = 0,
    On  = 1
  };

  RearClearanceLampWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;
};



///
/// FrontCornerLeftLampWrapper
///
class FrontCornerLeftLampWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    Off = 0,
    On  = 1
  };

  FrontCornerLeftLampWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;
};


///
/// FrontCornerRightLampWrapper
///
class FrontCornerRightLampWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    Off = 0,
    On  = 1
  };

  FrontCornerRightLampWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;
};

///
/// FrontPosWrapper
///
class FrontPosWrapper : public SignalWrapper
{
public:

  enum class State : vfc::uint32_t
  {
    Off = 0,
    On  = 1
  };

  FrontPosWrapper() = default;

  virtual vfc::uint32_t toValue(const std::string& f_stringValue) const override;

protected:

  virtual vfc::uint32_t querySignal(pc::core::Framework* f_framework) const override;
};


///
/// SignalWrapperRegistry
///
class SignalWrapperRegistry
{
public:

  explicit SignalWrapperRegistry(pc::core::Framework* f_framework);

  bool update();

  void registerSignal(const std::string& f_name, const std::shared_ptr<SignalWrapper>& f_singalWrapper);

  std::shared_ptr<SignalWrapper> getSignalWrapper(const std::string& f_name) const;

private:

  // Private copy constructor and assignment operator
  SignalWrapperRegistry(const SignalWrapperRegistry& f_other) = delete;
  SignalWrapperRegistry& operator=(const SignalWrapperRegistry& f_other) = delete;

  pc::core::Framework* m_framework;
  std::map< std::string, std::shared_ptr<SignalWrapper> > m_signalWrappers;
};


///
/// SignalReq
///
class SignalReq
{
public:

  SignalReq(
    const std::shared_ptr<SignalWrapper>& f_signal,
    vfc::uint32_t f_referenceValue,
    bool f_not);

  bool check() const;

private:

  // Private copy constructor and assignment operator
  SignalReq(const SignalReq& f_other) = delete;
  SignalReq& operator=(const SignalReq& f_other) = delete;

private:

  std::shared_ptr<SignalWrapper> m_signal;
  const vfc::uint32_t m_referenceValue;
  const bool m_not;

};


///
/// Condition
///
class Condition
{
public:

  Condition() = default;

  void addSignalReq(const std::shared_ptr<SignalReq>& f_item);

  bool evaluate() const;

private:

  // Private copy constructor and assignment operator
  Condition(const Condition& f_other) = delete;
  Condition& operator=(const Condition& f_other) = delete;

private:

  std::vector< std::shared_ptr<SignalReq> > m_signalReqs;
};


///
/// LightNode
///
class LightNode
{
public:

  LightNode(osg::Node* f_lightNode);

  void addCondition(const std::shared_ptr<Condition>& f_condition);

  void update();

private:

  // Private copy constructor and assignment operator
  LightNode(const LightNode& f_other);
  LightNode& operator=(const LightNode& f_other);

private:

  osg::ref_ptr<osg::Node> m_lightNode;
  std::vector< std::shared_ptr<Condition> > m_conditions;

};


///
/// LightNodeUpdateCallback
///
class LightNodeUpdateCallback : public osg::NodeCallback
{
public:

  LightNodeUpdateCallback(const std::shared_ptr<SignalWrapperRegistry>& f_registry);

  virtual void operator() (osg::Node* f_node, osg::NodeVisitor* f_nv) override;

  void addLightNode(const std::shared_ptr<LightNode>& f_lightNode);

  const SignalWrapperRegistry* getRegistry() const
  {
    return m_registry.get();
  }

protected:

  virtual ~LightNodeUpdateCallback() = default;

private:

  // Private copy constructor and assignment operator
  LightNodeUpdateCallback(const LightNodeUpdateCallback& f_other) = delete;
  LightNodeUpdateCallback& operator=(const LightNodeUpdateCallback& f_other) = delete;

private:

  std::shared_ptr<SignalWrapperRegistry> m_registry;
  std::vector< std::shared_ptr<LightNode> > m_lightNodes;

};


///
/// LightStateJsonParser
///
class LightStateJsonParser
{
public:

  explicit LightStateJsonParser(const std::shared_ptr<SignalWrapperRegistry>& f_registry);

  LightNodeUpdateCallback* parse(const std::string& f_filename, osg::Group* f_sceneGraph);

  virtual void onHint(const std::string& f_hint, osg::Node* f_node);

private:

  std::shared_ptr<SignalWrapperRegistry> m_registry;

};

} // namespace lightstate
} // namespace common
} // namespace assets
} // namespace cc

#endif