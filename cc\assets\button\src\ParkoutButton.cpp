//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/button/inc/ParkoutButton.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/imgui/inc/imgui_manager.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"

#include "cc/imgui/inc/imgui_manager.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace button
{
namespace parkout
{

pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutFrontLeftCrossSettings("ParkoutFrontLeftCrossSettings");
pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutFrontLeftParallelSettings("ParkoutFrontLeftParallelSettings");
pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutFrontOutSettings("ParkoutFrontOutSettings");
pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutFrontRightCrossSettings("ParkoutFrontRightCrossSettings");
pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutFrontRightParallelSettings("ParkoutFrontRightParallelSettings");
pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutBackOutSettings("ParkoutBackOutSettings");
pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutBackLeftCrossSettings("ParkoutBackLeftCrossSettings");
pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutBackRightCrossSettings("ParkoutBackRightCrossSettings");

//!
//! ParkoutButtonGroup
//!
ParkoutButtonGroup::ParkoutButtonGroup(
    cc::core::AssetId    f_assetId,
    pc::core::View*      f_referenceView,
    pc::core::Framework* f_framework)
    : ButtonGroup(f_assetId)
    , m_framework(f_framework)
{
    const auto l_ParkoutFrontLeftCrossButton = new ParkoutButton(
        f_assetId,
        f_framework,
        g_ParkoutFrontLeftCrossSettings.get(),
        cc::target::common::EPocDirSel::FrontLeftCross,
        "ParkoutFrontLeftCrossButton",
        f_referenceView);
    const auto l_ParkoutFrontLeftParallelButton = new ParkoutButton(
        f_assetId,
        f_framework,
        g_ParkoutFrontLeftParallelSettings.get(),
        cc::target::common::EPocDirSel::FrontLeftParallel,
        "ParkoutFrontLeftParallelButton",
        f_referenceView);
    const auto l_ParkoutFrontOutButton = new ParkoutButton(
        f_assetId,
        f_framework,
        g_ParkoutFrontOutSettings.get(),
        cc::target::common::EPocDirSel::FrontOut,
        "ParkoutFrontOutButton",
        f_referenceView);
    const auto l_ParkoutFrontRightCrossButton = new ParkoutButton(
        f_assetId,
        f_framework,
        g_ParkoutFrontRightCrossSettings.get(),
        cc::target::common::EPocDirSel::FrontRightCross,
        "ParkoutFrontRightCrossButton",
        f_referenceView);
    const auto l_ParkoutFrontRightParallelButton = new ParkoutButton(
        f_assetId,
        f_framework,
        g_ParkoutFrontRightParallelSettings.get(),
        cc::target::common::EPocDirSel::FrontRightParallel,
        "ParkoutFrontRightParallelButton",
        f_referenceView);
    const auto l_ParkoutBackOutButton = new ParkoutButton(
        f_assetId,
        f_framework,
        g_ParkoutBackOutSettings.get(),
        cc::target::common::EPocDirSel::BackOut,
        "ParkoutBackOutButton",
        f_referenceView);
    const auto l_ParkoutBackLeftCrossButton = new ParkoutButton(
        f_assetId,
        f_framework,
        g_ParkoutBackLeftCrossSettings.get(),
        cc::target::common::EPocDirSel::BackLeftCross,
        "ParkoutBackLeftCrossButton",
        f_referenceView);
    const auto l_ParkoutBackRightCrossButton = new ParkoutButton(
        f_assetId,
        f_framework,
        g_ParkoutBackRightCrossSettings.get(),
        cc::target::common::EPocDirSel::BackRightCross,
        "ParkoutBackRightCrossButton",
        f_referenceView);

    addButton(l_ParkoutFrontLeftCrossButton);
    addButton(l_ParkoutFrontLeftParallelButton);
    addButton(l_ParkoutFrontOutButton);
    addButton(l_ParkoutFrontRightCrossButton);
    addButton(l_ParkoutFrontRightParallelButton);
    addButton(l_ParkoutBackOutButton);
    addButton(l_ParkoutBackLeftCrossButton);
    addButton(l_ParkoutBackRightCrossButton);

    l_ParkoutFrontLeftCrossButton->setHoriReferenceView(f_referenceView);
    l_ParkoutFrontLeftParallelButton->setHoriReferenceView(f_referenceView);
    l_ParkoutFrontOutButton->setHoriReferenceView(f_referenceView);
    l_ParkoutFrontRightCrossButton->setHoriReferenceView(f_referenceView);
    l_ParkoutFrontRightParallelButton->setHoriReferenceView(f_referenceView);
    l_ParkoutBackOutButton->setHoriReferenceView(f_referenceView);
    l_ParkoutBackLeftCrossButton->setHoriReferenceView(f_referenceView);
    l_ParkoutBackRightCrossButton->setHoriReferenceView(f_referenceView);

    l_ParkoutFrontLeftCrossButton->setVertReferenceView(f_referenceView);
    l_ParkoutFrontLeftParallelButton->setVertReferenceView(f_referenceView);
    l_ParkoutFrontOutButton->setVertReferenceView(f_referenceView);
    l_ParkoutFrontRightCrossButton->setVertReferenceView(f_referenceView);
    l_ParkoutFrontRightParallelButton->setVertReferenceView(f_referenceView);
    l_ParkoutBackOutButton->setVertReferenceView(f_referenceView);
    l_ParkoutBackLeftCrossButton->setVertReferenceView(f_referenceView);
    l_ParkoutBackRightCrossButton->setVertReferenceView(f_referenceView);
}

void ParkoutButtonGroup::update()
{
    m_enabled = true;
}

//!
//! ParkoutButton
//!
ParkoutButton::ParkoutButton(
    cc::core::AssetId              f_assetId,
    pc::core::Framework*           f_framework,
    const ParkoutButtonSettings*   f_settings,
    cc::target::common::EPocDirSel f_parkoutDirection,
    const std::string&             f_name,
    osg::Camera*                   f_referenceView)
    : Button(f_assetId, f_referenceView) // PRQA S 2759 // PRQA S 2323
    , m_framework(f_framework) // PRQA S 2323
    , m_settings(f_settings) // PRQA S 2323
    , m_parkoutDirection(f_parkoutDirection) // PRQA S 2323
{
    osg::Object::setName(f_name);
    setIconAtMiddle(false);
    setPositionHori(m_settings->m_horiPos);
    setPositionVert(m_settings->m_vertPos);
    setTexturePath(CONCAT_PATH_VEHICLE_MODEL(m_settings->m_textures.m_night.m_AvailableTexturePath));
    Button::init(); // PRQA S 2759
}

void ParkoutButton::onInvalid()
{
    setIconEnable(false);
}

void ParkoutButton::onUnavailable()
{
    setIconEnable(true);
    const auto l_path_ = CONCAT_PATH_VEHICLE_MODEL(m_settings->m_textures.m_night.m_UnavailableTexturePath);
    XLOG_INFO(g_AppContext, "[svs]: m_night.onUnavailable"<< l_path_);
    if (isDayTheme())
    {
        setTexturePath(CONCAT_PATH_VEHICLE_MODEL(m_settings->m_textures.m_day.m_UnavailableTexturePath));
    }
    if (isNightTheme())
    {
        setTexturePath(CONCAT_PATH_VEHICLE_MODEL(m_settings->m_textures.m_night.m_UnavailableTexturePath));
    }
}

void ParkoutButton::onAvailable()
{
    const auto l_path_ = CONCAT_PATH_VEHICLE_MODEL(m_settings->m_textures.m_night.m_AvailableTexturePath);
    XLOG_INFO(g_AppContext, "[svs]: m_night.m_AvailableTexturePath"<< l_path_);
    setIconEnable(true);
    if (isDayTheme())
    {
        setTexturePath(CONCAT_PATH_VEHICLE_MODEL(m_settings->m_textures.m_day.m_AvailableTexturePath));
    }
    if (isNightTheme())
    {
        setTexturePath(CONCAT_PATH_VEHICLE_MODEL(m_settings->m_textures.m_night.m_AvailableTexturePath));
    }
}

void ParkoutButton::onPressed()
{
    const auto l_path_ = CONCAT_PATH_VEHICLE_MODEL(m_settings->m_textures.m_night.m_PressedTexturePath);
    XLOG_INFO(g_AppContext, "[svs]: m_night.m_PressedTexturePath"<< l_path_);
    setIconEnable(true);
    if (isDayTheme())
    {
        setTexturePath(CONCAT_PATH_VEHICLE_MODEL(m_settings->m_textures.m_day.m_PressedTexturePath));
    }
    if (isNightTheme())
    {
        setTexturePath(CONCAT_PATH_VEHICLE_MODEL(m_settings->m_textures.m_night.m_PressedTexturePath));
    }
}

void ParkoutButton::onReleased()
{
    if (checkTouchInsideResponseArea())
    {
        IMGUI_LOG("Buttons", "ParkoutButton", static_cast<int>(m_parkoutDirection));
        if (cc::daddy::CustomDaddyPorts::sm_ParkoutSelectedDirection_SenderPort.isConnected())
        {
            auto& container  = cc::daddy::CustomDaddyPorts::sm_ParkoutSelectedDirection_SenderPort.reserve();
            container.m_Data = m_parkoutDirection;
            cc::daddy::CustomDaddyPorts::sm_ParkoutSelectedDirection_SenderPort.deliver();
        }
    }
}

void ParkoutButton::update()
{
    using namespace cc::target::common;
    bool allPortsHaveData = true;

    // clang-format off
    GET_PORT_DATA(touchStatusContainer, m_framework->asCustomFramework()->m_HUTouchTypeReceiver, allPortsHaveData)
    GET_PORT_DATA(huRotateStatusContainer, m_framework->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver, allPortsHaveData)
    GET_PORT_DATA(hmiDataContainer, m_framework->asCustomFramework()->m_hmiDataReceiver, allPortsHaveData)
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, allPortsHaveData)
    GET_PORT_DATA(parkHmiContainer, m_framework->asCustomFramework()->m_ParkhmiToSvs_ReceiverPort, allPortsHaveData)
    // clang-format on

    if (!allPortsHaveData)
    {
        return;
    }

    m_apaStatus    = parkHmiContainer->m_Data.m_apaStatus;
    m_parkingStage = parkHmiContainer->m_Data.m_parkingStage;
    m_parkMode     = parkHmiContainer->m_Data.m_parkMode;

    const auto parkDir = parkHmiContainer->m_Data.m_pocEnabledDir;
    const auto selectedDirection = parkHmiContainer->m_Data.m_pocRecommandDir;
    // clang-format off
    switch (m_parkoutDirection)
    {
        case EPocDirSel::FrontLeftCross:
        {
            m_enable = parkDir.m_FrntLeCrossSts;
            break;
        }
        case EPocDirSel::FrontLeftParallel:
        {
            m_enable = parkDir.m_FrntLeParallelSts;
            break;
        }
        case EPocDirSel::FrontOut:
        {
            m_enable = parkDir.m_FrntCrossSts;
            break;
        }
        case EPocDirSel::FrontRightCross:
        {
            m_enable = parkDir.m_FrntRiCrossSts;
            break;
        }
        case EPocDirSel::FrontRightParallel:
        {
            m_enable = parkDir.m_FrntRiParallelSts;
            break;
        }
        case EPocDirSel::BackOut:
        {
            m_enable = parkDir.m_BackCrossSts;
            break;
        }
        case EPocDirSel::BackLeftCross:
        {
            m_enable = parkDir.m_BackLeCrossSts;
            break;
        }
        case EPocDirSel::BackRightCross:
        {
            m_enable = parkDir.m_BackRiCrossSts;
            break;
        }
        default:
        {
            m_enable = false;
            break;
        }
    }
    // clang-format on
    // XLOG_INFO(g_AppContext, "[ParkoutButton]: m_enable"<< static_cast<vfc::int32_t>(m_parkoutDirection));

    const bool touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
    setDayNightTheme(dayNightThemeContainer->m_Data);
    setRotateTheme(static_cast<cc::target::common::EThemeTypeHU>(huRotateStatusContainer->m_Data));
    setPositionHori(m_settings->m_horiPos);
    setPositionVert(m_settings->m_vertPos);
    setSize(m_settings->m_size);
    setHuX(hmiDataContainer->m_Data.m_huX);
    setHuY(hmiDataContainer->m_Data.m_huY);
    setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));

    setSettingModifiedCount(m_settings->getModifiedCount());

    const auto previousState = getState();

    ButtonState currentState = ButtonState::INVALID;
    if (m_parkingStage == EParkingStage::ParkOut && m_apaStatus == EApaStatus::ParkAssistStandby && m_enable)
    {
        currentState = ButtonState::AVAILABLE;
        XLOG_INFO(g_AppContext, "[ParkoutButton]: m_enable"<< static_cast<vfc::int32_t>(m_parkoutDirection));
    }

    if (IMGUI_GET_CHECKBOX_BOOL("ParkoutButton", getName().c_str()))
    {
        currentState = ButtonState::AVAILABLE;
    }

    if (currentState == ButtonState::AVAILABLE && m_parkoutDirection == selectedDirection)
    {
        currentState = ButtonState::PRESSED;
    }

    IMGUI_LOG("ParkoutButton", (getName() + "CurrentState").c_str(),
        currentState == ButtonState::INVALID ? "INVALID" :
        currentState == ButtonState::UNAVAILABLE ? "UNAVAILABLE" :
        currentState == ButtonState::AVAILABLE ? "AVAILABLE" :
        currentState == ButtonState::PRESSED ? "PRESSED" :
        currentState == ButtonState::RELEASED ? "RELEASED" : "UNKNOWN");

    IMGUI_LOG("ParkoutButton", (getName() + "Position").c_str(),
        std::to_string(m_positionHori.x()) + " " + std::to_string(m_positionHori.y()));

    if (currentState == ButtonState::INVALID)
    {
        setState(ButtonState::INVALID);
        return;
    }

    const bool touchInsideResponseArea = checkTouchInsideResponseArea();
//    const bool userDragging            = (touchStatus() == TOUCH_DOWN || touchStatus() == TOUCH_MOVE);
    const bool userFinishDragging      = touchStatus() == TOUCH_UP && touchStatusChanged;
    // if (touchInsideResponseArea && userDragging && currentState == AVAILABLE)
    // {
    //     currentState = PRESSED;
    // }
    if (touchInsideResponseArea && userFinishDragging && (currentState != PRESSED))
    {
        currentState = RELEASED;
    }
    if (previousState != currentState)
    {
        setState(currentState);
    }
}

} // namespace parkout
} // namespace button
} // namespace assets
} // namespace cc
