//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: NVA2HC NGUYEN DUC THIEN Van (CN/ESC-EPA1)
//  Department: CN/ESC
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ECALprogressoverlay.cpp
/// @brief
//=============================================================================

#include "cc/assets/ECALprogressoverlay/inc/ECALprogressoverlay.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "cc/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "CustomSystemConf.h"



using cc::util::logging::g_CpcContext;



namespace cc
{
namespace assets
{
namespace ECALprogressoverlay
{
static pc::util::coding::Item<ECALProgressOverlayCodingParams> g_ecalSettings("ECALProgressOverlay");


//!
//! @brief Construct a new ECALprogressoverlay:: ECALprogressoverlay object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
ECALprogressoverlay::ECALprogressoverlay(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays(f_assetId , nullptr) // PRQA S 2759 // PRQA S 2323
  , m_lastConfigUpdate(~0u) // PRQA S 2323
  , m_sequenceNumber(~0u) // PRQA S 2323
  , m_ECALprogressoverlayIcons() // PRQA S 2323
  , m_customFramework(f_customFramework) // PRQA S 2323
{
  init();
  CustomSetRenderOrder(1000u); // PRQA S 2759
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}


ECALprogressoverlay::~ECALprogressoverlay() = default;


void ECALprogressoverlay::traverse(osg::NodeVisitor& f_nv)
{

  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    const auto view = getReferenceView();
    if (view == nullptr)
    {
      const auto scene = m_customFramework->getScene();
      const auto refView = scene->getView("CpcView");
      setReferenceView(refView);
    }
    update();
  }

  pc::assets::ImageOverlays::traverse(f_nv);
}


void ECALprogressoverlay::init()
{
  const osg::Vec2 l_textboxPos  = {50.0f, 50.0f}; // 50 Percentage
  const osg::Vec2f l_textureSize = {cc::core::g_views->m_cpcViewport.m_size.x()/2.f, cc::core::g_views->m_cpcViewport.m_size.y()/2.f}; // PRQA S 3011
  m_ECALprogressoverlayIcons.addIcon(this,
  createIcon(CONCAT_PATH(g_ecalSettings->m_texturePathRedMask),getPositionByCameraId(cc::cpc::CAMERA_REAR),l_textureSize)); // REAR_FAILED
  m_ECALprogressoverlayIcons.addIcon(this,
  createIcon(CONCAT_PATH(g_ecalSettings->m_texturePathGreenMask), getPositionByCameraId(cc::cpc::CAMERA_REAR),l_textureSize)); // REAR_PASSED
  m_ECALprogressoverlayIcons.addIcon(this,
  createIcon(CONCAT_PATH(g_ecalSettings->m_texturePathRedMask),   getPositionByCameraId(cc::cpc::CAMERA_LEFT),l_textureSize)); // LEFT_FAILED
  m_ECALprogressoverlayIcons.addIcon(this,
  createIcon(CONCAT_PATH(g_ecalSettings->m_texturePathGreenMask), getPositionByCameraId(cc::cpc::CAMERA_LEFT),l_textureSize)); // LEFT_PASSED
  m_ECALprogressoverlayIcons.addIcon(this,
  createIcon(CONCAT_PATH(g_ecalSettings->m_texturePathRedMask),   getPositionByCameraId(cc::cpc::CAMERA_FRONT),l_textureSize)); // FRONT_FAILED
  m_ECALprogressoverlayIcons.addIcon(this,
  createIcon(CONCAT_PATH(g_ecalSettings->m_texturePathGreenMask), getPositionByCameraId(cc::cpc::CAMERA_FRONT),l_textureSize)); // FRONT_PASSED
  m_ECALprogressoverlayIcons.addIcon(this,
  createIcon(CONCAT_PATH(g_ecalSettings->m_texturePathRedMask),   getPositionByCameraId(cc::cpc::CAMERA_RIGHT),l_textureSize)); // RIGHT_FAILED
  m_ECALprogressoverlayIcons.addIcon(this,
  createIcon(CONCAT_PATH(g_ecalSettings->m_texturePathGreenMask), getPositionByCameraId(cc::cpc::CAMERA_RIGHT),l_textureSize)); // RIGHT_PASSED
  m_ECALprogressoverlayIcons.addIcon(this, createIcon(CONCAT_PATH(g_ecalSettings->m_texturePathCalibrationInProgress), l_textboxPos)); // CALIBRATION_IN_PROGRESS
  m_ECALprogressoverlayIcons.addIcon(this, createIcon(CONCAT_PATH(g_ecalSettings->m_texturePathCalibrationPassed), l_textboxPos)); // CALIBRATION_SUCCESSED
  m_ECALprogressoverlayIcons.addIcon(this, createIcon(CONCAT_PATH(g_ecalSettings->m_texturePathCalibrationFailed), l_textboxPos)); // CALIBRATION_FAILED
  // m_ECALprogressoverlayIcons.addIcon(this, createBtnIcon(g_settings->m_texturePathRecalibrateButton, uielements::g_uiSettings->m_settingRecalibrateButton.m_iconCenter));// RECALIBRATE_BUTTON
  // m_ECALprogressoverlayIcons.addIcon(this, createBtnIcon(g_settings->m_texturePathCancelButton, uielements::g_uiSettings->m_settingCancelButton.m_iconCenter)); // CANCEL_BUTTON
}


void ECALprogressoverlay::update()
{
  if (g_ecalSettings->getModifiedCount() != m_lastConfigUpdate)
  {
    init();
    m_lastConfigUpdate = g_ecalSettings->getModifiedCount();
  }

  // if (!m_customFramework->m_svsOverlayToCpc_ReceiverPort.hasData()) { XLOG_ERROR_OS(g_CpcContext) << "ECALprogressoverlay::update:  m_svsOverlayToCpc_ReceiverPort has no data"   << XLOG_ENDL; return; }
  if (!m_customFramework->m_cpcToSvsOverlay_ReceiverPort.hasData()) { return; }
  // if (!m_customFramework->m_CpcOverlaySwitch_Receiver.hasData())    { return; }


  // const auto l_svsToCpcContainer = m_customFramework->m_svsOverlayToCpc_ReceiverPort.getData();
  // auto l_svsToCpcData = l_svsToCpcContainer->m_Data;
  // if (l_svsToCpcData.m_isCancelButtonPressed || l_svsToCpcData.m_isRecalibrateButtonPressed)
  //         {
  //   // CPC need some time to trigger its runnable
  //   // We need to manually clear the result and set calibration status to in progress
  //   m_ECALprogressoverlayIcons.setAllEnabled(false);
  //   m_ECALprogressoverlayIcons.getIcon(CALIBRATION_IN_PROGRESS)->setEnabled(l_svsToCpcData.m_isCancelButtonPressed);
  //   return;
  // }

  // const auto& l_cpcOverlaySwitch = m_customFramework->m_CpcOverlaySwitch_Receiver.getData()->m_Data;
  // if (!l_cpcOverlaySwitch)
  // {
  //   XLOG_DEBUG_OS(g_CpcContext) << "ECALprogressoverlay::update() - CPC is not triggered" << XLOG_ENDL;
  //   return;
  // }

  const auto l_cpcToSvsContainer = m_customFramework->m_cpcToSvsOverlay_ReceiverPort.getData();
  if (m_sequenceNumber == l_cpcToSvsContainer->m_sequenceNumber)
  {
    // no new data, keep previous state
    return;
  }
  else
  {
    m_sequenceNumber = l_cpcToSvsContainer->m_sequenceNumber;
  }

  const auto& l_cpcToSvs = l_cpcToSvsContainer->m_Data;

  m_ECALprogressoverlayIcons.setAllEnabled(false);

//   vfc::uint32_t l_numCalibPassed = 0u;
//   vfc::uint32_t l_numCalibFailed = 0u;
//   vfc::uint32_t l_numCameraCalibrated = 0u;

//   for (vfc::uint32_t i = 0u; i < cc::cpc::NUMBER_OF_CAMERAS; ++i)
//   {
//     if (l_cpcToSvs.m_cameras[i].m_isCpcDone)
//     {
//       l_numCameraCalibrated++;
//       bool l_passed = false;

//       if (l_cpcToSvs.m_cameras[i].m_cpcResult == cc::cpc::CpcResult::OK)
//       {
//         l_numCalibPassed++;
//         l_passed = true;
//       }
//       else
//       {
//         l_numCalibFailed++;
//         l_passed = false;
//       }
//       m_ECALprogressoverlayIcons.getIcon(i * 2u + 1u)->setEnabled(l_passed);
//       m_ECALprogressoverlayIcons.getIcon(i * 2u)->setEnabled(!l_passed);
//     }
//   }

//   bool l_calibrationPassed = l_numCalibPassed == cc::cpc::NUMBER_OF_CAMERAS;
//   bool l_calibrationFailed = (l_numCalibFailed > 0u) && (l_numCameraCalibrated == cc::cpc::NUMBER_OF_CAMERAS);
//   bool l_calibrationInProgress = !l_calibrationPassed && !l_calibrationFailed;

  for( int i = 0; i < cc::cpc::NUMBER_OF_CAMERAS; ++i ) // PRQA S 2427
  {
    if( l_cpcToSvs.m_cameras[i].isDone() ) // PRQA S 3000
    {
      const bool ok = l_cpcToSvs.m_cameras[i].isGood(); // PRQA S 3000

      m_ECALprogressoverlayIcons.getIcon( i * 2 + 1 )->setEnabled( ok ); // PRQA S 3000
      m_ECALprogressoverlayIcons.getIcon( i * 2 )->setEnabled( !ok ); // PRQA S 3000
    }
  }

  const bool l_calibrationInProgress = !l_cpcToSvs.isDone();
  const bool l_calibrationPassed     = !l_calibrationInProgress &&  l_cpcToSvs.isGood();
  const bool l_calibrationFailed     = !l_calibrationInProgress && !l_calibrationPassed;

  m_ECALprogressoverlayIcons.getIcon(CALIBRATION_SUCCESSED)->setEnabled(l_calibrationPassed);
  m_ECALprogressoverlayIcons.getIcon(CALIBRATION_FAILED)->setEnabled(l_calibrationFailed);
  m_ECALprogressoverlayIcons.getIcon(CALIBRATION_IN_PROGRESS)->setEnabled(l_calibrationInProgress);
  // m_ECALprogressoverlayIcons.getIcon(RECALIBRATE_BUTTON)->setEnabled(l_calibrationPassed || l_calibrationFailed);
  // m_ECALprogressoverlayIcons.getIcon(CANCEL_BUTTON)->setEnabled(l_calibrationPassed || l_calibrationFailed);
}

// ! create icon utils
pc::assets::Icon* ECALprogressoverlay::createIcon(const std::string& f_iconPath, const osg::Vec2& f_iconPos, const osg::Vec2f& f_iconSize)
{
  pc::assets::Icon* const l_icon = new pc::assets::Icon(f_iconPath, true);
  l_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Percentage);
  l_icon->setSize(f_iconSize, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  return l_icon;
}

pc::assets::Icon* ECALprogressoverlay::createIcon(const std::string& f_iconPath, const osg::Vec2& f_iconPos)
{
  pc::assets::Icon* const l_icon = new pc::assets::Icon(f_iconPath, true);
  auto* const l_image = l_icon->getTexture()->getImage();
  l_icon->setSize(osg::Vec2(l_image->s(), l_image->t()), pc::assets::Icon::UnitType::Pixel); // PRQA S 3011
  l_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Percentage);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  return l_icon;
}

pc::assets::Icon* ECALprogressoverlay::createBtnIcon(const std::string& f_iconPath, const osg::Vec2& f_iconPos)
{
  pc::assets::Icon* const l_icon = new pc::assets::Icon(f_iconPath, true);
  auto* const l_image = l_icon->getTexture()->getImage();
  l_icon->setSize(osg::Vec2(l_image->s(), l_image->t()), pc::assets::Icon::UnitType::Pixel); // PRQA S 3011
  l_icon->setOrigin(pc::assets::Icon::Origin::TopLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  return l_icon;
}

// ! get position, origin is bottom left, position in percentage
osg::Vec2 ECALprogressoverlay::getPosition(CameraPosition f_position)
{
  switch (f_position)
  {
    case CameraPosition::TOP_LEFT:
    {
      return osg::Vec2{25.0f, 75.0f};
    }
    case CameraPosition::TOP_RIGHT:
    {
      return osg::Vec2{75.0f, 75.0f};
    }
    case CameraPosition::BOTTOM_LEFT:
    {
      return osg::Vec2{25.0f, 25.0f};
    }
    case CameraPosition::BOTTOM_RIGHT:
    {
      return osg::Vec2{75.0f, 25.0f};
    }
    default:
    {
      return osg::Vec2{25.0f, 75.0f};
    }
  }
}

// ! get camera position
osg::Vec2 ECALprogressoverlay::getPositionByCameraId(cc::cpc::CameraIndex f_camera)
{
  switch (f_camera)
  {
    case cc::cpc::CAMERA_FRONT:
    {
      return getPosition(CameraPosition::TOP_LEFT);
    }
    case cc::cpc::CAMERA_REAR:
    {
      return getPosition(CameraPosition::TOP_RIGHT);
    }
    case cc::cpc::CAMERA_LEFT:
    {
      return getPosition(CameraPosition::BOTTOM_LEFT);
    }
    case cc::cpc::CAMERA_RIGHT:
    {
      return getPosition(CameraPosition::BOTTOM_RIGHT);
    }
    default:
    {
      return getPosition(CameraPosition::TOP_LEFT);
    }
  }
}


} // namespace ECALprogressoverlay
} // namespace assets
} // namespace cc
