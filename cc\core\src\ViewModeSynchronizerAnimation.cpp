//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

// #include "ViewModeStateTransitionManager.h"
// #include "ViewModeToggle.h"

#include "cc/core/inc/ViewModeSynchronizerAnimation.h"

// #include "pc/svs/animation/inc/SerialAnimation.h"
// #include "pc/svs/animation/inc/ViewportAnimation.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"

#include "cc/core/src/ViewModeToggle.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"
#include "cc/assets/uielements/inc/Vehicle2DOverlay.h"
#include "cc/views/planview/inc/PlanViewEnlargeCallback.h"
#include "cc/assets/uielements/inc/WheelSeparator.h"
#include "cc/imgui/inc/imgui_manager.h"

#include <osg/NodeVisitor>

using cc::util::logging::g_viewModeSMContext;
using pc::util::logging::g_AppContext;

#define CHECK_PORT_DATA(containerName, port, flag)                                                                     \
    auto const containerName = port.getData();                                                                            \
    if (containerName == nullptr)                                                                                    \
    {                                                                                                                  \
        flag = false;                                                                                                \
    }

#define GET_PORT_DATA(dataDaddy, port, allPortHaveDataFlag)                                                            \
    const auto dataDaddy  = port.getData();  /* PRQA S 1030 */                                                       \
    (allPortHaveDataFlag) = (allPortHaveDataFlag) && (dataDaddy != nullptr);                                         \
    if (dataDaddy == nullptr)                                                                                        \
    {                                                                                                                  \
        XLOG_ERROR(g_viewModeSMContext, #port << " doesn't have data!\n");                                             \
    }

namespace cc
{
namespace core
{

//!
//! class ViewModeSynchronizerAnimation : public pc::animation::Action
//!
ViewModeSynchronizerAnimation::ViewModeSynchronizerAnimation(CustomFramework* f_framework)
    : m_framework(f_framework)
    , m_name("ViewModeSynchronizerAnimation")
{
    const auto mainLogic = cc::assets::trajectory::GeneralTrajectoryLine::getMainLogicPtr();
    if (mainLogic == nullptr)
    {
        // XLOG_ERROR(g_AppContext, "DebugGear - ViewModeSynchronizerAnimation - ViewModeSynchronizerAnimation(): mainLogic is nullptr");
        return;
    }
    // XLOG_ERROR(g_AppContext, "DebugGear - ViewModeSynchronizerAnimation - ViewModeSynchronizerAnimation(): mainLogic->disableUpdateGear()");
    // mainLogic->disableUpdateGear();
}

void ViewModeSynchronizerAnimation::onAction()
{
    // updateTrajectories();
    updateWheelSeparator();
}

void ViewModeSynchronizerAnimation::updateWheelSeparator()
{
    const auto wheelSeparator = dynamic_cast<cc::assets::uielements::wheelseparatorhorizontal::WheelSeparatorHorizontal*>(m_framework->getScene()->getView(cc::core::CustomViews::EView::WHEEL_SEPARATOR_HORIZONTAL_VIEW)->getAsset(AssetId::EASSETS_UI_WHEELSEPARATOR_HORIZONTAL)->getAsset());

    if (wheelSeparator == nullptr)
    {
        return;
    }

    wheelSeparator->getManager().update(wheelSeparator, m_framework);

}

// void ViewModeSynchronizerAnimation::updateTrajectories()
// {
//     const auto mainLogic = cc::assets::trajectory::GeneralTrajectoryLine::getMainLogicPtr();
//     if (!mainLogic)
//     {
//         // XLOG_ERROR(g_AppContext, "DebugGear - ViewModeSynchronizerAnimation - updateTrajectories(): mainLogic is nullptr");
//         return;
//     }

//     if (m_framework->m_gearReceiver.hasData())
//     {
//         auto& l_container = m_framework->m_gearReceiver.getData();
//         const auto  l_gear      = static_cast<pc::daddy::EGear>(l_container->m_Data);
//         std::string gear;
//         switch (l_gear)
//         {
//         case pc::daddy::EGear::GEAR_N: gear = "GEAR_N"; break;
//         case pc::daddy::EGear::GEAR_R: gear = "GEAR_R"; break;
//         case pc::daddy::EGear::GEAR_P: gear = "GEAR_P"; break;
//         case pc::daddy::EGear::GEAR_D: gear = "GEAR_D"; break;
//         default:
//             gear = "INVALID";
//             break;
//         }
//         // XLOG_ERROR(g_AppContext, "DebugGear - ViewModeSynchronizerAnimation - updateTrajectories(): set gear to " << gear);
//         mainLogic->setGear(l_gear);
//     }

// }

//!
//! class VehicleEnlargeAction : public pc::animation::Action
//!
VehicleEnlargeAction::VehicleEnlargeAction(CustomFramework* f_framework, pc::daddy::ViewMode f_mode)
    : m_framework(f_framework)
    , m_mode(f_mode)
    , m_name("VehicleEnlargeAction")
{
}

void VehicleEnlargeAction::onAction()
{
    syncVehicle2DOverlay();
}

void VehicleEnlargeAction::updateVehicle2DEnlargeStatus(pc::core::View* f_view, int f_enlargeStatus)
{
    if (f_view == nullptr)
    {
        return;
    }

    if (f_view->getNodeMask() == 0u)
    {
        // return;
    }

    //! UpdateCallback
    const auto l_enlargeCallback = dynamic_cast<cc::views::planview::PlanViewEnlargeCallback*>(f_view->getUpdateCallback());
    if (l_enlargeCallback == nullptr)
    {
        return;
    }
    if ((isImageInImageView(m_mode.m_curr) && !isImageInImageView(m_mode.m_prev)) ||
        (!isImageInImageView(m_mode.m_curr) && isImageInImageView(m_mode.m_prev)))
    {
        l_enlargeCallback->setEnlargeStatus(static_cast<cc::daddy::PlanViewEnlargeStatus>(f_enlargeStatus), false);
    }
    else
    {
        const auto planViewTypePrevious = checkPlanViewType(m_mode.m_prev);
        const auto planViewTypeCurrent = checkPlanViewType(m_mode.m_curr);
        const auto l_isPreviousEnlarge = isEnlargeFrontRearView(m_mode.m_prev);
        const auto l_isCurrentEnlarge = isEnlargeFrontRearView(m_mode.m_curr);
        const bool isBev = (f_enlargeStatus == cc::daddy::PlanViewEnlargeStatus::ENLARGE_MIDDLE);
        bool l_instantToggle = false;
        if ((planViewTypeCurrent != planViewTypePrevious) && (l_isPreviousEnlarge || l_isCurrentEnlarge))
        {
            l_instantToggle = true;
        }
        else if (planViewTypeCurrent == PlanViewType::FULLSCREEN && planViewTypePrevious == PlanViewType::NORMAL && isBev)
        {
            l_instantToggle = true;
        }
        else if (planViewTypeCurrent == PlanViewType::NORMAL && planViewTypePrevious == PlanViewType::FULLSCREEN && isBev)
        {
            l_instantToggle = true;
        }
        else
        {
        }
        const bool animate = (f_view->getNodeMask() != 0u) && (!l_instantToggle);
        l_enlargeCallback->setEnlargeStatus(static_cast<cc::daddy::PlanViewEnlargeStatus>(f_enlargeStatus), animate || IMGUI_GET_CHECKBOX_BOOL("Settings", "Animate"));
    }

    //! Vehicle2DOverlay
    const auto l_vehicle2dOverlay = dynamic_cast<cc::assets::uielements::Vehicle2DOverlay*>(f_view->getAsset(cc::core::AssetId::EASSETS_VEHICLE_2D_ICON));
    if (l_vehicle2dOverlay == nullptr)
    {
        return;
    }
    const auto l_vehicle2dManager = l_vehicle2dOverlay->getManager();
    if (l_vehicle2dManager == nullptr)
    {
        return;
    }

    l_vehicle2dManager->setEnlargeStatus(static_cast<cc::daddy::PlanViewEnlargeStatus>(f_enlargeStatus));
}


void VehicleEnlargeAction::updatePlanViewTrajectory(pc::core::View* f_view, int f_enlargeStatus)
{
    switch (f_enlargeStatus)
    {
    case cc::daddy::NO_ENLARGE:
    {
        // enable outermostline and disable wheeltrack in normal mode
        if (f_view != nullptr)
        {
            f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES, true);
            f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES_COLORFUL, true);
            f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_WHEEL_TRACKS, false);
        }
        break;
    }
    case cc::daddy::ENLARGE_FRONT:
    case cc::daddy::ENLARGE_REAR:
    {
        // disable outermostline and show wheeltrack in enlarge mode
        if (f_view != nullptr)
        {
            f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES, false);
            f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES_COLORFUL, false);
            f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_WHEEL_TRACKS, true);
        }
    }
    default:
    {
        // enable outermostline and disable wheeltrack in normal mode
        if (f_view != nullptr)
        {
            f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES, true);
            f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES_COLORFUL, true);
            f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_WHEEL_TRACKS, false);
        }
        break;
    }
    }
}

void VehicleEnlargeAction::syncVehicle2DOverlay()
{
    const auto scene = m_framework->getScene();
    if (scene == nullptr)
    {
        // XLOG_INFO_OS(g_viewModeSMContext) << m_name << "::syncVehicle2DOverlay: " << "Scene isn't initialized" << XLOG_ENDL;
        return;
    }
    bool allPortsHaveData = true;
    CHECK_PORT_DATA(enlargeContainer, m_framework->m_PlanViewEnlargeStatusReceiver, allPortsHaveData)
    CHECK_PORT_DATA(bevContainer, m_framework->m_BirdEyeViewSwitch_ReceiverPort, allPortsHaveData)

    if (!allPortsHaveData)
    {
        // XLOG_INFO_OS(g_viewModeSMContext) << m_name << "::syncVehicle2DOverlay: " << "some ports don't have data!" << XLOG_ENDL;
        return;
    }

    auto l_enlargeStatus = enlargeContainer->m_Data;
    if (l_enlargeStatus == cc::daddy::PlanViewEnlargeStatus::NO_ENLARGE && (bevContainer->m_Data == true))
    {
        l_enlargeStatus = cc::daddy::PlanViewEnlargeStatus::ENLARGE_MIDDLE;
    }

    //! Vehicle2D
    {
        const auto l_plan_view_vehicle2d = scene->getView(CustomViews::PLAN_VIEW_VEHICLE2D);
        const auto l_hori_parking_plan_view_vehicle2d = scene->getView(CustomViews::HORI_PARKING_PLAN_VIEW_VEHICLE2D);
        const auto l_remote_plan_view_vehicle2d = scene->getView(CustomViews::REMOTE_PLAN_VIEW_VEHICLE2D);
        const auto l_plan_view_fullscreen_vehicle2d = scene->getView(CustomViews::PLAN_VIEW_FULLSCREEN_VEHICLE2D);
        const auto l_image_in_image_vehicle_2d = scene->getView(CustomViews::IMAGE_IN_IMAGE_VEHICLE_2D);
        updateVehicle2DEnlargeStatus(l_plan_view_vehicle2d, static_cast<int>(l_enlargeStatus));
        updateVehicle2DEnlargeStatus(l_hori_parking_plan_view_vehicle2d, static_cast<int>(l_enlargeStatus));
        updateVehicle2DEnlargeStatus(l_remote_plan_view_vehicle2d, static_cast<int>(l_enlargeStatus));
        updateVehicle2DEnlargeStatus(l_plan_view_fullscreen_vehicle2d, static_cast<int>(l_enlargeStatus));
        updateVehicle2DEnlargeStatus(l_image_in_image_vehicle_2d, static_cast<int>(l_enlargeStatus));

        //! Trajectories
        updatePlanViewTrajectory(l_plan_view_vehicle2d, static_cast<int>(l_enlargeStatus));
        updatePlanViewTrajectory(l_hori_parking_plan_view_vehicle2d, static_cast<int>(l_enlargeStatus));
        updatePlanViewTrajectory(l_remote_plan_view_vehicle2d, static_cast<int>(l_enlargeStatus));
        updatePlanViewTrajectory(l_plan_view_fullscreen_vehicle2d, static_cast<int>(l_enlargeStatus));
        updatePlanViewTrajectory(l_image_in_image_vehicle_2d, static_cast<int>(l_enlargeStatus));
    }

    //! USS
    {
        const auto l_plan_view_vehicle2d = scene->getView(CustomViews::PLAN_VIEW_USS_OVERLAYS);
        const auto l_hori_parking_plan_view_vehicle2d = scene->getView(CustomViews::HORI_PARKING_PLAN_VIEW_USS_OVERLAYS);
        // auto l_remote_plan_view_vehicle2d = scene->getView(CustomViews::REMOTE_PLAN_VIEW);
        const auto l_plan_view_fullscreen_vehicle2d = scene->getView(CustomViews::PLAN_VIEW_FULLSCREEN_USS_OVERLAYS);
        const auto l_image_in_image_vehicle_2d = scene->getView(CustomViews::IMAGE_IN_IMAGE_VEHICLE_2D);
        updateVehicle2DEnlargeStatus(l_plan_view_vehicle2d, static_cast<int>(l_enlargeStatus));
        updateVehicle2DEnlargeStatus(l_hori_parking_plan_view_vehicle2d, static_cast<int>(l_enlargeStatus));
        // updateVehicle2DEnlargeStatus(l_remote_plan_view_vehicle2d, static_cast<int>(l_enlargeStatus));
        updateVehicle2DEnlargeStatus(l_plan_view_fullscreen_vehicle2d, static_cast<int>(l_enlargeStatus));
        updateVehicle2DEnlargeStatus(l_image_in_image_vehicle_2d, static_cast<int>(l_enlargeStatus));
    }

    //! Floor
    {
        const auto l_plan_view_vehicle2d = scene->getView(CustomViews::PLAN_VIEW);
        const auto l_hori_parking_plan_view_vehicle2d = scene->getView(CustomViews::HORI_PARKING_PLAN_VIEW);
        const auto l_remote_plan_view_vehicle2d = scene->getView(CustomViews::REMOTE_PLAN_VIEW);
        const auto l_plan_view_fullscreen_vehicle2d = scene->getView(CustomViews::PLAN_VIEW_FULLSCREEN);
        const auto l_image_in_image_vehicle_2d = scene->getView(CustomViews::IMAGE_IMAGE_PLANVIEW);
        updateVehicle2DEnlargeStatus(l_plan_view_vehicle2d, static_cast<int>(l_enlargeStatus));
        updateVehicle2DEnlargeStatus(l_hori_parking_plan_view_vehicle2d, static_cast<int>(l_enlargeStatus));
        updateVehicle2DEnlargeStatus(l_remote_plan_view_vehicle2d, static_cast<int>(l_enlargeStatus));
        updateVehicle2DEnlargeStatus(l_plan_view_fullscreen_vehicle2d, static_cast<int>(l_enlargeStatus));
        updateVehicle2DEnlargeStatus(l_image_in_image_vehicle_2d, static_cast<int>(l_enlargeStatus));
    }
}

} // namespace core
} // namespace cc
