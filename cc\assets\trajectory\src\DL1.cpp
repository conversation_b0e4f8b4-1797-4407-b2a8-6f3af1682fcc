//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "pc/svs/util/osgx/inc/Quantization.h"
#include "cc/assets/trajectory/inc/DL1.h"
#include "cc/assets/trajectory/inc/Helper.h"

#include "osg/Texture2D"
#include "osgDB/ReadFile"
#include "osgDB/WriteFile"

/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3804
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace trajectory
{
// This multiplier is to widen the quad stripe to have enough room for the blur on the downsampled mipmaps.
constexpr vfc::float32_t g_extraWidthForBlurMul = 1.2f; // (1 <= )

osg::DrawElements* createSurfaceDL(vfc::uint32_t f_numLayoutPoints)
{
  const vfc::uint32_t l_numIndices = 3u + (f_numLayoutPoints - 1u) * 12u + 3u;
  osg::DrawElementsUShort* const l_indices = new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), l_numIndices);

  vfc::uint32_t l_indexCounter = 0u;
  for(vfc::uint32_t i = 0u; i < f_numLayoutPoints; ++i)
  {
    // first 3 vertices to make the first TRIANGLES
    if (i == 0u)
    {
      (*l_indices)[l_indexCounter]      = static_cast<osg::DrawElementsUShort::value_type>(i);
      (*l_indices)[l_indexCounter + 1u] = static_cast<osg::DrawElementsUShort::value_type>(i + 2u);
      (*l_indices)[l_indexCounter + 2u] = static_cast<osg::DrawElementsUShort::value_type>(i + 1u);
      l_indexCounter += 3u;
    }

    if (i < f_numLayoutPoints - 1u)
    {
      // Top triagnle
      // 1st triangle
      (*l_indices)[l_indexCounter]    = static_cast<osg::DrawElementsUShort::value_type>((i * 3u) + 1u);
      (*l_indices)[l_indexCounter+1u] = static_cast<osg::DrawElementsUShort::value_type>((i * 3u) + 2u);
      (*l_indices)[l_indexCounter+2u] = static_cast<osg::DrawElementsUShort::value_type>((i + 1u) * 3u + 1u);
      // 2nd triangle
      (*l_indices)[l_indexCounter+3u] = static_cast<osg::DrawElementsUShort::value_type>((i * 3u) + 2u);
      (*l_indices)[l_indexCounter+4u] = static_cast<osg::DrawElementsUShort::value_type>((i + 1u) * 3u + 2u);
      (*l_indices)[l_indexCounter+5u] = static_cast<osg::DrawElementsUShort::value_type>((i + 1u) * 3u + 1u);
      // Bottom triagnle
      // 1st triangle
      (*l_indices)[l_indexCounter+6u] = static_cast<osg::DrawElementsUShort::value_type>((i * 3u) + 2u);
      (*l_indices)[l_indexCounter+7u] = static_cast<osg::DrawElementsUShort::value_type>(i * 3u);
      (*l_indices)[l_indexCounter+8u] = static_cast<osg::DrawElementsUShort::value_type>((i + 1u) * 3u + 2u);
      // 2nd triangle
      (*l_indices)[l_indexCounter+9u]  = static_cast<osg::DrawElementsUShort::value_type>((i + 1u) * 3u + 2u);
      (*l_indices)[l_indexCounter+10u] = static_cast<osg::DrawElementsUShort::value_type>(i * 3u);
      (*l_indices)[l_indexCounter+11u] = static_cast<osg::DrawElementsUShort::value_type>((i + 1u) * 3u);

      l_indexCounter += 12u;
    }
    else
    {
      (*l_indices)[l_indexCounter]      = static_cast<osg::DrawElementsUShort::value_type>((f_numLayoutPoints - 1u) * 3u + 1u);
      (*l_indices)[l_indexCounter + 1u] = static_cast<osg::DrawElementsUShort::value_type>((f_numLayoutPoints - 1u) * 3u + 2u);
      (*l_indices)[l_indexCounter + 2u] = static_cast<osg::DrawElementsUShort::value_type>((f_numLayoutPoints - 1u) * 3u);
      l_indexCounter += 3u;
    }

  }
  return l_indices;
}

/**
 * Calculates the parameter for the DistanceLine and generates the vertices for the geometry
 * @param f_renderBinOrder: RenderOrdere so it will be rendered on top of the baseplate
 * @param f_trajParams: General const parameters for all trackTypes that are given by TrajectoryCodingParams with CodingParamters.xml
 * @param f_DIDescriptor: General const parameters for the DistanceIndicators also given by TrajectoryCodingParams with CodingParamters.xml
 * @param f_leftOutermostLine: Limits the DistanceLine to the left
 * @param f_rightOutermostLine: Limits the DistanceLine to the right
 */
DL1::DL1(
  pc::core::Framework* f_framework,
  vfc::float32_t f_height,
  const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
  const assets::trajectory::OutermostLine* const f_leftOutermostLine,
  const assets::trajectory::OutermostLine* const f_rightOutermostLine,
  vfc::uint32_t f_numLayoutPoints)
  : GeneralTrajectoryLine(
      f_framework,
      commontypes::Middle_enm /*Dummy, not used*/,
      0u,
      f_height,
      f_trajParams,
      true)
  , m_leftOutermostLine(f_leftOutermostLine)
  , m_rightOutermostLine(f_rightOutermostLine)
  , m_numLayoutPoints(f_numLayoutPoints)
{
  osg::Vec2ubArray* const l_texCoords = new osg::Vec2ubArray(m_numLayoutPoints * 3u);
  l_texCoords->setNormalize(true);
  for (vfc::uint32_t i = 0u; i < f_numLayoutPoints; ++i)
  {
    (*l_texCoords)[i * 3u]       = osg::Vec2ub(0u,   127u);
    (*l_texCoords)[(i * 3u) + 1u] = osg::Vec2ub(0u,   127u);
    (*l_texCoords)[(i * 3u) + 2u] = osg::Vec2ub(127u, 127u);
  }
  m_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray());
  l_colors->setBinding(osg::Array::BIND_OVERALL);
  l_colors->push_back(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f)); // The color comes from the texture

  m_geometry->addPrimitiveSet(createSurfaceDL(m_numLayoutPoints));  // PRQA S 3804  // PRQA S 3803

  const vfc::float32_t lc_halfWidth           = std::abs(m_trajParams.DL1_Width) * 0.5f;
  const vfc::float32_t lc_halfGradientWidth   = std::abs(m_trajParams.GradientWidth) * 0.5f;
  const vfc::float32_t lc_gradientOuterEndPos = lc_halfWidth + lc_halfGradientWidth; // Measured from the middle line (right side of the image)
  const vfc::float32_t lc_halfGeometryWidth   = lc_gradientOuterEndPos * g_extraWidthForBlurMul;
  m_lineGeometryWidth                = lc_halfGeometryWidth * 2.0f;
}


DL1::~DL1() = default;


vfc::float32_t DL1::getDistanceLineWidth() const
{
  return m_lineGeometryWidth;
}

osg::Image* DL1::create1DTexture() const
{
  static constexpr vfc::uint32_t  lc_imageWidth  = 64u; // Image width in pixels.
  static constexpr vfc::uint32_t  lc_imageHeight = 1u;  // Image height in pixels.
  static constexpr vfc::uint32_t  lc_imageDepth  = 1u;  // Image depth in pixels, in case of a 3D image.
  static constexpr vfc::float32_t         lc_imageGeometryWidth = static_cast<vfc::float32_t>(lc_imageWidth - 1u);

  const vfc::float32_t lc_halfWidth           = std::abs(m_trajParams.DL1_Width) * 0.5f;
  const vfc::float32_t lc_halfGradientWidth   = std::abs(m_trajParams.GradientWidth) * 0.5f;
  const vfc::float32_t lc_gradientInnerEndPos = lc_halfWidth - lc_halfGradientWidth; // Measured from the middle line (right side of the image)
  const vfc::float32_t lc_gradientOuterEndPos = lc_halfWidth + lc_halfGradientWidth; // Measured from the middle line (right side of the image)
  const vfc::float32_t lc_gradientInnerEndPos_Normalized_Right = lc_gradientInnerEndPos / m_lineGeometryWidth + 0.5f; // Measured from the right side of the image.
  const vfc::float32_t lc_gradientOuterEndPos_Normalized_Right = lc_gradientOuterEndPos / m_lineGeometryWidth + 0.5f; // Measured from the right side of the image.
  const vfc::float32_t lc_gradientInnerEndPos_Normalized_Left = 1.0f - lc_gradientInnerEndPos_Normalized_Right;   // Measured from the left side of the image.
  const vfc::float32_t lc_gradientOuterEndPos_Normalized_Left = 1.0f - lc_gradientOuterEndPos_Normalized_Right;   // Measured from the left side of the image.

  const osg::Vec4ub l_lineColor_Inside = pc::util::osgx::toVec4ub(g_trajCodingParams->m_DL1_Color);
  osg::Vec4ub l_lineColor_Outside = l_lineColor_Inside;
  l_lineColor_Outside.a() = 0u;

  osg::Image* const l_image = new osg::Image;
  l_image->allocateImage(lc_imageWidth, lc_imageHeight, lc_imageDepth, GL_RGBA, GL_UNSIGNED_BYTE); //PRQA S 3143
  osg::Vec4ub* l_data = reinterpret_cast<osg::Vec4ub*> (l_image->data());
  for (vfc::uint32_t x = 0u; x < lc_imageWidth; ++x)
  {
    const vfc::float32_t l_x_normalized = static_cast<vfc::float32_t>(x) / lc_imageGeometryWidth;

    if ( (l_x_normalized < lc_gradientOuterEndPos_Normalized_Left)
      || (l_x_normalized > lc_gradientOuterEndPos_Normalized_Right) )
    {
      (*l_data) = l_lineColor_Outside;
    }
    else if ( (l_x_normalized > lc_gradientInnerEndPos_Normalized_Left)
           && (l_x_normalized < lc_gradientInnerEndPos_Normalized_Right) )
    {
      (*l_data) = l_lineColor_Inside;
    }
    else
    {
      if (l_x_normalized < lc_gradientInnerEndPos_Normalized_Left)
      {
        (*l_data) = helper::TrajectoryHelper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_Outside, l_lineColor_Inside, lc_gradientOuterEndPos_Normalized_Left, lc_gradientInnerEndPos_Normalized_Left, l_x_normalized);
      }
      else
      {
        (*l_data) = helper::TrajectoryHelper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_Inside, l_lineColor_Outside, lc_gradientInnerEndPos_Normalized_Right, lc_gradientOuterEndPos_Normalized_Right, l_x_normalized);
      }
    }
    ++l_data;
  }

  return l_image;
}


void DL1::generateVertexData()
{
  generateVertexData_usingTexture();
}


void DL1::generateVertexData_usingTexture()  // PRQA S 6041
{
  bool  l_DL1HasAllIntersectionPoints_OnTheLeft  = true;
  bool  l_DL1HasAllIntersectionPoints_OnTheRight = true;
  vfc::float32_t l_DL1FrontLineX = 0.0f;
  vfc::float32_t l_DL1RearLineX = 0.0f;

  const cc::assets::trajectory::mainlogic::Inputs_st& l_inputs = sm_mainLogicRefPtr->getInputDataRef();
  const vfc::float32_t lc_halfGeometryWidth = m_lineGeometryWidth * 0.5f;
  const vfc::float32_t lc_halfWidth = m_trajParams.DL1_Width * 0.5f;
  const vfc::float32_t lc_touchPointToGeometryOuterEdge = lc_halfGeometryWidth - lc_halfWidth;

  if (cc::assets::trajectory::mainlogic::Forward_enm == l_inputs.External.Car.DrivingDirection)
  {
    l_DL1FrontLineX = l_inputs.External.Car.FrontBumperPos
                    + m_trajParams.DL1_Offset_Front
                    + lc_halfGeometryWidth;
    l_DL1RearLineX  = l_DL1FrontLineX
                    - m_lineGeometryWidth;
  }
  else // if (cc::assets::trajectory::mainlogic::Backward_enm == l_inputs.External.Car.DrivingDirection)
  {
    l_DL1FrontLineX = l_inputs.External.Car.RearBumperPos
                    - m_trajParams.DL1_Offset_Rear
                    + lc_halfGeometryWidth;
    l_DL1RearLineX  = l_DL1FrontLineX
                    - m_lineGeometryWidth;
  }

  const vfc::float32_t l_DL1MiddleLineX  = (l_DL1FrontLineX + l_DL1RearLineX) * 0.5f;

  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (m_geometry->getVertexArray());
  const vfc::uint32_t l_numOfVerts = m_numLayoutPoints * 3u;
  l_vertices->resize(l_numOfVerts);

  (*l_vertices)[0u].x() = l_DL1RearLineX;   (*l_vertices)[0u].z() = m_height;
  (*l_vertices)[1u].x() = l_DL1FrontLineX;  (*l_vertices)[1u].z() = m_height;
  (*l_vertices)[2u].x() = l_DL1MiddleLineX; (*l_vertices)[2u].z() = m_height;
  (*l_vertices)[l_numOfVerts - 3u].x() = l_DL1RearLineX;   (*l_vertices)[l_numOfVerts - 3u].z() = m_height;
  (*l_vertices)[l_numOfVerts - 2u].x() = l_DL1FrontLineX;  (*l_vertices)[l_numOfVerts - 2u].z() = m_height;
  (*l_vertices)[l_numOfVerts - 1u].x() = l_DL1MiddleLineX; (*l_vertices)[l_numOfVerts - 1u].z() = m_height;


  vfc::float32_t l_visibleOuterRadius_Left = 0.0f;
  vfc::float32_t l_visibleOuterLateralOffset_Left = 0.0f;
  vfc::float32_t l_visibleOuterRadius_Right = 0.0f;
  vfc::float32_t l_visibleOuterLateralOffset_Right = 0.0f;
  m_leftOutermostLine ->getOuterRadiusAndOffset(l_visibleOuterRadius_Left,  l_visibleOuterLateralOffset_Left);
  m_rightOutermostLine->getOuterRadiusAndOffset(l_visibleOuterRadius_Right, l_visibleOuterLateralOffset_Right);


  if (cc::assets::trajectory::commontypes::Rotation_enm == l_inputs.Internal.VehicleMovementType)
  {
    const vfc::float32_t l_DL1LeftRadius       = l_visibleOuterRadius_Left  + lc_touchPointToGeometryOuterEdge * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
    const vfc::float32_t l_DL1RightRadius      = l_visibleOuterRadius_Right + lc_touchPointToGeometryOuterEdge * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
    const vfc::float32_t l_DL1InnerLeftRadius  = l_DL1LeftRadius  + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
    const vfc::float32_t l_DL1InnerRightRadius = l_DL1RightRadius + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
    if ( ! (sm_mainLogicRefPtr->getYFromTurningCircleEquation(l_DL1LeftRadius,       (*l_vertices)[0u].x(), (*l_vertices)[0u].y())) )
    { l_DL1HasAllIntersectionPoints_OnTheLeft = false; }
    if ( ! (sm_mainLogicRefPtr->getYFromTurningCircleEquation(l_DL1LeftRadius,       (*l_vertices)[1u].x(), (*l_vertices)[1u].y())) )
    { l_DL1HasAllIntersectionPoints_OnTheLeft = false; }
    if ( ! (sm_mainLogicRefPtr->getYFromTurningCircleEquation(l_DL1InnerLeftRadius,  (*l_vertices)[2u].x(), (*l_vertices)[2u].y())) )
    { l_DL1HasAllIntersectionPoints_OnTheLeft = false; }
    if ( ! (sm_mainLogicRefPtr->getYFromTurningCircleEquation(l_DL1RightRadius,      (*l_vertices)[l_numOfVerts - 3u].x(), (*l_vertices)[l_numOfVerts - 3u].y())) )
    { l_DL1HasAllIntersectionPoints_OnTheRight = false; }
    if ( ! (sm_mainLogicRefPtr->getYFromTurningCircleEquation(l_DL1RightRadius,      (*l_vertices)[l_numOfVerts - 2u].x(), (*l_vertices)[l_numOfVerts - 2u].y())) )
    { l_DL1HasAllIntersectionPoints_OnTheRight = false; }
    if ( ! (sm_mainLogicRefPtr->getYFromTurningCircleEquation(l_DL1InnerRightRadius, (*l_vertices)[l_numOfVerts - 1u].x(), (*l_vertices)[l_numOfVerts - 1u].y())) )
    { l_DL1HasAllIntersectionPoints_OnTheRight = false; }
  }
  else // if (Translation_enm == l_inputs.Internal.VehicleMovementType)
  {
    // In this case "l_DL1HasAllIntersectionPoints" is considered to be true.
    // It is true already.
    // l_DL1HasAllIntersectionPoints = true;

    const vfc::float32_t l_DL1LeftOffset       = l_visibleOuterLateralOffset_Left  + lc_touchPointToGeometryOuterEdge;
    const vfc::float32_t l_DL1RightOffset      = l_visibleOuterLateralOffset_Right - lc_touchPointToGeometryOuterEdge;
    const vfc::float32_t l_DL1InnerLeftOffset  = l_DL1LeftOffset  - lc_halfGeometryWidth;
    const vfc::float32_t l_DL1InnerRightOffset = l_DL1RightOffset + lc_halfGeometryWidth;
    if ( ! (sm_mainLogicRefPtr->getYFromLineEquation(l_DL1LeftOffset,       (*l_vertices)[0u].x(), (*l_vertices)[0u].y())) )
    { l_DL1HasAllIntersectionPoints_OnTheLeft = false; }
    if ( ! (sm_mainLogicRefPtr->getYFromLineEquation(l_DL1LeftOffset,       (*l_vertices)[1u].x(), (*l_vertices)[1u].y())) )
    { l_DL1HasAllIntersectionPoints_OnTheLeft = false; }
    if ( ! (sm_mainLogicRefPtr->getYFromLineEquation(l_DL1InnerLeftOffset,  (*l_vertices)[2u].x(), (*l_vertices)[2u].y())) )
    { l_DL1HasAllIntersectionPoints_OnTheLeft = false; }
    if ( ! (sm_mainLogicRefPtr->getYFromLineEquation(l_DL1RightOffset,      (*l_vertices)[l_numOfVerts - 3u].x(), (*l_vertices)[l_numOfVerts - 3u].y())) )
    { l_DL1HasAllIntersectionPoints_OnTheRight = false; }
    if ( ! (sm_mainLogicRefPtr->getYFromLineEquation(l_DL1RightOffset,      (*l_vertices)[l_numOfVerts - 2u].x(), (*l_vertices)[l_numOfVerts - 2u].y())) )
    { l_DL1HasAllIntersectionPoints_OnTheRight = false; }
    if ( ! (sm_mainLogicRefPtr->getYFromLineEquation(l_DL1InnerRightOffset, (*l_vertices)[l_numOfVerts - 1u].x(), (*l_vertices)[l_numOfVerts - 1u].y())) )
    { l_DL1HasAllIntersectionPoints_OnTheRight = false; }
  }

  if (false == l_DL1HasAllIntersectionPoints_OnTheLeft)
  {
    // Just set the left end to be outside of the viewport
    (*l_vertices)[0u].y() =  5.0f;
    (*l_vertices)[1u].y() =  5.0f;
    (*l_vertices)[2u].y() =  5.0f;
  }

  if (false == l_DL1HasAllIntersectionPoints_OnTheRight)
  {
    // Just set the right end to be outside of the viewport
    (*l_vertices)[l_numOfVerts - 3u].y() = -5.0f;
    (*l_vertices)[l_numOfVerts - 2u].y() = -5.0f;
    (*l_vertices)[l_numOfVerts - 1u].y() = -5.0f;
  }

  // Add the middle vertices
  if (m_numLayoutPoints > 2u)
  {
    const vfc::float32_t l_segmentedYRear   = ((*l_vertices)[0u].y() - (*l_vertices)[l_numOfVerts - 3u].y())/ (static_cast<vfc::float32_t>(m_numLayoutPoints) - 1.0f) * 1.0f;
    const vfc::float32_t l_segmentedYFront  = ((*l_vertices)[1u].y() - (*l_vertices)[l_numOfVerts - 2u].y())/ (static_cast<vfc::float32_t>(m_numLayoutPoints) - 1.0f) * 1.0f;
    const vfc::float32_t l_segmentedYMiddle = ((*l_vertices)[2u].y() - (*l_vertices)[l_numOfVerts - 1u].y())/ (static_cast<vfc::float32_t>(m_numLayoutPoints) - 1.0f) * 1.0f;
    for (vfc::uint32_t i = 1u; i < m_numLayoutPoints - 1u; ++i)
    {
      (*l_vertices)[i * 3u].x()     = l_DL1RearLineX;   (*l_vertices)[i * 3u].z()     = m_height;
      (*l_vertices)[i * 3u + 1u].x() = l_DL1FrontLineX;  (*l_vertices)[i * 3u + 1u].z() = m_height;
      (*l_vertices)[i * 3u + 2u].x() = l_DL1MiddleLineX; (*l_vertices)[i * 3u + 2u].z() = m_height;

      (*l_vertices)[i * 3u].y()      = (*l_vertices)[0u].y() - static_cast<vfc::float32_t>(i) * l_segmentedYRear;
      (*l_vertices)[i * 3u + 1u].y() = (*l_vertices)[1u].y() - static_cast<vfc::float32_t>(i) * l_segmentedYFront;
      (*l_vertices)[i * 3u + 2u].y() = (*l_vertices)[2u].y() - static_cast<vfc::float32_t>(i) * l_segmentedYMiddle;
    }
  }

  l_vertices->dirty();
  m_geometry->dirtyBound();

  setCull(false);
}


} // namespace trajectory
} // namespace assets
} // namespace cc
