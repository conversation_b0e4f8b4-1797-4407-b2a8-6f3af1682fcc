#include "cc/assets/common/inc/LightStateClasses.h"
#include "pc/generic/rapidjson/document.h"
#include "pc/generic/rapidjson/istreamwrapper.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "pc/svs/util/math/inc/FloatComp.h"

#include <unordered_map>
#include <fstream>


using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace common
{
namespace lightstate
{


//!
//! BlinkingIndicatorSettings
//!
class BlinkingIndicatorSettings : public pc::util::coding::ISerializable
{
public:

    BlinkingIndicatorSettings()
    : m_blinkPeriod(10u) // PRQA S 2323 // PRQA S 4052
    {
    }

    SERIALIZABLE(BlinkingIndicatorSettings)
    {
        ADD_MEMBER(vfc::uint32_t, blinkPeriod);
    }

    vfc::uint32_t m_blinkPeriod;
};

pc::util::coding::Item<BlinkingIndicatorSettings> g_lightSettings("BlinkingIndicator");

namespace
{

osg::Node* findSceneGraphNode(const std::string& f_nodeName, osg::Group* f_graph)
{
  if (nullptr == f_graph)
  {
    return nullptr;
  }
  pc::util::osgx::NodeFinder l_nodeFinder(f_nodeName);
  f_graph->accept(l_nodeFinder);
  osg::ref_ptr<osg::Node> l_result = l_nodeFinder.getFoundNode();
  return l_result.release();
}

} // namespace


///
/// SignalWrapper
///
SignalWrapper::SignalWrapper(vfc::uint32_t f_value)
  : m_value(f_value) // PRQA S 2323
{
}


bool SignalWrapper::update(pc::core::Framework* f_framework)
{
  const vfc::uint32_t l_newValue = querySignal(f_framework);
  if (l_newValue != m_value)
  {
    m_value = l_newValue;
    return true;
  }
  return false;
}


///
/// HeadlightWrapper
///
static const std::unordered_map<std::string, HeadlightWrapper::State> s_valueNameHeadlightWrapperMapping = {
  { "None",     HeadlightWrapper::State::None } ,
  { "LowBeam",  HeadlightWrapper::State::LowBeam },
  { "HighBeam", HeadlightWrapper::State::HighBeam },
  { "Both", HeadlightWrapper::State::Both }
};

vfc::uint32_t HeadlightWrapper::toValue(const std::string& f_stringValue) const
{
  return findOrInvalid(s_valueNameHeadlightWrapperMapping, f_stringValue);
}


vfc::uint32_t HeadlightWrapper::querySignal(pc::core::Framework* f_framework) const
{
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
  if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
  {
    const cc::daddy::CustomVehicleLightsDaddy* const l_lightState = l_pCustomFramework->m_VehicleLightsReceiver.getData();
    if (l_lightState != nullptr)
    {
      if( true == l_lightState->m_Data.m_mainBeamIndication && true == l_lightState->m_Data.m_lowBeamIndication)
      {
        return static_cast<vfc::uint32_t> (State::Both);
      }
      else if ( true == l_lightState->m_Data.m_mainBeamIndication )
      {
        return static_cast<vfc::uint32_t> (State::HighBeam);
      }
      else if ( true == l_lightState->m_Data.m_lowBeamIndication )
      {
        return static_cast<vfc::uint32_t> (State::LowBeam);
      }
      else
      {
        return static_cast<vfc::uint32_t> (State::None);
      }
    }
  }
  return static_cast<vfc::uint32_t> (State::None);
}

///
/// DayWrapper
///
static const std::unordered_map<std::string, DayWrapper::State> s_valueNameDayWrapperMapping = {
  { "Off", DayWrapper::State::Off },
  { "On",  DayWrapper::State::On },
  { "DayLight_L", DayWrapper::State::DayLight_L},
  { "DayLight_R", DayWrapper::State::DayLight_R}
};
vfc::uint32_t DayWrapper::toValue(const std::string& f_stringValue) const
{
  return findOrInvalid(s_valueNameDayWrapperMapping, f_stringValue);
}


vfc::uint32_t DayWrapper::querySignal(pc::core::Framework* f_framework) const
{
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
  if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
  {
    const cc::daddy::CustomVehicleLightsDaddy* const l_DayLightState = l_pCustomFramework->m_VehicleLightsReceiver.getData();

    if (l_DayLightState != nullptr)
    {
      if ( true == l_DayLightState->m_Data.m_leftHeadLightState && true == l_DayLightState->m_Data.m_rightHeadLightState )
      {
        return static_cast<vfc::uint32_t>(State::On);
      }
      else if ( true == l_DayLightState->m_Data.m_leftHeadLightState )
      {
        return static_cast<vfc::uint32_t>(State::DayLight_L);
      }
      else if (true == l_DayLightState->m_Data.m_rightHeadLightState)
      {
        return static_cast<vfc::uint32_t>(State::DayLight_R);
      }
      else
      {
        return static_cast<vfc::uint32_t>(State::Off);
      }
    }
  }
  return static_cast<vfc::uint32_t> (State::Off);
}

///
/// RearPosWrapper
///
static const std::unordered_map<std::string, RearPosWrapper::State> s_valueNameRearPosWrapperMapping = {
  { "Off", RearPosWrapper::State::Off },
  { "On",  RearPosWrapper::State::On },
};
vfc::uint32_t RearPosWrapper::toValue(const std::string& f_stringValue) const
{
  return findOrInvalid(s_valueNameRearPosWrapperMapping, f_stringValue);
}


vfc::uint32_t RearPosWrapper::querySignal(pc::core::Framework* f_framework) const
{
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
  if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
  {
    const cc::daddy::CustomVehicleLightsDaddy* const l_RearPosState = l_pCustomFramework->m_VehicleLightsReceiver.getData();

    if (l_RearPosState != nullptr)
    {
      if ( 1u == l_RearPosState->m_Data.m_rearPosLightState )
      {
        return static_cast<vfc::uint32_t>(State::On);
      }
      else
      {
        return static_cast<vfc::uint32_t>(State::Off);
      }
    }
  }
  return static_cast<vfc::uint32_t> (State::Off);
}

///
/// BrakeLightWrapper
///
static const std::unordered_map<std::string, BrakeLightWrapper::State> s_valueNameBrakeLightWrapperMapping = {
  { "Off", BrakeLightWrapper::State::Off },
  { "On",  BrakeLightWrapper::State::On },
};
vfc::uint32_t BrakeLightWrapper::toValue(const std::string& f_stringValue) const
{
  return findOrInvalid(s_valueNameBrakeLightWrapperMapping, f_stringValue);
}


vfc::uint32_t BrakeLightWrapper::querySignal(pc::core::Framework* f_framework) const
{
  if(nullptr == f_framework)
  {
    return 0u;
  }
  if ( f_framework->m_brakeLightStateReceiver.isConnected() )
  {
    const pc::daddy::BrakeLightStateDaddy* const l_brakeLightState = f_framework->m_brakeLightStateReceiver.getData();

    if (l_brakeLightState != nullptr)
    {
      switch (l_brakeLightState->m_Data)
      {
        case pc::daddy::BRAKE_LIGHT_OFF:
        {
          return static_cast<vfc::uint32_t> (State::Off);
        }
        case pc::daddy::BRAKE_LIGHT_ON:
        {
          return static_cast<vfc::uint32_t> (State::On);
        }
        default:
        {
          break;
        }
      }
    }
  }
  return static_cast<vfc::uint32_t> (State::Off);
}


///
/// ReverseGearWrapper
///
static const std::unordered_map<std::string, ReverseGearWrapper::State> s_valueNameReverseGearWrapperMapping = {
  { "Off", ReverseGearWrapper::State::Off },
  { "On",  ReverseGearWrapper::State::On },
};
vfc::uint32_t ReverseGearWrapper::toValue(const std::string& f_stringValue) const
{
  return findOrInvalid(s_valueNameReverseGearWrapperMapping, f_stringValue);
}


vfc::uint32_t ReverseGearWrapper::querySignal(pc::core::Framework* f_framework) const
{
  if(nullptr == f_framework)
  {
    return 0u;
  }
#if 0
  const pc::daddy::GearDaddy* const l_reverseGearState = f_framework->m_gearReceiver.getData();
  if (l_reverseGearState != nullptr)
  {
    if (pc::daddy::GEAR_R == l_reverseGearState->m_Data)
    {
      return static_cast<vfc::uint32_t> (State::On);
    }
  }
  return static_cast<vfc::uint32_t> (State::Off);
#else
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework);
  if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
  {
    const cc::daddy::CustomVehicleLightsDaddy* const l_lightState = l_pCustomFramework->m_VehicleLightsReceiver.getData();

    if (l_lightState != nullptr)
    {
      if ( 1u == l_lightState->m_Data.m_reverseLightState)
      {
        return static_cast<vfc::uint32_t>(State::On);
      }
      else
      {
        return static_cast<vfc::uint32_t>(State::Off);
      }
    }
  }
  return static_cast<vfc::uint32_t> (State::Off);
#endif
}


///
/// IndicatorWrapper
///
static const std::unordered_map<std::string, IndicatorWrapper::State> s_valueNameIndicatorWrapperMapping = {
  { "Idle",  IndicatorWrapper::State::Idle },
  { "Left",  IndicatorWrapper::State::Left },
  { "Right", IndicatorWrapper::State::Right },
  { "Warn",  IndicatorWrapper::State::Warn }
};
vfc::uint32_t IndicatorWrapper::toValue(const std::string& f_stringValue) const
{
  return findOrInvalid(s_valueNameIndicatorWrapperMapping, f_stringValue);
}


vfc::uint32_t IndicatorWrapper::querySignal(pc::core::Framework* f_framework) const
{
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
  if (l_pCustomFramework->m_VehicleLightsReceiver.isConnected())
  {
    const cc::daddy::CustomVehicleLightsDaddy* const l_pCustomData = l_pCustomFramework->m_VehicleLightsReceiver.getData();

    // XLOG_INFO_OS(g_AppContext) <<"[IndicatorWrapper]: querySignal !!" << XLOG_ENDL;

    if (nullptr != l_pCustomData)
    {
        if (cc::target::common::EturnSignalstatus_::Turn_Signal_DangerWarningSignal == static_cast<cc::target::common::EturnSignalstatus_>(l_pCustomData->m_Data.m_turnSignalStatus)) // PRQA S 4899
        {
          // XLOG_INFO_OS(g_AppContext) <<"[svs]: Turn_Signal_DangerWarningSignal is !!" << static_cast<int> (l_pCustomData->m_Data.m_turnSignalStatus) << XLOG_ENDL;
          return static_cast<vfc::uint32_t> (State::Warn);
        }
        else if(cc::target::common::EturnSignalstatus_::Turn_Signal_NOTWORKING == static_cast<cc::target::common::EturnSignalstatus_>(l_pCustomData->m_Data.m_turnSignalStatus)) // PRQA S 4899
        {
          // XLOG_INFO_OS(g_AppContext) <<"[svs]: Turn_Signal_NOTWORKING is !!" << static_cast<int> (l_pCustomData->m_Data.m_turnSignalStatus) << XLOG_ENDL;
          return static_cast<vfc::uint32_t> (State::Idle);
        }
        else if(cc::target::common::EturnSignalstatus_::Turn_Signal_LeftTurnSignalLightNormalFlashing == static_cast<cc::target::common::EturnSignalstatus_>(l_pCustomData->m_Data.m_turnSignalStatus)) // PRQA S 4899
        {
          // XLOG_INFO_OS(g_AppContext) <<"[svs]: Turn_Signal_LeftTurnSignalLightNormalFlashing is !!" << static_cast<int> (l_pCustomData->m_Data.m_turnSignalStatus) << XLOG_ENDL;
          return static_cast<vfc::uint32_t> (State::Left);
        }
        else if(cc::target::common::EturnSignalstatus_::Turn_Signal_RightTurnSignalLightFlashingNormally == static_cast<cc::target::common::EturnSignalstatus_>(l_pCustomData->m_Data.m_turnSignalStatus)) // PRQA S 4899
        {
          // XLOG_INFO_OS(g_AppContext) <<"[svs]: Turn_Signal_RightTurnSignalLightFlashingNormally is !!" << static_cast<int> (l_pCustomData->m_Data.m_turnSignalStatus) << XLOG_ENDL;
          return static_cast<vfc::uint32_t> (State::Right);
        }
        else
        {
          // Do nothing
          // XLOG_INFO_OS(g_AppContext) <<"[IndicatorWrapper]: else is !!"
          //                                     << static_cast<int> (l_pCustomData->m_Data.m_turnSignalStatus) << XLOG_ENDL;
        }
    }
    // return static_cast<vfc::uint32_t> (State::Idle);
  }

  // Not used in HC23 after 20230815
  // const pc::daddy::IndicatorStateDaddy* l_indicatorState = f_framework->m_indicatorStateReceiver.getData();
  // if (l_indicatorState != nullptr)
  // {
  //   switch (l_indicatorState->m_Data)
  //   {
  //     case pc::daddy::INDICATOR_OFF:
  //       return static_cast<vfc::uint32_t> (State::Idle);
  //     case pc::daddy::INDICATOR_LEFT:
  //       return static_cast<vfc::uint32_t> (State::Left);
  //     case pc::daddy::INDICATOR_RIGHT:
  //       return static_cast<vfc::uint32_t> (State::Right);
  //     case pc::daddy::INDICATOR_WARN:
  //       return static_cast<vfc::uint32_t> (State::Warn);
  //     default: break;
  //   }
  // }
  return static_cast<vfc::uint32_t> (State::Idle);
}


///
/// SideIndicatorWrapper
///
static const std::unordered_map<std::string, SideIndicatorWrapper::State> s_valueNameSideIndicatorWrapperMapping = {
  { "Idle",  SideIndicatorWrapper::State::Idle },
  { "Left",  SideIndicatorWrapper::State::Left },
  { "Right", SideIndicatorWrapper::State::Right },
  { "Warn",  SideIndicatorWrapper::State::Warn }
};
vfc::uint32_t SideIndicatorWrapper::toValue(const std::string& f_stringValue) const
{
  return findOrInvalid(s_valueNameSideIndicatorWrapperMapping, f_stringValue);
}


vfc::uint32_t SideIndicatorWrapper::querySignal(pc::core::Framework* f_framework) const
{
  vfc::uint32_t l_indicator = 0u;
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076

  // XLOG_INFO_OS(g_AppContext) <<"[SideIndicatorWrapper]: querySignal !!" << XLOG_ENDL;

  if (l_pCustomFramework->m_VehicleLightsReceiver.isConnected())
  {
      const cc::daddy::CustomVehicleLightsDaddy* const l_pCustomData = l_pCustomFramework->m_VehicleLightsReceiver.getData();

      if (nullptr != l_pCustomData)
      {
          if (cc::target::common::EturnSignalstatus_::Turn_Signal_DangerWarningSignal == static_cast<cc::target::common::EturnSignalstatus_>(l_pCustomData->m_Data.m_turnSignalStatus)) // PRQA S 4899
          {
            // XLOG_INFO_OS(g_AppContext) <<"[SideIndicatorWrapper]: Turn_Signal_DangerWarningSignal is !!"
            //                       << static_cast<int> (l_pCustomData->m_Data.m_turnSignalStatus) << XLOG_ENDL;
            l_indicator = static_cast<vfc::uint32_t> (State::Warn);
          }
          else if (cc::target::common::EturnSignalstatus_::Turn_Signal_LeftTurnSignalLightNormalFlashing == static_cast<cc::target::common::EturnSignalstatus_>(l_pCustomData->m_Data.m_turnSignalStatus)) // PRQA S 4899
          {
            // XLOG_INFO_OS(g_AppContext) <<"[SideIndicatorWrapper]: Turn_Signal_LeftTurnSignalLightNormalFlashing is !!"
            //                       << static_cast<int> (l_pCustomData->m_Data.m_turnSignalStatus) << XLOG_ENDL;
            l_indicator = static_cast<vfc::uint32_t> (State::Left);
          }
          else if (cc::target::common::EturnSignalstatus_::Turn_Signal_RightTurnSignalLightFlashingNormally == static_cast<cc::target::common::EturnSignalstatus_>(l_pCustomData->m_Data.m_turnSignalStatus)) // PRQA S 4899
          {
            // XLOG_INFO_OS(g_AppContext) <<"[SideIndicatorWrapper]: Turn_Signal_RightTurnSignalLightFlashingNormally is !!"
            //                                   << static_cast<int> (l_pCustomData->m_Data.m_turnSignalStatus) << XLOG_ENDL;
            l_indicator = static_cast<vfc::uint32_t> (State::Right);
          }
          else
          {
            // XLOG_INFO_OS(g_AppContext) <<"[SideIndicatorWrapper]: else is !!"
            //                                   << static_cast<int> (l_pCustomData->m_Data.m_turnSignalStatus) << XLOG_ENDL;
            // const pc::daddy::IndicatorStateDaddy* l_indicatorState = f_framework->m_indicatorStateReceiver.getData();
            // if (l_indicatorState != nullptr)
            // {
            //   switch (l_indicatorState->m_Data)
            //   {
            //     case pc::daddy::INDICATOR_OFF:
            //       l_indicator = static_cast<vfc::uint32_t> (State::Idle);
            //       break;
            //     case pc::daddy::INDICATOR_LEFT:
            //       l_indicator = static_cast<vfc::uint32_t> (State::Left);
            //       break;
            //     case pc::daddy::INDICATOR_RIGHT:
            //       l_indicator = static_cast<vfc::uint32_t> (State::Right);
            //       break;
            //     case pc::daddy::INDICATOR_WARN:
            //       l_indicator = static_cast<vfc::uint32_t> (State::Warn);
            //       break;
            //     default: break;
            //   }
            // }
          }
      }
  }

  static vfc::uint32_t l_progress = 0u;
  if (l_indicator != static_cast<vfc::uint32_t> (State::Idle))
  {
    l_progress += 1u;
    if (l_progress <= g_lightSettings->m_blinkPeriod)
    {
      // toggle off
      l_indicator = static_cast<vfc::uint32_t> (State::Idle);
    }
    else if (l_progress <= 2u * g_lightSettings->m_blinkPeriod)
    {
      // toggle on
    }
    else if (l_progress >= 2u * g_lightSettings->m_blinkPeriod)
    {
      // reset
      l_progress = 0u;
    }
    else
    {
      // Do nothing
    }
  }

  return l_indicator;
}

///
/// SmallLightWrapper
///
static const std::unordered_map<std::string, SmallLightWrapper::State> s_valueNameSmallLightWrapperMapping = {
  { "Off", SmallLightWrapper::State::Off },
  { "On",  SmallLightWrapper::State::On },
};
vfc::uint32_t SmallLightWrapper::toValue(const std::string& f_stringValue) const
{
  return findOrInvalid(s_valueNameSmallLightWrapperMapping, f_stringValue);
}


vfc::uint32_t SmallLightWrapper::querySignal(pc::core::Framework* f_framework) const
{
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
  if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
  {
    const cc::daddy::CustomVehicleLightsDaddy* const l_DayLightState = l_pCustomFramework->m_VehicleLightsReceiver.getData();

    if (l_DayLightState != nullptr)
    {
      if ( 1u == l_DayLightState->m_Data.m_daytimeRunningLights )
      {
        return static_cast<vfc::uint32_t>(State::On);
      }
      else
      {
        return static_cast<vfc::uint32_t>(State::Off);
      }
    }
  }
  return static_cast<vfc::uint32_t> (State::Off);
}


///
/// FogLightWrapper
///
static const std::unordered_map<std::string, FogLightWrapper::State> s_valueNameFogLightWrapperMapping = {
  { "Off", FogLightWrapper::State::Off },
  { "On",  FogLightWrapper::State::On },
};
vfc::uint32_t FogLightWrapper::toValue(const std::string& f_stringValue) const
{
  return findOrInvalid(s_valueNameFogLightWrapperMapping, f_stringValue);
}

vfc::uint32_t FogLightWrapper::querySignal(pc::core::Framework* f_framework) const
{
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
  if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
  {
    const cc::daddy::CustomVehicleLightsDaddy* const l_RearPosState = l_pCustomFramework->m_VehicleLightsReceiver.getData();

    if (l_RearPosState != nullptr)
    {
      if ( 1u == l_RearPosState->m_Data.m_fogLightStatus )
      {
        return static_cast<vfc::uint32_t>(State::On);
      }
      else
      {
        return static_cast<vfc::uint32_t>(State::Off);
      }
    }
  }
  return static_cast<vfc::uint32_t> (State::Off);
}



///
/// FrontClearanceLampWrapper
///
vfc::uint32_t FrontClearanceLampWrapper::toValue(const std::string& f_stringValue) const
{
  static const std::unordered_map<std::string, State> s_valueNameMapping = {
    { "Off", State::Off },
    { "On",  State::On },
  };
  return findOrInvalid(s_valueNameMapping, f_stringValue);
}

vfc::uint32_t FrontClearanceLampWrapper::querySignal(pc::core::Framework* f_framework) const
{
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework);
  if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
  {
    const cc::daddy::CustomVehicleLightsDaddy* const l_lightState = l_pCustomFramework->m_VehicleLightsReceiver.getData();

    if (l_lightState != nullptr)
    {
      if ( 1u == l_lightState->m_Data.m_frontClearanceLightState )
      {
        return static_cast<vfc::uint32_t>(State::On);
      }
      else
      {
        return static_cast<vfc::uint32_t>(State::Off);
      }
    }
  }
  return static_cast<vfc::uint32_t> (State::Off);
}

///
/// FrontClearanceLampWrapper
///
vfc::uint32_t RearClearanceLampWrapper::toValue(const std::string& f_stringValue) const
{
  static const std::unordered_map<std::string, State> s_valueNameMapping = {
    { "Off", State::Off },
    { "On",  State::On },
  };
  return findOrInvalid(s_valueNameMapping, f_stringValue);
}

vfc::uint32_t RearClearanceLampWrapper::querySignal(pc::core::Framework* f_framework) const
{
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework);
  if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
  {
    const cc::daddy::CustomVehicleLightsDaddy* const l_lightState = l_pCustomFramework->m_VehicleLightsReceiver.getData();

    if (l_lightState != nullptr)
    {
      if ( 1u == l_lightState->m_Data.m_rearClearanceLightState )
      {
        return static_cast<vfc::uint32_t>(State::On);
      }
      else
      {
        return static_cast<vfc::uint32_t>(State::Off);
      }
    }
  }
  return static_cast<vfc::uint32_t> (State::Off);
}


///
/// FrontCornerLeftLampWrapper
///
static const std::unordered_map<std::string, FrontCornerLeftLampWrapper::State> s_valueNameFrontCornerLeftLampMapping = {
  { "Off", FrontCornerLeftLampWrapper::State::Off },
  { "On",  FrontCornerLeftLampWrapper::State::On },
};
vfc::uint32_t FrontCornerLeftLampWrapper::toValue(const std::string& f_stringValue) const
{
  return findOrInvalid(s_valueNameFrontCornerLeftLampMapping, f_stringValue);
}

vfc::uint32_t FrontCornerLeftLampWrapper::querySignal(pc::core::Framework* f_framework) const
{
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
  if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
  {
    const cc::daddy::CustomVehicleLightsDaddy* const l_lightState = l_pCustomFramework->m_VehicleLightsReceiver.getData();

    if (l_lightState != nullptr)
    {
      if ( 1u == l_lightState->m_Data.m_frontLeftCornerLightState )
      {
        return static_cast<vfc::uint32_t>(State::On);
      }
      else
      {
        return static_cast<vfc::uint32_t>(State::Off);
      }
    }
  }
  return static_cast<vfc::uint32_t> (State::Off);
}


///
/// FrontCornerRightLampWrapper
///
static const std::unordered_map<std::string, FrontCornerRightLampWrapper::State> s_valueNameFrontCornerRightLampMapping = {
  { "Off", FrontCornerRightLampWrapper::State::Off },
  { "On",  FrontCornerRightLampWrapper::State::On },
};
vfc::uint32_t FrontCornerRightLampWrapper::toValue(const std::string& f_stringValue) const
{
  return findOrInvalid(s_valueNameFrontCornerRightLampMapping, f_stringValue);
}

vfc::uint32_t FrontCornerRightLampWrapper::querySignal(pc::core::Framework* f_framework) const
{
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
  if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
  {
    const cc::daddy::CustomVehicleLightsDaddy* const l_lightState = l_pCustomFramework->m_VehicleLightsReceiver.getData();

    if (l_lightState != nullptr)
    {
      if ( 1u == l_lightState->m_Data.m_frontRightCornerLightState)
      {
        return static_cast<vfc::uint32_t>(State::On);
      }
      else
      {
        return static_cast<vfc::uint32_t>(State::Off);
      }
    }
  }
  return static_cast<vfc::uint32_t> (State::Off);
}

///
/// FrontPosWrapper
///
static const std::unordered_map<std::string, FrontPosWrapper::State> s_valueFrontPosWrapperNameMapping = {
    { "Off", FrontPosWrapper::State::Off },
    { "On",  FrontPosWrapper::State::On },
};
vfc::uint32_t FrontPosWrapper::toValue(const std::string& f_stringValue) const
{

  return findOrInvalid(s_valueFrontPosWrapperNameMapping, f_stringValue);
}

vfc::uint32_t FrontPosWrapper::querySignal(pc::core::Framework* f_framework) const
{
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework);
  if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
  {
    const cc::daddy::CustomVehicleLightsDaddy* const l_lightState = l_pCustomFramework->m_VehicleLightsReceiver.getData();

    if (l_lightState != nullptr)
    {
      if ( 1u == l_lightState->m_Data.m_frontPosLightState)
      {
        return static_cast<vfc::uint32_t>(State::On);
      }
      else
      {
        return static_cast<vfc::uint32_t>(State::Off);
      }
    }
  }
  return static_cast<vfc::uint32_t> (State::Off);
}
///
/// SignalWrapperRegistry
///
SignalWrapperRegistry::SignalWrapperRegistry(pc::core::Framework* f_framework)
  : m_framework(f_framework) // PRQA S 2323
  , m_signalWrappers()
{
}


bool SignalWrapperRegistry::update()
{
  bool l_signalsChanged = false;
  for (auto l_itr = m_signalWrappers.begin(); l_itr != m_signalWrappers.end(); ++l_itr) // PRQA S 4297 // PRQA S 4687
  {
    if (l_itr->second)
    {
      l_signalsChanged = (l_itr->second->update(m_framework) || l_signalsChanged);
    }
  }
  return l_signalsChanged;
}


void SignalWrapperRegistry::registerSignal(const std::string& f_name, const std::shared_ptr<SignalWrapper>& f_singalWrapper)
{
  m_signalWrappers[f_name] = f_singalWrapper;
}


std::shared_ptr<SignalWrapper> SignalWrapperRegistry::getSignalWrapper(const std::string& f_name) const
{
  const auto l_res = m_signalWrappers.find(f_name);
  if (l_res != m_signalWrappers.end())
  {
    return l_res->second;
  }
  return std::shared_ptr<SignalWrapper>(nullptr);
}


///
/// SignalReq
///
SignalReq::SignalReq(
  const std::shared_ptr<SignalWrapper>& f_signal,
  vfc::uint32_t f_referenceValue,
  bool f_not)
  : m_signal(f_signal) // PRQA S 2323
  , m_referenceValue(f_referenceValue) // PRQA S 2323
  , m_not(f_not) // PRQA S 2323
{
}


bool SignalReq::check() const
{
  bool is_equal = m_referenceValue == m_signal->getValue();
  const vfc::uint32_t l_var1 = static_cast<vfc::uint32_t>(is_equal);
  const vfc::uint32_t l_var2 = static_cast<vfc::uint32_t>(m_not);
  return l_var1 != l_var2;
}


///
/// Condition
///
void Condition::addSignalReq(const std::shared_ptr<SignalReq>& f_item)
{
  m_signalReqs.push_back(f_item);
}


bool Condition::evaluate() const
{
  for (const auto& l_req : m_signalReqs)
  {
    if (l_req)
    {
      if (!l_req->check())
      {
        return false; // logic AND if a single requirement is false
      }
    }
  }
  return true;
}


///
/// LightNode
///
LightNode::LightNode(osg::Node* f_lightNode)
  : m_lightNode(f_lightNode) // PRQA S 2323
  , m_conditions()
{
}


void LightNode::addCondition(const std::shared_ptr<Condition>& f_condition)
{
  m_conditions.push_back(f_condition);
}


void LightNode::update()
{
  if (!m_lightNode.valid())
  {
    return;
  }

  bool l_result = false;
  for (const auto& l_cond : m_conditions)
  {
    if (l_cond->evaluate()) // logical OR a single condition is sufficient
    {
      l_result = true;
      break;
    }
  }

  if (l_result)
  {
    m_lightNode->setNodeMask(~0u);
  }
  else
  {
    m_lightNode->setNodeMask(0u);
  }
}


///
/// LightNodeUpdateCallback
///
LightNodeUpdateCallback::LightNodeUpdateCallback(const std::shared_ptr<SignalWrapperRegistry>& f_registry)
  : m_registry(f_registry) // PRQA S 2323 // PRQA S 4052
  , m_lightNodes{}
{
}


void LightNodeUpdateCallback::operator() (osg::Node* f_node, osg:: NodeVisitor* f_nv)
{
  if (m_registry->update())
  {
    for (auto& l_lightNode : m_lightNodes)
    {
      l_lightNode->update();
    }
  }
  traverse(f_node, f_nv);
}


void LightNodeUpdateCallback::addLightNode(const std::shared_ptr<LightNode>& f_lightNode)
{
  m_lightNodes.push_back(f_lightNode);
}


///
/// LightStateJsonParser
///
namespace
{
constexpr const char* JSON_LIGHTNODES_STR  = "LightNodes";
constexpr const char* JSON_CONDITIONS_STR  = "Conditions";
constexpr const char* JSON_SIGNALREQS_STR  = "SignalRequirements";
constexpr const char* JSON_NAME_STR        = "Name";
constexpr const char* JSON_VALUE_STR       = "Value";
constexpr const char* JSON_VALUENOT_STR    = "ValueNot";
constexpr const char* JSON_HINT_STR        = "Hint";


bool checkMember(
  const rapidjson::Value& f_jsonObject,
  const std::string&      f_memberName,
  const rapidjson::Type   f_expectedType)
{
  if (!f_jsonObject.HasMember(f_memberName.c_str()))
  {
    return false;
  }
  if (f_expectedType != f_jsonObject[f_memberName.c_str()].GetType())
  {
    return false;
  }
  return true;
}


bool parseSignalRequirements(
  const rapidjson::Value& f_jsonObject,
  const std::shared_ptr<Condition>& f_condition,
  const LightNodeUpdateCallback* f_callback)
{
  if (nullptr == f_callback)
  {
    return false;
  }
   // Get the signal name
  if (!checkMember(f_jsonObject, JSON_NAME_STR, rapidjson::kStringType))
  {
    XLOG_ERROR(g_AppContext, "LightStateJsonParser::parseSignalRequirements() Member \"" << JSON_NAME_STR <<
      "\" could not be found or is not a string");
    return false;
  }

  const std::string l_signalName = f_jsonObject[JSON_NAME_STR].GetString();
  auto l_signalWrapper = f_callback->getRegistry()->getSignalWrapper(l_signalName);
  if (l_signalWrapper == nullptr)
  {
    XLOG_ERROR(g_AppContext, "LightStateJsonParser::parseSignalRequirements() \"" << l_signalName <<
      "\" does not name a registred signal");
    return false;
  }

  // Check for the keys "Value" or "ValueNot"
  bool l_valueNot = false;
  if (!checkMember(f_jsonObject, JSON_VALUE_STR, rapidjson::kStringType))
  {
    l_valueNot = true;
    if(!checkMember(f_jsonObject, JSON_VALUENOT_STR, rapidjson::kStringType))
    {
      XLOG_ERROR(g_AppContext, "LightStateJsonParser::parseSignalRequirements() Attribute \"" << JSON_VALUE_STR <<
        "\" or " << "\"" << JSON_VALUENOT_STR << " \" is missing or is not a string value");
      return false;
    }
  }
  // Get the expected value
  const std::string l_signalValueStr = f_jsonObject[l_valueNot ? JSON_VALUENOT_STR : JSON_VALUE_STR].GetString();
  const vfc::uint32_t l_signalValue = l_signalWrapper->toValue(l_signalValueStr);
  if (l_signalValue == SignalWrapper::INVALID_VALUE)
  {
    XLOG_ERROR(g_AppContext, "LightStateJsonParser::parseSignalRequirements() The value \"" << l_signalValueStr <<
      "\" does not map to a valid state of the \"" << l_signalName << "\" signal");
    return false;
  }

  // Add the signal name and the expected value to a signal request object
  const std::shared_ptr<SignalReq> l_signalReq = std::make_shared<SignalReq>(l_signalWrapper, l_signalValue, l_valueNot); // PRQA S 4126
  f_condition->addSignalReq(l_signalReq);
  // XLOG_DEBUG_OS(g_AppContext) << "LightStateJsonParser::parseSignalRequirements() Added \"" << l_signalName <<
  //   "\" " << (l_valueNot ? "!=" : "==") << " \"" << l_signalValueStr << "\"" << XLOG_ENDL;
  return true;
}


bool parseSignals(
  const rapidjson::Value& f_jsonObject,
  const std::shared_ptr<Condition>& f_condition,
  const LightNodeUpdateCallback* f_callback)
{
  // Check if a condition is valid
  if (!checkMember(f_jsonObject, JSON_SIGNALREQS_STR, rapidjson::kArrayType))
  {
    XLOG_ERROR(g_AppContext, "LightStateJsonParser::parseSignals(): Member \"" << JSON_SIGNALREQS_STR <<
      "\" could not be found or is not an array");
    return false;
  }
  // Get the signals requirements and walk through them
  const rapidjson::Value& l_jsonSignals = f_jsonObject[JSON_SIGNALREQS_STR];
  for (rapidjson::Value::ConstValueIterator l_it = l_jsonSignals.Begin(); l_it != l_jsonSignals.End(); ++l_it)
  {
    if (!parseSignalRequirements(*l_it, f_condition, f_callback))
    {
      return false;
    }
  }
  return true;
}


bool parseConditions(
  const rapidjson::Value&  f_jsonObject,
  const std::shared_ptr<LightNode>& f_lightNode,
  const LightNodeUpdateCallback* f_callback)
{
  // Check for array of conditions
  if (!checkMember(f_jsonObject, JSON_CONDITIONS_STR, rapidjson::kArrayType))
  {
    XLOG_ERROR(g_AppContext, "LightStateJsonParser::parseConditions(): Attribute \"" << JSON_CONDITIONS_STR <<
      "\" could not be found or is not an array");
    return false;
  }
  // Get the conditions and walk through them
  const rapidjson::Value& l_jsonConditions = f_jsonObject[JSON_CONDITIONS_STR];
  for (rapidjson::Value::ConstValueIterator l_it = l_jsonConditions.Begin(); l_it != l_jsonConditions.End(); ++l_it)
  {
    auto const l_condition = std::make_shared<Condition>();
    if (!parseSignals(*l_it, l_condition, f_callback))
    {
      return false;
    }
    f_lightNode->addCondition(l_condition);
  }
  return true;
}


bool parseLightNode(
  LightStateJsonParser& f_parser,
  const rapidjson::Value&  f_jsonObject,
  LightNodeUpdateCallback* f_callback,
  osg::Group* f_sceneGraph)
{
  if (f_callback == nullptr)
  {
    XLOG_ERROR(g_AppContext, "LightStateJsonParser: LightNodeUpdateCallback pointer is invalid!");
    return false;
  }

  // Check for the string attribute "Name"
  if (!checkMember(f_jsonObject, JSON_NAME_STR, rapidjson::kStringType))
  {
    XLOG_ERROR(g_AppContext, "LightStateJsonParser: Attribute \"Name\" could not be found or is not a string");
    return false;
  }
  const std::string l_nodeName = f_jsonObject[JSON_NAME_STR].GetString();
  // Try to find the light node in the scene graph and setup a proxy for updating it
  osg::Node* l_sceneGraphNode = findSceneGraphNode(l_nodeName, f_sceneGraph);
  if (l_sceneGraphNode != nullptr)
  {
    const auto l_lightNode = std::make_shared<LightNode>(l_sceneGraphNode);
    if (!parseConditions(f_jsonObject, l_lightNode, f_callback))
    {
      return false;
    }
    f_callback->addLightNode(l_lightNode);
    // check for hints
    if (checkMember(f_jsonObject, JSON_HINT_STR, rapidjson::kStringType))
    {
      const std::string l_hint = f_jsonObject[JSON_HINT_STR].GetString();
      f_parser.onHint(l_hint, l_sceneGraphNode);
    }
    XLOG_DEBUG(g_AppContext, "LightStateJsonParser:: successfully parsed light node \"" << l_nodeName << "\"");
  }
  else
  {
    XLOG_WARN(g_AppContext, "LightStateJsonParser: The light node \"" << l_nodeName << "\" could not be found in the vehicle model");
  }
  return true;
}

} // namespace


LightStateJsonParser::LightStateJsonParser(const std::shared_ptr<SignalWrapperRegistry>& f_registry)
  : m_registry(f_registry) // PRQA S 2323
{
}


LightNodeUpdateCallback* LightStateJsonParser::parse(const std::string& f_filename, osg::Group* f_sceneGraph)
{
  // Read JSON file
  std::ifstream l_jsonFile(f_filename);
  if (!l_jsonFile.is_open())
  {
    XLOG_ERROR(g_AppContext, "LightStateJsonParser: File can't be opened: \"" <<
      f_filename << "\". Parsing failed!");
    return nullptr;
  }

  rapidjson::IStreamWrapper l_streamWrapper(l_jsonFile);

  // Parse JSON
  rapidjson::Document l_doc;
  l_doc.ParseStream(l_streamWrapper); // PRQA S 3803

  // Check the validity of the JSON document
  if (l_doc.HasParseError())
  {
    XLOG_ERROR(g_AppContext, "LightStateJsonParser: Invalid JSON: \"" <<
      f_filename << "\". Parsing failed!");
    return nullptr;
  }

  if (!checkMember(l_doc, JSON_LIGHTNODES_STR, rapidjson::kArrayType))
  {
    XLOG_ERROR(g_AppContext, "LightStateJsonParser: JSON file \"" <<
      f_filename << "\" has unexpected structure");
    return nullptr;
  }

  // Create the whole structure
  osg::ref_ptr<LightNodeUpdateCallback> l_callback = new LightNodeUpdateCallback(m_registry);

  // Get the array from the JSON
  const rapidjson::Value& jsonLightNodes = l_doc[JSON_LIGHTNODES_STR].GetArray();
  for (rapidjson::Value::ConstValueIterator it = jsonLightNodes.Begin(); it != jsonLightNodes.End(); ++it)
  {
    if (!parseLightNode(*this, *it, l_callback.get(), f_sceneGraph))
    {
      return nullptr;
    }
  }
  XLOG_INFO(g_AppContext, "LightStateJsonParser: Light States parsed successfully from \"" <<
    f_filename << "\"");

  return l_callback.release();
}


void LightStateJsonParser::onHint(const std::string& /* f_hint */, osg::Node* /* f_node */)
{
  // empty per default
}



} // namespace lightstate
} // namespace common
} // namespace assets
} // namespace cc

