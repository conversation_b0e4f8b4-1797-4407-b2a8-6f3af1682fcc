//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomIcon.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_CUSTOMICON_H
#define CC_ASSETS_UIELEMENTS_CUSTOMICON_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include <osgDB/ReadFile>

namespace cc
{
namespace assets
{
namespace uielements
{

//!
//! CustomIcon
//!
class CustomIcon : public pc::assets::Icon
{
public:

  enum AnimationStyle : vfc::uint32_t
  {
    NONE_EFFECT = 0u,
    FLASHING_EFFECT = 1u,
    FADEIN_FADEOUT_EFFECT = 2u,
    AUGMENTED_WAVE_EFFECT = 3u
  };

  enum AnimationDir : vfc::uint32_t
  {
    START_FROM_TOP = 0u,
    START_FROM_BOTTOM = 1u
  };

  CustomIcon(const std::string& f_filename, bool f_isLeft, bool f_isParkSpace, bool f_isParkUI, bool f_isHoriScreen);
  CustomIcon(const std::string& f_filename, bool f_isLeft, bool f_isParkSpace, bool f_isParkUI, bool f_isHoriScreen,
             const AnimationStyle f_animationStyle, const AnimationDir f_animationDir);

  void init(const std::string& f_filename);

  void setRotateAngle(const vfc::float32_t& f_rotAngle);
  void setTexture(osg::Texture2D* f_texture);
  void setAnimation(const AnimationStyle f_animationStyle);
  void setHorizontalScreen(const bool f_isHoriScreen);

  const AnimationStyle getAnimation() const;
  void updateShaderUniform();

  const osg::Vec2f getIconSize() const { return m_iconSize; }

protected:
  virtual ~CustomIcon() = default;
  //! Copy constructor is not permitted.
  CustomIcon (const CustomIcon& other); // = delete
  //! Copy assignment operator is not permitted.
  CustomIcon& operator=(const CustomIcon& other); // = delete

  void updateGeometry(
    osg::Geometry* f_geometry,
    const osg::Vec2f& f_origin,
    const osg::Vec2f& f_size,
    vfc::float32_t f_left,
    vfc::float32_t f_bottom,
    vfc::float32_t f_right,
    vfc::float32_t f_top) const override;

  osg::Geometry* createGeometry() const override;

private:
  vfc::float32_t m_rotAngle;
  bool  m_isLeft;
  bool  m_isParkSpace;
  bool  m_isParkUI;
  bool  m_isHoriScreen;
  AnimationStyle m_animationStyle;
  AnimationDir m_animationDir;
  osg::Vec2f m_iconSize;
};


//!
//! CustomIconData
//!
class CustomIconData
{
public:

  CustomIconData(std::string f_string, osg::Vec2f f_iconPos, osg::Vec2f f_iconSize)
    : m_iconPath(f_string)
    , m_iconPos(f_iconPos)
    , m_iconSize(f_iconSize)
    , m_enable(false)
    , m_added(false)
    , m_iconPtr(nullptr)
  {
  }

  CustomIconData(std::string f_string, osg::Vec2f f_iconPos)
    : m_iconPath(f_string)
    , m_iconPos(f_iconPos)
    , m_iconSize(0.0f, 0.0f)
    , m_enable(false)
    , m_added(false)
    , m_iconPtr(nullptr)
  {
  }

  void setEnabled(bool f_enable) { m_enable = f_enable; }
  const bool isEnabled() const { return m_enable; }
  void setAdded(bool f_added) { m_added = f_added; }
  const bool isAdded() const { return m_added; }
  const std::string getFilePath() const { return m_iconPath; }
  const osg::Vec2f getIconPos() const { return m_iconPos; }
//   const osg::Vec2f getIconSize(cc::target::common::EThemeTypeHU f_theme) const;

  void setIconSize(osg::Vec2f f_size) { m_iconSize = f_size; }
  void setIconPtr(pc::assets::Icon* f_iconPtr) { m_iconPtr = f_iconPtr; }
  pc::assets::Icon* getIconPtr() { return m_iconPtr; }

private:
  std::string  m_iconPath;
  osg::Vec2f   m_iconPos;
  osg::Vec2f   m_iconSize;
  bool         m_enable;
  bool         m_added;
  pc::assets::Icon* m_iconPtr;  // use for remove icon
};

//!
//! CustomIconGroup
//!
template<typename Key>
class CustomIconGroup
{
public:
  typedef std::map<Key, CustomIconData* > CustomIconDataList;

  CustomIconGroup() = default;

  void setAllEnabled(bool f_enable)
  {
    for (vfc::int32_t i = 0; i < m_icons.size(); i++)
    {
      m_icons[static_cast<Key>(i)]->setEnabled(f_enable);
    }
  }

  void addIcon(Key f_index, const std::string& f_filepath, osg::Vec2f f_iconPos)
  {
    printLogMessage(f_index);
    if (m_icons[f_index] != nullptr)
    {
      delete(m_icons[f_index]);
    }
    CustomIconData* l_iconData = new CustomIconData(f_filepath, f_iconPos);
    m_icons[f_index] = l_iconData;
  }

  void addIcon(Key f_index, const std::string& f_filepath, osg::Vec2f f_iconPos, osg::Vec2f f_iconSize)
  {
    printLogMessage(f_index);
    if (m_icons[f_index] != nullptr)
    {
      delete(m_icons[f_index]);
    }
    CustomIconData* l_iconData = new CustomIconData(f_filepath, f_iconPos, f_iconSize);
    m_icons[f_index] = l_iconData;
  }

  vfc::uint32_t getNumIcons() const
  {
    return static_cast<vfc::uint32_t>(m_icons.size());
  }

  CustomIconDataList getIconList() const
  {
    return m_icons;
  }

  CustomIconData* getIcon(Key f_index)
  {
    if (getNumIcons() > static_cast<vfc::uint32_t>(f_index))
    {
      return m_icons[f_index];
    }
    return nullptr;
  }

  virtual std::string getLabelFromKey(Key f_index) = 0;

protected:
  virtual void printLogMessage(Key f_index) = 0;

private:
  CustomIconDataList m_icons;
};

} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_CUSTOMICON_H
