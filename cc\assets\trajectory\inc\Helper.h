//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_HELPER_H
#define CC_ASSETS_TRAJECTORY_HELPER_H

#include "vfc/core/vfc_types.hpp"
#include <osg/Geometry>

namespace cc
{
namespace assets
{
namespace trajectory
{
namespace helper
{


enum RGBA_en
{
  R = 0,
  G = 1,
  B = 2,
  A = 3
};

class TrajectoryHelper
{
public:
    static void Rotate2DPoint(osg::Vec2f& f_PointToRotate, vfc::float32_t f_cos, vfc::float32_t f_sin);
    static void Rotate2DPoint(osg::Vec2f& f_PointToRotate, vfc::float32_t f_Angle);
    static void Rotate2DPoint(
        osg::Vec2f&       f_PointToRotate,
        const osg::Vec2f& f_PointToRotateAround,
        vfc::float32_t    f_cos,
        vfc::float32_t    f_sin);

    static void Rotate2DPoint(osg::Vec2f& f_PointToRotate, const osg::Vec2f& f_PointToRotateAround, vfc::float32_t f_Angle);

    static void SwapVerticesVec4f(osg::Vec4f& f_Vertex1, osg::Vec4f& f_Vertex2);

    static vfc::float32_t smoothstep(
        const vfc::float32_t f_out_0,
        const vfc::float32_t f_out_1,
        const vfc::float32_t f_in_0,
        const vfc::float32_t f_in_1,
        vfc::float32_t       f_in);

    static osg::Vec4ub smoothstep_Vec4ub(
        const osg::Vec4ub&   f_out_0,
        const osg::Vec4ub&   f_out_1,
        const vfc::float32_t f_in_0,
        const vfc::float32_t f_in_1,
        vfc::float32_t       f_in);
    static osg::Vec4ub smoothstep_GGX_Vec4ub(
        const osg::Vec4ub&   f_out_0,
        const osg::Vec4ub&   f_out_1,
        const vfc::float32_t f_in_0,
        const vfc::float32_t f_in_1,
        vfc::float32_t       f_in);
    static osg::Vec4ub smoothstep_Vec4fIn_Vec4ubOut(
        const osg::Vec4f&    f_out_0,
        const osg::Vec4f&    f_out_1,
        const vfc::float32_t f_in_0,
        const vfc::float32_t f_in_1,
        vfc::float32_t       f_in);
    static osg::Vec4f smoothstep_Vec4f(
        const osg::Vec4f&    f_out_0,
        const osg::Vec4f&    f_out_1,
        const vfc::float32_t f_in_0,
        const vfc::float32_t f_in_1,
        vfc::float32_t       f_in);
    static bool isInfinity(vfc::float32_t f_value);
};

class RotationFunctor
{
public:

  RotationFunctor(vfc::float32_t f_angle);

  void rotate(osg::Vec2f& f_PointToRotate) const;
  void rotate(osg::Vec2f& f_PointToRotate, const osg::Vec2f& f_PointToRotateAround) const;

private:

  vfc::float32_t m_cos;
  vfc::float32_t m_sin;
};


class Comparator
{
public:
  Comparator();
  virtual ~Comparator();
  virtual bool Compare(vfc::float32_t f_A, vfc::float32_t f_B) const = 0;
};

class A_LessThan_B : public Comparator
{
  virtual bool Compare(vfc::float32_t f_A, vfc::float32_t f_B) const;
};

class A_GreaterThan_B : public Comparator
{
  virtual bool Compare(vfc::float32_t f_A, vfc::float32_t f_B) const;
};


} // namespace helper
} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_HELPER_H
