//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>sch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "pc/svs/util/osgx/inc/Quantization.h"
#include "cc/assets/trajectory/inc/OutermostLine.h"
#include "cc/assets/trajectory/inc/Helper.h"

/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3804
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace trajectory
{

// This multiplier is to widen the quad stripe to have enough room for the blur on the downsampled mipmaps.
constexpr vfc::float32_t g_extraWidthForBlurMul = 1.2f; // (1 <= )

/**
 * Calculates the parameter for OutermostLine on the f_side it is and pass them to Frame.cpp to generate the vertices for the geometry
 * @param f_side: If it is for the OutermostLine on the right or left side
 * @param f_renderBinOrder: RenderOrdere so it will be rendered on top of the baseplate
 * @param f_trajParams: General const parameters for all trackTypes that are given by TrajectoryCodingParams with CodingParamters.xml
 * @param f_DIDescriptor: General const parameters for the DistanceIndicators also given by TrajectoryCodingParams with CodingParamters.xml
 * @param f_numOfVerts_BeforeDIs/f_numOfVerts_BetweenDIs/f_numOfVerts_AfterDIs:
 *                Number of vertices to generate the geometry afterwards: Before/Between/After the indicators on the OutermostLine
 */
OutermostLine::OutermostLine(
  pc::core::Framework* f_framework,
  cc::assets::trajectory::commontypes::Side_en f_side,
  vfc::float32_t f_height,
  const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
  const DIDescriptor_st& f_DIDescriptor,
  vfc::uint32_t f_numOfVerts_BeforeDIs,
  vfc::uint32_t f_numOfVerts_BetweenDIs,
  vfc::uint32_t f_numOfVerts_AfterDIs)
  : GeneralTrajectoryLine(
      f_framework,
      f_side,
      5u,
      f_height,
      f_trajParams,
      true)
  , m_DIDescriptor(f_DIDescriptor)
  , mc_numOfDIs(static_cast<vfc::uint32_t> (f_DIDescriptor.DIs.size()))
  , mc_numOfVerts_BeforeDIs (f_numOfVerts_BeforeDIs)
  , mc_numOfVerts_BetweenDIs(f_numOfVerts_BetweenDIs)
  , mc_numOfVerts_AfterDIs  (f_numOfVerts_AfterDIs)
  , m_DIGeometryWidths(f_DIDescriptor.DIs.size())
  , m_DIGeometryLength(0.01f) // Default value
  , m_visibleOuterRadius{}
  , m_visibleOuterLateralOffset{}
{
  m_geometry->addPrimitiveSet(new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES)));  // PRQA S 3804  // PRQA S 3803

  osg::Vec2ubArray* const l_texCoords = new osg::Vec2ubArray;
  l_texCoords->setNormalize(true);
  m_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);

  //! init static data
  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray());
  l_colors->setBinding(osg::Array::BIND_PER_VERTEX);

  const vfc::float32_t lc_halfWidth           = std::abs(m_trajParams.OutermostLine_Width) * 0.5f;
  const vfc::float32_t lc_halfGradientWidth   = std::abs(m_trajParams.GradientWidth) * 0.5f;
  const vfc::float32_t lc_gradientOuterEndPos = lc_halfWidth + lc_halfGradientWidth; // Measured from the middle line (right side of the image)
  const vfc::float32_t lc_halfGeometryWidth   = lc_gradientOuterEndPos * g_extraWidthForBlurMul;
  m_lineGeometryWidth                = lc_halfGeometryWidth * 2.0f;

  if (0u < mc_numOfDIs)
  {
    m_DIGeometryLength = m_DIDescriptor.DILength;
    if (0.01f > m_DIGeometryLength)
    {
      m_DIGeometryLength = 0.01f; // Just to avoid faces with zero area.
    }

    for (vfc::uint32_t l_DIIndex = 0u; l_DIIndex < mc_numOfDIs; ++l_DIIndex)
    {
      m_DIGeometryWidths[l_DIIndex] = (m_DIDescriptor.DIs[l_DIIndex].Thickness + m_trajParams.GradientWidth) * g_extraWidthForBlurMul;
      if (m_lineGeometryWidth > m_DIGeometryWidths[l_DIIndex])
      {
        m_DIGeometryWidths[l_DIIndex] = m_lineGeometryWidth;
      }
    }
  }
}


OutermostLine::~OutermostLine() = default;


osg::Image* OutermostLine::create1DTexture() const
{
  constexpr vfc::uint32_t  lc_imageWidth  = 64u; // Image width in pixels.
  constexpr vfc::uint32_t  lc_imageHeight = 1u;  // Image height in pixels.
  constexpr vfc::uint32_t  lc_imageDepth  = 1u;  // Image depth in pixels, in case of a 3D image.
  constexpr vfc::float32_t         lc_imageGeometryWidth = static_cast<vfc::float32_t>(lc_imageWidth - 1);

  const vfc::float32_t lc_halfWidth           = std::abs(m_trajParams.OutermostLine_Width) * 0.5f;
  const vfc::float32_t lc_halfGradientWidth   = std::abs(m_trajParams.GradientWidth) * 0.5f;
  const vfc::float32_t lc_gradientInnerEndPos = lc_halfWidth - lc_halfGradientWidth; // Measured from the middle line (right side of the image)
  const vfc::float32_t lc_gradientOuterEndPos = lc_halfWidth + lc_halfGradientWidth; // Measured from the middle line (right side of the image)

  const vfc::float32_t lc_gradientInnerEndPos_Normalized_Right = lc_gradientInnerEndPos / m_lineGeometryWidth + 0.5f; // Measured from the right side of the image.
  const vfc::float32_t lc_gradientOuterEndPos_Normalized_Right = lc_gradientOuterEndPos / m_lineGeometryWidth + 0.5f; // Measured from the right side of the image.
  const vfc::float32_t lc_gradientInnerEndPos_Normalized_Left = 1.0f - lc_gradientInnerEndPos_Normalized_Right;   // Measured from the left side of the image.
  const vfc::float32_t lc_gradientOuterEndPos_Normalized_Left = 1.0f - lc_gradientOuterEndPos_Normalized_Right;   // Measured from the left side of the image.

  const osg::Vec4ub l_lineColor_Inside = osg::Vec4ub(
    255u,
    255u,
    255u,
    pc::util::osgx::toUByte(m_trajParams.OutermostLine_Color_Manual.a()));

  osg::Vec4ub l_lineColor_Outside = l_lineColor_Inside;
  l_lineColor_Outside.a() = 0u;

  osg::Image* const l_image = new osg::Image;
  l_image->allocateImage(lc_imageWidth, lc_imageHeight, lc_imageDepth, GL_RGBA, GL_UNSIGNED_BYTE); //PRQA S 3143
  osg::Vec4ub* l_data = reinterpret_cast<osg::Vec4ub*> (l_image->data());
  for (vfc::uint32_t x = 0u; x < lc_imageWidth; ++x)
  {
    const vfc::float32_t l_x_normalized = static_cast<vfc::float32_t>(x) / lc_imageGeometryWidth;

    if ( (l_x_normalized < lc_gradientOuterEndPos_Normalized_Left)
      || (l_x_normalized > lc_gradientOuterEndPos_Normalized_Right) )
    {
      (*l_data) = l_lineColor_Outside;
    }
    else if ( (l_x_normalized > lc_gradientInnerEndPos_Normalized_Left)
           && (l_x_normalized < lc_gradientInnerEndPos_Normalized_Right) )
    {
      (*l_data) = l_lineColor_Inside;
    }
    else
    {
      if (l_x_normalized < lc_gradientInnerEndPos_Normalized_Left)
      {
        (*l_data) = cc::assets::trajectory::helper::TrajectoryHelper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_Outside, l_lineColor_Inside, lc_gradientOuterEndPos_Normalized_Left, lc_gradientInnerEndPos_Normalized_Left, l_x_normalized);
      }
      else
      {
        (*l_data) = cc::assets::trajectory::helper::TrajectoryHelper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_Inside, l_lineColor_Outside, lc_gradientInnerEndPos_Normalized_Right, lc_gradientOuterEndPos_Normalized_Right, l_x_normalized);
      }
    }
    ++l_data;
  }
  return l_image;
}


void OutermostLine::generateVertexData()
{
  generateVertexData_usingTexture();
}


void OutermostLine::getOuterRadiusAndOffset(vfc::float32_t& f_visibleOuterRadius, vfc::float32_t& f_visibleOuterLateralOffset) const
{
  f_visibleOuterRadius        = m_visibleOuterRadius;
  f_visibleOuterLateralOffset = m_visibleOuterLateralOffset;
}


void OutermostLine::generateVertexData_usingTexture()  // PRQA S 6040  // PRQA S 6041  // PRQA S 6044
{
  // *** 1. Create frame ***
  m_frame.removeAllPoints();

  vfc::float32_t l_touchPointAngle = 0.0f;
  vfc::float32_t l_touchPointLongitudinalPos = 0.0f;
  const vfc::float32_t lc_halfGeometryWidth = m_lineGeometryWidth * 0.5f;
  const vfc::float32_t lc_halfWidth = m_trajParams.OutermostLine_Width * 0.5f;
  const vfc::float32_t lc_touchPointToGeometryOuterEdge = lc_halfGeometryWidth - lc_halfWidth;
  const vfc::float32_t lc_touchPointToGeometryInnerEdge = m_lineGeometryWidth - lc_touchPointToGeometryOuterEdge;
  const vfc::float32_t lc_halfWheeltrackWidth = m_trajParams.WheelTrack_Width_Whole * 0.5f; // Half of the whole width of the wheel track
  const vfc::float32_t lc_halfGradientWidth = std::abs(m_trajParams.GradientWidth) * 0.5f;

  if (cc::assets::trajectory::commontypes::Rotation_enm == sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType)
  {
    // When it's rotation
    if (cc::assets::trajectory::commontypes::Left_enm == mc_side)
    {
      // Left outermost line:
      m_frameRadiuses[0u] = sm_mainLogicRefPtr->getModelDataRef().LeftTouchPoint.Radius
                             + lc_touchPointToGeometryOuterEdge * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
      m_frameRadiuses[2u] = sm_mainLogicRefPtr->getModelDataRef().LeftTouchPoint.Radius
                             + lc_touchPointToGeometryInnerEdge * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
      // Middle line of the trajectory line (holds support points for the DI):
      m_frameRadiuses[1u] = (m_frameRadiuses[0u] + m_frameRadiuses[2u]) * 0.5f;
      // The inside of the inner edge of the DI's geometry:
      m_frameRadiuses[4u] = m_frameRadiuses[2u]
                             + m_DIGeometryLength * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
      // The outside of the inner edge of the DI's geometry:
      m_frameRadiuses[3u] = m_frameRadiuses[4u]
                             + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;

      m_visibleOuterRadius = sm_mainLogicRefPtr->getModelDataRef().LeftTouchPoint.Radius;

      // Clamping the outermost line radius to avoid overlapping with the wheel track
      if (sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint.y() > 0.0f)
      {
        // If the Ackermann point is on the left
        const vfc::float32_t l_radiusLimit = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Radius - lc_halfWheeltrackWidth - lc_halfGradientWidth - m_trajParams.OL_WT_minGap;
        const vfc::float32_t l_diff = l_radiusLimit - m_frameRadiuses[4u];
        if (l_diff < 0.0f)
        {
          m_frameRadiuses[0u]   += l_diff;
          m_frameRadiuses[1u]   += l_diff;
          m_frameRadiuses[2u]   += l_diff;
          m_frameRadiuses[3u]   += l_diff;
          m_frameRadiuses[4u]   += l_diff;
          m_visibleOuterRadius += l_diff;
        }
      }
      else
      {
        // If the Ackermann point is on the right
        const vfc::float32_t l_radiusLimit = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Radius + lc_halfWheeltrackWidth + lc_halfGradientWidth + m_trajParams.OL_WT_minGap;
        const vfc::float32_t l_diff = l_radiusLimit - m_frameRadiuses[4u];
        if (l_diff > 0.0f)
        {
          m_frameRadiuses[0u]   += l_diff;
          m_frameRadiuses[1u]   += l_diff;
          m_frameRadiuses[2u]   += l_diff;
          m_frameRadiuses[3u]   += l_diff;
          m_frameRadiuses[4u]   += l_diff;
          m_visibleOuterRadius += l_diff;
        }
      }

      m_frame.setVertexLineRadius(0u, m_frameRadiuses[0u]);
      m_frame.setVertexLineRadius(1u, m_frameRadiuses[1u]);
      m_frame.setVertexLineRadius(2u, m_frameRadiuses[2u]);
      m_frame.setVertexLineRadius(3u, m_frameRadiuses[3u]);
      m_frame.setVertexLineRadius(4u, m_frameRadiuses[4u]);
      l_touchPointAngle = sm_mainLogicRefPtr->getModelDataRef().LeftTouchPoint.Angle;
    }
    else
    {
      // Right outermost line:
      m_frameRadiuses[4u] = sm_mainLogicRefPtr->getModelDataRef().RightTouchPoint.Radius
                             + lc_touchPointToGeometryOuterEdge * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
      m_frameRadiuses[2u] = sm_mainLogicRefPtr->getModelDataRef().RightTouchPoint.Radius
                             + lc_touchPointToGeometryInnerEdge * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
      // Middle line of the trajectory line (holds support points for the DI):
      m_frameRadiuses[3u] = (m_frameRadiuses[4u] + m_frameRadiuses[2u]) * 0.5f;
      // The inside of the inner edge of the DI's geometry:
      m_frameRadiuses[0u] = m_frameRadiuses[2u]
                             + m_DIGeometryLength * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
      // The outside of the inner edge of the DI's geometry:
      m_frameRadiuses[1u] = m_frameRadiuses[0u]
                             + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

      m_visibleOuterRadius = sm_mainLogicRefPtr->getModelDataRef().RightTouchPoint.Radius;

      // Clamping the outermost line radius to avoid overlapping with the wheel track
      if (sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint.y() > 0.0f)
      {
        // If the Ackermann point is on the left
        const vfc::float32_t l_radiusLimit = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Radius + lc_halfWheeltrackWidth + lc_halfGradientWidth + m_trajParams.OL_WT_minGap;
        const vfc::float32_t l_diff = l_radiusLimit - m_frameRadiuses[0u];
        if (l_diff > 0.0f)
        {
          m_frameRadiuses[0u]   += l_diff;
          m_frameRadiuses[1u]   += l_diff;
          m_frameRadiuses[2u]   += l_diff;
          m_frameRadiuses[3u]   += l_diff;
          m_frameRadiuses[4u]   += l_diff;
          m_visibleOuterRadius += l_diff;
        }
      }
      else
      {
        // If the Ackermann point is on the right
        const vfc::float32_t l_radiusLimit = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Radius - lc_halfWheeltrackWidth - lc_halfGradientWidth - m_trajParams.OL_WT_minGap;
        const vfc::float32_t l_diff = l_radiusLimit - m_frameRadiuses[0u];
        if (l_diff < 0.0f)
        {
          m_frameRadiuses[0u]   += l_diff;
          m_frameRadiuses[1u]   += l_diff;
          m_frameRadiuses[2u]   += l_diff;
          m_frameRadiuses[3u]   += l_diff;
          m_frameRadiuses[4u]   += l_diff;
          m_visibleOuterRadius += l_diff;
        }
      }


      m_frame.setVertexLineRadius(0u, m_frameRadiuses[4u]);
      m_frame.setVertexLineRadius(1u, m_frameRadiuses[3u]);
      m_frame.setVertexLineRadius(2u, m_frameRadiuses[2u]);
      m_frame.setVertexLineRadius(3u, m_frameRadiuses[1u]);
      m_frame.setVertexLineRadius(4u, m_frameRadiuses[0u]);
      l_touchPointAngle = sm_mainLogicRefPtr->getModelDataRef().RightTouchPoint.Angle;
    }
  }
  else
  {
    // When it's translation
    if (cc::assets::trajectory::commontypes::Left_enm == mc_side)
    {
      // Left outermost line:
      m_frameLateralOffsets[0u] = sm_mainLogicRefPtr->getModelDataRef().LeftTouchPoint.Pos.y()
                                   + lc_touchPointToGeometryOuterEdge * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
      m_frameLateralOffsets[2u] = sm_mainLogicRefPtr->getModelDataRef().LeftTouchPoint.Pos.y()
                                   + lc_touchPointToGeometryInnerEdge * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
      // Middle line of the trajectory line (holds support points for the DI):
      m_frameLateralOffsets[1u] = (m_frameLateralOffsets[0u] + m_frameLateralOffsets[2u]) * 0.5f;
      // The inside of the inner edge of the DI's geometry:
      m_frameLateralOffsets[4u] = m_frameLateralOffsets[2u]
                                   + m_DIGeometryLength * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
      // The outside of the inner edge of the DI's geometry:
      m_frameLateralOffsets[3u] = m_frameLateralOffsets[4u]
                                   + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;

      m_visibleOuterLateralOffset = sm_mainLogicRefPtr->getModelDataRef().LeftTouchPoint.Pos.y();

      // Clamping the outermost line y pos to avoid overlapping with the wheel track
      const vfc::float32_t l_yLimit = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y() + lc_halfWheeltrackWidth + lc_halfGradientWidth + m_trajParams.OL_WT_minGap;
      const vfc::float32_t l_diff = l_yLimit - m_frameLateralOffsets[4u];
      if (l_diff > 0.0f)
      {
        m_frameLateralOffsets[0u]    += l_diff;
        m_frameLateralOffsets[1u]    += l_diff;
        m_frameLateralOffsets[2u]    += l_diff;
        m_frameLateralOffsets[3u]    += l_diff;
        m_frameLateralOffsets[4u]    += l_diff;
        m_visibleOuterLateralOffset += l_diff;
      }

      m_frame.setVertexLineOffset(0u, m_frameLateralOffsets[0u]);
      m_frame.setVertexLineOffset(1u, m_frameLateralOffsets[1u]);
      m_frame.setVertexLineOffset(2u, m_frameLateralOffsets[2u]);
      m_frame.setVertexLineOffset(3u, m_frameLateralOffsets[3u]);
      m_frame.setVertexLineOffset(4u, m_frameLateralOffsets[4u]);
      l_touchPointLongitudinalPos = sm_mainLogicRefPtr->getModelDataRef().LeftTouchPoint.Pos.x();
    }
    else
    {
      // Right outermost line:
      m_frameLateralOffsets[4u] = sm_mainLogicRefPtr->getModelDataRef().RightTouchPoint.Pos.y()
                                   + lc_touchPointToGeometryOuterEdge * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
      m_frameLateralOffsets[2u] = sm_mainLogicRefPtr->getModelDataRef().RightTouchPoint.Pos.y()
                                   + lc_touchPointToGeometryInnerEdge * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
      // Middle line of the trajectory line (holds support points for the DI):
      m_frameLateralOffsets[3u] = (m_frameLateralOffsets[4u] + m_frameLateralOffsets[2u]) * 0.5f;
      // The inside of the inner edge of the DI's geometry:
      m_frameLateralOffsets[0u] = m_frameLateralOffsets[2u]
                                   + m_DIGeometryLength * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
      // The outside of the inner edge of the DI's geometry:
      m_frameLateralOffsets[1u] = m_frameLateralOffsets[0u]
                                   + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

      m_visibleOuterLateralOffset = sm_mainLogicRefPtr->getModelDataRef().RightTouchPoint.Pos.y();

      // Clamping the outermost line y pos to avoid overlapping with the wheel track
      const vfc::float32_t l_yLimit = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y() - lc_halfWheeltrackWidth - lc_halfGradientWidth - m_trajParams.OL_WT_minGap;
      const vfc::float32_t l_diff = l_yLimit - m_frameLateralOffsets[0u];
      if (l_diff < 0.0f)
      {
        m_frameLateralOffsets[0u]    += l_diff;
        m_frameLateralOffsets[1u]    += l_diff;
        m_frameLateralOffsets[2u]    += l_diff;
        m_frameLateralOffsets[3u]    += l_diff;
        m_frameLateralOffsets[4u]    += l_diff;
        m_visibleOuterLateralOffset += l_diff;
      }

      m_frame.setVertexLineOffset(0u, m_frameLateralOffsets[4u]);
      m_frame.setVertexLineOffset(1u, m_frameLateralOffsets[3u]);
      m_frame.setVertexLineOffset(2u, m_frameLateralOffsets[2u]);
      m_frame.setVertexLineOffset(3u, m_frameLateralOffsets[1u]);
      m_frame.setVertexLineOffset(4u, m_frameLateralOffsets[0u]);
      l_touchPointLongitudinalPos = sm_mainLogicRefPtr->getModelDataRef().RightTouchPoint.Pos.x();
    }
  }
  m_frame.setBumperLineAngle(0u, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Angle);
  m_frame.setBumperLinePos  (0u, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Pos.x());

  osg::Vec4f l_lineColor;
  if (sm_mainLogicRefPtr->getInputDataRef().External.Parking.AutomaticParking)
  {
    l_lineColor = m_trajParams.OutermostLine_Color_Auto;
  }
  else
  {
    l_lineColor = m_trajParams.OutermostLine_Color_Manual;
  }
  l_lineColor.a() = 1.0f;

  cc::assets::trajectory::commontypes::ControlPoint_st l_controlPoint;
  const vfc::float32_t l_startAngle = l_touchPointAngle;
  const vfc::float32_t l_endAngle =
      m_frame.getBumperLineAngle(0u)
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        * (m_trajParams.Length / sm_mainLogicRefPtr->getModelDataRef().BumperCenterPoint.Radius);
  const vfc::float32_t l_startPos = l_touchPointLongitudinalPos;
  const vfc::float32_t l_endPos =
      m_frame.getBumperLinePos(0u)
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        * m_trajParams.Length;
  l_controlPoint.Angle = l_startAngle;
  l_controlPoint.LongitudinalPos = l_startPos;
  l_controlPoint.Color = l_lineColor;
  l_controlPoint.Index = 0u; // Dummy value. Will be calculated later.
  m_frame.addControlPoint(0u, l_controlPoint);
  m_frame.addControlPoint(2u, l_controlPoint);

  vfc::float32_t l_tempAngle =
      l_controlPoint.Angle
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * (1.3f / m_frame.getVertexLineRadius(0u));
  vfc::float32_t l_tempPos =
      l_controlPoint.LongitudinalPos
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * 1.3f;
  m_frame.setFadeInStartAngle(0u, l_tempAngle);
  m_frame.setFadeInStartAngle(1u, l_tempAngle);
  m_frame.setFadeInStartAngle(2u, l_tempAngle);
  m_frame.setFadeInStartAngle(3u, l_tempAngle);
  m_frame.setFadeInStartAngle(4u, l_tempAngle);

  m_frame.setFadeInStartPos(0u, l_tempPos);
  m_frame.setFadeInStartPos(1u, l_tempPos);
  m_frame.setFadeInStartPos(2u, l_tempPos);
  m_frame.setFadeInStartPos(3u, l_tempPos);
  m_frame.setFadeInStartPos(4u, l_tempPos);

  m_frame.setFadeInEndAngle(0u, l_controlPoint.Angle);
  m_frame.setFadeInEndAngle(1u, l_controlPoint.Angle);
  m_frame.setFadeInEndAngle(2u, l_controlPoint.Angle);
  m_frame.setFadeInEndAngle(3u, l_controlPoint.Angle);
  m_frame.setFadeInEndAngle(4u, l_controlPoint.Angle);

  m_frame.setFadeInEndPos(0u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeInEndPos(1u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeInEndPos(2u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeInEndPos(3u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeInEndPos(4u, l_controlPoint.LongitudinalPos);


  vfc::uint32_t l_dynamicNumOfDIs = 0u;
  if (0u < mc_numOfDIs)
  {
    if (cc::assets::trajectory::commontypes::Rotation_enm == sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType)
    {
      l_dynamicNumOfDIs = getDynamicNumOfDIs(m_frame.getBumperLineAngle(0u), l_endAngle);
      for (vfc::uint32_t l_DIindex = 0u; l_DIindex < l_dynamicNumOfDIs; ++l_DIindex)
      {
        const vfc::float32_t l_distIndAngle = m_frame.getBumperLineAngle(0u)
          + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
          * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
          * (m_DIDescriptor.DIs[l_DIindex].Pos / sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Radius);
        addDistIndPoints_Tex(
            l_distIndAngle, m_DIGeometryWidths[l_DIindex],
            m_frame, sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection, sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul);
      }
    }
    else// if (Translation_enm == sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType)
    {
      l_dynamicNumOfDIs = getDynamicNumOfDIs_Straight(m_frame.getBumperLinePos(0u), l_endPos);
      for (vfc::uint32_t l_DIindex = 0u; l_DIindex < l_dynamicNumOfDIs; ++l_DIindex)
      {
        // The longitudinal positions for the DI
        const vfc::float32_t l_distIndPos = m_frame.getBumperLinePos(0u)
          + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
          * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
          * m_DIDescriptor.DIs[l_DIindex].Pos;
        addDistIndPoints_Tex_Straight(
            l_distIndPos, m_DIGeometryWidths[l_DIindex],
            m_frame, sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection, sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul);
      }
    }
  }

  l_controlPoint.Angle = l_endAngle;
  l_controlPoint.LongitudinalPos = l_endPos;
  l_controlPoint.Color = l_lineColor;
  m_frame.addControlPoint(0u, l_controlPoint);
  m_frame.addControlPoint(2u, l_controlPoint);

  l_tempAngle = m_frame.getBumperLineAngle(0u)
      + 0.5f * (l_controlPoint.Angle - m_frame.getBumperLineAngle(0u));
  l_tempPos = m_frame.getBumperLinePos(0u)
      + 0.5f * (l_controlPoint.LongitudinalPos - m_frame.getBumperLinePos(0u));
  m_frame.setFadeOutStartAngle(0u, l_tempAngle);
  m_frame.setFadeOutStartAngle(1u, l_tempAngle);
  m_frame.setFadeOutStartAngle(2u, l_tempAngle);
  m_frame.setFadeOutStartAngle(3u, l_tempAngle);
  m_frame.setFadeOutStartAngle(4u, l_tempAngle);

  m_frame.setFadeOutStartPos(0u, l_tempPos);
  m_frame.setFadeOutStartPos(1u, l_tempPos);
  m_frame.setFadeOutStartPos(2u, l_tempPos);
  m_frame.setFadeOutStartPos(3u, l_tempPos);
  m_frame.setFadeOutStartPos(4u, l_tempPos);

  m_frame.setFadeOutEndAngle(0u, l_controlPoint.Angle);
  m_frame.setFadeOutEndAngle(1u, l_controlPoint.Angle);
  m_frame.setFadeOutEndAngle(2u, l_controlPoint.Angle);
  m_frame.setFadeOutEndAngle(3u, l_controlPoint.Angle);
  m_frame.setFadeOutEndAngle(4u, l_controlPoint.Angle);

  m_frame.setFadeOutEndPos(0u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeOutEndPos(1u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeOutEndPos(2u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeOutEndPos(3u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeOutEndPos(4u, l_controlPoint.LongitudinalPos);

  // *** 2. Create vertices (and colors) ***
  // Generate line vertices
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (m_geometry->getVertexArray());
  l_vertices->clear();

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray());
  l_colors->clear();

  osg::Vec2ubArray* const l_texCoords = static_cast<osg::Vec2ubArray*> (m_geometry->getTexCoordArray(0u));
  l_texCoords->clear();

  // Vertex amounts for the consecutive vertex lines (segments). I.e.: in between, before and after the DIs.
  std::vector<vfc::uint32_t> l_numOfVerts(mc_numOfDIs + 1u);
  l_numOfVerts[0u] = mc_numOfVerts_BeforeDIs;
  for (vfc::uint32_t l_segmentIndex = 1u; l_segmentIndex < l_dynamicNumOfDIs; ++l_segmentIndex)
  {
    l_numOfVerts[l_segmentIndex] = mc_numOfVerts_BetweenDIs;
  }
  l_numOfVerts[l_dynamicNumOfDIs] = mc_numOfVerts_AfterDIs;

  const vfc::float32_t l_translationAngle_Rad = osg::DegreesToRadians(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle);
  static_cast<void>(m_frame.generateVertices(
      0u, 0u, 1u, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
      l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm,
      l_numOfVerts[0u], sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
      l_translationAngle_Rad));
  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < l_numOfVerts[0u]; ++l_vertexIndex)
  {
    l_texCoords->push_back(osg::Vec2ub(0u, 127u));
  }
  for (vfc::uint32_t l_DIindex = 0u; l_DIindex < l_dynamicNumOfDIs; ++l_DIindex)
  {
    const vfc::uint32_t l_i2 = l_DIindex * 2u;
    m_frame.generateVertices(  // PRQA S 3803
        0u, 2u + l_i2, 3u + l_i2, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
        l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm,
        l_numOfVerts[l_DIindex + 1u], sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
        l_translationAngle_Rad);
    for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < l_numOfVerts[l_DIindex + 1u]; ++l_vertexIndex)
    {
      l_texCoords->push_back(osg::Vec2ub(0u, 127u));
    }
  }

  m_frame.generateVertices(  // PRQA S 3803
      2u, 0u, 1u, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
      l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm,
      l_numOfVerts[0u], sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
      l_translationAngle_Rad);
  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < l_numOfVerts[0u]; ++l_vertexIndex)
  {
    l_texCoords->push_back(osg::Vec2ub(255u, 127u));
  }
  for (vfc::uint32_t l_DIindex = 0u; l_DIindex < l_dynamicNumOfDIs; ++l_DIindex)
  {
    const vfc::uint32_t l_i2 = l_DIindex * 2u;
    m_frame.generateVertices(  // PRQA S 3803
        2u, 2u + l_i2, 3u + l_i2, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
        l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm,
        l_numOfVerts[l_DIindex + 1u], sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
        l_translationAngle_Rad);
    for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < l_numOfVerts[l_DIindex + 1u]; ++l_vertexIndex)
    {
      l_texCoords->push_back(osg::Vec2ub(255u, 127u));
    }
  }


  // Generate the rest of the DI vertices
  for (vfc::uint32_t l_DIindex = 0u; l_DIindex < l_dynamicNumOfDIs; ++l_DIindex)
  {
    const vfc::uint32_t l_i2 = l_DIindex * 2u;
    m_frame.generateVertices(  // PRQA S 3803
        1u, 0u + l_i2, 1u + l_i2, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
        l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm, 2u, sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
        l_translationAngle_Rad);
    for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < 2u; ++l_vertexIndex)
    {
      l_texCoords->push_back(osg::Vec2ub(127u, 127u));
    }
    m_frame.generateVertices(  // PRQA S 3803
        3u, 0u + l_i2, 1u + l_i2, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
        l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm, 2u, sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
        l_translationAngle_Rad);
    for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < 2u; ++l_vertexIndex)
    {
      l_texCoords->push_back(osg::Vec2ub(127u, 127u));
    }
    m_frame.generateVertices(  // PRQA S 3803
        4u, 0u + l_i2, 1u + l_i2, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
        l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm, 2u, sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
        l_translationAngle_Rad);
    for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < 2u; ++l_vertexIndex)
    {
      l_texCoords->push_back(osg::Vec2ub(255u, 127u));
    }
  }

  // *** 3-A. Create indices ***
  osg::DrawElementsUShort* const l_indices = static_cast<osg::DrawElementsUShort*> (m_geometry->getPrimitiveSet(0u));
  l_indices->clear();

  m_frame.generateIndices(0u, 0u, 2u, 0u, l_numOfVerts[0u], l_indices);
  for (vfc::uint32_t l_DIindex = 0u; l_DIindex < l_dynamicNumOfDIs; ++l_DIindex)
  {
    const vfc::uint32_t l_i2 = l_DIindex * 2u;
    m_frame.generateIndices(
        0u, 2u + l_i2, 2u, 2u + l_i2, l_numOfVerts[l_DIindex + 1u], l_indices);
  }


  // *** 3-B. Create DI indices ***
  for (vfc::uint32_t l_DIindex = 0u; l_DIindex < l_dynamicNumOfDIs; ++l_DIindex)
  {
    const vfc::uint32_t l_i2 = l_DIindex * 2u;
    m_frame.generateDIIndices_Tex(l_indices, mc_side,
                                  1u + l_i2, 2u + l_i2,
                                  0u + l_i2, 1u + l_i2,
                                  1u + l_i2, 2u + l_i2,
                                  0u + l_i2, 1u + l_i2,
                                  0u + l_i2, 1u + l_i2);
  }

  l_vertices->dirty();
  l_colors->dirty();
  l_texCoords->dirty();
  l_indices->dirty();
  m_geometry->dirtyBound();

  setCull(false);
}


vfc::uint32_t OutermostLine::getDynamicNumOfDIs(vfc::float32_t f_bumperLineAngle,
                                               vfc::float32_t f_endAngle)
{
  vfc::uint32_t l_dynamicNumOfDIs = 0u;
  cc::assets::trajectory::helper::Comparator * l_comp = nullptr;
  cc::assets::trajectory::helper::A_LessThan_B    l_Front_IsLessThan_Rear;
  cc::assets::trajectory::helper::A_GreaterThan_B l_Front_IsGreaterThan_Rear;

  if (cc::assets::trajectory::commontypes::ToLeft_enm == sm_mainLogicRefPtr->getInputDataRef().Internal.TurningDirection)
  {
    // If the Ackermann point is on the LEFT side. (Top view, Dyn70k.X up, Dyn70k.Y left)
    if (cc::assets::trajectory::mainlogic::Forward_enm == sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection)
    {
      l_comp = &l_Front_IsGreaterThan_Rear;
    }
    else// if (Backward_enm == m_inputs.External.Car.DrivingDirection)
    {
      l_comp = &l_Front_IsLessThan_Rear;
    }
  }
  else
  {
    // If the Ackermann point is on the RIGHT side. (Top view, Dyn70k.X up, Dyn70k.Y left)
    if (cc::assets::trajectory::mainlogic::Forward_enm == sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection)
    {
      l_comp = &l_Front_IsLessThan_Rear;
    }
    else// if (Backward_enm == m_inputs.External.Car.DrivingDirection)
    {
      l_comp = &l_Front_IsGreaterThan_Rear;
    }
  }

  assert(nullptr != l_comp);

  for (vfc::uint32_t l_DIindex = 0u; l_DIindex < mc_numOfDIs; ++l_DIindex)
  {
    const vfc::float32_t l_distIndAngle = f_bumperLineAngle
                     + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
                       * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
                       * (m_DIDescriptor.DIs[l_DIindex].Pos / sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Radius);
    const vfc::float32_t l_angleToOuterDIEdge = sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
                         * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
                         * ((m_DIDescriptor.DIs[l_DIindex].Thickness * 0.5f) / sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Radius);
    const vfc::float32_t l_angleToFurtherDIEdge = l_distIndAngle + l_angleToOuterDIEdge;

    if ((l_comp != nullptr) && l_comp->Compare(f_endAngle, l_angleToFurtherDIEdge))
    {
      ++l_dynamicNumOfDIs;
    }
  }

  return l_dynamicNumOfDIs;
}


vfc::uint32_t OutermostLine::getDynamicNumOfDIs_Straight(vfc::float32_t f_bumperLinePos,
                                                        vfc::float32_t f_endPos)
{
  vfc::uint32_t l_dynamicNumOfDIs = 0u;
  cc::assets::trajectory::helper::Comparator * l_comp = nullptr;
  cc::assets::trajectory::helper::A_LessThan_B    l_Front_IsLessThan_Rear;
  cc::assets::trajectory::helper::A_GreaterThan_B l_Front_IsGreaterThan_Rear;

  if (cc::assets::trajectory::mainlogic::Forward_enm == sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection)
  {
    l_comp = &l_Front_IsGreaterThan_Rear;
  }
  else// if (Backward_enm == f_drivingDir)
  {
    l_comp = &l_Front_IsLessThan_Rear;
  }

  assert(nullptr != l_comp);

  for (vfc::uint32_t l_DIindex = 0u; l_DIindex < mc_numOfDIs; ++l_DIindex)
  {
    const vfc::float32_t l_distIndPos = f_bumperLinePos
                   + sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
                     * m_DIDescriptor.DIs[l_DIindex].Pos;
    const vfc::float32_t l_posToOuterDIEdge = sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
                         * (m_DIDescriptor.DIs[l_DIindex].Thickness * 0.5f);
    const vfc::float32_t l_posToFurtherDIEdge = l_distIndPos + l_posToOuterDIEdge;

    if ((l_comp != nullptr) && l_comp->Compare(f_endPos, l_posToFurtherDIEdge))
    {
      ++l_dynamicNumOfDIs;
    }
  }

  return l_dynamicNumOfDIs;
}


void OutermostLine::addDistIndPoints_Tex(
    vfc::float32_t f_distIndAngle, vfc::float32_t f_distIndThickness, cc::assets::trajectory::frame::Frame & f_frame,
    cc::assets::trajectory::mainlogic::DrivingDirection_en f_drivingDir, vfc::float32_t f_leftRightDirMul)
{
  const vfc::float32_t l_halfDistIndThickness = f_distIndThickness * 0.5f;
  const vfc::float32_t l_halfLineGeometryWidth = m_lineGeometryWidth * 0.5f;
  std::array<vfc::uint32_t, 5> l_vertexLineIndices;
  std::array<vfc::uint32_t, 4> l_lateralLineIndices;

  l_vertexLineIndices[0u] = 0u;
  l_vertexLineIndices[1u] = 1u;
  l_vertexLineIndices[2u] = 2u;
  l_vertexLineIndices[3u] = 3u;
  l_vertexLineIndices[4u] = 4u;

  if (cc::assets::trajectory::mainlogic::Forward_enm == f_drivingDir)
  {
    l_lateralLineIndices[0u] = 0u;
    l_lateralLineIndices[1u] = 1u;
    l_lateralLineIndices[2u] = 2u;
    l_lateralLineIndices[3u] = 3u;
  }
  else // if (cc::assets::trajectory::mainlogic::Backward_enm == f_drivingDir)
  {
    l_lateralLineIndices[0u] = 3u;
    l_lateralLineIndices[1u] = 2u;
    l_lateralLineIndices[2u] = 1u;
    l_lateralLineIndices[3u] = 0u;
  }


  const vfc::float32_t l_angleToInnerPoints = (l_halfDistIndThickness - l_halfLineGeometryWidth)
                                 / f_frame.getVertexLineRadius(0u);
  const vfc::float32_t l_angleToOuterPoints = l_halfDistIndThickness
                                 / f_frame.getVertexLineRadius(0u);

  // Control points, from the nearest to the farthest.
  std::array<cc::assets::trajectory::commontypes::ControlPoint_st, 4> l_controlPoints;

  if (sm_mainLogicRefPtr->getInputDataRef().External.Parking.AutomaticParking)
  {
    l_controlPoints[0u].Color = m_trajParams.OutermostLine_Color_Auto;
  }
  else
  {
    l_controlPoints[0u].Color = m_trajParams.OutermostLine_Color_Manual;
  }
  l_controlPoints[0u].Color.a() = 1.0f;

  l_controlPoints[0u].Index = 0u; // Dummy value. Will be calculated later.

  l_controlPoints[1u] = l_controlPoints[0u];
  l_controlPoints[2u] = l_controlPoints[0u];
  l_controlPoints[3u] = l_controlPoints[0u];

  l_controlPoints[0u].Angle = f_distIndAngle - (f_leftRightDirMul * l_angleToOuterPoints);
  l_controlPoints[1u].Angle = f_distIndAngle - (f_leftRightDirMul * l_angleToInnerPoints);
  l_controlPoints[2u].Angle = f_distIndAngle + (f_leftRightDirMul * l_angleToInnerPoints);
  l_controlPoints[3u].Angle = f_distIndAngle + (f_leftRightDirMul * l_angleToOuterPoints);

  f_frame.addControlPoint(l_vertexLineIndices[0u], l_controlPoints[l_lateralLineIndices[0u]]);
  f_frame.addControlPoint(l_vertexLineIndices[0u], l_controlPoints[l_lateralLineIndices[3u]]);
  f_frame.addControlPoint(l_vertexLineIndices[1u], l_controlPoints[l_lateralLineIndices[1u]]);
  f_frame.addControlPoint(l_vertexLineIndices[1u], l_controlPoints[l_lateralLineIndices[2u]]);
  f_frame.addControlPoint(l_vertexLineIndices[2u], l_controlPoints[l_lateralLineIndices[0u]]);
  f_frame.addControlPoint(l_vertexLineIndices[2u], l_controlPoints[l_lateralLineIndices[3u]]);
  f_frame.addControlPoint(l_vertexLineIndices[3u], l_controlPoints[l_lateralLineIndices[1u]]);
  f_frame.addControlPoint(l_vertexLineIndices[3u], l_controlPoints[l_lateralLineIndices[2u]]);
  f_frame.addControlPoint(l_vertexLineIndices[4u], l_controlPoints[l_lateralLineIndices[0u]]);
  f_frame.addControlPoint(l_vertexLineIndices[4u], l_controlPoints[l_lateralLineIndices[3u]]);
}


void OutermostLine::addDistIndPoints_Tex_Straight(
    vfc::float32_t f_distIndPos, vfc::float32_t f_distIndThickness, cc::assets::trajectory::frame::Frame & f_frame,
    cc::assets::trajectory::mainlogic::DrivingDirection_en f_drivingDir, vfc::float32_t f_leftRightDirMul)
{
  const vfc::float32_t l_halfDistIndThickness = f_distIndThickness * 0.5f;
  const vfc::float32_t l_halfLineGeometryWidth = m_lineGeometryWidth * 0.5f;
  std::array<vfc::uint32_t, 5> l_vertexLineIndices;
  std::array<vfc::uint32_t, 4> l_lateralLineIndices;

  l_vertexLineIndices[0u] = 0u;
  l_vertexLineIndices[1u] = 1u;
  l_vertexLineIndices[2u] = 2u;
  l_vertexLineIndices[3u] = 3u;
  l_vertexLineIndices[4u] = 4u;

  if (cc::assets::trajectory::mainlogic::Forward_enm == f_drivingDir)
  {
    l_lateralLineIndices[0u] = 0u;
    l_lateralLineIndices[1u] = 1u;
    l_lateralLineIndices[2u] = 2u;
    l_lateralLineIndices[3u] = 3u;
  }
  else // if (cc::assets::trajectory::mainlogic::Backward_enm == f_drivingDir)
  {
    l_lateralLineIndices[0u] = 3u;
    l_lateralLineIndices[1u] = 2u;
    l_lateralLineIndices[2u] = 1u;
    l_lateralLineIndices[3u] = 0u;
  }


  const vfc::float32_t l_distanceToInnerPoints = l_halfDistIndThickness - l_halfLineGeometryWidth;
  const vfc::float32_t l_distanceToOuterPoints = l_halfDistIndThickness;

  // Control points, from the nearest to the farthest.
  std::array<cc::assets::trajectory::commontypes::ControlPoint_st, 4> l_controlPoints;

  if (sm_mainLogicRefPtr->getInputDataRef().External.Parking.AutomaticParking)
  {
    l_controlPoints[0u].Color = m_trajParams.OutermostLine_Color_Auto;
  }
  else
  {
    l_controlPoints[0u].Color = m_trajParams.OutermostLine_Color_Manual;
  }
  l_controlPoints[0u].Color.a() = 1.0f;

  l_controlPoints[0u].Index = 0u; // Dummy value. Will be calculated later.

  l_controlPoints[1u] = l_controlPoints[0u];
  l_controlPoints[2u] = l_controlPoints[0u];
  l_controlPoints[3u] = l_controlPoints[0u];

  l_controlPoints[0u].LongitudinalPos = f_distIndPos - (f_leftRightDirMul * l_distanceToOuterPoints);
  l_controlPoints[1u].LongitudinalPos = f_distIndPos - (f_leftRightDirMul * l_distanceToInnerPoints);
  l_controlPoints[2u].LongitudinalPos = f_distIndPos + (f_leftRightDirMul * l_distanceToInnerPoints);
  l_controlPoints[3u].LongitudinalPos = f_distIndPos + (f_leftRightDirMul * l_distanceToOuterPoints);

  f_frame.addControlPoint(l_vertexLineIndices[0u], l_controlPoints[l_lateralLineIndices[0u]]);
  f_frame.addControlPoint(l_vertexLineIndices[0u], l_controlPoints[l_lateralLineIndices[3u]]);
  f_frame.addControlPoint(l_vertexLineIndices[1u], l_controlPoints[l_lateralLineIndices[1u]]);
  f_frame.addControlPoint(l_vertexLineIndices[1u], l_controlPoints[l_lateralLineIndices[2u]]);
  f_frame.addControlPoint(l_vertexLineIndices[2u], l_controlPoints[l_lateralLineIndices[0u]]);
  f_frame.addControlPoint(l_vertexLineIndices[2u], l_controlPoints[l_lateralLineIndices[3u]]);
  f_frame.addControlPoint(l_vertexLineIndices[3u], l_controlPoints[l_lateralLineIndices[1u]]);
  f_frame.addControlPoint(l_vertexLineIndices[3u], l_controlPoints[l_lateralLineIndices[2u]]);
  f_frame.addControlPoint(l_vertexLineIndices[4u], l_controlPoints[l_lateralLineIndices[0u]]);
  f_frame.addControlPoint(l_vertexLineIndices[4u], l_controlPoints[l_lateralLineIndices[3u]]);
}


} // namespace trajectory
} // namespace assets
} // namespace cc
