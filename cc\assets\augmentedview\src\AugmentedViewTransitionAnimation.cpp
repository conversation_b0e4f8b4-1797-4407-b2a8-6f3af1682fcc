//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/augmentedview/inc/AugmentedViewTransition.h"
#include "cc/assets/augmentedview/inc/TransitionWaveAnimation.h"
#include "cc/assets/augmentedview/inc/PeriodicScanAnimation.h" // PRQA S 1060


namespace cc
{
namespace assets
{
namespace augmentedview
{

AugmentedViewTransition::AugmentedViewTransitionAnimation::AugmentedViewTransitionAnimation(bool f_toAugmented, bool f_instantSwitch, AugmentedViewTransition* f_augmentedViewTransition)
: m_toAugmented(f_toAugmented), // PRQA S 2323 // PRQA S 4052
  m_instantSwitch(f_instantSwitch), // PRQA S 2323
  m_augmentedViewTransition(f_augmentedViewTransition) // PRQA S 2323
{
}


vfc::float32_t AugmentedViewTransition::AugmentedViewTransitionAnimation::getFixedDuration() const
{
  return 0.0f;
}

bool AugmentedViewTransition::AugmentedViewTransitionAnimation::hasFixedDuration() const
{
  return false;
}

bool AugmentedViewTransition::AugmentedViewTransitionAnimation::onUpdate(vfc::float32_t /* f_elapsedTime */)
{
  if (m_instantSwitch)
  {
    return true;
  }
  return (!m_augmentedViewTransition->isTransitioning());
}


void AugmentedViewTransition::AugmentedViewTransitionAnimation::onBegin()
{
  if (m_toAugmented)
  {
    if (m_instantSwitch)
    {
      m_augmentedViewTransition->switchToAugmentedView();
    }
    else
    {
      m_augmentedViewTransition->transitionToAugmentedView();
    }
  }
  else
  {
    if (m_instantSwitch)
    {
      m_augmentedViewTransition->switchFromAugmentedView();
    }
    else
    {
      m_augmentedViewTransition->transitionFromAugmentedView();
    }
  }
}


} // namespace augmentedview
} // namespace assets
} // namespace cc
