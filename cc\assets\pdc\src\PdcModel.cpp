//-------------------------------------------------------------------------------
// Copyright (c) 2021 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/pdc/inc/PdcModel.h"
#include "cc/assets/pdc/inc/PdcHelper.h"
#include "cc/assets/pdc/inc/PdcSettings.h"
#include "cc/assets/pdc/inc/PdcUpdateVisitor.h"
#include "cc/core/inc/CustomUltrasonic.h"

#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/util/math/inc/Conversions.hpp"

namespace cc
{
namespace assets
{
namespace pdc
{

const float PdcModel::INF_DISTANCE = 2.0f;

///
/// StateTransition
///
StateTransition::StateTransition(float f_transitionDuration, bool f_initialState)
  : m_duration(f_transitionDuration)
  , m_progress(f_initialState ? f_transitionDuration : 0.0f)
  , m_lastUpdate()
  , m_state(f_initialState ? State::On : State::Off)
{
}

void StateTransition::setState(bool f_state, const TimePoint& f_time)
{

  switch (m_state)
  {
    case State::On:
    {
      if (!f_state)
      {
        m_state = State::TransitionOff;
      }
      break;
    }
    case State::TransitionOn:
    {
      if (f_state)
      {
        m_progress += std::chrono::duration<float>(f_time - m_lastUpdate).count();
        if (m_progress >= m_duration)
        {
          m_progress = m_duration;
          m_state = State::On;
        }
      }
      else
      {
        m_state = State::TransitionOff;
      }
      break;
    }
    case State::TransitionOff:
    {
      if (!f_state)
      {
        m_progress -= std::chrono::duration<float> (f_time - m_lastUpdate).count();
        if (0.0f >= m_progress)
        {
          m_progress = 0.0f;
          m_state = State::Off;
        }
      }
      else
      {
        m_state = State::TransitionOn;
      }
      break;
    }
    case State::Off:
    {
      if (f_state)
      {
        m_state = State::TransitionOn;
      }
      break;
    }
    default:
    {
      break;
    }

  }
  m_lastUpdate = f_time;
}


float StateTransition::getInterpolatedState() const
{
  return pc::util::smoothstep(0.0f, m_duration, m_progress);
}


///
/// PdcSector
///
PdcSector::PdcSector()
  : m_distance()
  , m_base()
  , m_normal()
  , m_onPathState(0.8f, false)
  , m_snapToNext(0.5f, false)
  , m_maxDistance(g_pdcSettings->m_maxDistance) //  will be overwritten with the 6 passed codingparameter in the consturctor of the PdcModel()
{
}


bool PdcSector::isValid() const
{
  return m_distance[PdcSector::HANDLE_CENTER] <= m_maxDistance;
}


bool PdcSector::isConnectedToNext() const
{
  return (m_snapToNext.getState() == StateTransition::State::On);
}


namespace
{

float converge(float f_current, float f_target, float f_div, float f_limit)
{
  const float l_delta = f_target - f_current;
  if (std::abs(l_delta) < f_limit)
  {
    return f_target;
  }
  return f_current + (l_delta / f_div);
}

std::size_t convertZone(int index)
{
  switch(index)
  {
    case 0:
    {
      return 1;
    }
    case 1:
    {
      return 4;
    }
    case 2:
    {
      return 6;
    }
    case 3:
    {
      return 8;
    }
    case 4:
    {
      return 10;
    }
    case 5:
    {
      return 12;
    }
    case 6:
    {
      return 15;
    }
    case 7:
    {
      return 18;
    }
    case 8:
    {
      return 21;
    }
    case 9:
    {
      return 24;
    }
    case 10:
    {
      return 26;
    }
    case 11:
    {
      return 28;
    }
    case 12:{
      return 30;
    }
    case 13:
    {
      return 32;
    }
    case 14:
    {
      return 35;
    }
    case 15:
    {
      return 38;
    }
    default:
    {

    }
  }
}

std::size_t convertZoneLeftBorder(int index)
{
  switch(index)
  {

    case 0:
    {
      return 2;
    }
    case 1:
    {
      return 5;
    }
    case 2:
    {
      return 7;
    }
    case 3:
    {
      return 9;
    }
    case 4:
    {
      return 11;
    }
    case 5:
    {
      return 13;
    }
    case 6:
    {
      return 16;
    }
    case 7:
    {
      return 19;
    }
    case 8:
    {
      return 22;
    }
    case 9:
    {
      return 25;
    }
    case 10:
    {
      return 27;
    }
    case 11:
    {
      return 29;
    }
    case 12:
    {
      return 31;
    }
    case 13:
    {
      return 33;
    }
    case 14:
    {
      return 36;
    }
    case 15:
    {
      return 39;
    }
    default:
    {

    }
  }
}

std::size_t convertZoneRightBorder(int index)
{
  switch(index)
  {
    case 0:
    {
      return 0;
    }
    case 1:
    {
      return 3;
    }
    case 2:
    {
      return 6;
    }
    case 3:
    {
      return 8;
    }
    case 4:
    {
      return 10;
    }
    case 5:
    {
      return 12;
    }
    case 6:
    {
      return 14;
    }
    case 7:
    {
      return 17;
    }
    case 8:
    {
      return 20;
    }
    case 9:
    {
      return 23;
    }
    case 10:
    {
      return 26;
    }
    case 11:
    {
      return 28;
    }
    case 12:
    {
      return 30;
    }
    case 13:
    {
      return 32;
    }
    case 14:
    {
      return 34;
    }
    case 15:
    {
      return 37;
    }
    default:
    {
      return 0;
    }
    }
}
}


///
/// PdcModel
///
PdcModel::PdcModel(const cc::core::CustomZoneLayout& f_layout)
{
  // PAS zone layout point typically lie inside the vehicle countour, however the uss distance values
  // are measured relative to the contour. As a consequence, the layout base points need to be shifted to match
  // the vehicle contour in order to correctly display the corresponding distances
  const pc::util::Polygon2D l_vehicleContour = cc::core::g_vehicleContour->toPolygon2D();
  for (std::size_t i = 0; i < NUM_SECTORS; ++i)
  {
    unsigned int l_index = 0;
    l_index = static_cast<unsigned int>(i);

    const std::array<pc::vehicle::LineData, PdcSector::NUM_HANDLES> l_lines = {
      {
        f_layout.getRightBorderLine(convertZoneRightBorder(l_index)),
        f_layout.getMiddleLine(convertZone(l_index)),
        f_layout.getLeftBorderLine(convertZoneLeftBorder(l_index))
      }
    };
    PdcSector& l_sector = m_sectors[i];

    setSectorZoneParams(l_sector, l_index);

    for (std::size_t j = 0; j < PdcSector::NUM_HANDLES; ++j)
    {
      l_sector.m_distance[j] = PdcModel::INF_DISTANCE;
      const osg::Vec2& l_innerPoint = l_lines[j].m_innerPoint;
      const osg::Vec2& l_outerPoint = l_lines[j].m_outerPoint;
      osg::Vec2 l_contourPoint;
      const bool l_intersectionFound = pc::util::findIntersectionWithPolygon(
        l_vehicleContour,
        pc::util::LineSegment2D(l_innerPoint, l_outerPoint),
        l_contourPoint);
      if (l_intersectionFound)
      {
        l_sector.m_base[j] = l_contourPoint;
      }
      else
      {
        l_sector.m_base[j] = l_innerPoint;
      }

      l_sector.m_normal[j] = l_lines[j].m_direction;
    }
  }
}

void PdcModel::setSectorZoneParams(PdcSector& f_sector, unsigned int f_index)
{
  if(f_index == 0u ||
     f_index == 7u ||
     f_index == 8u ||
     f_index == 15u )
  {
    f_sector.m_zone = PdcSector::FRONTREAR;
    f_sector.m_maxDistance = g_pdcSettings->m_maxDist0;
  }
  else if(f_index == 2u ||
     f_index == 3u ||
     f_index == 4u ||
     f_index == 5u ||
     f_index == 10u ||
     f_index == 11u ||
     f_index == 12u ||
     f_index == 13u )
  {
    f_sector.m_zone = PdcSector::LEFTRIGHT;
    f_sector.m_maxDistance = g_pdcSettings->m_maxDist1;
  }
  else
  {
    f_sector.m_zone = PdcSector::CORNER;
    f_sector.m_maxDistance = g_pdcSettings->m_maxDist2;
  }
}


void PdcModel::update(PdcUpdateVisitor& f_visitor)
{
  const pc::vehicle::UltrasonicData* const l_usData = f_visitor.getUltrasonicData();

  if (l_usData!=nullptr)
  {
    update(l_usData);
  }
}

void PdcModel::update(const pc::vehicle::UltrasonicData* f_usData)
{
  if (nullptr != f_usData)
  {
    const auto l_now = StateTransition::TimePoint::clock::now();
    updateControlPoints(*f_usData, l_now);
    updateSnapping(l_now);
    updateSpline();
    updateSplineNormals();
  }
}


void PdcModel::updateControlPoints(const pc::vehicle::UltrasonicData& f_usData, const StateTransition::TimePoint& f_time)
{
  static constexpr float CONVERGE_STEP_DIV = 4.0f; // in every iteration (frame) the distance will be divided by this value
  static constexpr float CONVERGE_LIMIT = 0.0005f; // animation stops if the distance difference is smaller than one millimeter (+/-)

  //For uss data above m_maxDistance, no need of fly out animation and for sector distance above m_maxDistance,
  // no need of fly in animation.Thereby distance division takes place once(frame) only.

  static constexpr float CONVERGE_LONG_STEP_DIV = 1.0f;

  float l_distance = 0.0f;
  float l_convergeDiv = 0.0f;
  for (std::size_t i = 0; i < NUM_SECTORS; ++i)
  {
    unsigned int l_index = 0;
    l_index = static_cast<unsigned int>(i);

    PdcSector& l_sector = m_sectors[i];
    float l_sectorDistance = 0.0f;
    l_sectorDistance = l_sector.getDistance();
    if ((l_sectorDistance < l_sector.m_maxDistance) && (f_usData[convertZone(l_index)].getDistance() <= l_sector.m_maxDistance))
      {
        l_convergeDiv = CONVERGE_STEP_DIV;
      }
    else
      {
        l_convergeDiv = CONVERGE_LONG_STEP_DIV;
      }
    l_distance = converge(
        l_sector.getDistance(),
        std::min(f_usData[convertZone(l_index)].getDistance(), PdcModel::INF_DISTANCE),
        l_convergeDiv,
        CONVERGE_LIMIT);
    l_distance = std::max(l_distance, g_pdcSettings->m_minDistance);
    l_sector.setDistance(PdcSector::HANDLE_CENTER, l_distance);
    l_sector.m_onPathState.setState(f_usData[convertZone(l_index)].getOnPath(), f_time);
  }
}


bool PdcModel::belowSnappingThreshold(const float f_sectorDistance, const float f_sectorNextDistance, const float f_threshold) const
{
  bool l_isBelowSnappingThreshold = false;
  if(std::abs(f_sectorDistance - f_sectorNextDistance) < f_threshold)
  {
    l_isBelowSnappingThreshold = true;
  }
  return l_isBelowSnappingThreshold;
}


void PdcModel::updateSnapping(const StateTransition::TimePoint& f_time)
{
  for (std::size_t i = 0; i < NUM_SECTORS; ++i)
  {
    PdcSector& l_sector = m_sectors[i];
    PdcSector& l_sectorNext = m_sectors[(i + 1) % static_cast<int>(NUM_SECTORS)];
    float l_snappingThreshold = pc::util::mm2m(g_pdcSettings->m_spline_threshold);
    if(l_sector.m_snapToNext.getState() == StateTransition::State::On ||
       l_sector.m_snapToNext.getState() == StateTransition::State::TransitionOn)
    {
      l_snappingThreshold = pc::util::mm2m(g_pdcSettings->m_spline_threshold + g_pdcSettings->m_spline_threshold_hysterese);
    }

    if(belowSnappingThreshold(l_sector.getDistance(),
                              l_sectorNext.getDistance(),
                              l_snappingThreshold))
    {
      l_sector.m_snapToNext.setState(l_sector.isValid() && l_sectorNext.isValid(), f_time);
    }
    else // else snapping is deactivated
    {
      l_sector.m_snapToNext.setState(false, f_time);
    }
    const float l_middleDistance = (l_sector.getDistance() + l_sectorNext.getDistance()) / 2.0f;
    const float l_snappingRate = l_sector.m_snapToNext.getInterpolatedState();
    const float l_leftHandleDist = lerp(l_sector.getDistance(), l_middleDistance, l_snappingRate);
    if((l_sectorNext.getDistance() < l_sectorNext.m_maxDistance) && (l_sector.getDistance() < l_sector.m_maxDistance))
    {
      l_sector.setDistance(PdcSector::HANDLE_LEFT, l_leftHandleDist);
    }
    else
    {
      l_sector.setDistance(PdcSector::HANDLE_LEFT,l_sector.getDistance());
    }
    const float l_rightHandleDistNext = lerp(l_sectorNext.getDistance(), l_middleDistance, l_snappingRate);
    if((l_sectorNext.getDistance() < l_sectorNext.m_maxDistance) && (l_sector.getDistance() < l_sector.m_maxDistance))
    {
    l_sectorNext.setDistance(PdcSector::HANDLE_RIGHT, l_rightHandleDistNext);
    }
    else
    {
      l_sectorNext.setDistance(PdcSector::HANDLE_RIGHT,l_sectorNext.getDistance());
    }
    // helper data for spline computation
    const float l_supportDistLeft = lerp(l_sector.getDistance(), l_sectorNext.getDistance(), l_snappingRate);
    l_sector.m_supportDistanceLeft = l_supportDistLeft;
    const float l_supportDistRightNext = lerp(l_sectorNext.getDistance(), l_sector.getDistance(), l_snappingRate);
    l_sectorNext.m_supportDistanceRight = l_supportDistRightNext;
  }
}


void PdcModel::updateSpline()
{
  auto l_splinePointsIter = m_splinePoints.begin();
  for (std::size_t i = 0; i < NUM_SECTORS; ++i)
  {
    const PdcSector& l_sector = m_sectors[i];
    const PdcSector& l_sectorPrev = m_sectors[(i + static_cast<int>(NUM_SECTORS) - 1) % static_cast<int>(NUM_SECTORS)];
    const PdcSector& l_sectorNext = m_sectors[(i + 1) % static_cast<int>(NUM_SECTORS)];
    typedef std::array<osg::Vec2, 5> ControlPointSet;
    const ControlPointSet l_cps = {
      {
        l_sectorPrev.getControlPoint(PdcSector::HANDLE_CENTER, l_sector.m_supportDistanceRight),
        l_sector.getControlPoint(PdcSector::HANDLE_RIGHT),
        l_sector.getControlPoint(PdcSector::HANDLE_CENTER),
        l_sector.getControlPoint(PdcSector::HANDLE_LEFT),
        l_sectorNext.getControlPoint(PdcSector::HANDLE_CENTER, l_sector.m_supportDistanceLeft)
      }
    };
    l_splinePointsIter = computeSpline(l_cps, l_splinePointsIter, NUM_SPLINE_POINTS_PER_SECTOR);
  }
}


void PdcModel::updateSplineNormals()
{
  for (std::size_t i = 0; i < NUM_SECTORS; ++i)
  {
    const std::size_t j = i * static_cast<std::size_t>(NUM_SPLINE_POINTS_PER_SECTOR);
    osg::Vec2 l_normal = computeOrthogonal(m_splinePoints[j], m_splinePoints[j + 1]);
    m_splineNormals[j] = l_normal;
    for (std::size_t k = 1; k < static_cast<std::size_t>(NUM_SPLINE_POINTS_PER_SECTOR) - 1; ++k)
    {
      const osg::Vec2 l_normalNext = computeOrthogonal(m_splinePoints[j + k], m_splinePoints[j + k + 1]);
      osg::Vec2 l_smoothedNormal = l_normal + l_normalNext;
      l_smoothedNormal.normalize();
      m_splineNormals[j + k] = l_smoothedNormal;
      l_normal = l_normalNext;
    }
    m_splineNormals[j + static_cast<std::size_t>(NUM_SPLINE_POINTS_PER_SECTOR) - 1] = l_normal;
  }
}


bool PdcModel::hasActiveSectors() const
{
  for (const auto& l_sector : m_sectors)
  {
    if (l_sector.isValid())
    {
      return true;
    }
  }
  return false;
}

} // namespace pdc
} // namespace assets
} // namespace cc
