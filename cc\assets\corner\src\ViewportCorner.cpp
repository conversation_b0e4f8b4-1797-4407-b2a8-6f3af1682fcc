//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
#include "cc/target/common/inc/commonInterface.h"
#include "cc/assets/corner/inc/ViewportCorner.h"
#include "cc/core/inc/CustomFramework.h"

namespace cc
{
namespace assets
{
namespace viewcorner
{

static pc::assets::Icon* createIcon(
    const std::string& f_texturePath,
    const osg::Vec2f& f_positionPixel,
    const pc::assets::Icon::Origin /*f_origin*/,
    const pc::assets::Icon::Alignment f_horizontalAlignment,
    const pc::assets::Icon::Alignment f_verticalAlignment
)
{
    pc::assets::Icon* const icon = new pc::assets::Icon{f_texturePath, true};
    auto* const textureImage = icon->getTexture()->getImage();
    icon->setSize({static_cast<float>(textureImage->s()), static_cast<float>(textureImage->t())}, pc::assets::Icon::UnitType::Pixel); // PRQA S 2446
    icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
    icon->setPosition(f_positionPixel, pc::assets::Icon::UnitType::Percentage);
    icon->setAlignmentHorizontal(f_horizontalAlignment);
    icon->setAlignmentVertical(f_verticalAlignment);
    return icon;
}

ViewportCorner::ViewportCorner(
    cc::core::CustomFramework* f_customFramework,
    cc::core::AssetId f_assetId,
    osg::Camera* f_referenceView,
    bool f_enableTopLeft,
    bool f_enableBottomLeft,
    bool f_enableTopRight,
    bool f_enableBottomRight
)
: pc::assets::ImageOverlays(f_assetId, f_referenceView) // PRQA S 2323
, m_customFramework{f_customFramework}
, m_texturePath{""}
, m_iconGroupDay{new pc::assets::IconGroup}
, m_iconGroupNight{new pc::assets::IconGroup}
, m_enableTopLeft{f_enableTopLeft}
, m_enableBottomLeft{f_enableBottomLeft}
, m_enableTopRight{f_enableTopRight}
, m_enableBottomRight{f_enableBottomRight}
, m_dirty{true} // ensure first update
, m_theme{cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT}
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
    // relative to bottom left
    const osg::Vec2f topLeftPosition     = {0.0f,    100.0f};
    const osg::Vec2f topRightPosition    = {100.0f,  100.0f};
    const osg::Vec2f bottomLeftPosition  = {0.0f,    0.0f  };
    const osg::Vec2f bottomRightPosition = {100.0f,  0.0f  };
    using namespace pc::assets; // PRQA S 2520
    m_iconGroupDay->addIcon(this,   createIcon(CONCAT_PATH("cc/resources/ui/day/leftTopCorner.png"),       topLeftPosition,      Icon::Origin::BottomLeft, Icon::Alignment::Left,  Icon::Alignment::Bottom));
    m_iconGroupDay->addIcon(this,   createIcon(CONCAT_PATH("cc/resources/ui/day/leftBottomCorner.png"),    bottomLeftPosition,   Icon::Origin::BottomLeft, Icon::Alignment::Left,  Icon::Alignment::Top));
    m_iconGroupDay->addIcon(this,   createIcon(CONCAT_PATH("cc/resources/ui/day/rightTopCorner.png"),      topRightPosition,     Icon::Origin::BottomLeft, Icon::Alignment::Right, Icon::Alignment::Bottom));
    m_iconGroupDay->addIcon(this,   createIcon(CONCAT_PATH("cc/resources/ui/day/rightBottomCorner.png"),   bottomRightPosition,  Icon::Origin::BottomLeft, Icon::Alignment::Right, Icon::Alignment::Top));
    m_iconGroupNight->addIcon(this, createIcon(CONCAT_PATH("cc/resources/ui/night/leftTopCorner.png"),     topLeftPosition,      Icon::Origin::BottomLeft, Icon::Alignment::Left,  Icon::Alignment::Bottom));
    m_iconGroupNight->addIcon(this, createIcon(CONCAT_PATH("cc/resources/ui/night/leftBottomCorner.png"),  bottomLeftPosition,   Icon::Origin::BottomLeft, Icon::Alignment::Left,  Icon::Alignment::Top));
    m_iconGroupNight->addIcon(this, createIcon(CONCAT_PATH("cc/resources/ui/night/rightTopCorner.png"),    topRightPosition,     Icon::Origin::BottomLeft, Icon::Alignment::Right, Icon::Alignment::Bottom));
    m_iconGroupNight->addIcon(this, createIcon(CONCAT_PATH("cc/resources/ui/night/rightBottomCorner.png"), bottomRightPosition,  Icon::Origin::BottomLeft, Icon::Alignment::Right, Icon::Alignment::Top));
}


void ViewportCorner::traverse(osg::NodeVisitor& f_nv)
{
    if (f_nv.getVisitorType() == osg::NodeVisitor::UPDATE_VISITOR)
    {
        using namespace cc::target::common;
        if (m_customFramework->m_dayNightThemeDaddy_Receiver.hasData())
        {
            const auto l_data = m_customFramework->m_dayNightThemeDaddy_Receiver.getData()->m_Data;
            // only update if receive DAY or NIGHT values
            if (l_data == cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT || l_data == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            {
                m_dirty = m_theme != l_data;
                m_theme = l_data;
            }
        }
        if (m_dirty)
        {
            m_dirty = false;
            m_iconGroupDay->setAllEnabled(m_theme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY);
            m_iconGroupNight->setAllEnabled(m_theme == cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT);
            auto const iconGroupActive = m_theme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY ? m_iconGroupDay : m_iconGroupNight;
            iconGroupActive->getIcon(TOP_LEFT_CORNER)->setEnabled(m_enableTopLeft);
            iconGroupActive->getIcon(BOTTOM_LEFT_CORNER)->setEnabled(m_enableBottomLeft);
            iconGroupActive->getIcon(TOP_RIGHT_CORNER)->setEnabled(m_enableTopRight);
            iconGroupActive->getIcon(BOTTOM_RIGHT_CORNER)->setEnabled(m_enableBottomRight);
        }
    }
    pc::assets::ImageOverlays::traverse(f_nv);
}


void ViewCornerSpaceUpdateVisitor::applyViewport(pc::core::Viewport& f_viewport)
{
    if (m_spacingEnableVec[LEFT])
    {
        if (m_spacingModeVec[LEFT] == FULL_WIDTH)
        {
            f_viewport.m_origin.x() += m_spacingWidthVec[LEFT]; // PRQA S 3011
            f_viewport.m_size.x()   -= m_spacingWidthVec[LEFT]; // PRQA S 3011
        }
        if (m_spacingModeVec[LEFT] == HALF_WIDTH)
        {
            f_viewport.m_origin.x() += m_spacingWidthVec[LEFT] / 2.0; // PRQA S 3011
            f_viewport.m_size.x()   -= m_spacingWidthVec[LEFT] / 2.0; // PRQA S 3011
        }
    }

    if (m_spacingEnableVec[TOP])
    {
        if (m_spacingModeVec[TOP] == FULL_WIDTH)
        {
            f_viewport.m_size.y() -= m_spacingWidthVec[TOP]; // PRQA S 3011
        }
        if (m_spacingModeVec[TOP] == HALF_WIDTH)
        {
            f_viewport.m_size.y() -= m_spacingWidthVec[TOP] / 2.0; // PRQA S 3011
        }
    }

    if (m_spacingEnableVec[RIGHT])
    {
        if (m_spacingModeVec[RIGHT] == FULL_WIDTH)
        {
            f_viewport.m_size.x() -= m_spacingWidthVec[RIGHT]; // PRQA S 3011
        }
        if (m_spacingModeVec[RIGHT] == HALF_WIDTH)
        {
            f_viewport.m_size.x() -= m_spacingWidthVec[RIGHT] / 2.0; // PRQA S 3011
        }
    }

    if (m_spacingEnableVec[BOTTOM])
    {
        if (m_spacingModeVec[BOTTOM] == FULL_WIDTH)
        {
            f_viewport.m_origin.y() += m_spacingWidthVec[BOTTOM];
            f_viewport.m_size.y()   -= m_spacingWidthVec[BOTTOM];
        }
        if (m_spacingModeVec[BOTTOM] == HALF_WIDTH)
        {
            f_viewport.m_origin.y() += m_spacingWidthVec[BOTTOM] / 2.0;
            f_viewport.m_size.y()   -= m_spacingWidthVec[BOTTOM] / 2.0;
        }
    }
}


void ViewCornerSpaceUpdateVisitor::apply(osg::Camera &f_camera)
{
    const auto viewport = f_camera.getViewport();
    /**
     *                 top
     *       ***********************
     *       *                     *
     * left  *                     * right
     *       *                     *
     *       *                     *
     *       O**********************
     *               bottom
     */

    if (m_spacingEnableVec[LEFT])
    {
        if (m_spacingModeVec[LEFT] == FULL_WIDTH)
        {
            viewport->x()     += m_spacingWidthVec[LEFT];
            viewport->width() -= m_spacingWidthVec[LEFT];
        }
        if (m_spacingModeVec[LEFT] == HALF_WIDTH)
        {
            viewport->x()     += m_spacingWidthVec[LEFT] / 2.0;
            viewport->width() -= m_spacingWidthVec[LEFT] / 2.0;
        }
    }

    if (m_spacingEnableVec[TOP])
    {
        if (m_spacingModeVec[TOP] == FULL_WIDTH)
        {
            viewport->height() -= m_spacingWidthVec[TOP];
        }
        if (m_spacingModeVec[TOP] == HALF_WIDTH)
        {
            viewport->height() -= m_spacingWidthVec[TOP] / 2.0;
        }
    }

    if (m_spacingEnableVec[RIGHT])
    {
        if (m_spacingModeVec[RIGHT] == FULL_WIDTH)
        {
            viewport->width() -= m_spacingWidthVec[RIGHT];
        }
        if (m_spacingModeVec[RIGHT] == HALF_WIDTH)
        {
            viewport->width() -= m_spacingWidthVec[RIGHT] / 2.0;
        }
    }

    if (m_spacingEnableVec[BOTTOM])
    {
        if (m_spacingModeVec[BOTTOM] == FULL_WIDTH)
        {
            viewport->y()     += m_spacingWidthVec[BOTTOM];
            viewport->height() -= m_spacingWidthVec[BOTTOM];
        }
        if (m_spacingModeVec[BOTTOM] == HALF_WIDTH)
        {
            viewport->y()     += m_spacingWidthVec[BOTTOM] / 2.0;
            viewport->height() -= m_spacingWidthVec[BOTTOM] / 2.0;
        }
    }
}

} // namespace viewcorner
} // namespace assets
} // namespace cc
