//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CameraIcon.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/CameraIcon.h"
#include "cc/assets/uielements/inc/HmiElementsSettings.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "CustomSystemConf.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for view camera icons
enum ViewCameraType
{
  CAMERA_ICON_SV_FR,
  CAMERA_ICON_SV_RE,
  CAMERA_ICON_FW_LE,
  CAMERA_ICON_FW_RI,
  CAMERA_ICON_RW_LE,
  CAMERA_ICON_RW_RI,
  CAMERA_ICON_PV_FL,
  CAMERA_ICON_PV_FR,
  CAMERA_ICON_PV_RL,
  CAMERA_ICON_PV_RR,
  CAMERA_ICON_SV_FR_ACT,
  CAMERA_ICON_SV_RE_ACT,
  CAMERA_ICON_FW_LE_ACT,
  CAMERA_ICON_FW_RI_ACT,
  CAMERA_ICON_RW_LE_ACT,
  CAMERA_ICON_RW_RI_ACT,
  CAMERA_ICON_PV_FL_ACT,
  CAMERA_ICON_PV_FR_ACT,
  CAMERA_ICON_PV_RL_ACT,
  CAMERA_ICON_PV_RR_ACT
};

//!
//! @brief Construct a new CameraIcon Manager:: CameraIcon Manager object
//!
//! @param f_config
//!
CameraIconManager::CameraIconManager()
  : m_lastConfigUpdate(~0u)
{
}


CameraIconManager::~CameraIconManager()
{
}

// ! transfer the vehicle coordinate to view port coordinate
osg::Vec2f CameraIconManager::transferToPlanViewCoord(const osg::Vec2f f_vehicleCoord, const osg::Matrixf f_mat)
{
  osg::Vec3f l_temp = osg::Vec3f(f_vehicleCoord, 0.0f);
  osg::Vec3f l_screenCoord = l_temp * f_mat;
  return osg::Vec2f(l_screenCoord.x() - cc::core::g_views->m_planViewport.m_origin.x(), l_screenCoord.y());
}

// ! transfer to start from bottom left
osg::Vec2f CameraIconManager::transferToBottomLeft(const osg::Vec2f f_iconPos)
{
  return osg::Vec2f(f_iconPos.x() - cc::core::g_views->m_planViewport.m_origin.x(), cc::core::g_views->m_planViewport.m_size.y() - f_iconPos.y());
}


void CameraIconManager::init(pc::assets::ImageOverlays* f_imageOverlays)
{
  // ! init icons
  m_cameraIcons.clear(f_imageOverlays);

  // ! The standard views' camera icons
  // geometry description, the below icons will be in sequence of the map
  //  -- 0 --
  // | ---- |
  // 2 ---- 3
  // | ---- |
  // 4 ---- 5
  // | ---- |
  //  -- 1 --
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamV, transferToBottomLeft(g_uiSettings->m_camSvFr.m_iconCenter), g_uiSettings->m_camSvFr.m_iconSize, false, false));  // 0, front view
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamV, transferToBottomLeft(g_uiSettings->m_camSvRe.m_iconCenter), g_uiSettings->m_camSvRe.m_iconSize, false, true));   // 1, rear view
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamH, transferToBottomLeft(g_uiSettings->m_camFwLe.m_iconCenter), g_uiSettings->m_camFwLe.m_iconSize, false, false));  // 2, front wheel view left
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamH, transferToBottomLeft(g_uiSettings->m_camFwRi.m_iconCenter), g_uiSettings->m_camFwRi.m_iconSize, true, false));   // 3, front wheel view right
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamH, transferToBottomLeft(g_uiSettings->m_camRwLe.m_iconCenter), g_uiSettings->m_camRwLe.m_iconSize, false, false));  // 4, front wheel view left
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamH, transferToBottomLeft(g_uiSettings->m_camRwRi.m_iconCenter), g_uiSettings->m_camRwRi.m_iconSize, true, false));   // 5, front wheel view right

  // ! The perspective views' camera icons
  // geometry description, the below icons will be in sequence of the map
  // | ---- |
  // 6 ---- 7
  // | ---- |
  // 8 ---- 9
  // | ---- |
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamO, transferToBottomLeft(g_uiSettings->m_camPvFL.m_iconCenter), g_uiSettings->m_camPvFL.m_iconSize, false, false));  // 6, front left perspective view
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamO, transferToBottomLeft(g_uiSettings->m_camPvFR.m_iconCenter), g_uiSettings->m_camPvFR.m_iconSize, true, false));   // 7, front right perspective view
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamO, transferToBottomLeft(g_uiSettings->m_camPvRL.m_iconCenter), g_uiSettings->m_camPvRL.m_iconSize, false, true));   // 8, rear left perspective view
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamO, transferToBottomLeft(g_uiSettings->m_camPvRR.m_iconCenter), g_uiSettings->m_camPvRR.m_iconSize, true, true));    // 9, rear right perspective view

    // ! The standard views' camera icons:Active mode
  // geometry description, the below icons will be in sequence of the map
  //  -- 0 --
  // | ---- |
  // 2 ---- 3
  // | ---- |
  // 4 ---- 5
  // | ---- |
  //  -- 1 --
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamVActive, transferToBottomLeft(g_uiSettings->m_camSvFr.m_iconCenter), g_uiSettings->m_camSvFr.m_iconSize, false, false));  // 0, front view
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamVActive, transferToBottomLeft(g_uiSettings->m_camSvRe.m_iconCenter), g_uiSettings->m_camSvRe.m_iconSize, false, true));   // 1, rear view
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamHActive, transferToBottomLeft(g_uiSettings->m_camFwLe.m_iconCenter), g_uiSettings->m_camFwLe.m_iconSize, false, false));  // 2, front wheel view left
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamHActive, transferToBottomLeft(g_uiSettings->m_camFwRi.m_iconCenter), g_uiSettings->m_camFwRi.m_iconSize, true, false));   // 3, front wheel view right
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamHActive, transferToBottomLeft(g_uiSettings->m_camRwLe.m_iconCenter), g_uiSettings->m_camRwLe.m_iconSize, false, false));  // 4, front wheel view left
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamHActive, transferToBottomLeft(g_uiSettings->m_camRwRi.m_iconCenter), g_uiSettings->m_camRwRi.m_iconSize, true, false));   // 5, front wheel view right

  // ! The perspective views' camera icons: Active mode
  // geometry description, the below icons will be in sequence of the map
  // | ---- |
  // 6 ---- 7
  // | ---- |
  // 8 ---- 9
  // | ---- |
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamOActive, transferToBottomLeft(g_uiSettings->m_camPvFL.m_iconCenter), g_uiSettings->m_camPvFL.m_iconSize, false, false));  // 6, front left perspective view
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamOActive, transferToBottomLeft(g_uiSettings->m_camPvFR.m_iconCenter), g_uiSettings->m_camPvFR.m_iconSize, true, false));   // 7, front right perspective view
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamOActive, transferToBottomLeft(g_uiSettings->m_camPvRL.m_iconCenter), g_uiSettings->m_camPvRL.m_iconSize, false, true));   // 8, rear left perspective view
  m_cameraIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCamOActive, transferToBottomLeft(g_uiSettings->m_camPvRR.m_iconCenter), g_uiSettings->m_camPvRR.m_iconSize, true, true));    // 9, rear right perspective view
}


void CameraIconManager::update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework)    // PRQA S 6044
{
  // ! check if config has changed
  if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
  {
    init(f_imageOverlays);
    m_lastConfigUpdate = g_uiSettings->getModifiedCount();
  }
  else
  {
    // ! disable active icons before each loop
    m_cameraIcons.getIcon(CAMERA_ICON_SV_FR_ACT)->setEnabled(false);
    m_cameraIcons.getIcon(CAMERA_ICON_SV_RE_ACT)->setEnabled(false);
    m_cameraIcons.getIcon(CAMERA_ICON_FW_LE_ACT)->setEnabled(false);
    m_cameraIcons.getIcon(CAMERA_ICON_FW_RI_ACT)->setEnabled(false);
    m_cameraIcons.getIcon(CAMERA_ICON_RW_LE_ACT)->setEnabled(false);
    m_cameraIcons.getIcon(CAMERA_ICON_RW_RI_ACT)->setEnabled(false);
    m_cameraIcons.getIcon(CAMERA_ICON_PV_FL_ACT)->setEnabled(false);
    m_cameraIcons.getIcon(CAMERA_ICON_PV_FR_ACT)->setEnabled(false);
    m_cameraIcons.getIcon(CAMERA_ICON_PV_RL_ACT)->setEnabled(false);
    m_cameraIcons.getIcon(CAMERA_ICON_PV_RR_ACT)->setEnabled(false);
  }

  // ! check the view mode status (standard or perspective views)
  if (f_framework->m_viewModeStatus_ReceiverPort.hasData())
  {
    const cc::daddy::SVSViewModeStsDaddy_t* l_viewMode = f_framework->m_viewModeStatus_ReceiverPort.getData();
    vfc::uint8_t l_vm = l_viewMode->m_Data;

    if (1u == l_vm)
    {
      // standard views
      m_cameraIcons.getIcon(CAMERA_ICON_SV_FR)->setEnabled(true);
      m_cameraIcons.getIcon(CAMERA_ICON_SV_RE)->setEnabled(true);
      m_cameraIcons.getIcon(CAMERA_ICON_FW_LE)->setEnabled(true);
      m_cameraIcons.getIcon(CAMERA_ICON_FW_RI)->setEnabled(true);
      m_cameraIcons.getIcon(CAMERA_ICON_RW_LE)->setEnabled(true);
      m_cameraIcons.getIcon(CAMERA_ICON_RW_RI)->setEnabled(true);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_FL)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_FR)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_RL)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_RR)->setEnabled(false);
    }
    else if (2u == l_vm)
    {
      // perspective views
      m_cameraIcons.getIcon(CAMERA_ICON_SV_FR)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_SV_RE)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_FW_LE)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_FW_RI)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_RW_LE)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_RW_RI)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_FL)->setEnabled(true);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_FR)->setEnabled(true);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_RL)->setEnabled(true);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_RR)->setEnabled(true);
    }
    else
    {
      // no views
      m_cameraIcons.getIcon(CAMERA_ICON_SV_FR)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_SV_RE)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_FW_LE)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_FW_RI)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_RW_LE)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_RW_RI)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_FL)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_FR)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_RL)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_RR)->setEnabled(false);
    }
  }

  // ! check the current view ID
  if (f_framework->m_currentView_ReceiverPort.hasData())
  {
    const cc::daddy::SVSCurrentViewDaddy_t* l_data = f_framework->m_currentView_ReceiverPort.getData();
    EScreenID l_curviewid = static_cast<EScreenID>(l_data->m_Data);    //PRQA S 3013

    switch (l_curviewid)
    {
      case EScreenID_SINGLE_FRONT_NORMAL:
      {
        m_cameraIcons.getIcon(CAMERA_ICON_SV_FR)->setEnabled(false);
        m_cameraIcons.getIcon(CAMERA_ICON_SV_FR_ACT)->setEnabled(true);

      } break;
      case EScreenID_SINGLE_REAR_NORMAL_ON_ROAD:
      {
        m_cameraIcons.getIcon(CAMERA_ICON_SV_RE)->setEnabled(false);
        m_cameraIcons.getIcon(CAMERA_ICON_SV_RE_ACT)->setEnabled(true);
      } break;
      case EScreenID_WHEEL_FRONT_DUAL:
      {
        m_cameraIcons.getIcon(CAMERA_ICON_FW_LE)->setEnabled(false);
        m_cameraIcons.getIcon(CAMERA_ICON_FW_RI)->setEnabled(false);
        m_cameraIcons.getIcon(CAMERA_ICON_FW_LE_ACT)->setEnabled(true);
        m_cameraIcons.getIcon(CAMERA_ICON_FW_RI_ACT)->setEnabled(true);
      } break;
      case EScreenID_WHEEL_REAR_DUAL:
      {
        m_cameraIcons.getIcon(CAMERA_ICON_RW_LE)->setEnabled(false);
        m_cameraIcons.getIcon(CAMERA_ICON_RW_RI)->setEnabled(false);
        m_cameraIcons.getIcon(CAMERA_ICON_RW_LE_ACT)->setEnabled(true);
        m_cameraIcons.getIcon(CAMERA_ICON_RW_RI_ACT)->setEnabled(true);
      } break;
      case EScreenID_THREAT_FRONT:
      {
        m_cameraIcons.getIcon(CAMERA_ICON_SV_FR)->setEnabled(false);
        m_cameraIcons.getIcon(CAMERA_ICON_SV_FR_ACT)->setEnabled(true);
      } break;
      case EScreenID_THREAT_REAR:
      {
        m_cameraIcons.getIcon(CAMERA_ICON_SV_RE)->setEnabled(false);
        m_cameraIcons.getIcon(CAMERA_ICON_SV_RE_ACT)->setEnabled(true);
      } break;
      case EScreenID_PERSPECTIVE_FL:
      {
        m_cameraIcons.getIcon(CAMERA_ICON_PV_FL)->setEnabled(false);
        m_cameraIcons.getIcon(CAMERA_ICON_PV_FL_ACT)->setEnabled(true);
      } break;
      case EScreenID_PERSPECTIVE_FR:
      {
        m_cameraIcons.getIcon(CAMERA_ICON_PV_FR)->setEnabled(false);
        m_cameraIcons.getIcon(CAMERA_ICON_PV_FR_ACT)->setEnabled(true);
      } break;
      case EScreenID_PERSPECTIVE_RL:
      {
        m_cameraIcons.getIcon(CAMERA_ICON_PV_RL)->setEnabled(false);
        m_cameraIcons.getIcon(CAMERA_ICON_PV_RL_ACT)->setEnabled(true);
      } break;
      case EScreenID_PERSPECTIVE_RR:
      {
        m_cameraIcons.getIcon(CAMERA_ICON_PV_RR)->setEnabled(false);
        m_cameraIcons.getIcon(CAMERA_ICON_PV_RR_ACT)->setEnabled(true);
      } break;
      default:
        break;
    }
  }

  // disable all camicon when parking active
  if(f_framework->m_parkHmiParkingStatusReceiver.hasData())
  {
    const cc::daddy::ParkStatusDaddy_t* l_parksts = f_framework->m_parkHmiParkingStatusReceiver.getData();
    cc::target::common::EPARKStatusR2L l_curparksts = l_parksts->m_Data;

    if ( (cc::target::common::EPARKStatusR2L::PARK_Searching == l_curparksts)
      || (cc::target::common::EPARKStatusR2L::PARK_Guidance_active == l_curparksts)
      || (cc::target::common::PARK_Guidance_suspend == l_curparksts)
      || (cc::target::common::EPARKStatusR2L::PARK_Completed == l_curparksts)
      || (cc::target::common::PARK_Failure == l_curparksts)
      || (cc::target::common::EPARKStatusR2L::PARK_Terminated == l_curparksts)
      || (cc::target::common::EPARKStatusR2L::PARK_AssistStandby == l_curparksts) )
    {
      m_cameraIcons.getIcon(CAMERA_ICON_SV_FR)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_SV_RE)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_FW_LE)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_FW_RI)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_RW_LE)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_RW_RI)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_FL)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_FR)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_RL)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_RR)->setEnabled(false);
    }
  }

  // disable all camicon when gear in R
  if(f_framework->m_gearReceiver.hasData())
  {
    const pc::daddy::GearDaddy* l_gear = f_framework->m_gearReceiver.getData();
    pc::daddy::EGear l_curgear = static_cast<pc::daddy::EGear>(l_gear->m_Data);    //PRQA S 3013

    if(pc::daddy::GEAR_R == l_curgear)
    {
      m_cameraIcons.getIcon(CAMERA_ICON_SV_FR)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_SV_RE)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_FW_LE)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_FW_RI)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_RW_LE)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_RW_RI)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_FL)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_FR)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_RL)->setEnabled(false);
      m_cameraIcons.getIcon(CAMERA_ICON_PV_RR)->setEnabled(false);
    }
  }
}


pc::assets::Icon* CameraIconManager::createIcon(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const osg::Vec2f& f_iconSize, const bool f_flipHorizontal, const bool f_flipVertical) const
{
  pc::assets::Icon* l_icon = new pc::assets::Icon(f_iconPath, true);
  l_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setSize(f_iconSize, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setFlipHorizontal(f_flipHorizontal);
  l_icon->setFlipVertical(f_flipVertical);
  l_icon->setEnabled(false);
  return l_icon;
}


//!
//! @brief Construct a new CameraIcon:: CameraIcon object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
CameraIcon::CameraIcon(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId, pc::core::View* f_view)
  : cc::assets::uielements::CustomImageOverlays(f_assetId, f_view)                  // PRQA S 2966
  , m_customFramework(f_customFramework)
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1);
}


CameraIcon::~CameraIcon()
{
}


void CameraIcon::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    m_manager.update(this, m_customFramework);
  }
  pc::assets::ImageOverlays::traverse(f_nv);
}


} // namespace uielements
} // namespace assets
} // namespace cc