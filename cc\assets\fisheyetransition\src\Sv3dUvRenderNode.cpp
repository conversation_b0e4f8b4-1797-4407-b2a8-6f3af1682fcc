/// @copyright (C) 2017 <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "cc/assets/fisheyetransition/inc/Sv3dUvRenderNode.h"

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/core/inc/VideoTexture.h"
#include "pc/svs/factory/inc/SV3DNode.h"
#include "pc/svs/views/warpfisheyeview/inc/FisheyeModels.h"

#include "osgDB/WriteFile"
#include <cassert>

namespace cc
{
namespace assets
{
namespace fisheyetransition
{

SV3DUVRenderNode::SV3DUVRenderNode(pc::factory::SV3DNode* f_sv3dNode, pc::factory::SingleCamArea f_area)
    : m_sv3dNode(f_sv3dNode) // PRQA S 2323 // PRQA S 4052
    , m_lastVertexArrayUpdate(0) // PRQA S 2323
{
    if(f_sv3dNode == nullptr){
        return;
    }

    using namespace pc::factory; // PRQA S 2520
    setNumChildrenRequiringUpdateTraversal(1);

    const SV3DGeode* const l_singleCam = m_sv3dNode->getSingleCamGeode();
    const SV3DGeode* const l_twoCam    = m_sv3dNode->getTwoCamGeode();

    osg::Geode* const l_geode = new osg::Geode;
    osg::Group::addChild(l_geode); // PRQA S 3803

    std::vector<osg::Geometry*> l_geometries; // PRQA S 4117 # false positive, l_geometries has been modified
    switch (f_area)
    {
    case SingleCamArea::SINGLE_CAM_FRONT:
    {
        l_geometries.push_back(new osg::Geometry(
            *l_singleCam->getGeometry(static_cast<vfc::uint32_t>(SingleCamArea::SINGLE_CAM_FRONT)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_singleCam->getGeometry(static_cast<vfc::uint32_t>(SingleCamArea::SINGLE_CAM_LEFT)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_singleCam->getGeometry(static_cast<vfc::uint32_t>(SingleCamArea::SINGLE_CAM_RIGHT)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_twoCam->getGeometry(static_cast<vfc::uint32_t>(TwoCamArea::TWO_CAM_FRONT_LEFT)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_twoCam->getGeometry(static_cast<vfc::uint32_t>(TwoCamArea::TWO_CAM_FRONT_RIGHT)),
            osg::CopyOp::SHALLOW_COPY));
    }
    break;
    case SingleCamArea::SINGLE_CAM_RIGHT:
    {
        l_geometries.push_back(new osg::Geometry(
            *l_singleCam->getGeometry(static_cast<vfc::uint32_t>(SingleCamArea::SINGLE_CAM_RIGHT)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_singleCam->getGeometry(static_cast<vfc::uint32_t>(SingleCamArea::SINGLE_CAM_FRONT)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_singleCam->getGeometry(static_cast<vfc::uint32_t>(SingleCamArea::SINGLE_CAM_REAR)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_twoCam->getGeometry(static_cast<vfc::uint32_t>(TwoCamArea::TWO_CAM_FRONT_RIGHT)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_twoCam->getGeometry(static_cast<vfc::uint32_t>(TwoCamArea::TWO_CAM_REAR_RIGHT)),
            osg::CopyOp::SHALLOW_COPY));
    }
    break;
    case SingleCamArea::SINGLE_CAM_REAR:
    {
        l_geometries.push_back(new osg::Geometry(
            *l_singleCam->getGeometry(static_cast<vfc::uint32_t>(SingleCamArea::SINGLE_CAM_REAR)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_singleCam->getGeometry(static_cast<vfc::uint32_t>(SingleCamArea::SINGLE_CAM_LEFT)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_singleCam->getGeometry(static_cast<vfc::uint32_t>(SingleCamArea::SINGLE_CAM_RIGHT)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_twoCam->getGeometry(static_cast<vfc::uint32_t>(TwoCamArea::TWO_CAM_REAR_LEFT)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_twoCam->getGeometry(static_cast<vfc::uint32_t>(TwoCamArea::TWO_CAM_REAR_RIGHT)),
            osg::CopyOp::SHALLOW_COPY));
    }
    break;
    case SingleCamArea::SINGLE_CAM_LEFT:
    {
        l_geometries.push_back(new osg::Geometry(
            *l_singleCam->getGeometry(static_cast<vfc::uint32_t>(SingleCamArea::SINGLE_CAM_LEFT)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_singleCam->getGeometry(static_cast<vfc::uint32_t>(SingleCamArea::SINGLE_CAM_REAR)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_singleCam->getGeometry(static_cast<vfc::uint32_t>(SingleCamArea::SINGLE_CAM_FRONT)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_twoCam->getGeometry(static_cast<vfc::uint32_t>(TwoCamArea::TWO_CAM_REAR_LEFT)),
            osg::CopyOp::SHALLOW_COPY));
        l_geometries.push_back(new osg::Geometry(
            *l_twoCam->getGeometry(static_cast<vfc::uint32_t>(TwoCamArea::TWO_CAM_FRONT_LEFT)),
            osg::CopyOp::SHALLOW_COPY));
    }
    break;

    default:
    {
        return;
    }
    }
    // Also add nocam geode to get full UV coverage, we will mask it later on
    l_geometries.push_back(new osg::Geometry(*f_sv3dNode->getNoCamGeode()->getGeometry(), osg::CopyOp::SHALLOW_COPY));
    // Remove nocam stateset
    (*l_geometries.back()).setStateSet(nullptr);

    // overwrite tex coord binding and add to geode
    assert(l_geometries.size() > 0);
    osg::Array* const l_texCoord = l_geometries.front()->getTexCoordArray(0);

    for (std::size_t i = 0; i < l_geometries.size(); i++) // PRQA S 4297 // PRQA S 4687
    {
        l_geometries[i]->setTexCoordArray(0, l_texCoord);
        l_geode->addDrawable(l_geometries[i]); // PRQA S 3803
    }

    osg::StateSet*                         const l_stateSet = l_geode->getOrCreateStateSet();
    pc::core::BasicShaderProgramDescriptor l_uvShader("renderUV");
    l_uvShader.apply(l_stateSet); // PRQA S 3803
}

void SV3DUVRenderNode::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        //! SV3D nodes are typically updated if the sat cam calib or mask changed.
        //! However, this will only happen if the corresponding SV3D node is active in the current scene graph.
        //! To ensure a correct update behavior we manually forward the update visitor to the related SV3D node.
        m_sv3dNode->accept(f_nv);

        if (m_sv3dNode->getVertexArray()->getModifiedCount() != m_lastVertexArrayUpdate)
        {
            //! since we're using a shared/external vertex array, the bounding spheres of the drawables of this node
            //! might not be up-to-date any more, if the vertex array of the associated SV3D node has been modified
            osg::Geode*  const l_geode        = getChild(0)->asGeode();
            const unsigned int l_numDrawables = l_geode->getNumDrawables(); // PRQA S 2427
            for (unsigned int i = 0; i < l_numDrawables; ++i) // PRQA S 2427
            {
                l_geode->getDrawable(i)->dirtyBound();
            }
            m_lastVertexArrayUpdate = m_sv3dNode->getVertexArray()->getModifiedCount();
        }
    }
    osg::Group::traverse(f_nv);
}

} // namespace fisheyetransition
} // namespace assets
} // namespace cc
