/// @copyright (C) 2024 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.

#include "cc/assets/trajectory/inc/CrabTrajectory.h"
#include "cc/imgui/inc/imgui_manager.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

#include <osg/Texture2D>
#include <osgDB/ReadFile>
#include <osgDB/WriteFile>

#include "vfc/core/vfc_math.hpp"

using pc::vehicle::g_mechanicalData;

namespace cc
{
namespace assets
{
namespace trajectory
{

class CrabTrajectorySettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(CrabTrajectorySettings)
    {
        ADD_MEMBER(osg::Vec4f, color);
        ADD_MEMBER(osg::Vec4f, colorBorder);
        ADD_FLOAT_MEMBER(wheelOffset);
    }

    osg::Vec4f m_color{1.0f, 1.0f, 1.0f, 0.5f};
    osg::Vec4f m_colorBorder{1.0f, 1.0f, 1.0f, 0.9f};
    vfc::float32_t m_wheelOffset{0.05f};
};

static pc::util::coding::Item<CrabTrajectorySettings> g_crabTrajSettings("CrabTrajectorySettings");

constexpr float g_blurMul = 1.2f; // (1 <= ) // PRQA S 2446

// pc::core::Framework*                         f_framework,
// cc::assets::trajectory::commontypes::Side_en f_side,
// vfc::uint32_t                                f_numOfVertexLines,
// vfc::float32_t                               f_height,
// const TrajectoryParams_st&                   f_trajParams,
// bool                                         f_hideCallback);
CrabTrajectory::CrabTrajectory(
    pc::core::Framework*       f_framework,
    commontypes::Side_en      /*f_side*/ ,
    const TrajectoryParams_st& f_trajParams,
    float                      f_height, // PRQA S 2446
    unsigned int               f_numOfVerts) // PRQA S 2427
    : GeneralTrajectoryLine(f_framework, commontypes::Middle_enm, 2u, f_height, f_trajParams, true) // PRQA S 2323
    , mc_numOfVerts(f_numOfVerts), m_colorArray(nullptr) // PRQA S 2323
{
    m_geometry->addPrimitiveSet(new osg::DrawElementsUShort(osg::PrimitiveSet::TRIANGLES)); // PRQA S 3803

    osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*>(m_geometry->getColorArray()); // PRQA S 3076
    l_colors->setBinding(osg::Array::BIND_PER_VERTEX);

    osg::Vec2ubArray* const l_texCoords = new osg::Vec2ubArray;
    l_texCoords->setNormalize(true);
    l_texCoords->reserve(2u * mc_numOfVerts);
    for (unsigned int l_vertexIndex = 0u; l_vertexIndex < mc_numOfVerts; l_vertexIndex++)
    {
        l_texCoords->push_back(osg::Vec2ub(0u, 127u));
    }
    for (unsigned int l_vertexIndex = 0u; l_vertexIndex < mc_numOfVerts; l_vertexIndex++)
    {
        l_texCoords->push_back(osg::Vec2ub(255u, 127u));
    }
    m_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);
}

void CrabTrajectory::setTextureData(
    osg::Image* f_image,
    osg::Vec4ub f_lineColor_Inside,
    osg::Vec4ub f_lineColor_BorderLine)
{
    if (f_image == nullptr)
    {
        return;
    }
    osg::Vec4ub* l_data = reinterpret_cast<osg::Vec4ub*>(f_image->data()); // PRQA S 3030
    const vfc::int32_t tmp = f_image->s() - 1;
    const float lc_imageGeometryWidth = static_cast<float>(tmp); // PRQA S 2446
    const float lc_halfGradientWidth  = std::abs(g_trajCodingParams->m_gradientWidth) * 0.5f; // PRQA S 2446
    const float lc_halfWholeWidth = // PRQA S 2446
        g_trajCodingParams->m_outermostLine_Width * 0.5f; // Half of the whole width of the wheel track
    const float lc_halfGeometryWidth = (lc_halfWholeWidth + lc_halfGradientWidth) * g_blurMul;

    std::array<float, 1> l_absDistancesFromCenter;
    l_absDistancesFromCenter[0] = lc_halfWholeWidth + lc_halfGradientWidth;

    std::array<float, 2> l_normalizedPositions;
    l_normalizedPositions[0] = (lc_halfGeometryWidth - l_absDistancesFromCenter[0]) / m_lineGeometryWidth;
    l_normalizedPositions[1] = 1.0f - l_normalizedPositions[0];

    for (vfc::uint32_t x{0}; x < static_cast<vfc::uint32_t>(f_image->s()); ++x)
    {
        const float l_x_normalized = static_cast<float>(x) / lc_imageGeometryWidth;

        if ((l_x_normalized < l_normalizedPositions[0]) || (l_x_normalized > l_normalizedPositions[1]))
        {
            // Outside the wheel track
            (*l_data) = f_lineColor_BorderLine;
        }
        else
        {
            // Middle of the right border line
            (*l_data) = f_lineColor_Inside;
        }
        ++l_data;
    }
}

osg::Image* CrabTrajectory::create1DTexture()
{
    osg::Vec4ub l_lineColor_Inside;
    osg::Vec4ub l_lineColor_BorderLine;

    l_lineColor_Inside.r() = static_cast<vfc::uint8_t>(osg::clampBetween((g_trajCodingParams->m_DrivablePath_Color.r()) * 255.0f, 0.0f, 255.0f));
    l_lineColor_Inside.g() = static_cast<vfc::uint8_t>(osg::clampBetween((g_trajCodingParams->m_DrivablePath_Color.b()) * 255.0f, 0.0f, 255.0f));
    l_lineColor_Inside.b() = static_cast<vfc::uint8_t>(osg::clampBetween((g_trajCodingParams->m_DrivablePath_Color.g()) * 255.0f, 0.0f, 255.0f));
    l_lineColor_Inside.a() = static_cast<vfc::uint8_t>(osg::clampBetween((g_trajCodingParams->m_DrivablePath_Color.a()) * 255.0f, 0.0f, 255.0f));

    l_lineColor_BorderLine.r() = static_cast<vfc::uint8_t>(osg::clampBetween((g_trajCodingParams->m_DrivablePath_BorderColor.r()) * 255.0f, 0.0f, 255.0f));
    l_lineColor_BorderLine.g() = static_cast<vfc::uint8_t>(osg::clampBetween((g_trajCodingParams->m_DrivablePath_BorderColor.b()) * 255.0f, 0.0f, 255.0f));
    l_lineColor_BorderLine.b() = static_cast<vfc::uint8_t>(osg::clampBetween((g_trajCodingParams->m_DrivablePath_BorderColor.g()) * 255.0f, 0.0f, 255.0f));
    l_lineColor_BorderLine.a() = static_cast<vfc::uint8_t>(osg::clampBetween((g_trajCodingParams->m_DrivablePath_BorderColor.a()) * 255.0f, 0.0f, 255.0f));

    constexpr unsigned int lc_imageWidth  = 256U; // Image width in pixels. // PRQA S 2427
    constexpr unsigned int lc_imageHeight = 1U;   // Image height in pixels. // PRQA S 2427
    constexpr unsigned int lc_imageDepth  = 1U;   // Image depth in pixels, in case of a 3D image // PRQA S 2427
    osg::Image* const l_image = new osg::Image;
    l_image->allocateImage(lc_imageWidth, lc_imageHeight, lc_imageDepth, GL_RGBA, GL_UNSIGNED_BYTE);
    setTextureData(l_image, l_lineColor_Inside, l_lineColor_BorderLine);
    return l_image;
}

osg::Image* CrabTrajectory::create2DTexture(const std::string& f_filename) const
{
    return osgDB::readImageFile(f_filename);
}

void CrabTrajectory::generateVertexData()
{
    generateVertexData_usingTexture();
}

void CrabTrajectory::generateVertexData_usingTexture()
{
    m_frame.removeAllPoints();

    const float l_trajectoryLength                = g_trajCodingParams->m_length;
    // float       l_wheelCenterAngle                = 0.0f;
    // float       l_wheelCenterRadius               = 0.0f;
    float       l_wheelCenterLongitudinalPosLeft  = 0.0f;
    float       l_wheelCenterLongitudinalPosRight = 0.0f;
    const float lc_halfGeometryWidth = m_lineGeometryWidth * 0.5f; // + IMGUI_GET_SLIDER_FLOAT("Settings", "HalfGeometryWidth", 0.1f, 5.0f);
    const vfc::float32_t lc_halfWidth = m_trajParams.OutermostLine_Width * 0.5f;
    const vfc::float32_t lc_touchPointToGeometryOuterEdge = lc_halfGeometryWidth - lc_halfWidth;
    const vfc::float32_t lc_touchPointToGeometryInnerEdge = m_lineGeometryWidth - lc_touchPointToGeometryOuterEdge; // PRQA S 4209
    const vfc::float32_t lc_halfWheeltrackWidth = m_trajParams.WheelTrack_Width_Whole * 0.5f; // Half of the whole width of the wheel track // PRQA S 4209
    const vfc::float32_t lc_halfGradientWidth = std::abs(m_trajParams.GradientWidth) * 0.5f; // PRQA S 4209

    osg::Vec2f leftPos{0.0f, 0.0f};
    osg::Vec2f rightPos{0.0f, 0.0f};
    {
        auto drivingDirection = sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection;
        auto turningDirection = sm_mainLogicRefPtr->getInputDataRef().Internal.TurningDirection;
        {
            using namespace mainlogic;
            using namespace commontypes;
            if (drivingDirection == DrivingDirection_en::Forward_enm && turningDirection == TurningDirection_en::ToLeft_enm)
            {
                leftPos = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_OppToDrvDir.Pos;
                rightPos = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos;
            }
            else if (drivingDirection == DrivingDirection_en::Forward_enm && turningDirection == TurningDirection_en::ToRight_enm)
            {
                leftPos = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos;
                rightPos = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_OppToDrvDir.Pos;
            }
            else if (drivingDirection == DrivingDirection_en::Backward_enm && turningDirection == TurningDirection_en::ToRight_enm)
            {
                leftPos = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_OppToDrvDir.Pos;
                rightPos = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos;
            }
            else // if (drivingDirection == DrivingDirection_en::Backward_enm && turningDirection == TurningDirection_en::ToLeft_enm)
            {
                leftPos = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos;
                rightPos = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_OppToDrvDir.Pos;
            }
        }
        leftPos.y() += g_crabTrajSettings->m_wheelOffset;
        rightPos.y() -= g_crabTrajSettings->m_wheelOffset;
    }
    l_wheelCenterLongitudinalPosLeft  = leftPos.x();
    l_wheelCenterLongitudinalPosRight = rightPos.x();

    // vfc::float32_t offset   = IMGUI_GET_SLIDER_FLOAT("Settings", "Offset", -20.0f, 20.0f);
    // // vfc::float32_t offset = -2.0f;
    // if (ToLeft_enm == sm_mainLogicRefPtr->getInputDataRef().Internal.TurningDirection)
    // {
    //     l_wheelCenterLongitudinalPosLeft  = leftPos.x() + offset;
    //     l_wheelCenterLongitudinalPosRight = leftPos.x() + offset;
    // }
    // else
    // {
    //     l_wheelCenterLongitudinalPosLeft  = rightPos.x() + offset;
    //     l_wheelCenterLongitudinalPosRight = rightPos.x() + offset;
    // }
    m_frameLateralOffsets[0] =
        leftPos.y() + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
    m_frameLateralOffsets[1] =
        rightPos.y() + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

    m_frame.setVertexLineOffset(0, m_frameLateralOffsets[0]);
    m_frame.setVertexLineOffset(1, m_frameLateralOffsets[1]);

    m_frame.setBumperLineAngle(0, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Angle);
    m_frame.setBumperLinePos(0, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Pos.x());

    float l_startPosLeft  = 0.0f;
    float l_startPosRight = 0.0f;

    l_startPosLeft  = l_wheelCenterLongitudinalPosLeft;
    l_startPosRight = l_wheelCenterLongitudinalPosRight;

    const osg::Vec4f l_lineColor = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f); // With texturing just the alpha matters in the shader.
    commontypes::ControlPoint_st l_controlPoint;

    l_controlPoint.LongitudinalPos = l_startPosLeft;
    l_controlPoint.Color           = l_lineColor;
    l_controlPoint.Index           = 0; // Dummy value. Will be calculated later.
    m_frame.addControlPoint(0, l_controlPoint);
    l_controlPoint.LongitudinalPos = l_startPosRight;
    m_frame.addControlPoint(1, l_controlPoint);
    l_controlPoint.Color = l_lineColor;

    float l_endPos = 0.0f; // PRQA S 2446
    l_endPos = m_frame.getBumperLinePos(0) + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                                                 sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul *
                                                 l_trajectoryLength;

    l_controlPoint.LongitudinalPos = l_endPos;
    m_frame.addControlPoint(0, l_controlPoint);
    m_frame.addControlPoint(1, l_controlPoint);

    m_frame.setFadeIn(0u, true);
    m_frame.setFadeIn(1u, true);
    m_frame.setFadeOut(0u, false);
    m_frame.setFadeOut(1u, false);

    m_frame.setFadeInStartPos(0u, l_startPosLeft);
    m_frame.setFadeInStartPos(1u, l_startPosRight);
    m_frame.setFadeInEndPos(0u, l_endPos - IMGUI_GET_SLIDER_FLOAT("Settings", "FadeOffset", 0.0f, 10.0f));
    m_frame.setFadeInEndPos(1u, l_endPos - IMGUI_GET_SLIDER_FLOAT("Settings", "FadeOffset", 0.0f, 10.0f));

    osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*>(m_geometry->getVertexArray());
    l_vertices->clear();

    m_colorArray = static_cast<osg::Vec4Array*>(m_geometry->getColorArray());
    m_colorArray->clear();

    const float l_translationAngle_Rad =
        osg::DegreesToRadians(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle);

    m_frame.generateVertices(
        0,
        0,
        1,
        sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint,
        m_height,
        l_vertices,
        m_colorArray,
        frame::VertexDistributionMode_en::Manual_enm,
        mc_numOfVerts,
        commontypes::Translation_enm,
        l_translationAngle_Rad);

    m_frame.generateVertices(
        1,
        0,
        1,
        sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint,
        m_height,
        l_vertices,
        m_colorArray,
        frame::VertexDistributionMode_en::Manual_enm,
        mc_numOfVerts,
        commontypes::Translation_enm,
        l_translationAngle_Rad);

    // *** 3. Create indices ***
    osg::DrawElementsUShort* const l_indices = static_cast<osg::DrawElementsUShort*>(m_geometry->getPrimitiveSet(0));
    l_indices->clear();
    m_frame.generateIndices(0, 0, 1, 0, mc_numOfVerts, l_indices);

    l_vertices->dirty();
    m_colorArray->dirty();
    l_indices->dirty();
    m_geometry->dirtyBound();

    setCull(false);
}

} // namespace trajectory
} // namespace assets
} // namespace cc
