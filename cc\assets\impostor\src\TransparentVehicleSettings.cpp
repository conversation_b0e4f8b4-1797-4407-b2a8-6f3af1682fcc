//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CSP9BP Csikos Patrik (XC-AS/EPW2-BP)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  TransparentVehicleSettings.cpp
/// @brief
//=============================================================================

#include "cc/assets/impostor/inc/TransparentVehicleSettings.h"
#include "pc/svs/util/math/inc/FloatComp.h"

namespace cc
{
namespace assets
{
namespace impostor
{

pc::util::coding::Item<TransparentVMData> g_transpVMData("TransparentVehicleModel");

float TransparentVMData::calculateTransparencyLevel(const float f_elapsedTime, float f_currentTransparency, const vfc::uint8_t f_transpLevel) const
{
  const float l_step = g_transpVMData->m_transparencyStep * f_elapsedTime;
  if( (isGreater(f_currentTransparency, g_transpVMData->m_targetTransparency) && 1u == f_transpLevel)
   || (isGreater(f_currentTransparency, 0.0f) && 2u == f_transpLevel))
  {
    f_currentTransparency -= l_step;
  }
  else if (isLess(f_currentTransparency, g_transpVMData->m_targetTransparency) && 1u == f_transpLevel
   ||     (isLess(f_currentTransparency, 1.0f) && 0u == f_transpLevel))
  {
    f_currentTransparency += l_step;
  }
  else
  {

  }

  // current - target < step then set to target to avoid
  if (0u == f_transpLevel)
  {
    if (isLess(std::abs(f_currentTransparency - 1.f), l_step))
    {
      f_currentTransparency = 1.0f;
    }
  }

  if (1u == f_transpLevel)
  {
    if (isLess(std::abs(f_currentTransparency - g_transpVMData->m_targetTransparency), l_step))
    {
      f_currentTransparency = g_transpVMData->m_targetTransparency;
    }
  }

  if (2u == f_transpLevel)
  {
    if (isLess(f_currentTransparency, l_step))
    {
      f_currentTransparency = 0.0f;
    }
  }

  return f_currentTransparency;
}
} // cc
} // assets
} // impostor
