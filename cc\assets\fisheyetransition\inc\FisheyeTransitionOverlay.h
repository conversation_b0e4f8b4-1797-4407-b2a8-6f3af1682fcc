//-------------------------------------------------------------------------------
// Copyright (c) 2020 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_FISHEYE_TRANSITION_FISHEYE_TRANSITION_OVERLAY_HPP
#define CC_ASSETS_FISHEYE_TRANSITION_FISHEYE_TRANSITION_OVERLAY_HPP

// #include "cc/core/inc/CustomScene.h"
#include "pc/svs/animation/inc/Animation.h"
#include "pc/svs/core/inc/Asset.h"
#include "pc/svs/factory/inc/SV3DNode.h"
#include "pc/svs/views/warpfisheyeview/inc/FisheyeModels.h"

#include "osg/Camera"
#include "osg/Group"
#include "osg/ref_ptr"

namespace pc
{
namespace core
{
class Framework;
class View;
} // namespace core

namespace views
{
namespace warpfisheye
{
class WarpFisheyeView;
} // namespace warpfisheye
} // namespace views
} // namespace pc

namespace cc
{
namespace assets
{
namespace fisheyetransition
{
osg::ref_ptr<osg::Texture2D> createTexture(const std::string& f_filename);
class FisheyeTransitionOverlay : public pc::core::Asset
{
public:
    FisheyeTransitionOverlay(
        cc::core::AssetId                        f_assetId,
        pc::core::View&                          f_sourceView,
        cc::core::AssetId                        f_sv3dAsset,
        unsigned int                             f_subsampleFactor,
        pc::factory::SV3DNode&                   f_floor,
        pc::factory::SV3DNode&                   f_bowl,
        pc::factory::SingleCamArea               f_area,
        pc::views::warpfisheye::WarpFisheyeView& f_fisheyeView,
        pc::views::warpfisheye::FisheyeModel&    f_pModel,
        pc::core::Framework*                     f_framework);

    pc::animation::Animation*
    createTransitionAnimation(float f_startBlend, float f_endBlend, float f_duration, bool f_fromFisheye);
    pc::core::Framework* getFramework()
    {
        return m_framework;
    }

protected:
    virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
    void startTransition(float f_startBlend, float f_endBlend, float f_duration);

    class TransitionAnimation : public pc::animation::Animation
    {
    public:
        TransitionAnimation(
            float                     f_startBlend,
            float                     f_endBlend,
            float                     f_duration,
            FisheyeTransitionOverlay& f_overlay,
            bool                      f_isFromFisheye);

        virtual float getFixedDuration() const override;
        virtual bool  hasFixedDuration() const override;

        virtual bool supportsCancellation() const override;

    protected:
        virtual void onBegin() override;
        virtual bool onUpdate(float f_elapsed) override;
        virtual void onEnd(bool /*f_canceled*/) override;

    private:
        FisheyeTransitionOverlay& m_overlay;
        float                     m_startBlend;
        float                     m_endBlend;
        float                     m_duration;
        bool                      m_isFromFisheye;
    };

    void updateTransitionAnimation(float f_blendValue);
    bool requiresSourceUVUpdate();
    void showFisheyeView();

    void plot();

    pc::core::Framework* m_framework; //!< Access calibration via \ref pc::core::Framework
    pc::core::View&      m_parentView;
    cc::core::AssetId    m_parentViewSv3dAsset;

    pc::views::warpfisheye::WarpFisheyeView& m_fisheyeView;

    vfc::uint16_t m_calibSeqNumber;        //!< Seq Number for camera calibration
    float         m_capturedSteeringAngle; // corresponding steering angle of the captured source image
    vfc::uint16_t m_settingsModifCount;    // modified count for panorama settings

    bool m_requiresUpdate;
    bool m_transitionEnabled;
    bool m_hasSourceData;
    bool m_hasTargetData;
    bool m_isFromFisheye;

    unsigned int m_sourceDataCaptureFrame;
    unsigned int m_sourceDataDownloadFrame;
    unsigned int m_width;
    unsigned int m_height;

    osg::ref_ptr<osg::Image>                           m_sourceUVMap;
    osg::ref_ptr<osg::Camera>                          m_captureUVCamera;
    osg::ref_ptr<osg::Camera>                          m_textureDisplayCamera;
    osg::ref_ptr<osg::Geode>                           m_textureDisplayGeode;
    pc::views::warpfisheye::FisheyeModel&              m_model;

    std::vector<osg::Vec2us> m_sourceUV;
    std::vector<osg::Vec2us> m_targetUV;

    float m_blendValue;
    bool  m_updateSteeringValue;
};

} // namespace fisheyetransition
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_FISHEYE_TRANSITION_FISHEYE_TRANSITION_OVERLAY_HPP
