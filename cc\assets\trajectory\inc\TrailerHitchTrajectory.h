//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_SUBASSETS_TRAILERHITCHTRAJECTORY
#define CC_ASSETS_TRAJECTORY_SUBASSETS_TRAILERHITCHTRAJECTORY

#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace trajectory
{
namespace mainlogic
{
  class MainLogic;
  struct ModelData_st;
  struct Inputs_st;
} // namespace mainlogic


//!
//! @brief TrailerHitchTrajectory
//!
//!
class TrailerHitchTrajectory : public cc::assets::trajectory::GeneralTrajectoryLine
{
public:

  TrailerHitchTrajectory(
    const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
    vfc::uint32_t f_numOfVerts,
    pc::core::Framework* f_framework);

  virtual void generateVertexData();
  virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:
  virtual ~TrailerHitchTrajectory();
  const vfc::uint32_t mc_numOfVerts;
  pc::core::Framework* m_framework;
  vfc::uint32_t m_lastCalibUpdate;

};


//!
//! @brief TrailerHitchCircle
//!
//!
class TrailerHitchCircle : public osg::Group
{
public:

  TrailerHitchCircle(pc::core::Framework* f_framework);

protected:

  virtual ~TrailerHitchCircle();

  osg::Image* create1DTexture();
  void createCircleGeometry();
  void loadTexture(osg::Geometry* f_geometry);
  void applyTexShaderToGeometry(osg::Geometry* f_geometry, vfc::uint32_t f_renderBinOrder, bool f_depthTest, bool f_depthBufferWrite, bool f_blend);
  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
  //! Copy constructor is not permitted.
  TrailerHitchCircle (const TrailerHitchCircle& other); // = delete
  //! Copy assignment operator is not permitted.
  TrailerHitchCircle& operator=(const TrailerHitchCircle& other); // = delete

private:
  osg::ref_ptr<osg::Geode> m_circleGeode;
  pc::core::Framework* m_framework;
  vfc::uint32_t m_lastCalibUpdate;
};


} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_SUBASSETS_TRAILERHITCHTRAJECTORY
