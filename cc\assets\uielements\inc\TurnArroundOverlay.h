//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#ifndef CC_ASSETS_UIELEMENT_TURN_ARROUND_OVERLAY_H
#define CC_ASSETS_UIELEMENT_TURN_ARROUND_OVERLAY_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace uielements
{

class TurnArroundOverlaySettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(TurnArroundOverlaySettings)
    {
        ADD_STRING_MEMBER(turnArroundCalibrateVehicle);
        ADD_STRING_MEMBER(compassCalibrateVehicle);
        ADD_STRING_MEMBER(carbCalibrateVehicle);
        ADD_STRING_MEMBER(hmCalibrateVehicle);
        ADD_FLOAT_MEMBER(scaleFactor);
        ADD_FLOAT_MEMBER(viewportOffset);
        ADD_MEMBER(osg::Vec2f, gearPos);
        ADD_MEMBER(osg::Vec2i, hmCalibrateVehiclePos);
        ADD_MEMBER(osg::Vec2i, turnArroundCalibrateVehiclePos);
        ADD_MEMBER(osg::Vec2i, compassCalibrateVehiclePos);
        ADD_MEMBER(osg::Vec2i, carbCalibrateVehiclePos);
        ADD_FLOAT_MEMBER(meterPerPixelHM);
        ADD_FLOAT_MEMBER(meterPerPixelTR);
        ADD_FLOAT_MEMBER(meterPerPixelCrab);
    }

    std::string m_turnArroundCalibrateVehicle = "cc/resources/CalibrateVehicle.png";
    std::string m_compassCalibrateVehicle = "cc/resources/compass.png";
    std::string m_carbCalibrateVehicle = "cc/resources/crabTurnOverlay.png";
    std::string m_hmCalibrateVehicle = "cc/resources/horizontalMoveOverlay.png";
    vfc::float32_t m_viewportOffset = 0.3f;
    vfc::float32_t m_scaleFactor = 1.0f;
    osg::Vec2f m_gearPos =  osg::Vec2f(50.0f, 50.0f);
    osg::Vec2i     m_hmCalibrateVehiclePos;
    osg::Vec2i     m_turnArroundCalibrateVehiclePos;
    osg::Vec2i     m_compassCalibrateVehiclePos;
    osg::Vec2i     m_carbCalibrateVehiclePos;
    vfc::float32_t m_meterPerPixelHM = 3.f/360.f;
    vfc::float32_t m_meterPerPixelTR = 0.01;
    vfc::float32_t m_meterPerPixelCrab = 0.01;
};

extern pc::util::coding::Item<TurnArroundOverlaySettings> g_defaultSettings;

class TurnArroundOverlay : public pc::assets::ImageOverlays
{
public:
    enum TurnArroundIcon
    {
        CALIBRATE_VEHICLE = 0,
        // CALIBRATE_GEAR_D = 1,
        // CALIBRATE_GEAR_R = 2,
        // CALIBRATE_GEAR_P = 3,
        // CALIBRATE_GEAR_N = 4,
        NUM_ICONS
    };
public:
    TurnArroundOverlay(
        pc::core::Framework* f_framework,
        cc::core::AssetId                 f_assetId,
        const TurnArroundOverlaySettings& f_settings,
        std::string                       f_imagePath,
        osg::Vec2i                        f_iconPos,
        osg::Camera*                      f_referenceView = nullptr);

    void traverse(osg::NodeVisitor& f_nv) override;

private:
    void init();
    void update(vfc::float64_t f_time);

private:
    pc::core::Framework*               m_framework;
    const TurnArroundOverlaySettings& m_settings;
    vfc::uint32_t                     m_modifiedCount = ~0u;
    pc::assets::IconGroup             m_icons;
    // pc::assets::IconGroup             m_gearIcons;
    std::string                       m_imagePath;
    osg::Vec2i                        m_imagePos;
};

class ViewEnlargeCallback : public osg::NodeCallback
{
public:
    ViewEnlargeCallback(
        osg::Camera* f_camera,
        pc::core::Framework* f_framework,
        pc::virtcam::VirtualCamera f_camPos,
        const std::string& f_name
    );

    virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv);

protected:
    virtual ~ViewEnlargeCallback() = default;

private:
    //! Copy constructor is not permitted.
    ViewEnlargeCallback (const ViewEnlargeCallback& other) = delete;
    //! Copy assignment operator is not permitted.
    ViewEnlargeCallback& operator=(const ViewEnlargeCallback& other) = delete;

    void updatePlanView(osg::Camera* f_camera);

private:
    vfc::float64_t m_top;
    vfc::float64_t m_bottom;
    vfc::float64_t m_left;
    vfc::float64_t m_right;
    vfc::float64_t m_zNear;
    vfc::float64_t m_zFar;
    pc::virtcam::VirtualCamera m_camPos;
};

class TurnArroundProjectionUpdateVisitor : public osg::NodeVisitor
{
public:
    TurnArroundProjectionUpdateVisitor(const std::string f_imagePath);

    void apply(osg::Node& f_node) override;

private:
    vfc::float64_t m_top    = 0.0;
    vfc::float64_t m_bottom = 0.0;
    vfc::float64_t m_left   = 0.0;
    vfc::float64_t m_right  = 0.0;
    const std::string m_imagePath;
};

class MeterPerPixelProjectionUpdateVisitor : public osg::NodeVisitor
{
public:
    MeterPerPixelProjectionUpdateVisitor(vfc::float64_t f_meterPerPixel);

    void apply(osg::Node& f_node) override;

private:
    vfc::float64_t m_top    = 0.0;
    vfc::float64_t m_bottom = 0.0;
    vfc::float64_t m_left   = 0.0;
    vfc::float64_t m_right  = 0.0;
    vfc::float64_t m_meterPerPixel  = 0.0;
};

} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENT_TURN_ARROUND_OVERLAY_H
