//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_BUTTON_H
#define CC_ASSETS_BUTTON_H

#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/assets/uielements/inc/CustomIcon.h"
#include "cc/util/logging/inc/LoggingContexts.h"

using pc::util::logging::g_AppContext;

#define GET_PORT_DATA(dataDaddy, port, allPortHaveDataFlag)                                                            \
    const auto dataDaddy  = (port).getData();                                                                          \
    (allPortHaveDataFlag) = (allPortHaveDataFlag) && ((dataDaddy) != nullptr);                                         \
    if ((dataDaddy) == nullptr)                                                                                        \
    {                                                                                                                  \
        XLOG_ERROR(g_AppContext, #port << " doesn't have data!\n");                                                    \
    }

namespace pc
{
namespace core
{
class View;
} // namespace core

} // namespace pc

namespace cc
{
namespace assets
{
namespace uielements
{
class CustomIcon;
} // namespace uielements

namespace button
{

class ButtonTexturePath : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(ButtonTexturePath)
    {
        ADD_STRING_MEMBER(PressedTexturePath);
        ADD_STRING_MEMBER(AvailableTexturePath);
        ADD_STRING_MEMBER(UnavailableTexturePath);
    }

    std::string m_PressedTexturePath;
    std::string m_AvailableTexturePath;
    std::string m_UnavailableTexturePath;
};


class ButtonTextureSettings : public pc::util::coding::ISerializable
{
public:

    SERIALIZABLE(ButtonTextureSettings)
    {
        ADD_MEMBER(ButtonTexturePath, day);
        ADD_MEMBER(ButtonTexturePath, night);
    }

    ButtonTexturePath m_day;
    ButtonTexturePath m_night;
};


class Button : public pc::assets::ImageOverlays
{
public:
    using RotateThemeType = cc::target::common::EThemeTypeHU;
    using DayNightThemeType = cc::target::common::EThemeTypeDayNight;

    enum ButtonState : vfc::uint8_t
    {
        INVALID = 0u,
        UNAVAILABLE = 1u,
        AVAILABLE = 2u,
        PRESSED = 3u,
        RELEASED = 4u
    };

    enum TouchStatus : vfc::uint8_t
    {
        TOUCH_INVALID = 0u,
        TOUCH_DOWN = 1u,
        TOUCH_UP = 2u,
        TOUCH_MOVE = 3u
    };

public:
    Button(cc::core::AssetId f_assetId, osg::Camera* f_referenceView=nullptr);

    void traverse(osg::NodeVisitor& f_nv) override;

    osg::Vec2f getIconPositionScreen();

    osg::Vec2f getResponseArea()
    {
        return m_responseArea;
    }

    RotateThemeType getRotateTheme() const
    {
        return m_rotateTheme;
    }

    DayNightThemeType getDayNightTheme() const
    {
        return m_dayNightTheme;
    }

    void setHoriReferenceView(pc::core::View* f_view)
    {
        m_horiReferenceView = f_view;
    }

    void setVertReferenceView(pc::core::View* f_view)
    {
        m_vertReferenceView = f_view;
    }

    void setRotateTheme(RotateThemeType f_theme)
    {
        if (f_theme != m_rotateTheme)
        {
            m_rotateTheme = f_theme;
            setReferenceView(m_rotateTheme == RotateThemeType::ETHEME_TYPE_HORI ? m_horiReferenceView : m_vertReferenceView);
            m_dirty = true;
        }
    }

    void setDayNightTheme(DayNightThemeType f_theme)
    {
        if (f_theme != m_dayNightTheme)
        {
            m_dayNightTheme = f_theme;
            m_dirty = true;
            m_dirtyState = true; // force update texture
        }
    }

    void setIconAtMiddle(bool f_enable)
    {
        m_iconAtMiddle = f_enable;
    }

    void setPositionHori(osg::Vec2f f_position)
    {
        m_positionHori = f_position;
    }

    void setSize(osg::Vec2f f_size)
    {
        m_size = f_size;
    }

    void setPositionVert(osg::Vec2f f_position)
    {
        m_positionVert = f_position;
    }

    void setTexturePath(const std::string& f_texturePath)
    {
        if (m_texturePath != f_texturePath)
        {
            m_texturePath = f_texturePath;
            m_dirty = true;
        }
    }

    void setIconEnable(bool f_enable)
    {
        m_iconEnable = f_enable;
    }

    bool isDayTheme()
    {
        return m_dayNightTheme == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY;
    }

    bool isNightTheme()
    {
        return m_dayNightTheme == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT;
    }

    bool checkTouchInsideResponseArea();

protected:
    virtual void update() = 0;

    ButtonState getState()
    {
        return m_state;
    }

    void setState(ButtonState f_state)
    {
        if (m_state != f_state)
        {
            m_state = f_state;
            m_dirtyState = true;
        }
    }

    vfc::uint32_t getSettingModifiedCount() const
    {
        return m_settingModifiedCount;
    }

    void setSettingModifiedCount(vfc::uint32_t f_modifiedCount)
    {
        if (m_settingModifiedCount != f_modifiedCount)
        {
            m_settingModifiedCount = f_modifiedCount;
            init();
        }
    }

    TouchStatus touchStatus() const { return m_touchStatus; }
    void setTouchStatus(const TouchStatus &touchStatus) { m_touchStatus = touchStatus; }

    vfc::uint16_t huX() const { return m_huX; }
    void setHuX(const vfc::uint16_t &huX) { m_huX = huX; }

    vfc::uint16_t huY() const { return m_huY; }
    void setHuY(const vfc::uint16_t &huY) { m_huY = huY; }

protected:
    void init();

private:
    virtual void onInvalid() = 0;
    virtual void onUnavailable() = 0;
    virtual void onAvailable() = 0;
    virtual void onPressed() = 0;
    virtual void onReleased() = 0;

    void updateIconCenter();

protected:
    osg::Vec2f m_size;
    osg::Vec2f m_iconCenter;
    osg::Vec2f m_responseArea;
    bool m_iconEnable = false;
    bool m_iconAtMiddle = false;
    osg::Vec2f m_positionHori;
    osg::Vec2f m_positionVert;
    TouchStatus m_touchStatus = TOUCH_INVALID;
    vfc::uint16_t m_huX = 0u;
    vfc::uint16_t m_huY = 0u;

    ButtonState m_state = INVALID;
    osg::ref_ptr<uielements::CustomIcon> m_icon;
    osg::ref_ptr<pc::core::View> m_horiReferenceView;
    osg::ref_ptr<pc::core::View> m_vertReferenceView;
    std::string m_texturePath;
    RotateThemeType m_rotateTheme = RotateThemeType::ETHEME_TYPE_HORI;
    DayNightThemeType m_dayNightTheme = DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT;
    bool m_dirty = true;
    bool m_dirtyState = true;
    vfc::uint32_t m_settingModifiedCount;
};

} // namespace button
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_BUTTON_H
