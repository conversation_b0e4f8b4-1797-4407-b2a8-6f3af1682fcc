//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
#ifndef CC_ASSETS_DISTANCE_OVERLAY_H
#define CC_ASSETS_DISTANCE_OVERLAY_H

#include <osg/Geode>
#include <osg/Group>
#include <osgText/Text>

#include "cc/target/common/inc/commonInterface.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace core
{
class CustomZoneLayout;
} // namespace core

namespace assets
{
namespace distanceoverlay
{

class DistanceOverlaySettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(DistanceOverlaySettings)
    {
        ADD_STRING_MEMBER(font);
        ADD_MEMBER(osg::Vec4f, color);
        ADD_FLOAT_MEMBER(offsetDistance);
        ADD_FLOAT_MEMBER(characterSize);
        ADD_FLOAT_MEMBER(bumperCharacterSize);
        ADD_STRING_MEMBER(stopTexture);
        ADD_FLOAT_MEMBER(frontStopCorner);
        ADD_FLOAT_MEMBER(rearStopCorner);
        ADD_FLOAT_MEMBER(leftStopCorner);
        ADD_FLOAT_MEMBER(rightStopCorner);
        ADD_MEMBER(osg::Vec2f, stopSize);
        ADD_FLOAT_MEMBER(stopDistanceThreshold);
        ADD_FLOAT_MEMBER(displayThreshold);
        ADD_FLOAT_MEMBER(displayThresholdSide);
        ADD_FLOAT_MEMBER(displayThresholdCorner);
    }

    std::string    m_font           = CONCAT_PATH("cc/resources/HYQiHei_55S.ttf");
    osg::Vec4f     m_color          = {1.0f, 1.0f, 1.0f, 1.0f};
    vfc::float32_t m_offsetDistance = 1.0f;
    vfc::float32_t m_sideOffsetDistance = 1.0f;
    vfc::float32_t m_characterSize  = 0.5f;
    vfc::float32_t m_bumperCharacterSize  = 0.2f;

    std::string    m_stopTexture     = CONCAT_PATH("cc/resources/stop.png");
    vfc::float32_t m_frontStopCorner = {5.7f};
    vfc::float32_t m_rearStopCorner  = {-3.5f};
    vfc::float32_t m_rightStopCorner = {-3.5f};
    vfc::float32_t m_leftStopCorner  = {3.5f};
    osg::Vec2f     m_stopSize        = {2.0f, 0.5f};

    vfc::float32_t m_stopDistanceThreshold = 0.19f;
    vfc::float32_t m_displayThreshold = 1.2f;
    vfc::float32_t m_displayThresholdSide{0.5f};
    vfc::float32_t m_displayThresholdCorner{0.6f};
};

extern pc::util::coding::Item<DistanceOverlaySettings> g_distanceOverlaySettings;
class DistanceOverlay : public osg::Group
{
public:
    DistanceOverlay(cc::core::CustomZoneLayout* f_zoneLayout, vfc::float32_t f_characterSize, pc::core::Framework* f_framework);

    void traverse(osg::NodeVisitor& f_nv) override;

    bool isStopDistanceEnabled() const { return m_stopDistanceEnabled; }
    void setStopDistanceEnabled(bool stopDistanceEnabled) { m_stopDistanceEnabled = stopDistanceEnabled; }

private:
    void init();
    void updateInput();
    void update();
    void updateFrontSector();
    void updateRearSector();
    void updateLeftSector();
    void updateRightSector();
    void updateImgui();
    bool checkDistanceThreshold(int f_index, vfc::float32_t f_distance);

private:
    static constexpr int NUM_ZONES = cc::target::sysconf::E_ULTRASONIC_NUM_ZONES;
    std::array<osg::Vec2f, NUM_ZONES>     m_innerPoints;
    std::array<osg::Vec2f, NUM_ZONES>     m_outerPoints;
    std::array<osg::Vec2f, NUM_ZONES>     m_positions;
    std::array<vfc::float32_t, NUM_ZONES> m_distances;

    cc::core::CustomZoneLayout* m_zoneLayout;
    pc::core::Framework*        m_framework;

    osg::ref_ptr<osg::Geode>    m_frontTextGeode;
    osg::ref_ptr<osg::Geode>    m_rearTextGeode;
    osg::ref_ptr<osg::Geode>    m_rightTextGeode;
    osg::ref_ptr<osg::Geode>    m_leftTextGeode;
    osg::ref_ptr<osg::Geode>    m_frontImageGeode;
    osg::ref_ptr<osg::Geode>    m_rearImageGeode;
    osg::ref_ptr<osg::Geode>    m_rightImageGeode;
    osg::ref_ptr<osg::Geode>    m_leftImageGeode;
    osg::ref_ptr<osgText::Text> m_frontText;
    osg::ref_ptr<osgText::Text> m_rearText;
    osg::ref_ptr<osgText::Text> m_rightText;
    osg::ref_ptr<osgText::Text> m_leftText;

    bool           m_dirty                    = true;
    vfc::uint32_t  m_modifiedCount            = ~0u;
    vfc::float32_t m_lastClosestDistanceFront = 10.0f;
    vfc::float32_t m_lastClosestDistanceRear  = 10.0f;
    vfc::float32_t m_lastClosestDistanceRight = 10.0f;
    vfc::float32_t m_lastClosestDistanceLeft  = 10.0f;
    vfc::float32_t m_characterSize = 0.5f;
    // interface for variant handling
    bool m_stopDistanceEnabled = true;
};

} // namespace distanceoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_DISTANCE_OVERLAY_H
