//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_ASSETS_H
#define CC_ASSETS_TRAJECTORY_ASSETS_H

#include "pc/svs/core/inc/Asset.h"
#include "cc/assets/trajectory/inc/OutermostLine.h"
#include "cc/assets/trajectory/inc/OutermostLineColorful.h"
#include "cc/assets/trajectory/inc/DL1.h"

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace trajectory
{
struct TrajectoryParams_st;
struct DIDescriptor_st;

constexpr vfc::float32_t g_height = 0.005f;
constexpr vfc::uint32_t g_numVerticesWheelTracks = 384u;

//!
//! @brief composit of all outermost lines
//!
//!
class OutermostLinesAsset : public pc::core::Asset
{
public:

  OutermostLinesAsset(
    cc::core::AssetId f_assetId,
    pc::core::Framework* f_framework,
    const TrajectoryParams_st& f_params,
    const DIDescriptor_st& f_diDescriptor);

  OutermostLine* getLeft()
  {
    return m_left.get();
  }

  OutermostLine* getRight()
  {
    return m_right.get();
  }

private:
  //! Copy constructor is not permitted.
  OutermostLinesAsset (const OutermostLinesAsset& other); // = delete
  //! Copy assignment operator is not permitted.
  OutermostLinesAsset& operator=(const OutermostLinesAsset& other); // = delete

protected:
  ~OutermostLinesAsset() = default;

private:

  osg::ref_ptr<OutermostLine> m_left;
  osg::ref_ptr<OutermostLine> m_right;
};

class OutermostLinesAssetColorful : public pc::core::Asset
{
public:

  OutermostLinesAssetColorful(
    cc::core::AssetId f_assetId,
    pc::core::Framework* f_framework,
    const TrajectoryParams_st& f_params,
    const DIDescriptor_st& f_diDescriptor);

  OutermostLineColorful* getLeft()
  {
    return m_left.get();
  }

  OutermostLineColorful* getRight()
  {
    return m_right.get();
  }
protected:
  ~OutermostLinesAssetColorful() = default;

private:
  //! Copy constructor is not permitted.
  OutermostLinesAssetColorful (const OutermostLinesAssetColorful& other); // = delete
  //! Copy assignment operator is not permitted.
  OutermostLinesAssetColorful& operator=(const OutermostLinesAssetColorful& other); // = delete

private:

  osg::ref_ptr<OutermostLineColorful> m_left;
  osg::ref_ptr<OutermostLineColorful> m_right;
};


//!
//! @brief DistanceLineAsset
//!
//!
class DistanceLineAsset : public pc::core::Asset
{
public:

  DistanceLineAsset(
    cc::core::AssetId f_assetId,
    pc::core::Framework* f_framework,
    const TrajectoryParams_st& f_params,
    const OutermostLine* f_leftOutermostLine,
    const OutermostLine* f_rightOutermostLine,
    vfc::uint32_t f_numLayoutPoints);

  DL1* getDistanceLine()
  {
    return m_distanceLine.get();
  }

protected:
  ~DistanceLineAsset() = default;

private:
  //! Copy constructor is not permitted.
  DistanceLineAsset (const DistanceLineAsset& other); // = delete
  //! Copy assignment operator is not permitted.
  DistanceLineAsset& operator=(const DistanceLineAsset& other); // = delete

private:

  osg::ref_ptr<DL1> m_distanceLine;
};


//!
//! @brief Composit asset of wheel tracks
//!
//!
class WheelTracksAsset : public pc::core::Asset
{
public:

  WheelTracksAsset(
    cc::core::AssetId f_assetId,
    pc::core::Framework* f_framework,
    const TrajectoryParams_st& f_params,
    const DL1* f_distanceLine);

protected:
  ~WheelTracksAsset() = default;

private:
  //! Copy constructor is not permitted.
  WheelTracksAsset (const WheelTracksAsset& other); // = delete
  //! Copy assignment operator is not permitted.
  WheelTracksAsset& operator=(const WheelTracksAsset& other); // = delete

};


//!
//! @brief Composit asset of Trailer Assist Line
//!
//!
class TrailerAssistLineAsset : public pc::core::Asset
{
public:

  TrailerAssistLineAsset(
    cc::core::AssetId f_assetId,
    pc::core::Framework* f_framework,
    const TrajectoryParams_st& f_params);

protected:
  ~TrailerAssistLineAsset() = default;

private:
  //! Copy constructor is not permitted.
  TrailerAssistLineAsset (const TrailerAssistLineAsset& other); // = delete
  //! Copy assignment operator is not permitted.
  TrailerAssistLineAsset& operator=(const TrailerAssistLineAsset& other); // = delete
};


//!
//! ActionPointsAsset
//!
class ActionPointsAsset : public pc::core::Asset
{
public:

  ActionPointsAsset(
    cc::core::AssetId f_assetId,
    pc::core::Framework* f_framework,
    const TrajectoryParams_st& f_params,
    const DL1* f_distanceLine);

protected:
  ~ActionPointsAsset() = default;

private:
  //! Copy constructor is not permitted.
  ActionPointsAsset (const ActionPointsAsset& other); // = delete
  //! Copy assignment operator is not permitted.
  ActionPointsAsset& operator=(const ActionPointsAsset& other); // = delete
};


//!
//! CoverPlateAsset
//!
class CoverPlateAsset : public pc::core::Asset
{
public:

  CoverPlateAsset(
    cc::core::AssetId f_assetId,
    pc::core::Framework* f_framework,
    const TrajectoryParams_st& f_params,
    vfc::uint32_t f_numOfVerts);

protected:
  ~CoverPlateAsset() = default;

private:
  //! Copy constructor is not permitted.
  CoverPlateAsset (const CoverPlateAsset& other); // = delete
  //! Copy assignment operator is not permitted.
  CoverPlateAsset& operator=(const CoverPlateAsset& other); // = delete
};


//!
//! RefLineAsset
//!
class RefLineAsset : public pc::core::Asset
{
public:

  RefLineAsset(
    cc::core::AssetId f_assetId,
    pc::core::Framework* f_framework,
    const TrajectoryParams_st& f_params);

protected:
  ~RefLineAsset() = default;

private:
  //! Copy constructor is not permitted.
  RefLineAsset (const RefLineAsset& other); // = delete
  //! Copy assignment operator is not permitted.
  RefLineAsset& operator=(const RefLineAsset& other); // = delete
};


//!
//! TrailerHitchAsset
//!
class TrailerHitchTrajectoryAsset : public pc::core::Asset
{
public:

  TrailerHitchTrajectoryAsset(
    cc::core::AssetId f_assetId,
    pc::core::Framework* f_framework,
    const TrajectoryParams_st& f_params);

protected:
  ~TrailerHitchTrajectoryAsset() = default;

private:
  //! Copy constructor is not permitted.
  TrailerHitchTrajectoryAsset (const TrailerHitchTrajectoryAsset& other); // = delete
  //! Copy assignment operator is not permitted.
  TrailerHitchTrajectoryAsset& operator=(const TrailerHitchTrajectoryAsset& other); // = delete
};

//!
//! CrabTrajectoryAsset
//!
class CrabTrajectoryAsset : public pc::core::Asset
{
public:

  CrabTrajectoryAsset(
    cc::core::AssetId f_assetId,
    pc::core::Framework* f_framework,
    const TrajectoryParams_st& f_params);

protected:
  ~CrabTrajectoryAsset() = default;

private:
  //! Copy constructor is not permitted.
  CrabTrajectoryAsset (const CrabTrajectoryAsset& other); // = delete
  //! Copy assignment operator is not permitted.
  CrabTrajectoryAsset& operator=(const CrabTrajectoryAsset& other); // = delete
};

} // namespace trajectory
} // namespace assets
} // namespace cc


#endif // CC_ASSETS_TRAJECTORY_ASSETS_H
