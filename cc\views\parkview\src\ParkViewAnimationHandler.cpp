//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/views/parkview/inc/ParkViewAnimationHandler.h"
#include "cc/assets/augmentedview/inc/AugmentedViewTransition.h"
#include "cc/assets/parkingspace/inc/ParkingSpace.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/views/parkview/inc/ParkView.h"
#include "cc/views/surroundview/inc/SurroundView.h"
#include "cc/virtcam/inc/VirtualCameraUpdater.h"

#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/animation/inc/Action.h"
#include "pc/svs/animation/inc/Animation.h"
#include "pc/svs/animation/inc/AnimationManager.h"
#include "pc/svs/animation/inc/BowlAnimationFactory.h"
#include "pc/svs/animation/inc/CameraAnimationFactory.h"
#include "pc/svs/animation/inc/SerialAnimation.h"
#include "pc/svs/core/inc/Asset.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/Scene.h"
#include "pc/svs/core/inc/View.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/math/inc/FloatComp.h"

// #include "cc/assets/parkingspace/inc/ParkingSpace.h"
// #include "cc/assets/parkingspots/inc/ParkingSpotManager.h"

#define CHECK_PORT_DATA(containerName, port, flag)                                                                     \
    auto(containerName) = (port).getData();                                                                            \
    if ((containerName) == nullptr)                                                                                    \
    {                                                                                                                  \
        (flag) = false;                                                                                                \
        std::cout << #port << " doesn't have data\n";                                                                  \
    }

#define GET_PORT_DATA(dataDaddy, port, allPortHaveDataFlag)                                                            \
    const auto dataDaddy  = (port).getData();  /* PRQA S 1030 */                                                       \
    (allPortHaveDataFlag) = (allPortHaveDataFlag) && ((dataDaddy) != nullptr);                                         \
    if ((dataDaddy) == nullptr)                                                                                        \
    {                                                                                                                  \
        XLOG_ERROR(g_AppContext, #port << " doesn't have data!\n");                                                    \
    }

#define DEBUG_PARK_VIEW_ANIMATION_HANDLER 1

#if DEBUG_PARK_VIEW_ANIMATION_HANDLER
#include "cc/imgui/inc/imgui_manager.h"
#endif

namespace cc
{
namespace core
{
using pc::util::logging::g_AppContext;

class ViewAnimationAction : public pc::animation::Action
{
public:
    ViewAnimationAction(vfc::int32_t f_screenId, cc::daddy::EAanimState f_state)
        : m_screenId(f_screenId)
        , m_state(f_state)
    {
    }

private:
    void onAction() override // PRQA S 1724
    {
        // Inform SM, so further manual requests can be handled
        if (cc::daddy::CustomDaddyPorts::sm_viewAnimationCompleted_SenderPort.isConnected())
        {
            cc::daddy::ViewAnimationCompleted_t& l_container =
                cc::daddy::CustomDaddyPorts::sm_viewAnimationCompleted_SenderPort.reserve();
            l_container.m_Data.m_screenId = m_screenId;
            l_container.m_Data.m_state    = m_state;
            cc::daddy::CustomDaddyPorts::sm_viewAnimationCompleted_SenderPort.deliver();
        }
    }

private:
    vfc::int32_t           m_screenId;
    cc::daddy::EAanimState m_state;
};

class VirtualCameraViewAnimation : public pc::animation::Action
{
public:
    VirtualCameraViewAnimation(cc::virtcam::VirtualCameraUpdater* f_cameraUpdater, bool f_complete=false)
        : m_cameraUpdater(f_cameraUpdater)
        , m_complete(f_complete)
    {
    }

private:
    void onAction() override // PRQA S 1724
    {
        m_cameraUpdater->updateReferenceView();
        m_cameraUpdater->setCompleteStage(m_complete);
    }

private:
    cc::virtcam::VirtualCameraUpdater* m_cameraUpdater;
    bool m_complete;
};

//!
//! ParkViewAnimationHandler
//!
ParkViewAnimationHandler::ParkViewAnimationHandler(pc::core::Framework* f_framework)
    : m_sequenceNumber(0u)
    , m_framework(f_framework)
{
    //! we always want to receive frame events regardless if
    //! marked as handled
    setIgnoreHandledEventsMask(osgGA::GUIEventAdapter::FRAME); // PRQA S 3143
    m_scene           = m_framework->getScene();
    m_transitionQueue = m_framework->getAnimationManager()->createAnimationQueue(0u);
    setName("ParkViewAnimationHandler");
}

void ParkViewAnimationHandler::updateImgui()
{
    m_dirty                              = false;
    m_showCar                            = IMGUI_GET_CHECKBOX_BOOL("VirtualReality", "ShowCar");
    int                         camPosId = IMGUI_GET_SLIDER_INT("VirtualReality", "CamPos", 0, 6);
    cc::virtcam::VirtualCamEnum l_virtualCamPos;
    switch (camPosId)
    {
        case 0:
        {
            l_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_SEARCHING;
            IMGUI_LOG("VirtualReality", "CurrentCamPos", "SEARCHING");
            break;
        }
        case 1: // PRQA S 2880
        {
            l_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_CONFIRM;
            IMGUI_LOG("VirtualReality", "CurrentCamPos", "CONFIRM");
            break;
        }
        case 2:
        {
            l_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_GUIDANCE;
            IMGUI_LOG("VirtualReality", "CurrentCamPos", "GUIDANCE");
            break;
        }
        case 3:
        {
            l_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_COMPLETE_CROSS;
            IMGUI_LOG("VirtualReality", "CurrentCamPos", "COMPLETE_CROSS");
            break;
        }
        case 4:
        {
            l_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_COMPLETE_PARA;
            IMGUI_LOG("VirtualReality", "CurrentCamPos", "COMPLETE_PARA");
            break;
        }
        case 5:
        {
            l_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_COMPLETE_DIGN;
            IMGUI_LOG("VirtualReality", "CurrentCamPos", "COMPLETE_DIGN");
            break;
        }
        case 6:
        {
            l_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_OUT_CONFIRM;
            IMGUI_LOG("VirtualReality", "CurrentCamPos", "PARK_OUT_CONFIRM");
            break;
        }
        default:
        {
            l_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_SEARCHING; // PRQA S 2880
            IMGUI_LOG("VirtualReality", "CurrentCamPos", "SEARCHING");
            break;
        }
    }

    if (l_virtualCamPos != m_virtualCamPos)
    {
        m_dirty         = true;
        m_virtualCamPos = l_virtualCamPos;
    }

    if (m_showSearchingWave != IMGUI_GET_CHECKBOX_BOOL("VirtualReality", "ShowSearchingWave"))
    {
        m_dirty             = true;
        m_showSearchingWave = IMGUI_GET_CHECKBOX_BOOL("VirtualReality", "ShowSearchingWave");
    }
}

void ParkViewAnimationHandler::updateInput()
{
    using cc::assets::parkingspace::g_parkingSpaceSettings;

    cc::core::CustomFramework* const l_framework = m_framework.asCustomFramework();

    bool allPortsHaveData = true;

    GET_PORT_DATA(parkHmiContainer, l_framework->m_ParkhmiToSvs_ReceiverPort, allPortsHaveData)
    GET_PORT_DATA(speedContainer,   l_framework->m_speedReceiver,             allPortsHaveData)

    if (!allPortsHaveData)
    {
        XLOG_ERROR(g_AppContext, " doesn't have data in parking view animation!\n");
        return;
    }

    if (m_apaStatus != parkHmiContainer->m_Data.m_apaStatus)
    {
        m_dirty = true;
    }

    m_apaStatus                         = parkHmiContainer->m_Data.m_apaStatus;
    m_parkingStage                      = parkHmiContainer->m_Data.m_parkingStage;
    m_parkMode                          = parkHmiContainer->m_Data.m_parkMode;
    m_parkingStandStill =
        (std::abs(speedContainer->m_Data) < 1e-3) ? cc::target::common::EVehMoveDir::STANDSTILL : cc::target::common::EVehMoveDir::FORWARD;
}

void ParkViewAnimationHandler::updateSearchingWave()
{
    const bool l_previousShowSearchingWave = m_showSearchingWave;

    //Searching wave showing logic 20225.01.25 TCC
    if ((m_apaStatus    == cc::target::common::EApaStatus::Searching) &&
        (m_parkMode     == cc::target::common::EParkingMode::Apa) &&
        (m_parkingStage == cc::target::common::EParkingStage::ParkIn))
    {
        m_showSearchingWave = true;
    }
    else
    {
        m_showSearchingWave = false;
    }

    if (l_previousShowSearchingWave != m_showSearchingWave)
    {
        m_dirty = true;
    }
}

void ParkViewAnimationHandler::updateAnimationStatus()
{
    using namespace cc::target::common;
    // Req 406
    // Need to confirm 20250125
    // if (APAPARKMODE_APA == m_curParkAPARPAParkMode &&
    //     PARKING_OUT == m_curParkngTypeSeld &&
    //     PARKDRV_Stop == m_curparkDriverInd)
    // {
    //     if (m_parkingStandStill != EVehMoveDir::STANDSTILL)
    //     {
    //         m_parkingStandStill = EVehMoveDir::STANDSTILL;
    //         m_dirty             = true;
    //     }
    // }

    const bool l_previousShowCar = m_showCar;
    const auto l_previousCamPos  = m_virtualCamPos;

    //Show car logic 20250125 TCC
    if (EParkingMode::Apa == m_parkMode)
    {
        m_showCar = true;
    }

    if (m_showCar)
    {
        // enum class EApaStatus : vfc::uint8_t
        // {
        //     PassiveStandBy = 0,
        //     Searching,
        //     GuidanceActive,
        //     GuidanceSuspend,
        //     GuidanceTerminate,
        //     GuidanceCompleted,
        //     AutomaticParkingIsNotAvailable,
        //     ParkAssistStandby
        // };
        switch (m_apaStatus)
        {
            case EApaStatus::PassiveStandBy:
            case EApaStatus::Searching:
            {
                // m_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_SEARCHING;
                if (m_parkingStandStill == EVehMoveDir::STANDSTILL)
                {
                    m_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_CONFIRM;
                }
                else
                {
                    m_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_SEARCHING;
                }
                break;
            }
            case EApaStatus::GuidanceActive:
            case EApaStatus::GuidanceSuspend:
            {
                m_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_GUIDANCE;
                break;
            }
            case EApaStatus::GuidanceTerminate:
            case EApaStatus::GuidanceCompleted:
            {
                // if (m_targetParkType == APASLOT_PARALLEL)
                // {
                //     m_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_COMPLETE_PARA;
                // }
                // else if (m_targetParkType == APASLOT_CROSS)
                // {
                //     m_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_COMPLETE_CROSS;
                // }
                // else if (m_targetParkType == APASLOT_DIAGONAL)
                // {
                //     m_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_COMPLETE_DIGN;
                // }
                // else
                // {
                m_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_COMPLETE_CROSS;
                // }
                break;
            }
            case EApaStatus::ParkAssistStandby:
            {
                m_virtualCamPos = (m_parkingStage == EParkingStage::ParkOut) ? cc::virtcam::VCAM_VIRTUAL_PARK_OUT_CONFIRM : cc::virtcam::VCAM_VIRTUAL_PARK_CONFIRM;
                break;
            }
            default:
            {
                // m_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_SEARCHING;
                if (m_parkingStandStill == EVehMoveDir::STANDSTILL)
                {
                    m_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_CONFIRM;
                }
                else
                {
                    m_virtualCamPos = cc::virtcam::VCAM_VIRTUAL_PARK_SEARCHING;
                }
                break;
            }
        }
    }

    if ((l_previousShowCar != m_showCar) || (l_previousCamPos != m_virtualCamPos))
    {
        m_dirty = true;
    }

    if (l_previousShowCar != m_showCar)
    {
        XLOG_INFO(g_AppContext, getName() + ": m_showCar changed to " << m_showCar);
    }
    if (l_previousCamPos != m_virtualCamPos)
    {
        XLOG_INFO(g_AppContext, getName() + ": m_virtualCamPos changed to " << (
            m_virtualCamPos == cc::virtcam::VCAM_VIRTUAL_PARK_SEARCHING ? "VCAM_VIRTUAL_PARK_SEARCHING" :
            m_virtualCamPos == cc::virtcam::VCAM_VIRTUAL_PARK_CONFIRM ? "VCAM_VIRTUAL_PARK_CONFIRM" :
            m_virtualCamPos == cc::virtcam::VCAM_VIRTUAL_PARK_GUIDANCE ? "VCAM_VIRTUAL_PARK_GUIDANCE" :
            m_virtualCamPos == cc::virtcam::VCAM_VIRTUAL_PARK_COMPLETE_CROSS ? "VCAM_VIRTUAL_PARK_COMPLETE_CROSS" :
            m_virtualCamPos == cc::virtcam::VCAM_VIRTUAL_PARK_COMPLETE_PARA ? "VCAM_VIRTUAL_PARK_COMPLETE_PARA" :
            m_virtualCamPos == cc::virtcam::VCAM_VIRTUAL_PARK_COMPLETE_DIGN ? "VCAM_VIRTUAL_PARK_COMPLETE_DIGN" :
            m_virtualCamPos == cc::virtcam::VCAM_VIRTUAL_PARK_OUT_CONFIRM ? "VCAM_VIRTUAL_PARK_OUT_CONFIRM" : "UNKNOWN"
        ));
    }
}

cc::assets::augmentedview::AugmentedViewTransition* cc::core::ParkViewAnimationHandler::getSearchingWave()
{
    using namespace cc::assets::augmentedview; // PRQA S 2520
    pc::core::Asset* const augmentedViewTransitionAsset =
        m_parkView.getAsset(cc::core::AssetId::EASSETS_AUGMENTED_VIEW_TRANSITION);
    if (augmentedViewTransitionAsset == nullptr)
    {
        return nullptr;
    }
    return dynamic_cast<AugmentedViewTransition*>(augmentedViewTransitionAsset->getAsset()); // PRQA S 3400
}

void ParkViewAnimationHandler::update()
{
    if (m_parkView.setAssetValue(cc::core::AssetId::EASSETS_VEHICLE, m_showCar))
    {
        IMGUI_LOG("VirtualReality", "ShowCarStatus", "Passed");
    }
    else
    {
        IMGUI_LOG("VirtualReality", "ShowCarStatus", "Failed");
    }
    if (m_dirty)
    {
        using namespace cc::assets::augmentedview; // PRQA S 2520
        //! Park Virtual View Animation
        const bool isComplete = m_apaStatus == cc::target::common::EApaStatus::GuidanceCompleted;
        const bool isPakrout = m_parkingStage == cc::target::common::EParkingStage::ParkOut;
        pc::animation::Animation* virtualViewAnimation   = nullptr;
        pc::animation::Animation* searchingWaveAnimation = nullptr;
        pc::animation::Animation* virtualCamAnimation = new VirtualCameraViewAnimation(
                        static_cast<cc::virtcam::VirtualCameraUpdater*>(m_parkView->getCameraUpdater()), isComplete);
        const auto                      cameraAnimationFactory = m_framework->getCameraAnimationFactory();

        if (m_showCar && !isComplete)
        {
            virtualViewAnimation = cameraAnimationFactory->createCameraFlightAnimation(
                m_parkView,
                m_parkView->getRenderManager(),
                m_virtualCamPos,
                pc::virtcam::FlightType::FLIGHT_VEHICLE_CENTERED);
        }

        //! Searching Wave Animation
        const auto searchingWave = getSearchingWave();
        if (searchingWave != nullptr)
        {
            if (m_showSearchingWave)
            {
                searchingWaveAnimation = searchingWave->createTransitionToAugmentedViewAnimation(true);
            }
            if (!m_showSearchingWave)
            {
                searchingWaveAnimation = searchingWave->createTransitionFromAugmentedViewAnimation(true);
            }
        }
        //virtualViewAnimation is control the camera to fly.
        //searchingWaveAnimation is control to showing the searching wave.
        //virtualCamAnimation is tell the virtual camera to update the virtual camera reference/paramters
        m_transitionQueue->append(
            pc::animation::parallel(virtualViewAnimation, searchingWaveAnimation, virtualCamAnimation),
            false);
        m_dirty = false;
    }
    return;
}

void ParkViewAnimationHandler::resetAnimation()
{
    pc::animation::Animation* virtualViewAnimation   = nullptr;
    pc::animation::Animation* searchingWaveAnimation = nullptr;

    const auto l_ParkVirtualView      = m_scene.getView(cc::core::CustomViews::PARK_SEARCHING_VIEW);
    const auto cameraAnimationFactory = m_framework.getCameraAnimationFactory();

    virtualViewAnimation = cameraAnimationFactory->createCameraJumpAction(
        l_ParkVirtualView, l_ParkVirtualView->getRenderManager(), cc::virtcam::VCAM_VIRTUAL_PARK_SEARCHING);

    //! Searching Wave Animation
    const auto searchingWave = getSearchingWave();
    if (searchingWave != nullptr)
    {
        if (m_showSearchingWave)
        {
            searchingWaveAnimation = searchingWave->createTransitionFromAugmentedViewAnimation(true);
            m_showSearchingWave    = false;
        }
    }
    m_transitionQueue->append(pc::animation::parallel(virtualViewAnimation, searchingWaveAnimation), false);

    // if ((m_parkView != nullptr))
    // {
    //     m_showCar = false;
    //     m_parkView->setAssetValue(cc::core::AssetId::EASSETS_VEHICLE, m_showCar);
    // }
}

bool ParkViewAnimationHandler::handle(
    const osgGA::GUIEventAdapter& f_ea,
    osgGA::GUIActionAdapter& /* f_aa */) // PRQA S 1724
{
    if (osgGA::GUIEventAdapter::FRAME == f_ea.getEventType())
    {
        using namespace cc::target::common; // PRQA S 2520
        if (m_parkView.getNodeMask() == 0u)
        {
            if (!m_isInResetState)
            {
                m_isInResetState = true;
                // It creates animation to reset its state.
                // Reset carshow and searching wave state to false
                resetAnimation();
            }
            return false;
        }
        m_isInResetState = false;

        updateInput();

        if ((m_apaStatus == cc::target::common::EApaStatus::Searching) && (m_preApaStatus != cc::target::common::EApaStatus::Searching))
        {
            if (IMGUI_GET_CHECKBOX_BOOL("VirtualReality", "Searching Reset"))
            {
                resetAnimation(); // PRQA S 2880
            }
        }

        //Using Imgui to SIL test
        if (!IMGUI_GET_CHECKBOX_BOOL("VirtualReality", "AnimationImguiOverwrite"))
        {
            //Using real data
            updateSearchingWave();
            updateAnimationStatus();
        }
        else
        {
            //Using fake Imgui data
            updateImgui();
        }

        update();

        m_preApaStatus = m_apaStatus;
    }
    return false;
}

} // namespace core
} // namespace cc
