//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  Vehicle2DOverlay.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_VEHICLE2D_OVERLAY_H
#define CC_ASSETS_UIELEMENTS_VEHICLE2D_OVERLAY_H

#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
// #include "cc/views/planview/inc/PlanViewEnlargeCallback.h"
// #include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"

#include "cc/target/common/inc/commonInterface.h"
#include <osg/Matrixf>

namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace uielements
{

//======================================================
// VehicleVehicle2DSettings
//------------------------------------------------------
/// Setting class for VehicleVehicle2D
/// <AUTHOR>
//======================================================
class Vehicle2DOverlaySettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(Vehicle2DOverlaySettings)
    {
        ADD_STRING_MEMBER(frontLeftDoorOpen);
        ADD_STRING_MEMBER(frontRightDoorOpen);
        ADD_STRING_MEMBER(hoodOpen);
        ADD_STRING_MEMBER(rearLeftDoorOpen);
        ADD_STRING_MEMBER(rearRightDoorOpen);
        ADD_STRING_MEMBER(trunkOpen);
        ADD_STRING_MEMBER(vehicleBodyOpen);
        ADD_STRING_MEMBER(frontLeftDoorClose);
        ADD_STRING_MEMBER(frontRightDoorClose);
        ADD_STRING_MEMBER(hoodClose);
        ADD_STRING_MEMBER(rearLeftDoorClose);
        ADD_STRING_MEMBER(rearRightDoorClose);
        ADD_STRING_MEMBER(trunkClose);
    }

    std::string m_frontLeftDoorOpen;
    std::string m_frontRightDoorOpen;
    std::string m_hoodOpen;
    std::string m_rearLeftDoorOpen;
    std::string m_rearRightDoorOpen;
    std::string m_trunkOpen;
    std::string m_vehicleBodyOpen;
    std::string m_frontLeftDoorClose;
    std::string m_frontRightDoorClose;
    std::string m_hoodClose;
    std::string m_rearLeftDoorClose;
    std::string m_rearRightDoorClose;
    std::string m_trunkClose;
};

//!
//! Vehicle2DSettings
//!
class Vehicle2DSettings : public pc::util::coding::ISerializable
{
public:
    Vehicle2DSettings()
        : m_frontLeftDoorOpenMaskTexturePath(
              CONCAT_PATH_VEHICLE_MODEL("cc/vehicle_model/vehicle2d/frontLeftDoorMask.png"))
        , m_frontRightDoorOpenMaskTexturePath(
              CONCAT_PATH_VEHICLE_MODEL("cc/vehicle_model/vehicle2d/frontRightDoorMask.png"))
        , m_trunkOpenMaskTexturePath(CONCAT_PATH_VEHICLE_MODEL("cc/vehicle_model/vehicle2d/trunkMask.png"))
        , m_proportion(146.0f, 400.0f)
        , m_aspectRatioOfVehicle(0.46f)
        , m_planViewPos(215.0f, 355.0f)
        , m_parkingPlanViewPos(854.0f, 355.0f)
        , m_avmBackgroundPos(0.0f, 0.0f)
        , m_planViewPosRemote(400.0f, 540.0f)
        , m_planViewFLPosOffset(0.0f, 0.0f)
        , m_planViewBodyPosOffset(0.0f, 0.0f)
        , m_planViewFRPosOffset(0.0f, 0.0f)
        , m_planViewRLPosOffset(0.0f, 0.0f)
        , m_planViewRRPosOffset(0.0f, 0.0f)
        , m_planViewHoodPosOffset(0.0f, 0.0f)
        , m_planViewTrunkPosOffset(0.0f, 0.0f)
        , m_planViewDoorScale(0.0f, 0.0f)
        , m_planViewHoodPosScale(0.0f, 0.0f)
        , m_planViewTrunkPosScale(0.0f, 0.0f)
        , m_horiParkingLeftMaskOffset(0.0f, 0.0f)
        , m_horiParkingRightMaskOffset(0.0f, 0.0f)
        , m_horiParkingBottomMaskOffset(0.0f, 0.0f)
        , m_horiParkingMaskScaleFactor(1.11f)
        , m_vertParkingLeftMaskOffset(0.0f, 0.0f)
        , m_vertParkingRightMaskOffset(0.0f, 0.0f)
        , m_vertParkingMaskScaleFactor(1.11f)
    {
    }

    SERIALIZABLE(Vehicle2DSettings)
    {
        // color texture path
        ADD_MEMBER(Vehicle2DOverlaySettings, azure);
        ADD_MEMBER(Vehicle2DOverlaySettings, black);
        ADD_MEMBER(Vehicle2DOverlaySettings, blue);
        ADD_MEMBER(Vehicle2DOverlaySettings, gray);
        ADD_MEMBER(Vehicle2DOverlaySettings, mattegray);
        ADD_MEMBER(Vehicle2DOverlaySettings, mushanpink);
        ADD_MEMBER(Vehicle2DOverlaySettings, qianshancui);
        ADD_MEMBER(Vehicle2DOverlaySettings, red);
        ADD_MEMBER(Vehicle2DOverlaySettings, white);
        ADD_MEMBER(Vehicle2DOverlaySettings, silverglazewhite);
        ADD_MEMBER(Vehicle2DOverlaySettings, duduwhite);
        ADD_MEMBER(Vehicle2DOverlaySettings, junWareGray);
        ADD_MEMBER(Vehicle2DOverlaySettings, transparent);
        ADD_MEMBER(Vehicle2DOverlaySettings, golden);
        ADD_MEMBER(Vehicle2DOverlaySettings, blackgolden);

        // mask texture path
        ADD_STRING_MEMBER(frontLeftDoorOpenMaskTexturePath);
        ADD_STRING_MEMBER(frontRightDoorOpenMaskTexturePath);
        ADD_STRING_MEMBER(trunkOpenMaskTexturePath);
        // gear status
        ADD_STRING_MEMBER(gearStatusDrive);
        ADD_STRING_MEMBER(gearStatusReverse);
        ADD_STRING_MEMBER(gearStatusPark);
        ADD_STRING_MEMBER(gearStatusNeutral);
        // image properties
        ADD_MEMBER(osg::Vec2f, proportion);
        ADD_FLOAT_MEMBER(aspectRatioOfVehicle);
        ADD_MEMBER(osg::Vec2f, planViewPos);
        ADD_MEMBER(osg::Vec2f, planViewPosVert);
        ADD_MEMBER(osg::Vec2f, parkingPlanViewPos);
        ADD_MEMBER(osg::Vec2f, avmBackgroundPos);
        ADD_MEMBER(osg::Vec2f, planViewPosRemote);
        // offset
        ADD_MEMBER(osg::Vec2f, planViewBodyPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewBodyWithoutDoorPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewFLPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewFRPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewRLPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewRRPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewHoodPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewTrunkPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewTransparentBodyPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewTransparentBodyWithoutDoorPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewTransparentFLPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewTransparentFRPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewTransparentRLPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewTransparentRRPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewTransparentHoodPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewTransparentTrunkPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewFrontLeftDoorOpenMaskPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewFrontRightDoorOpenMaskPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewTrunkOpenMaskPosOffset);
        ADD_MEMBER(vfc::float32_t, gearOffsetPercentage);
        // scale
        ADD_MEMBER(osg::Vec2f, planViewDoorScale);
        ADD_MEMBER(osg::Vec2f, planViewHoodPosScale);
        ADD_MEMBER(osg::Vec2f, planViewTrunkPosScale);
        // mask settings
        ADD_MEMBER(osg::Vec2f, horiParkingLeftMaskOffset);
        ADD_MEMBER(osg::Vec2f, horiParkingRightMaskOffset);
        ADD_FLOAT_MEMBER(horiParkingMaskScaleFactor);
        ADD_MEMBER(osg::Vec2f, vertParkingLeftMaskOffset);
        ADD_MEMBER(osg::Vec2f, vertParkingRightMaskOffset);
        ADD_MEMBER(osg::Vec2f, normalMaskScaleFactor);
        ADD_MEMBER(osg::Vec2f, fullscreenMaskScaleFactor);
        ADD_MEMBER(osg::Vec2f, imageInImageScaleFactor);
        // gear
        ADD_FLOAT_MEMBER(gearSizePercentage);
        // Arrow
        ADD_INT_MEMBER(numArrows);
        ADD_STRING_MEMBER(arrowTexturePath);
        ADD_FLOAT_MEMBER(arrowSizePercentage);
        ADD_MEMBER(osg::Vec2f, arrowGap);
        ADD_MEMBER(osg::Vec2f, arrowOffsetPos);
        ADD_FLOAT_MEMBER(arrowReferenceWidth);
        ADD_MEMBER(osg::Vec2f, arrowSize);
        ADD_FLOAT_MEMBER(arrowStepPeriod);
        ADD_FLOAT_MEMBER(arrowPhaseDiff);
        ADD_FLOAT_MEMBER(arrowEnlargeOffsetPercentage);
    }

    Vehicle2DOverlaySettings m_azure;
    Vehicle2DOverlaySettings m_black;
    Vehicle2DOverlaySettings m_blue;
    Vehicle2DOverlaySettings m_gray;
    Vehicle2DOverlaySettings m_mattegray;
    Vehicle2DOverlaySettings m_mushanpink;
    Vehicle2DOverlaySettings m_qianshancui;
    Vehicle2DOverlaySettings m_red;
    Vehicle2DOverlaySettings m_white;
    Vehicle2DOverlaySettings m_silverglazewhite;
    Vehicle2DOverlaySettings m_duduwhite;
    Vehicle2DOverlaySettings m_junWareGray;
    Vehicle2DOverlaySettings m_transparent;
    Vehicle2DOverlaySettings m_golden;
    Vehicle2DOverlaySettings m_blackgolden;
    std::string m_frontLeftDoorOpenMaskTexturePath;
    std::string m_frontRightDoorOpenMaskTexturePath;
    std::string m_trunkOpenMaskTexturePath;

    std::string m_gearStatusDrive;
    std::string m_gearStatusReverse;
    std::string m_gearStatusPark;
    std::string m_gearStatusNeutral;

    osg::Vec2f     m_proportion; // 1st value is the valid pixel height of vehicle, 2nd is the pixel height of the pic
    vfc::float32_t m_aspectRatioOfVehicle;
    osg::Vec2f     m_planViewPos;
    osg::Vec2f     m_planViewPosVert;
    osg::Vec2f     m_parkingPlanViewPos;
    osg::Vec2f     m_avmBackgroundPos;
    osg::Vec2f     m_planViewPosRemote;

    // offset
    osg::Vec2f     m_planViewBodyPosOffset;
    osg::Vec2f     m_planViewBodyWithoutDoorPosOffset;
    osg::Vec2f     m_planViewFLPosOffset;
    osg::Vec2f     m_planViewFRPosOffset;
    osg::Vec2f     m_planViewRLPosOffset;
    osg::Vec2f     m_planViewRRPosOffset;
    osg::Vec2f     m_planViewHoodPosOffset;
    osg::Vec2f     m_planViewTrunkPosOffset;
    osg::Vec2f     m_planViewTransparentBodyPosOffset;
    osg::Vec2f     m_planViewTransparentBodyWithoutDoorPosOffset;
    osg::Vec2f     m_planViewTransparentFLPosOffset;
    osg::Vec2f     m_planViewTransparentFRPosOffset;
    osg::Vec2f     m_planViewTransparentRLPosOffset;
    osg::Vec2f     m_planViewTransparentRRPosOffset;
    osg::Vec2f     m_planViewTransparentHoodPosOffset;
    osg::Vec2f     m_planViewTransparentTrunkPosOffset;
    osg::Vec2f     m_planViewFrontLeftDoorOpenMaskPosOffset;
    osg::Vec2f     m_planViewFrontRightDoorOpenMaskPosOffset;
    osg::Vec2f     m_planViewTrunkOpenMaskPosOffset;
    vfc::float32_t m_gearOffsetPercentage;
    // scale
    osg::Vec2f m_planViewDoorScale;
    osg::Vec2f m_planViewHoodPosScale;
    osg::Vec2f m_planViewTrunkPosScale;
    // mask
    osg::Vec2f     m_horiParkingLeftMaskOffset;
    osg::Vec2f     m_horiParkingRightMaskOffset;
    osg::Vec2f     m_horiParkingBottomMaskOffset;
    vfc::float32_t m_horiParkingMaskScaleFactor;
    osg::Vec2f     m_vertParkingLeftMaskOffset;
    osg::Vec2f     m_vertParkingRightMaskOffset;
    vfc::float32_t m_vertParkingMaskScaleFactor;
    osg::Vec2f     m_normalMaskScaleFactor;
    osg::Vec2f     m_fullscreenMaskScaleFactor;
    osg::Vec2f     m_imageInImageScaleFactor;
    // gear
    vfc::float32_t m_gearSizePercentage;
    // arrow
    vfc::int32_t   m_numArrows                    = 3;
    std::string    m_arrowTexturePath             = "cc/resources/trajectory/arrow_avm.png";
    vfc::float32_t m_arrowSizePercentage          = 80.0f;
    osg::Vec2f     m_arrowGap           = {0.0f, 0.0f};
    osg::Vec2f     m_arrowOffsetPos     = {0.0f, 0.0f};
    vfc::float32_t m_arrowReferenceWidth          = 700.0f;
    osg::Vec2f     m_arrowSize                    = {80.0, 50.0f};
    vfc::float32_t m_arrowStepPeriod              = 1.5f;
    vfc::float32_t m_arrowPhaseDiff               = 1.2f;
    vfc::float32_t m_arrowEnlargeOffsetPercentage = 20.0f;
};

extern pc::util::coding::Item<Vehicle2DSettings> g_Vehicle2DSettings;

enum MaskType : vfc::uint32_t
{
    VEHICLE2D_TRUNK_OPEN_MASK,
    VEHICLE2D_FRONT_LEFT_DOOR_OPEN_MASK,
    VEHICLE2D_FRONT_RIGHT_DOOR_OPEN_MASK,
};

enum Vehicle2DOverlayType : vfc::uint32_t
{
    VEHICLE2D_BODY_OPEN,
    VEHICLE2D_FRONT_LEFT_DOOR_OPEN,
    VEHICLE2D_FRONT_RIGHT_DOOR_OPEN,
    VEHICLE2D_HOOD_OPEN,
    VEHICLE2D_REAR_LEFT_DOOR_OPEN,
    VEHICLE2D_REAR_RIGHT_DOOR_OPEN,
    VEHICLE2D_TRUNK_OPEN,
    VEHICLE2D_FRONT_LEFT_DOOR_CLOSE,
    VEHICLE2D_FRONT_RIGHT_DOOR_CLOSE,
    VEHICLE2D_HOOD_CLOSE,
    VEHICLE2D_REAR_LEFT_DOOR_CLOSE,
    VEHICLE2D_REAR_RIGHT_DOOR_CLOSE,
    VEHICLE2D_TRUNK_CLOSE,
    NUM_OVERLAYS,
};

enum OverlayType : vfc::uint32_t
{
    GEAR_STATUS_D,
    GEAR_STATUS_R,
    GEAR_STATUS_P,
    GEAR_STATUS_N,
};

//!
//! Vehicle2DOverlayManager
//!
class Vehicle2DOverlayManager
{
public:
    typedef std::vector<osg::ref_ptr<pc::assets::Icon>> IconList;
    using PlanViewEnlargeStatus = cc::daddy::PlanViewEnlargeStatus;

    Vehicle2DOverlayManager(cc::core::CustomFramework* f_framework, pc::assets::ImageOverlays* f_overlay);
    virtual ~Vehicle2DOverlayManager() = default;

    void init();
    void update();
    void updateReferenceTime(vfc::float32_t f_refTime)
    {
        m_currentReferenceTime = f_refTime;
    }

    static const Vehicle2DOverlaySettings getColorFromIndex(vfc::uint8_t f_colorIndex);

    void setEnlargeStatus(PlanViewEnlargeStatus f_enlargeStatus)
    {
        if (f_enlargeStatus != m_enlargeStatus)
        {
            m_dirty = true;
            m_enlargeStatusPrevious = m_enlargeStatus;
            m_enlargeStatus = f_enlargeStatus;
        }
    }

    // void updateReferenceView();
    void setEnlargeProgressPercentage(vfc::float32_t f_progressPercentage)
    {
        m_enlargeProgressPercentage = f_progressPercentage;
        m_dirty = true;
    }

    vfc::float32_t getEnlargeProgressPercentage() const
    {
        return m_enlargeProgressPercentage;
    }

    void setEnlargeFactor(vfc::float32_t f_enlargeFactor)
    {
        m_enlargeFactor = f_enlargeFactor;
    }

    void setBevMode(bool f_bevMode)
    {
        m_bevMode = f_bevMode;
    }

    bool getBevMode() const
    {
        return m_bevMode;
    }

    void setStartPosition()
    {
        m_startPositionDirty = true;
    }

    osg::Vec2f getStartPosition() const
    {
        return m_startPosition;
    }

    void updateIconsSizePosition();

    //! currently we have lots of instances of manager so make the interface static to sync among views
    static float getVehicleTransparent() { return sm_transparencyLevel; }
    static void setVehicleTransparent(float f_transparent);

private:
    //! Copy constructor is not permitted.
    Vehicle2DOverlayManager(const Vehicle2DOverlayManager& other); // = delete
    //! Copy assignment operator is not permitted.
    Vehicle2DOverlayManager& operator=(const Vehicle2DOverlayManager& other); // = delete

    void updateIconSizePosition(
        cc::assets::uielements::CustomIcon* f_icon,
        const osg::Vec2f&                   f_iconSize,
        const osg::Vec2f&                   f_iconCenter,
        const osg::Vec2f&                   /*f_iconPosOffset*/,
        const osg::Vec2f&                   /*f_iconScale*/,
        bool                                f_isHoriView);

        void updateVehicleIconSizePosition(
            cc::assets::uielements::CustomIcon* f_icon,
            const osg::Vec2f&                   f_iconCenter,
            const osg::Vec2f&                   f_iconPosOffset,
            vfc::float64_t                      f_iconScaler,
            bool                                f_isHoriView);
    bool updateInput();
    void updateGearAnimation();

private:
    void imguiInit();
    void imguiUpdate();
    void setVehicleTransparency(const float f_alpha);

private:
    cc::core::CustomFramework* m_framework;
    osg::Timer_t               m_frameTick;
    pc::assets::ImageOverlays* m_overlay;
    pc::assets::IconGroup      m_Vehicle2DOverlays;
    pc::assets::IconGroup      m_Vehicle2DTransparentOverlays;
    pc::assets::IconGroup      m_doorMasks;
    pc::assets::IconGroup      m_overlays;
    pc::assets::IconGroup      m_frontArrows;
    pc::assets::IconGroup      m_rearArrows;

    vfc::uint8_t                     m_colorIndex;
    vfc::uint32_t                    m_lastConfigUpdate = ~0u;
    bool                             m_parkActive       = false;
    cc::target::common::EThemeTypeHU m_theme;
    vfc::uint32_t                    m_viewId;
    vfc::uint32_t                    m_veh2dViewId;
    cc::daddy::PlanViewEnlargeStatus m_enlargeStatus            = daddy::PlanViewEnlargeStatus::NO_ENLARGE;
    cc::daddy::PlanViewEnlargeStatus m_enlargeStatusPrevious    = daddy::PlanViewEnlargeStatus::NO_ENLARGE;
    bool                             m_showTransparent          = false;
    bool                             m_frontRightDoorOpen       = false;
    bool                             m_frontLeftDoorOpen        = false;
    bool                             m_hoodOpen                 = false;
    bool                             m_trunkOpen                = false;
    bool                             m_rearRightDoorOpen        = false;
    bool                             m_rearLeftDoorOpen         = false;
    bool                             m_allDoorsAndMirrorsClosed = false;
    bool                             m_isFullscreen             = false;
    bool                             m_isImageInImage           = false;
    bool                             m_leftSideMirrorFlapped    = false;
    bool                             m_rightSideMirrorFlapped   = false;
    pc::daddy::EGear                 m_gearStatus;
    vfc::float32_t                   m_speed = 0.0f;
    vfc::float32_t                   m_impostorTransp = 1.0f;
    vfc::uint8_t                     m_transpLevel = 0;

    //! Internal
    bool m_dirty              = false;
    bool m_dirtyColor         = false;
    bool m_bevMode            = false;
    bool m_startPositionDirty = true;
    bool m_enlargeCustom      = false;

    //! Animation
    osg::Vec2f m_currentPosition;
    osg::Vec2f m_startPosition;
    osg::Vec2f m_targetPosition;
    vfc::float32_t m_enlargeFactor = 1.0f;
    vfc::float32_t m_enlargeProgressPercentage = 1.0f;
    vfc::float32_t m_iconScaler;
    vfc::uint32_t  m_currentActiveArrow   = 0u;
    vfc::float32_t m_lastReferenceTime    = 0.0f;
    vfc::float32_t m_currentReferenceTime = 0.0f;
    osg::Vec2f     m_viewportSize{};
    vfc::float32_t m_arrowOffsetPos = 0.0f;
    vfc::float32_t m_arrowGap = 0.0f;

private:
    float m_transparencyLevel = 1.0f;
    static float sm_transparencyLevel;
};

//!
//! Vehicle2DOverlay
//!
class Vehicle2DOverlay : public cc::assets::uielements::CustomImageOverlays
{
public:
    Vehicle2DOverlay(
        cc::core::CustomFramework* f_customFramework,
        cc::core::AssetId          f_assetId,
        osg::Camera*               f_referenceView);
    virtual ~Vehicle2DOverlay() = default;

    virtual void traverse(osg::NodeVisitor& f_nv) override;

    Vehicle2DOverlayManager* getManager()
    {
        return &m_manager;
    }

private:
    //! Copy constructor is not permitted.
    Vehicle2DOverlay(const Vehicle2DOverlay& other); // = delete
    //! Copy assignment operator is not permitted.
    Vehicle2DOverlay& operator=(const Vehicle2DOverlay& other); // = delete

    cc::core::CustomFramework* m_customFramework;
    Vehicle2DOverlayManager    m_manager;
};

osg::Vec2f calculateVertIconSize(bool f_parkActive);

osg::Vec2f calculateHoriIconSize(bool f_parkActive, bool f_isRemoteMode);

bool checkParkingActiveInVehicle2dOverlay(cc::target::common::EPARKStatusR2L f_parkStatus);

} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_VEHICLE2D_OVERLAY_H
