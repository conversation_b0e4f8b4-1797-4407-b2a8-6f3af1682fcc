//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Ha Thanh Phong (MS/EDA92-XC)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  Utils.h
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/Utils.h"
#include "cc/core/inc/CustomScene.h"

#include "vfc/core/vfc_types.hpp"

#include "osg/ref_ptr"
#include "osg/Image"
#include "osgDB/ReadFile"

using cc::core::g_views;

namespace cc
{
namespace assets
{
namespace uielements
{


static inline vfc::float32_t getScaleFactorHori()
{
  return static_cast<vfc::float32_t>(g_views->m_apaPlanViewport.m_size.x()) /
         static_cast<vfc::float32_t>(550u);
}


osg::Vec2f transferToBottomLeftHori(const osg::Vec2f f_iconPos)
{
  return osg::Vec2f(
    f_iconPos.x() - static_cast<vfc::float32_t>(cc::core::g_views->m_apaPlanViewport.m_origin.x()),
    static_cast<vfc::float32_t>(cc::core::g_views->m_apaPlanViewport.m_size.y()) - f_iconPos.y()
  );
}


osg::Vec2f transferToBottomLeftVert(const osg::Vec2f f_iconPos)
{
  return osg::Vec2f(f_iconPos.x() + static_cast<vfc::float32_t>(g_views->m_vertMainViewport.m_size.x()), f_iconPos.y());
}


osg::Vec2f getImageSize(const std::string& /*f_iconPath*/)
{
  return osg::Vec2f{0.0f, 0.0f};
}


void setImageSize(CustomIcon* f_icon, const bool& f_isHoriScreen)
{
  if (f_icon == nullptr)
  {
    return; // todo: maybe add more loggings.
  }
  osg::Vec2f l_iconSize = f_icon->getIconSize();
  if (f_isHoriScreen == false) // vert
  {
    f_icon->setSize(
      osg::Vec2f{l_iconSize.y(), l_iconSize.x()},
      pc::assets::Icon::UnitType::Pixel
    );
  }
  else // hori
  {
    f_icon->setSize(
      l_iconSize,
      pc::assets::Icon::UnitType::Pixel
    );
  }
}


pc::assets::Icon* createIcon(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const osg::Vec2f& f_iconSize, const bool& f_isHoriScreen)
{
  CustomIcon* const l_icon = new CustomIcon(f_iconPath, false, false, true, f_isHoriScreen);
  l_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setSize(f_iconSize, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  return l_icon;
}


pc::assets::Icon* createIcon(const std::string& f_iconPath, const cc::assets::uielements::UiIconSettings::IconPosition& f_iconPosition, const bool& f_isHoriScreen)
{
  CustomIcon* const l_icon = new CustomIcon(f_iconPath, false, false, true, f_isHoriScreen);
  l_icon->setOrigin(f_isHoriScreen ? pc::assets::Icon::Origin::TopLeft : pc::assets::Icon::Origin::BottomLeft);
  l_icon->setPosition(f_isHoriScreen ? f_iconPosition.m_hori : f_iconPosition.m_vert, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  setImageSize(l_icon, f_isHoriScreen);
  return l_icon;
}


pc::assets::Icon* createIcon(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const bool& f_isHoriScreen)
{
  CustomIcon* const l_icon = new CustomIcon(f_iconPath, false, false, true, f_isHoriScreen);
  l_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  setImageSize(l_icon, f_isHoriScreen);
  return l_icon;
}


pc::assets::Icon* createIconTopLeft(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const osg::Vec2f& f_iconSize, const bool& f_isHoriScreen)
{
  CustomIcon* const l_icon = new CustomIcon(f_iconPath, false, false, true, f_isHoriScreen);
  l_icon->setOrigin(pc::assets::Icon::Origin::TopLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setSize(f_iconSize, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  return l_icon;
}


pc::assets::Icon* createIconTopLeft(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const bool& f_isHoriScreen)
{
  CustomIcon* const l_icon = new CustomIcon(f_iconPath, false, false, true, f_isHoriScreen);
  l_icon->setOrigin(pc::assets::Icon::Origin::TopLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  setImageSize(l_icon, f_isHoriScreen);
  return l_icon;
}

pc::assets::Icon* createIconCenter(const std::string& f_iconPath, bool f_isHoriScreen)
{
  CustomIcon* const l_icon = new CustomIcon(f_iconPath, false, false, true, f_isHoriScreen);
  l_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
  l_icon->setPosition({0.0f, 0.0f}, pc::assets::Icon::UnitType::Percentage);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Left);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Left);
  l_icon->setEnabled(false);
  l_icon->setSize({100.0f, 100.0f}, pc::assets::Icon::UnitType::Percentage);
  return l_icon;
}

} // namespace uielements
} // namespace assets
} // namespace cc
