//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------
#include <cassert>
#include <algorithm>
#include <limits> // PRQA S 1060
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h" // PRQA S 1060
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060

#include "cc/assets/augmentedview/inc/TransitionWaveAnimation.h"

namespace cc
{
namespace assets
{
namespace augmentedview
{


TransitionWaveAnimation::TransitionWaveAnimation()
  : TransitionWaveAnimation(2.0f, 40.0f, true)
{
}


TransitionWaveAnimation::TransitionWaveAnimation(vfc::float32_t f_animationDuration, vfc::float32_t f_maxRadius, bool f_transitionToAugmentedView)
  : m_running() // PRQA S 2323
  , m_elapsedTime() // PRQA S 2323
  , m_waveRadius() // PRQA S 2323
  , m_waveAlpha() // PRQA S 2323
  , m_cameraFadeFactor() // PRQA S 2323
  , m_lastTime() // PRQA S 2323
  , m_maxWaveRadius() // PRQA S 2323
  , m_animationDuration() // PRQA S 2323
  , m_cameraFadeStartTime() // PRQA S 2323
  , m_cameraFadeEndTime() // PRQA S 2323
  , m_waveAlphaFadeStartDistance() // PRQA S 2323
  , m_waveAlphaFadeEndDistance() // PRQA S 2323
  , m_transitionToAugmentedView() // PRQA S 2323
{
  reset(f_animationDuration, f_maxRadius, f_transitionToAugmentedView);
}


// Reset animation to non-running state
void TransitionWaveAnimation::reset(vfc::float32_t f_animationDuration, vfc::float32_t f_maxRadius, bool f_transitionToAugmentedView)
{
  m_animationDuration = f_animationDuration;
  m_maxWaveRadius = f_maxRadius;
  m_transitionToAugmentedView = f_transitionToAugmentedView;

  // Non-exposed parameters for now
  m_waveAlphaFadeStartDistance = m_maxWaveRadius * 0.5f;
  m_waveAlphaFadeEndDistance = m_maxWaveRadius * 0.8f;

  m_cameraFadeStartTime = 0.2f * f_animationDuration;
  m_cameraFadeEndTime = 0.65f * f_animationDuration;
  reset();
}


// Reset animation to non-running state
void TransitionWaveAnimation::reset()
{
  // Give parameters a value which should be correct in most cases (depending on animation parameters)
  // this is just a defensive measure, because parameters should usually not be used before an update
  m_waveRadius       = m_transitionToAugmentedView ? 0.0f : m_maxWaveRadius;
  m_cameraFadeFactor = m_transitionToAugmentedView ? 0.0f : 1.0f;
  m_waveAlpha        = m_transitionToAugmentedView ? 1.0f : 0.0f;
  m_elapsedTime = 0.0f;
  m_lastTime    = -1.0f; // -1.0 signifies not updated
  m_running = false;
}


void TransitionWaveAnimation::start(vfc::float32_t f_animationDuration, vfc::float32_t f_maxRadius, bool f_transitionToAugmentedView)
{
  reset(f_animationDuration, f_maxRadius, f_transitionToAugmentedView);
  start();
}


void TransitionWaveAnimation::start()
{
  m_running = true;
}


void TransitionWaveAnimation::update(vfc::float32_t f_currentTime)
{
  if (m_lastTime < 0.0f)
  {
    m_lastTime = f_currentTime;
  }

  m_elapsedTime = f_currentTime - m_lastTime;
  m_elapsedTime += m_animationDuration * g_augmentedSettings->m_scanWaveTravelStartingPoint;

  vfc::float32_t r = std::min(1.0f, m_elapsedTime / m_animationDuration);
  if (!m_transitionToAugmentedView)
  {
    r = 1.0f - r;
  }
  r = std::max(r, g_augmentedSettings->m_scanWaveTravelStartingPoint);

  r *= r;
  m_waveRadius = m_maxWaveRadius * r;
  m_waveAlpha = 1.0f - std::max(0.f, (m_waveRadius - m_waveAlphaFadeStartDistance)/(m_waveAlphaFadeEndDistance - m_waveAlphaFadeStartDistance));

  // Fade factor based on elapsed time; might require tweaking so that both transition directions look similar
  // ...or switch to a radius based function
  m_cameraFadeFactor =  std::min(1.f, std::max(0.f, (m_elapsedTime-m_cameraFadeStartTime)/(m_cameraFadeEndTime-m_cameraFadeStartTime)));
  if (!m_transitionToAugmentedView)
  {
    m_cameraFadeFactor = 1.0f - m_cameraFadeFactor;
  }

  if (m_elapsedTime >= m_animationDuration)
  {
    m_running = false;
  }
}


bool TransitionWaveAnimation::isRunning() const
{
  return m_running;
}


bool TransitionWaveAnimation::hasFinished() const
{
  return (m_elapsedTime >= m_animationDuration);
}


vfc::float32_t TransitionWaveAnimation::getWaveRadius() const
{
  assert(m_lastTime >= 0.0f);
  return m_waveRadius;
}


vfc::float32_t TransitionWaveAnimation::getWaveAlphaFade() const
{
  assert(m_lastTime >= 0.0f);
  return m_waveAlpha;
}


vfc::float32_t TransitionWaveAnimation::getCameraFadeFactor() const
{
  assert(m_lastTime >= 0.0f);
  return m_cameraFadeFactor;
}


bool TransitionWaveAnimation::isTransitioningToAugmentedView() const
{
  return m_transitionToAugmentedView;
}


} // namespace augmentedview
} // namespace asset
} // namespace cc
