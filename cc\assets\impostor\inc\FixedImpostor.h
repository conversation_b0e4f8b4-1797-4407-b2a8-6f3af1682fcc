//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  FixedImpostor.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_IMPOSTOR_FIXEDIMPOSTOR_H
#define CC_ASSETS_IMPOSTOR_FIXEDIMPOSTOR_H


#include "pc/svs/core/inc/Asset.h"
#include "pc/svs/core/inc/View.h"
#include "pc/svs/util/osgx/inc/RenderToTextureCamera.h"
#include "pc/svs/virtcam/inc/VirtualCam.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"

#include <osg/Camera>
#include <osg/NodeVisitor>
#include <osg/ref_ptr>
#include <osg/Texture2D>

#include <array>

//! forward declarations
namespace pc
{
namespace core
{
class Framework;
} // namespace core
namespace vehiclemodel
{
class VehicleModel;
} // namespace vehiclemodel
} // namespace pc

namespace cc
{
namespace assets
{
namespace impostor
{

osg::Geode* createTextureSamplingQuad(osg::Texture2D* f_texture);

enum RenderPass
{
  RENDER_PASS_BELOW_IMPOSTOR,
  RENDER_PASS_IMPOSTOR,
  RENDER_PASS_ABOVE_IMPOSTOR,
  NUM_RENDER_PASSES
};

//======================================================
// ImpostorSettings
//------------------------------------------------------
/// Setting class for FixedImpostor
/// Settings can get from CodingParameters.xml
/// <AUTHOR>
//======================================================
class ImpostorSettings : public pc::util::coding::ISerializable
{
public:

  ImpostorSettings()
    : m_textureWidth (256u)
    , m_textureHeight(512u)
  {
  }

  SERIALIZABLE(ImpostorSettings)
  {
    ADD_UINT32_MEMBER(textureWidth);
    ADD_UINT32_MEMBER(textureHeight);
  }
  vfc::uint32_t m_textureWidth;
  vfc::uint32_t m_textureHeight;
};

extern pc::util::coding::Item<ImpostorSettings> g_fixedImposterSettings;

class TransparentVMData;
extern pc::util::coding::Item<TransparentVMData> g_transpVMData;

//======================================================
// ImpostorDoors
//------------------------------------------------------
/// Contains the doors of the car for the FixedImpostor.
/// Doors contains the names of them as string.
/// <AUTHOR>
//======================================================
class ImpostorDoors : public pc::core::Asset
{
public:
    ImpostorDoors(cc::core::AssetId f_assetId, pc::core::Framework* f_pFramework);
    virtual void traverse(osg::NodeVisitor& f_rNV) override;

protected:
    void init();

private:
    osg::Node* m_frontLeftInnerDoor;
    osg::Node* m_frontRightInnerDoor;
    osg::Node* m_rearLeftInnerDoor;
    osg::Node* m_rearRightInnerDoor;

    std::string m_nodeFL;
    std::string m_nodeFR;
    std::string m_nodeRL;
    std::string m_nodeRR;

    bool m_initialized;

    pc::core::Framework* m_pFramework;
};

//======================================================
// FixedImpostor
//------------------------------------------------------
/// Top view of the car.
/// this is an asset, even though it relies on the
/// vehicle model, because it does not require the
/// vehicle to be shown. it is just used to build the
/// texture. This class assumes that the vehicle model
/// never changes. the only change that triggers redraw
/// is loading of the vehicle model, this is done by
/// checking the number of children of the vehicle model
/// node.
/// <AUTHOR>
//======================================================
class FixedImpostor : public pc::core::Asset
{
public:

    FixedImpostor(
      cc::core::AssetId f_assetId,
      osg::Group* f_vehicleGroup,
      pc::vehiclemodel::VehicleModel* f_vehicleModel,
      int f_textureWidth, int f_textureHeight,
      vfc::float32_t f_planViewWidthInMeters, vfc::float32_t f_planViewLengthInMeters,
      vfc::float32_t f_vehicleWidthInMeters, vfc::float32_t f_vehicleLengthInMeters,
      const pc::virtcam::VirtualCamera& f_camPos,
      pc::core::Framework* f_pFramework );


    virtual void traverse(osg::NodeVisitor& f_rNV) override;

    void useTextureWithoutDoors(bool f_val);

private:
    void setupImpostor(
        int f_textureResX, int f_textureResY,
        vfc::float32_t f_planViewWidthInMeters, vfc::float32_t f_planViewLengthInMeters,
        vfc::float32_t f_vehicleWidthInMeters, vfc::float32_t f_vehicleLengthInMeters,
        const pc::virtcam::VirtualCamera& f_camPos);
    bool setDoorMasks(vfc::uint32_t f_mask); // needed for an impostor without doors
    bool setWheelMasks(vfc::uint32_t f_mask); // needed for an impostor without wheels

    osg::Node* getComponent(const std::string& f_componentName, bool f_suppressErrorMessage = false);

private:
    osg::ref_ptr<osg::Texture2D> m_pTexWithDoors;
    osg::ref_ptr<osg::Texture2D> m_pTexWithoutDoors;
    osg::ref_ptr<osg::Camera> m_pRTTcam;
    osg::ref_ptr<osg::Camera> m_pRTTcamWithoutDoors;
    osg::ref_ptr<osg::Camera> m_pImpostorCamera;
    osg::ref_ptr<osg::Geode> m_pImpostor;
    osg::ref_ptr<osg::Geode> m_pImpostorWithoutDoors;
    vfc::uint32_t m_sequenceNumberVehicleModel;
    osg::ref_ptr<osg::Group> m_pVehicleModel;
    osg::ref_ptr<pc::vehiclemodel::VehicleModel> m_pVehicleModelPC;
    pc::core::Framework* m_pFramework;

};


///
/// PostRenderStateSetUpdateCallback
///
class PostRenderStateSetUpdateCallback : public osg::StateSet::Callback
{
public:
  const vfc::float32_t getImpostorTransp()
  {
    return m_impostorTransp;
  }

  void setImpostorTransp(vfc::float32_t f_transparency)
  {
    m_impostorTransp = f_transparency;
  }

  void setTranspLevel(vfc::uint8_t f_transpLevel)
  {
    m_transpLevel = f_transpLevel;
  }

  PostRenderStateSetUpdateCallback(cc::core::CustomFramework* f_customFramework, vfc::float32_t f_impostorTransp=1.0f)
  : m_customFramework(f_customFramework)
  , m_frameTick(osg::Timer::instance()->tick())
  , m_impostorTransp(f_impostorTransp)
  {
  }

  virtual void operator() (osg::StateSet* f_stateSet, osg::NodeVisitor* /* f_nv */) override;

private:
  cc::core::CustomFramework* m_customFramework;
  osg::Timer_t               m_frameTick;
  vfc::float32_t m_impostorTransp;
  vfc::uint8_t m_transpLevel;
};

/**
 * \brief NB: The TransparentVehicleImpostor is an asset implementation and
 * makes use of the RTT-cam for the 3D vehicle model to create a 2D texture which is capable of alterations
 * in the transparenc. This transparent texture shall then be used to render over the desired scene
 * to construct the feel of a transparent vehicle model.
 *
 * <pre>
 *     2D-Texture               Transparent 2D-Texture
 * _________________              _________________
 * |                |             |                |
 * |       /\       |             |       /\       |
 * |      /  \      |             |      /  \      |
 * |     /----\     |             |     /----\     |
 * |       ||       |    >>>>>    |       ||       |
 * |       ||       |             |       ||       |
 * |       ||       |             |       ||       |
 * |                |             |                |
 * |                |             |                |
 * |________________|             |________________|
 * </pre>
 * Texture is rendering full viewport.
 */
class TransparentVehicleImpostor : public pc::core::Asset
{
public:
  TransparentVehicleImpostor(
    cc::core::CustomFramework* f_customFramework,
    cc::core::AssetId f_assetId,
    pc::vehiclemodel::VehicleModel* f_vehicleModel,
    const osg::Vec2us& f_viewportSize,
    bool f_isOnPlanView);

  static osg::StateSet* createStateSet(RenderPass f_renderPass, int f_renderBinImpostor);

  osg::Node::NodeMask getTraversalMask(RenderPass f_renderPass) const
  {
    return m_traversalMasks[static_cast<vfc::uint32_t>(f_renderPass)];
  }

  void setTraversalMask(RenderPass f_renderPass, osg::Node::NodeMask f_traversalMask)
  {
    m_traversalMasks[static_cast<vfc::uint32_t>(f_renderPass)] = f_traversalMask;
  }

  osg::ref_ptr<PostRenderStateSetUpdateCallback> getPRSSUpdateCallback() const
  {
    return m_postRenderSSUpdateCallback;
  }

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
  osg::ref_ptr<cc::core::CustomFramework> m_customFramework;
  osg::ref_ptr<pc::vehiclemodel::VehicleModel> m_vehicleModel;
  osg::ref_ptr<osg::Geode> m_pImpostorWithoutWheels;
  osg::ref_ptr<pc::util::osgx::RenderToTextureCamera> m_impostorSnapShotCamNode;
  osg::ref_ptr<osg::Camera> m_impostorPostRenderCamNode;
  osg::Vec3f m_lastEye;
  bool m_isTransparent;
  bool m_isOnPlanView;
  osg::ref_ptr<PostRenderStateSetUpdateCallback> m_postRenderSSUpdateCallback;
  cc::daddy::SolidBasePlateState m_prevBaseplateState;

  vfc::uint8_t m_SVSVehTransSts;
  vfc::uint8_t m_1stOff2On;

  // for handling the render order of the wheels and the 2D impostor
  typedef std::array<osg::ref_ptr<osg::StateSet>, NUM_RENDER_PASSES> StateSetArray;
  typedef std::array<osg::Node::NodeMask, NUM_RENDER_PASSES> NodeMaskArray;
  StateSetArray m_stateSets;
  NodeMaskArray m_traversalMasks;

  enum LookAtDirection  // Side of the vehicle that is seen from the virtual cam
  {
    STRAIGHT,
    LEFT,
    RIGHT
  };
  LookAtDirection m_lookAtDir;
  static LookAtDirection determineLookAtDirection(const osg::Vec3f &f_eyePosition);
  void updateTraversalMasks(void);

  bool updateRequired(const osgUtil::CullVisitor* f_cv);
};

} // namespace impostor
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_IMPOSTOR_FIXEDIMPOSTOR_H
