//-------------------------------------------------------------------------------
// Copyright (c) 2021 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_PDC_PDCSETTINGS_H
#define CC_ASSETS_PDC_PDCSETTINGS_H

#include "cc/assets/pdc/inc/PdcHelper.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"

namespace cc
{
namespace assets
{
namespace pdc
{


///
/// PdcSettings
///
class PdcSettings : public pc::util::coding::ISerializable
{
public:

  PdcSettings()
    : m_minDistance(0.0f)
    , m_maxDistance(1.6f)
    , m_spline_threshold(200.f)
    , m_spline_threshold_hysterese(250.f)
    , m_baseWidth(50.f)
    , m_baseHeight(0.02f)
    , m_uprightHeight(500.f)
    , m_baseOpacityIn(90)
    , m_baseOpacityOut(50)
    , m_uprightOpacityUpIn(20)
    , m_uprightOpacityUpOut(0)
    , m_uprightOpacityDownIn(70)
    , m_uprightOpacityDownOut(40)
    , m_colorsInside0(
      Color(255, 0, 60),   // pdc_col_in_col1 - no conversion to dec is necessary cause ub ColorArray is used
      Color(255, 150, 50), // pdc_col_in_col2
      Color(255, 240, 50)) // pdc_col_in_col3
    , m_colorsInside1(
      Color(255, 0, 60),   // pdc_col_in_col1 - no conversion to dec is necessary cause ub ColorArray is used
      Color(255, 150, 50), // pdc_col_in_col2
      Color(255, 240, 50)) // pdc_col_in_col3
    , m_colorsInside2(
      Color(255, 0, 60),   // pdc_col_in_col1 - no conversion to dec is necessary cause ub ColorArray is used
      Color(255, 150, 50), // pdc_col_in_col2
      Color(255, 240, 50)) // pdc_col_in_col3
    , m_colorsOutside0(
      Color(255, 0, 60),   // pdc_col_out_col1 - no conversion to dec is necessary cause ub ColorArray is used
      Color(255, 150, 50), // pdc_col_out_col2
      Color(255, 240, 50)) // pdc_col_out_col3
    , m_colorsOutside1(
      Color(255, 0, 60),   // pdc_col_out_col1 - no conversion to dec is necessary cause ub ColorArray is used
      Color(255, 150, 50), // pdc_col_out_col2
      Color(255, 240, 50)) // pdc_col_out_col3
    , m_colorsOutside2(
      Color(255, 0, 60),   // pdc_col_out_col1 - no conversion to dec is necessary cause ub ColorArray is used
      Color(255, 150, 50), // pdc_col_out_col2
      Color(255, 240, 50)) // pdc_col_out_col3
    , m_colorDistances0(
      0.3f, // dist1
      0.7f, // dist2
      1.2f) // dist3
    , m_colorDistances1(
      0.3f, // dist1
      0.5f, // dist2
      1.2f) // dist3
    , m_colorDistances2(
      0.3f, // dist1
      0.6f, // dist2
      1.2f) // dist3
    , m_maxDist0(1.2f)  //FrontRear Zone
    , m_maxDist1(0.5f)  //Side Zone
    , m_maxDist2(0.6f)  //Diagonal Zone
    , m_colorInterpolation(false) //color interpolation between distance
    , m_blendMargin(0.0f) //color blending margin (0.0 for no blending)
  {
  }

  SERIALIZABLE(PdcSettings)
  {
    ADD_FLOAT_MEMBER(minDistance);
    ADD_FLOAT_MEMBER(maxDistance);
    ADD_FLOAT_MEMBER(spline_threshold);
    ADD_FLOAT_MEMBER(spline_threshold_hysterese);
    ADD_FLOAT_MEMBER(baseWidth);
    ADD_FLOAT_MEMBER(baseHeight);
    ADD_FLOAT_MEMBER(uprightHeight);
    ADD_MEMBER(Opacity, baseOpacityIn);
    ADD_MEMBER(Opacity, baseOpacityOut);
    ADD_MEMBER(Opacity, uprightOpacityUpIn);
    ADD_MEMBER(Opacity, uprightOpacityUpOut);
    ADD_MEMBER(Opacity, uprightOpacityDownIn);
    ADD_MEMBER(Opacity, uprightOpacityDownOut);
    ADD_MEMBER(ColorValues, colorsInside0);
    ADD_MEMBER(ColorValues, colorsInside1);
    ADD_MEMBER(ColorValues, colorsInside2);
    ADD_MEMBER(ColorValues, colorsOutside0);
    ADD_MEMBER(ColorValues, colorsOutside1);
    ADD_MEMBER(ColorValues, colorsOutside2);
    ADD_MEMBER(DistanceValues, colorDistances0);
    ADD_MEMBER(DistanceValues, colorDistances1);
    ADD_MEMBER(DistanceValues, colorDistances2);
    ADD_FLOAT_MEMBER(maxDist0);
    ADD_FLOAT_MEMBER(maxDist1);
    ADD_FLOAT_MEMBER(maxDist2);
    ADD_FLOAT_MEMBER(blendMargin);
    ADD_BOOL_MEMBER(colorInterpolation);
    ADD_FLOAT_MEMBER(ptsOpaqueThreshold);
  }

  static void updateInterpolators();

  static Color getDistColorIn0(float f_distance);
  static Color getDistColorIn1(float f_distance);
  static Color getDistColorIn2(float f_distance);
  static Color getDistColorOut0(float f_distance);
  static Color getDistColorOut1(float f_distance);
  static Color getDistColorOut2(float f_distance);

  float m_minDistance;
  float m_maxDistance;
  float m_spline_threshold;           // distance threshold of neighbored zones when they snap
  float m_spline_threshold_hysterese; // hysterese value when to break up the snap again
  float m_baseWidth;                  // pdc_w
  float m_baseHeight;                 // height above ground
  float m_uprightHeight;              // pdc_h
  Opacity m_baseOpacityIn;            // pdc_op_base_in
  Opacity m_baseOpacityOut;           // pdc_op_base_out
  Opacity m_uprightOpacityUpIn;       // pdc_op_up_in
  Opacity m_uprightOpacityUpOut;      // pdc_op_up_out
  Opacity m_uprightOpacityDownIn;     // pdc_op_low_in
  Opacity m_uprightOpacityDownOut;    // pdc_op_low_out
  ColorValues m_colorsInside0;         // pdc_col_in (x)
  ColorValues m_colorsInside1;         // pdc_col_in (x)
  ColorValues m_colorsInside2;         // pdc_col_in (x)
  ColorValues m_colorsOutside0;        // pdc_col_out (x)
  ColorValues m_colorsOutside1;        // pdc_col_out (x)
  ColorValues m_colorsOutside2;        // pdc_col_out (x)
  DistanceValues m_colorDistances0;
  DistanceValues m_colorDistances1;
  DistanceValues m_colorDistances2;
  float m_maxDist0;
  float m_maxDist1;
  float m_maxDist2;
  float m_blendMargin;
  bool m_colorInterpolation;
  vfc::float32_t m_ptsOpaqueThreshold{0.4f};

};

extern pc::util::coding::Item<PdcSettings> g_pdcSettings;

} // namespace pdc
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PDC_PDCSETTINGS_H