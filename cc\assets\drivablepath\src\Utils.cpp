//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Ha Thanh Phong (MS/EDA92-XC)
//  Department: (MS/EDA92-XC)
//=============================================================================
/// @swcomponent SVS BYD
/// @file  Util.cpp
/// @brief
//=============================================================================

#include <cmath>
#include <cstring>
#include <cassert>

#include "cc/assets/drivablepath/inc/Utils.h"
#include "cc/assets/drivablepath/inc/DrivablePathManager.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/math/inc/FloatComp.h"

using pc::util::logging::g_AppContext;
using pc::vehicle::g_mechanicalData;

using std::memset;


namespace cc
{
namespace assets
{
namespace drivablepath
{

// Table look up data for Sine calculation
// sine table: angle [0,pi/2], sine range [0, 2^14]
static constexpr SInt16 rbp_TRIG_SinTab_ps16[rbp_TRIG_SIN_TABLE_dSIZE] =
{
  0, 1023, 2043, 3054, 4053, 5037, 6001, 6942, 7855, 8738, 9586, 10397, 11168, 11895, 12575,
  13207, 13787, 14313, 14783, 15195, 15548, 15840, 16071, 16239, 16343, 16384
};

// Table look up data for Tan calculation
// tangent table: angle [0, 41/32], tangent range [0, 3.36 * 2^13]
static constexpr SInt16 rbp_TRIG_TanTab_ps16[rbp_TRIG_TAN_TABLE_dSIZE] =
{
    0, 256, 513, 770, 1029, 1291, 1554, 1821, 2092, 2367, 2647, 2932, 3225, 3524, 3832, 4148, 4475,
    4814, 5165, 5530, 5910, 6309, 6727, 7167, 7632, 8124, 8649, 9209, 9809, 10457, 11158, 11922, 12758,
    13680, 14704, 15849, 17142, 18616, 20315, 22300, 24654, 27497
};

// angle table for arctan2: from pi/4 to the smallest representable angle >0
// 2^22 * arctan(2^(-n)), n=0..23
static constexpr SInt32 rbp_TRIG_ArcTan2Tab_ps32[rbp_TRIG_ATAN2_TABLE_dSIZE] =
{
  3294199, 1944679, 1027515, 521583, 261803, 131029, 65531, 32767, 16384, 8192,
  4096, 2048, 1024, 512, 256, 128, 64, 32, 16, 8, 4, 2, 1
};

/** LUT Klothoiden X Werte Fix-Komma 10 */
static constexpr UInt16 rbp_apgKloA3XF10_u16[rbp_apgKloLUTlength_du16] = {
  0, 51, 102, 154, 205, 256, 307, 358, 410, 461, 512, 563, 614,
    666, 717, 768, 819, 870, 921, 973, 1024, 1075, 1126, 1177, 1228,
    1279, 1330, 1381, 1432, 1483, 1534, 1584, 1635, 1686, 1736, 1787,
    1837, 1888, 1938, 1988, 2038, 2088, 2138, 2187, 2237, 2286, 2335,
    2384, 2433, 2481, 2529, 2577, 2625, 2673, 2720, 2767, 2813, 2860,
    2905, 2951, 2996
};

/** LUT Klothoiden Y Werte Fix-Komma 10 */
static constexpr UInt16 rbp_apgKloA3YF10_u16[rbp_apgKloLUTlength_du16] = {
  0, 0, 0, 0, 0, 0, 1, 1, 1, 2, 2, 3, 4, 5, 7, 8, 10, 12, 14, 16,
    19, 22, 25, 29, 33, 37, 42, 47, 52, 58, 64, 71, 78, 85, 93, 101,
    110, 120, 130, 140, 151, 163, 175, 188, 201, 215, 229, 244, 260,
    277, 294, 312, 330, 349, 369, 389, 411, 433, 455, 479, 503
};

/** LUT Klothoiden Index Psi Werte Fix-Komma 16.
 Die Klothoiden-LUT reicht von 0 bis 0,5 rad, oder Formstelle 1 (R = A) */
static constexpr UInt16 rbp_apgKloA3PsiF16_u16[rbp_apgKloLUTlength_du16] = {
  0, 9, 36, 82, 146, 228, 328, 446, 583, 737, 910, 1101, 1311,
    1538, 1784, 2048, 2330, 2631, 2949, 3286, 3641, 4014, 4405,
    4815, 5243, 5689, 6153, 6636, 7136, 7655, 8192, 8747, 9321,
    9912, 10522, 11150, 11796, 12461, 13144, 13844, 14564, 15301,
    16056, 16830, 17622, 18432, 19260, 20107, 20972, 21854, 22756,
    23675, 24612, 25568, 26542, 27534, 28545, 29573, 30620, 31685,
    32768
};

static constexpr UInt32 rbp_mtl_Sqrt_LookUp_cpu32[256] =
{
   0,    16,  22,  27,  32,  35,  39,  42,  45,  48,  50,  53,  55,  57,
   59,   61,  64,  65,  67,  69,  71,  73,  75,  76,  78,  80,  81,  83,
   84,   86,  87,  89,  90,  91,  93,  94,  96,  97,  98,  99, 101, 102,
   103, 104, 106, 107, 108, 109, 110, 112, 113, 114, 115, 116, 117, 118,
   119, 120, 121, 122, 123, 124, 125, 126, 128, 128, 129, 130, 131, 132,
   133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 144, 145,
   146, 147, 148, 149, 150, 150, 151, 152, 153, 154, 155, 155, 156, 157,
   158, 159, 160, 160, 161, 162, 163, 163, 164, 165, 166, 167, 167, 168,
   169, 170, 170, 171, 172, 173, 173, 174, 175, 176, 176, 177, 178, 178,
   179, 180, 181, 181, 182, 183, 183, 184, 185, 185, 186, 187, 187, 188,
   189, 189, 190, 191, 192, 192, 193, 193, 194, 195, 195, 196, 197, 197,
   198, 199, 199, 200, 201, 201, 202, 203, 203, 204, 204, 205, 206, 206,
   207, 208, 208, 209, 209, 210, 211, 211, 212, 212, 213, 214, 214, 215,
   215, 216, 217, 217, 218, 218, 219, 219, 220, 221, 221, 222, 222, 223,
   224, 224, 225, 225, 226, 226, 227, 227, 228, 229, 229, 230, 230, 231,
   231, 232, 232, 233, 234, 234, 235, 235, 236, 236, 237, 237, 238, 238,
   239, 240, 240, 241, 241, 242, 242, 243, 243, 244, 244, 245, 245, 246,
   246, 247, 247, 248, 248, 249, 249, 250, 250, 251, 251, 252, 252, 253,
   253, 254, 254, 255
};


Point_2f  ConvertPoint2NewCoor(const Point_2f &f_point, const CoorCenter_3f &f_coor)
{
  const vfc::float32_t l_x = f_point.m_point_x - f_coor.m_coor_x;
  const vfc::float32_t l_y = f_point.m_point_y - f_coor.m_coor_y;

  const vfc::float32_t l_cos = std::cos(-1.0f * f_coor.m_coor_yaw);
  const vfc::float32_t l_sin = std::sin(-1.0f * f_coor.m_coor_yaw);

  // vfc::float32_t l_cos = std::cos(-1.0f * f_coor.m_coor_yaw* 3.1415926f/180.0f);
  // vfc::float32_t l_sin = std::sin(-1.0f * f_coor.m_coor_yaw* 3.1415926f/180.0f);

  Point_2f l_result = {0, 0};
  l_result.m_point_x = l_x * l_cos - l_y * l_sin;
  l_result.m_point_y = l_x * l_sin + l_y * l_cos;

  return l_result;
}


Point_3f  ConvertPoint2NewCoor(const Point_3f &f_point, const CoorCenter_3f &f_coor)
{

  const vfc::float32_t l_x = f_point.m_point_x - f_coor.m_coor_x;
  const vfc::float32_t l_y = f_point.m_point_x - f_coor.m_coor_y;
  const vfc::float32_t l_yaw = f_point.m_point_phi - f_coor.m_coor_yaw;

  const vfc::float32_t l_cos = std::cos(-1.0f * f_coor.m_coor_yaw);
  const vfc::float32_t l_sin = std::sin(-1.0f * f_coor.m_coor_yaw);

  Point_3f l_result =  {0, 0, 0};

  l_result.m_point_x = l_x * l_cos - l_y * l_sin;
  l_result.m_point_y = l_x * l_sin + l_y * l_cos;
  l_result.m_point_phi= l_yaw;

  return l_result;

}


UInt16  rbp_mtl_u16_Add_u16_u16(UInt16 f_X_u16, UInt16 f_Y_u16)
{
    UInt32 l_Result_u32{0U};

  l_Result_u32 = ( static_cast<UInt32>(f_X_u16) + static_cast<UInt32>(f_Y_u16) );

  // underflow impossible --> no underflow treatment
  // limit to upper bound
  if( l_Result_u32 > (static_cast<UInt32>(rbp_MAX_du16)) )
  {
    l_Result_u32 = (static_cast<UInt32>(rbp_MAX_du16));
  }

  return (static_cast<UInt16>(l_Result_u32));
}


UInt16  rbp_mtl_ReducePrecision_u16(UInt16 f_X_u16, UInt8 f_BitNum_u8)
{
  // reduction has to be peformed
  if( f_BitNum_u8 > (static_cast<UInt8>(0)) )
  {
    f_X_u16 = rbp_mtl_u16_Add_u16_u16( f_X_u16, (static_cast<UInt16>(1 << (f_BitNum_u8-1))) );  //PRQA S 3133  //PRQA S 3135
    f_X_u16 = (f_X_u16 >> f_BitNum_u8);  //PRQA S 3010
  }

  return f_X_u16;
}


UInt32  rbp_mtl_u32_Div_u32_u32(UInt32 f_X_u32, UInt32 f_Y_u32)
{
  UInt32 l_Y2_u32;
  UInt32 l_Result_u32;

  // division by 0
  if( f_Y_u32 == 0 )
  {
    l_Result_u32 = rbp_MAX_du32;
  }
  else
  {
    // y / 2
    l_Y2_u32 = f_Y_u32 / 2;

    // rounding possible: x + y/2 wihtin UInt32 range
    if( f_X_u32 <= (rbp_MAX_du32 - l_Y2_u32) )
    {
      l_Result_u32 = ( f_X_u32 + l_Y2_u32 ) / f_Y_u32;
    }
    // rounding not possible: x + y/2 outside SInt32 range
    else
    {
      l_Result_u32 = f_X_u32 / f_Y_u32;
    }
  }
  return l_Result_u32;
}


static void rbp_apgCalcClothoidXYApprox_vd(
  const UInt16 f_lengthF10_u16,
  const UInt16 f_TauF16_u16,
  rbp_Type_Point_st * f_EndPoint_pst)
{
  UInt32 x_u32;
  UInt32 y_u32;

  // X = L * (1 - tau^2/10). Scaling:  F19 - (F16*F16)/F13 = F19
  x_u32 = static_cast<UInt32>(1<<19) - rbp_mtl_u32_Div_u32_u32(rbp_mtl_Square_mac(f_TauF16_u16), static_cast<UInt32>(10 * (1<<13)));
  // worst case: L = 8 m = 2^13. 2^19 * 2^13 = 2^32 -> no overflow, because x will be < 2^19 for L > 0
  x_u32 = rbp_mtl_u32_Div_u32_u32(x_u32 * f_lengthF10_u16, static_cast<UInt32>(1<<19)); // F19 * F10 / F19 = F10

  // Y = L * tau/3.   If tau <= 2^15, result is <= 2^15
  y_u32 = rbp_mtl_u32_Div_u32_u32(f_lengthF10_u16 * f_TauF16_u16, static_cast<UInt32>(3 * (1<<16)));  // F16 * F10 / F16 = F10

  f_EndPoint_pst->X_s16 = static_cast<SInt16>(x_u32);
  f_EndPoint_pst->Y_s16 = static_cast<SInt16>(y_u32);
}


SInt32  rbp_mtl_s32_Add_u32_s32(SInt32 f_X_s32, UInt32 f_Y_u32)
{
  //NOTE:Code from GLIWA.
  SInt32 l_Result_s32;

  if (f_X_s32 >= 0)
  {
    if ((f_Y_u32 >> 31) != 0)
    {
      // f_Y_u32 > rbp_MAX_ds32 ?
      // f_Y_u32 already is too big, so we will not add any positive number
      l_Result_s32 = rbp_MAX_ds32;
    }
    else
    {
      l_Result_s32 = f_X_s32 + static_cast<SInt32>(f_Y_u32);

      if ((l_Result_s32 >> 31) != 0)  //PRQA S 3003  //Code looks fine
      {
        // l_Result_s32 < 0 ?
        // overflow (l_Result_s32 > rbp_MAX_ds32) occurred
        l_Result_s32 = rbp_MAX_ds32;
      }
    }
  }
  else
  {
    // A negative f_X_s32 may not be enough to bring a really large f_Y_u32 into
    // signed 32bit range. Signed integer overflow may occur. By first getting the
    // absolute value of f_X_s32 (which works even in case of rbp_MIN_ds32 due to
    // signed int -> vfc::uint32_t type promotion) cast to UInt32 and adding
    // (UInt32)rbp_MAX_ds32 the entire check can be done in the UInt32 domain.
    // Otherwise, integer overflow in the check would (and did) occur.
    if( ( static_cast<UInt32>(-f_X_s32) + static_cast<UInt32>rbp_MAX_ds32 ) <= f_Y_u32 )  //PRQA S 3135
    {
      l_Result_s32 = rbp_MAX_ds32;
    }

    else
    {
      l_Result_s32 = f_X_s32 + static_cast<SInt32>(f_Y_u32);  //PRQA S 2891  //PRQA S 2851
    }
  }
  return l_Result_s32;
}


SInt32  rbp_mtl_ReducePrecision_s32(SInt32 f_X_s32, UInt8 f_BitNum_u8)
{
  UInt32 f_X_u32;

  // reduction has to be peformed
  if( f_BitNum_u8 > (static_cast<UInt8>(0)) )
  {
    // negative value
    if( f_X_s32 < 0L )
    {
      // -x is existing
      if( f_X_s32 > rbp_MIN_ds32 )
      {
        f_X_s32 = (-f_X_s32);
      }
      else
      {
        f_X_s32 = rbp_MAX_ds32;
      }
      f_X_s32 = rbp_mtl_s32_Add_u32_s32( f_X_s32, (static_cast<UInt32>(1 << (f_BitNum_u8-1))) );//PRQA S 3133  //PRQA S 3135

      // type cast to UInt32 for right shift
      // the represented value does not change since f_X_s32 is positive
      f_X_u32 = (static_cast<UInt32>(f_X_s32));
      f_X_s32 = static_cast<SInt32>(f_X_u32 >> f_BitNum_u8);  //PRQA S 3135
      f_X_s32 = (-f_X_s32);
    }
    // positive value
    else
    {
      f_X_s32 = rbp_mtl_s32_Add_u32_s32( f_X_s32, (static_cast<UInt32>(1 << (f_BitNum_u8-1))) );  //PRQA S 3133  //PRQA S 3135

      // type cast to UInt32 for right shift
      // the represented value does not change since f_X_s32 is positive
      f_X_u32 = (static_cast<UInt32>(f_X_s32));
      f_X_s32 = static_cast<SInt32>(f_X_u32 >> f_BitNum_u8);  //PRQA S 3135
    }
  }

  return f_X_s32;
}


UInt32  rbp_mtl_u32_Add_u32_u32(UInt32 f_X_u32, UInt32 f_Y_u32)
{
    //NOTE:Code from GLIWA.
    UInt32 l_Result_u32 = f_X_u32 + f_Y_u32;
    if (l_Result_u32<f_Y_u32)
    {
      // Can only happen due to overflow; use f_Y_u32 because f_X_u32 is kept in r3 which is also used for the return value => more freedom for the compiler
      /*The code is Optimized for GHS Compiler 201516_4fp(r3 register is used as Output).*/
      l_Result_u32 = rbp_MAX_du32;
    }
    return l_Result_u32;
}


UInt32  rbp_mtl_ReducePrecision_u32(UInt32 f_X_u32, UInt8 f_BitNum_u8)
{
  // reduction has to be peformed
  if( f_BitNum_u8 > (static_cast<UInt8>(0)) )
  {
    f_X_u32 = rbp_mtl_u32_Add_u32_u32( f_X_u32, (static_cast<UInt32>(1 << (f_BitNum_u8-1))) );  //PRQA S 3135    //PRQA S 3133
    f_X_u32 = (f_X_u32 >> f_BitNum_u8);
  }

  return f_X_u32;
}


SInt32  rbp_mtl_s32_Div_s32_s32(SInt32 f_X_s32, SInt32 f_Y_s32)
{
  SInt32 l_Y2_s32;
  SInt32 l_Result_s32;

  // division by 0
  if( f_Y_s32 == 0 )
  {
    if( f_X_s32 >= 0 )
    {
      l_Result_s32 = rbp_MAX_ds32;
    }
    else
    {
      l_Result_s32 = rbp_MIN_ds32;
    }
  }
  else
  {
    // y / 2
    l_Y2_s32 = f_Y_s32 / 2;

    // rounding for x >= 0
    if( f_X_s32 >= 0 )
    {
      // rounding possible: x + abs(y/2) wihtin SInt32 range
      if( f_X_s32 <= (rbp_MAX_ds32 - (rbp_mtl_Abs_mac(l_Y2_s32))) )
      {
        l_Result_s32 = ( f_X_s32 + (rbp_mtl_Abs_mac(l_Y2_s32))) / f_Y_s32;
      }
      // rounding not possible: x + abs(y/2) outside SInt32 range
      else
      {
        l_Result_s32 = f_X_s32 / f_Y_s32;
      }
    }
    // rounding for x < 0
    else
    {
      // rounding possible: -x - abs(y/2) wihtin SInt32 range
      if( f_X_s32 > (rbp_MIN_ds32 + (rbp_mtl_Abs_mac(l_Y2_s32))) )
      {
        l_Result_s32 = ( f_X_s32 - (rbp_mtl_Abs_mac(l_Y2_s32)) ) / f_Y_s32;
      }
      // rounding not possible: -x - abs(y/2) outside SInt32 range
      else
      {
        l_Result_s32 = f_X_s32 / f_Y_s32;
      }
    }
  }
  return l_Result_s32;
}


SInt16 rbp_vc_Kappa2RadiusF10_s16(SInt16 f_kappaF15_s16)
{
  SInt16 l_radius_s16;
  SInt32 l_radius_s32;

  if (f_kappaF15_s16 != 0)
  {
    //1/Kappa = F25/F15 = F10 m ggf + safety for clothoid retraction (area 0...5cm)
    l_radius_s32 = rbp_mtl_s32_Div_s32_s32(0x2000000, f_kappaF15_s16);

    if (rbp_mtl_Abs_mac(l_radius_s32) > static_cast<SInt32>rbp_MAX_ds16)
    {
      // too large to fit in SInt16
      l_radius_s16 = rbp_geo_RadiusStraight_ds16;
    }
    else
    {
      l_radius_s16 = static_cast<SInt16>(l_radius_s32);
    }
  }
  else
  {
    l_radius_s16 = rbp_geo_RadiusStraight_ds16;
  }

  return l_radius_s16;
}


static RBP_INLINE UInt16 rbp_mtl_Sqrt_Adjustment_u16 (UInt32 f_x_u32, UInt32 f_xn_u32)
{
  UInt32 l_xn2_u32;
  UInt32 l_twice_xn_u32;
  SInt32 l_comparator0_s32;
  SInt32 l_comparator1_s32;
  SInt32 l_comparator2_s32;

  l_xn2_u32 = f_xn_u32 * f_xn_u32;

  // |xn * xn - x|
  l_comparator0_s32 = static_cast<SInt32>(l_xn2_u32 - f_x_u32);  //PRQA S 3135

  if ( l_comparator0_s32 < 0 )
  {
    l_comparator0_s32 = -l_comparator0_s32;
  }

  l_twice_xn_u32 = f_xn_u32 << 1;

  // |x - (xn-1) * (xn-1)|
  l_comparator1_s32 = static_cast<SInt32>(((f_x_u32 - l_xn2_u32) + l_twice_xn_u32) - 1);  //PRQA S 3135

  if ( l_comparator1_s32 < 0 )
  {
    // need to correct for x == 0 case?
    l_comparator1_s32 = -l_comparator1_s32; // only gets here when x == 0
  }

  // |(xn+1) * (xn+1) - x|
  l_comparator2_s32 = static_cast<SInt32>(((l_xn2_u32 + l_twice_xn_u32) + 1) - f_x_u32);  //PRQA S 3135




  if (l_comparator0_s32 > l_comparator1_s32)
  {
    return static_cast<UInt16>((l_comparator1_s32 > l_comparator2_s32) ? ++f_xn_u32 : --f_xn_u32); //PRQA S 3360 //PRQA S 3362
  }

  return static_cast<UInt16>((l_comparator0_s32 > l_comparator2_s32) ? ++f_xn_u32 : f_xn_u32);  //PRQA S 3360
}


UInt16  rbp_mtl_Sqrt_u32_u16(UInt32 f_X_u32)
{
  UInt32 l_xn_u32;

  if ( f_X_u32 >= 0x10000 )
  {
    if ( f_X_u32 >= 0x1000000 )
    {
      if ( f_X_u32 >= 0x10000000 )
      {
        if ( f_X_u32 >= 0x40000000 )
        {
          l_xn_u32 = rbp_mtl_Sqrt_LookUp_cpu32[f_X_u32 >> 24] << 8;
        }
        else
        {
          l_xn_u32 = rbp_mtl_Sqrt_LookUp_cpu32[f_X_u32 >> 22] << 7;
        }
      }
      else
      {
        if ( f_X_u32 >= 0x4000000 )
        {
          l_xn_u32 = rbp_mtl_Sqrt_LookUp_cpu32[f_X_u32 >> 20] << 6;
        }
        else
        {
          l_xn_u32 = rbp_mtl_Sqrt_LookUp_cpu32[f_X_u32 >> 18] << 5;
        }
      }

      l_xn_u32 = (l_xn_u32 + 1 + (f_X_u32 / l_xn_u32)) >> 1;
      l_xn_u32 = (l_xn_u32 + 1 + (f_X_u32 / l_xn_u32)) >> 1;

      // overflow handling
      if ( l_xn_u32 > 65535 )
      {
        return rbp_MAX_du16;
      }
      else
      {
        return rbp_mtl_Sqrt_Adjustment_u16 (f_X_u32, l_xn_u32);
      }
    }
    else
    {
      if ( f_X_u32 >= 0x100000 )
      {
        if ( f_X_u32 >= 0x400000 )
        {
          l_xn_u32 = rbp_mtl_Sqrt_LookUp_cpu32[f_X_u32 >> 16] << 4;
        }
        else
        {
          l_xn_u32 = rbp_mtl_Sqrt_LookUp_cpu32[f_X_u32 >> 14] << 3;
        }
      }
      else
      {
        if ( f_X_u32 >= 0x40000 )
        {
          l_xn_u32 = rbp_mtl_Sqrt_LookUp_cpu32[f_X_u32 >> 12] << 2;
        }
        else
        {
          l_xn_u32 = rbp_mtl_Sqrt_LookUp_cpu32[f_X_u32 >> 10] << 1;
        }
      }

      l_xn_u32 = (l_xn_u32 + 1 + (f_X_u32 / l_xn_u32)) >> 1;

      return rbp_mtl_Sqrt_Adjustment_u16 (f_X_u32, l_xn_u32);
    }
  }
  else
  {
    if ( f_X_u32 >= 0x100 )
    {
      if ( f_X_u32 >= 0x1000 )
      {
        if ( f_X_u32 >= 0x4000 )
        {
          l_xn_u32 = (rbp_mtl_Sqrt_LookUp_cpu32[f_X_u32 >> 8]) + 1;
        }
        else
        {
          l_xn_u32 = (rbp_mtl_Sqrt_LookUp_cpu32[f_X_u32 >> 6] >> 1) + 1;
        }
      } else
      {
        if ( f_X_u32 >= 0x400 )
        {
          l_xn_u32 = (rbp_mtl_Sqrt_LookUp_cpu32[f_X_u32 >> 4] >> 2) + 1;
        }
        else
        {
          l_xn_u32 = (rbp_mtl_Sqrt_LookUp_cpu32[f_X_u32 >> 2] >> 3) + 1;
        }
      }

      return rbp_mtl_Sqrt_Adjustment_u16 (f_X_u32, l_xn_u32);
    }
    else
    {
      return rbp_mtl_Sqrt_Adjustment_u16 (f_X_u32, rbp_mtl_Sqrt_LookUp_cpu32[f_X_u32] >> 4);
    }
  }
}


void  rbp_geo_AngleSet_Fl_vd(rbp_Type_AngleFl_st* f_Angle_pst, Float32 f_Alpha_f32)
{
  assert(f_Angle_pst != nullptr);
  f_Angle_pst->Angle_f32 = f_Alpha_f32;
  // Using the IEEE standard library function for calculating sine and cosine
  f_Angle_pst->Cos_f32 = cosf(f_Alpha_f32);
  f_Angle_pst->Sin_f32 = sinf(f_Alpha_f32);
  return;
}

void rbp_tpc_Pos2LineSinCosFL_vd(rbp_Type_LineSinCosFl_st * f_VehPos_pst, const rbp_Type_LineFl_st * f_Pos_pst)
{
    if (f_VehPos_pst == nullptr ||
        f_Pos_pst == nullptr){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_VehPos_pst || f_Pos_pst" << XLOG_ENDL;
        return;
    }
    f_VehPos_pst->P_st.X_f32 = f_Pos_pst->P_st.X_f32;
    f_VehPos_pst->P_st.Y_f32 = f_Pos_pst->P_st.Y_f32;
    rbp_geo_AngleSet_Fl_vd(&f_VehPos_pst->Phi_st, f_Pos_pst->Phi_f32);
}

void rbp_tpc_ConvertPosFix2FL_vd(rbp_Type_LineSinCosFl_st * f_PosFL_pst, rbp_Type_LineSinCos_st const * f_PosFix_cpst, UInt16 f_scaling_u16)
{
    if (f_PosFL_pst == nullptr ||
        f_PosFix_cpst == nullptr){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_PosFL_pst || f_PosFix_cpst" << XLOG_ENDL;
        return;
    }
    assert((f_scaling_u16 != 0) && (nullptr != f_PosFL_pst) && (nullptr != f_PosFix_cpst));
    f_PosFL_pst->P_st.X_f32 = static_cast<Float32>(f_PosFix_cpst->P_st.X_s16) / static_cast<Float32>(f_scaling_u16);
    f_PosFL_pst->P_st.Y_f32 = static_cast<Float32>(f_PosFix_cpst->P_st.Y_s16) / static_cast<Float32>(f_scaling_u16);
    f_PosFL_pst->Phi_st.Angle_f32 =
        static_cast<Float32>(f_PosFix_cpst->Phi_st.Angle_s16 / 4096.f); // 2^12 -> 1
    f_PosFL_pst->Phi_st.Sin_f32 = sinf(f_PosFL_pst->Phi_st.Angle_f32);
    f_PosFL_pst->Phi_st.Cos_f32 = cosf(f_PosFL_pst->Phi_st.Angle_f32);
}


void rbp_tpc_ConvertPosFL2Fix_vd(rbp_Type_LineSinCos_st * f_PosFix_pst, rbp_Type_LineSinCosFl_st const * f_PosFL_cpst, UInt16 f_scaling_u16)
{
    if (f_PosFix_pst == nullptr ||
        f_PosFL_cpst == nullptr){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_PosFix_pst || f_PosFL_cpst" << XLOG_ENDL;
        return;
    }
    assert((nullptr != f_PosFix_pst) && (nullptr != f_PosFL_cpst));
    f_PosFix_pst->P_st.X_s16 = static_cast<SInt16>(
        rbp_FloatToFixedPoint_mac(f_PosFL_cpst->P_st.X_f32, f_scaling_u16));
    f_PosFix_pst->P_st.Y_s16 = static_cast<SInt16>(
        rbp_FloatToFixedPoint_mac(f_PosFL_cpst->P_st.Y_f32, f_scaling_u16));
    f_PosFix_pst->Phi_st.Cos_s16 = static_cast<SInt16>(
        rbp_FloatToFixedPoint_mac(f_PosFL_cpst->Phi_st.Cos_f32, rbp_Exp2_u16(14)));
    f_PosFix_pst->Phi_st.Sin_s16 = static_cast<SInt16>(
        rbp_FloatToFixedPoint_mac(f_PosFL_cpst->Phi_st.Sin_f32, rbp_Exp2_u16(14)));
    f_PosFix_pst->Phi_st.Angle_s16 = static_cast<SInt16>(
        rbp_FloatToFixedPoint_mac(f_PosFL_cpst->Phi_st.Angle_f32, rbp_Exp2_u16(12)));
}


void rbp_tpc_ConvertLineFix2FL_vd(rbp_Type_LineFl_st * f_LineFL_pst, rbp_Type_Line_st const * f_LineFix_cpst, UInt16 f_scaling_u16)
{
    if (f_LineFL_pst == nullptr ||
        f_LineFix_cpst == nullptr){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_LineFL_pst || f_LineFix_cpst" << XLOG_ENDL;
        return;
    }
    assert((f_scaling_u16 != 0) && (nullptr != f_LineFL_pst) && (nullptr != f_LineFix_cpst));
    f_LineFL_pst->P_st.X_f32 = static_cast<Float32>(f_LineFix_cpst->P_st.X_s16) / static_cast<Float32>(f_scaling_u16);
    f_LineFL_pst->P_st.Y_f32 = static_cast<Float32>(f_LineFix_cpst->P_st.Y_s16) / static_cast<Float32>(f_scaling_u16);
    f_LineFL_pst->Phi_f32    = static_cast<Float32>(f_LineFix_cpst->Phi_s16 / 4096.f); // 2^12 -> 1
}

void rbp_tpc_ConvertLineFL2Fix_vd(rbp_Type_Line_st * f_LineFix_pst, rbp_Type_LineFl_st const * f_LineFL_cpst, UInt16 f_scaling_u16)
{
    if (f_LineFix_pst == nullptr ||
        f_LineFL_cpst == nullptr){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_LineFix_pst || f_LineFL_cpst" << XLOG_ENDL;
        return;
    }

    assert((nullptr != f_LineFL_cpst) && (nullptr != f_LineFix_pst));
    f_LineFix_pst->P_st.X_s16 = static_cast<SInt16>(
        rbp_FloatToFixedPoint_mac(f_LineFL_cpst->P_st.X_f32, f_scaling_u16));
    f_LineFix_pst->P_st.Y_s16 = static_cast<SInt16>(
        rbp_FloatToFixedPoint_mac(f_LineFL_cpst->P_st.Y_f32, f_scaling_u16));
    f_LineFix_pst->Phi_s16 = static_cast<SInt16>(
        rbp_FloatToFixedPoint_mac(f_LineFL_cpst->Phi_f32, rbp_Exp2_u16(12)));
}


void rbp_tpc_ConvertPointFix2FL_vd(rbp_Type_PointFl_st * f_PointFL_pst, rbp_Type_Point_st const * f_PointFix_cpst, SInt16 f_scaling_s16)
{
    if (f_PointFL_pst   == nullptr ||
        f_PointFix_cpst == nullptr){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_PointFL_pst || f_PointFix_cpst" << XLOG_ENDL;
        return;
    }
    assert((f_scaling_s16 != 0) && (nullptr != f_PointFL_pst) && (nullptr != f_PointFix_cpst));
    f_PointFL_pst->X_f32 = static_cast<Float32>(f_PointFix_cpst->X_s16) / static_cast<Float32>(f_scaling_s16);
    f_PointFL_pst->Y_f32 = static_cast<Float32>(f_PointFix_cpst->Y_s16) / static_cast<Float32>(f_scaling_s16);
}

void rbp_tpc_ConvertPointFL2Fix_vd(rbp_Type_Point_st * f_PointFix_pst, rbp_Type_PointFl_st const * f_PointFL_cpst, SInt16 f_scaling_s16)
{
    if (f_PointFix_pst == nullptr ||
        f_PointFL_cpst == nullptr){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_PointFix_pst || f_PointFL_cpst" << XLOG_ENDL;
        return;
    }

    assert((nullptr != f_PointFix_pst) && (nullptr != f_PointFL_cpst));
    f_PointFix_pst->X_s16 = static_cast<SInt16>(
        rbp_FloatToFixedPoint_mac(f_PointFL_cpst->X_f32, f_scaling_s16));
    f_PointFix_pst->Y_s16 = static_cast<SInt16>(
        rbp_FloatToFixedPoint_mac(f_PointFL_cpst->Y_f32, f_scaling_s16));
}


SInt16  rbp_mtl_LinIntPow2Step_s16(const SInt16* f_Y_cps16, UInt8 f_LenY_u8, SInt16 f_Xmin_s16, SInt16 f_Xmax_s16, UInt8 f_StepBitsX_u8, SInt16 f_X_s16)
{
  UInt8 l_Index_u8;
  UInt16 l_Value_u16;
  SInt16 l_Value_s16;
  UInt32 l_Value_u32;

  assert((f_Y_cps16!= nullptr));

  // check for left boundary
  if( f_X_s16 <= f_Xmin_s16 )
  {
    l_Value_s16 = f_Y_cps16[0];
  }
  // check for right boundary
  else if( f_X_s16 >= f_Xmax_s16 )
  {
    l_Value_s16 = (f_Y_cps16[f_LenY_u8-1]);
  }
  else
  {
    // determine left index for y array
    l_Value_u16 = (static_cast<UInt16>(f_X_s16 - f_Xmin_s16));  //PRQA S 3135
    l_Index_u8 = static_cast<UInt8>(l_Value_u16 >> f_StepBitsX_u8);

    // check if index is valid
    if( l_Index_u8 >= (f_LenY_u8-1) )
    {
      l_Value_s16 = (f_Y_cps16[f_LenY_u8-1]);
    }
    else
    {
      // deltaX = x - x(index)
      l_Value_u16 -= static_cast<UInt16>( l_Index_u8 * (static_cast<UInt16>(1<<f_StepBitsX_u8)) );  //PRQA S 3133  //PRQA S 3135

      // deltaY = slope * deltaX
      if( f_Y_cps16[l_Index_u8+1] >= f_Y_cps16[l_Index_u8])
      {
        l_Value_u32 = (static_cast<UInt32>( (static_cast<UInt32>(f_Y_cps16[l_Index_u8+1]-f_Y_cps16[l_Index_u8])) * l_Value_u16  //PRQA S 3133  //PRQA S 3135
                                /* + ((UInt32)(1<<f_StepBitsX_u8)/2)*/
                                )
                       ) >> f_StepBitsX_u8;
        l_Value_s16 = (static_cast<SInt16>(l_Value_u32));
      }
      else
      {
        l_Value_u32 = (static_cast<UInt32>( (static_cast<UInt32>(f_Y_cps16[l_Index_u8]-f_Y_cps16[l_Index_u8+1])) * l_Value_u16  //PRQA S 3133  //PRQA S 3135
                                  /* + ((UInt32)(1<<f_StepBitsX_u8)/2)*/
                                )
                       ) >> f_StepBitsX_u8;
        l_Value_s16 = - (static_cast<SInt16>(l_Value_u32));  //PRQA S 3010
      }

      // result of interpolation
      l_Value_s16 = f_Y_cps16[l_Index_u8] + l_Value_s16;
    }
  }
  // return result of interpolation
  return l_Value_s16;
}


SInt16  rbp_trig_Sin_s16(SInt16 f_Phi_s16)
{
  // indicator for sign of the result
  UInt8 l_Sign_u8 = static_cast<UInt8>(0);

  SInt16 l_Sine_s16;

  // make angle positive
  if( f_Phi_s16 < 0 )
  {

    // guarantee the existence of a positive angle for the lowest possible negative input angle
    // the small error is neglected
    if( f_Phi_s16 == rbp_MIN_ds16 )
    {
      f_Phi_s16 = rbp_MIN_ds16 + (static_cast<SInt16>(1));
    }
    f_Phi_s16 = (-f_Phi_s16);  //PRQA S 3010

    // update sign indicator
    l_Sign_u8 = 1;
  }

  // check vor valid interpolation range [0,pi/2]
  // [2pi,2.546pi] -> [0,0.546pi]
  if( f_Phi_s16 > (static_cast<SInt16>rbp_mtl_2_PI_du16) )
  {
    f_Phi_s16 -= (static_cast<SInt16>rbp_mtl_2_PI_du16);
  }
  // [pi,2pi[ -> [0,pi[
  else if( f_Phi_s16 > (static_cast<SInt16>rbp_mtl_PI_du16) )
  {
    f_Phi_s16 -= (static_cast<SInt16>rbp_mtl_PI_du16);

    // update sign indicator
    l_Sign_u8 += 1;
  }
  else
  {// QAC
  }

  // check vor valid interpolation range [0,pi/2]
  if( f_Phi_s16 > (static_cast<SInt16>rbp_mtl_PI_2_du16) )
  {
    f_Phi_s16 -= (static_cast<SInt16>rbp_mtl_PI_2_du16);
    f_Phi_s16 = (static_cast<SInt16>rbp_mtl_PI_2_du16) - f_Phi_s16;  //PRQA S 3010
  }

  // interpolate sine
  l_Sine_s16 = rbp_mtl_LinIntPow2Step_s16( rbp_TRIG_SinTab_ps16, static_cast<UInt8>rbp_TRIG_SIN_TABLE_dSIZE,  //PRQA S 3840
      (static_cast<SInt16>(0)), (static_cast<SInt16>rbp_mtl_PI_2_du16), rbp_TRIG_SIN_ANGLE_STEPBITS,
      f_Phi_s16 );

  // adapt sign
  if( (rbp_mtl_OddValue_b(l_Sign_u8) )!= false )
  {
    l_Sine_s16 = (-l_Sine_s16);  //PRQA S 3010
  }

  return l_Sine_s16;
}


SInt16  rbp_trig_Cos_s16(SInt16 f_Phi_s16)
{
  SInt32 l_Value_s32;
  SInt16 l_Value_s16;
  SInt16 l_Result_s16;

  // cos(Phi) = sin(Phi+Pi/2)

  // add pi/2
  l_Value_s32 = ( (static_cast<SInt32>(f_Phi_s16)) + (static_cast<SInt32>rbp_mtl_PI_2_du16) );

  // underflow impossible --> no underflow treatment
  // overflow treatment
  if( l_Value_s32 > (static_cast<SInt32>rbp_MAX_ds16) )
  {
    l_Value_s32 -= (static_cast<SInt32>rbp_mtl_2_PI_du16);
  }

  l_Value_s16 = (static_cast<SInt16>(l_Value_s32));

  // perform sin
  l_Result_s16 = rbp_trig_Sin_s16( l_Value_s16 );

  return l_Result_s16;
}


SInt16  rbp_trig_NormalizeAngle_s16(SInt16 f_Angle_s16)
{
  if (f_Angle_s16 >= static_cast<SInt16> rbp_mtl_PI_du16)
  {
    f_Angle_s16 -= static_cast<SInt16> rbp_mtl_2_PI_du16;
  }
  if (f_Angle_s16 < -static_cast<SInt16> rbp_mtl_PI_du16)
  {
    f_Angle_s16 += static_cast<SInt16> rbp_mtl_2_PI_du16;
  }

  return f_Angle_s16;
}


void  rbp_geo_Angle_Set_vd(rbp_Type_Angle_st* f_Angle_pst, SInt16 f_Alpha_s16)
{
  assert((f_Angle_pst != nullptr));
  f_Angle_pst->Angle_s16 = rbp_trig_NormalizeAngle_s16(f_Alpha_s16);
  f_Angle_pst->Cos_s16 = rbp_trig_Cos_s16(f_Angle_pst->Angle_s16);
  f_Angle_pst->Sin_s16 = rbp_trig_Sin_s16(f_Angle_pst->Angle_s16);
  return;
}


void rbp_tpc_PathPoint2LineSinCos_vd(rbp_Type_LineSinCos_st * f_VehPos_pst, const rbp_Type_PathPoint_st* f_PathPoint_pst)
{
    if (f_VehPos_pst == nullptr ||
        f_PathPoint_pst == nullptr){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_VehPos_pst || f_PathPoint_pst" << XLOG_ENDL;
        return;
    }

    f_VehPos_pst->P_st = f_PathPoint_pst->Pos_st.P_st;
    rbp_geo_Angle_Set_vd(&f_VehPos_pst->Phi_st, f_PathPoint_pst->Pos_st.Phi_s16);
}


void rbp_vc_PredictPos_Fl_vd(
        rbp_Type_LineSinCosFl_st       * f_PredictedPos_pst,
        rbp_Type_LineSinCosFl_st const * f_StartPos_cpst,
        Float32                          f_Kappa_f32,
        Float32                          f_DeltaRear_f32,
        Float32                          f_deltaS_f32)
{
    if (f_PredictedPos_pst == nullptr ||
        f_StartPos_cpst == nullptr){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_PredictedPos_pst || f_StartPos_cpst" << XLOG_ENDL;
        return;
    }

    assert((nullptr != f_StartPos_cpst) && (nullptr != f_PredictedPos_pst));
    {
        Float32 l_DeltaX_f32;
        Float32 l_DeltaY_f32;
        Float32 l_DeltaPhi_f32;
        Float32 const l_phiPlusDeltaRear_f32 = f_StartPos_cpst->Phi_st.Angle_f32 + f_DeltaRear_f32;

    if(rbp_mtl_Abs_f32_mac(f_deltaS_f32) > rbp_eps_df32)
    {
      // handling of straight drive or crab steering
      if (rbp_mtl_Abs_f32_mac(f_Kappa_f32) <= rbp_geo_MinCurvature_df32)
      {
        l_DeltaX_f32 = f_deltaS_f32 * cosf(l_phiPlusDeltaRear_f32);
        l_DeltaY_f32 = f_deltaS_f32 * sinf(l_phiPlusDeltaRear_f32);

        f_PredictedPos_pst->Phi_st = f_StartPos_cpst->Phi_st;
      }
      else
      {
        // calculate new pos by turning round on circlecenter. Circle arc function: dPhi = ds / R = ds * Kappa
        l_DeltaPhi_f32 = f_deltaS_f32 * f_Kappa_f32;

        l_DeltaY_f32 = ( cosf(l_phiPlusDeltaRear_f32) - cosf(l_phiPlusDeltaRear_f32 + l_DeltaPhi_f32)) / f_Kappa_f32;
        l_DeltaX_f32 = (-sinf(l_phiPlusDeltaRear_f32) + sinf(l_phiPlusDeltaRear_f32 + l_DeltaPhi_f32)) / f_Kappa_f32;

        rbp_geo_AngleSet_Fl_vd(&f_PredictedPos_pst->Phi_st, f_StartPos_cpst->Phi_st.Angle_f32 + l_DeltaPhi_f32);
      }
      f_PredictedPos_pst->P_st.X_f32 = f_StartPos_cpst->P_st.X_f32 + l_DeltaX_f32;
      f_PredictedPos_pst->P_st.Y_f32 = f_StartPos_cpst->P_st.Y_f32 + l_DeltaY_f32;
    }
    else
    {
      // copy over source position. StartPos and predictedPos are not the same, s predictedPos is pointer to a usually just
      // instantiated Line
      *f_PredictedPos_pst = *f_StartPos_cpst;
    }
  }
}


rbp_Type_tpcClothoidResult_en rbp_tpc_CalcClothoidPsiViaLength_en(
  const UInt16 f_lengthF10_u16,
  const UInt16 f_AF10_u16,
  UInt16 *f_PSIF16_pu16
  )
{
    if (f_PSIF16_pu16 == nullptr){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_PSIF16_pu16 " << XLOG_ENDL;
        return rbp_apgClothoidUndefined_enm;
    }

    rbp_Type_tpcClothoidResult_en l_RetVal_en;

  /* check range of value*/
  const Boolean l_isLengthInAllowedRange_cb = (f_lengthF10_u16 <= static_cast<UInt16>(5792));
  const Boolean l_isAInAllowedRange_cb = ((f_AF10_u16 >= rbp_tpcMinClothoidParamAF10_du16) && (f_AF10_u16 <= rbp_tpcMaxClothoidParamAF10_du16));
  if ((l_isLengthInAllowedRange_cb == false) || (l_isAInAllowedRange_cb == false))
  {
    *f_PSIF16_pu16 = 0;
    l_RetVal_en = rbp_apgClothoidErrorCloParaOutOfRange_enm;
  }
  /* co-domain of psi is 0...0.5rad, therefor the clothoid parameter has to be larger than the clothoid length */
  else if (f_AF10_u16 < f_lengthF10_u16)
  {
    *f_PSIF16_pu16 = 0;
    l_RetVal_en = rbp_apgClothoidErrorCloParaBiggerAsLength_enm;
  }
  else
  {
    UInt32 l_temp1_u32;
    UInt32 l_temp2_u32;
    l_temp1_u32 = (static_cast<UInt32>(f_lengthF10_u16))  *  (static_cast<UInt32>(f_lengthF10_u16));
    l_temp1_u32 <<= static_cast<UInt32>(7); //128;    F10 * F10 * F7 --> F27
    // l_temp1_u32 = l_temp1_u32 / 2 * 2 ;     F28
    // The value is F27, but since it still has to be divided by 2, the value range can be increased to F28 at the same time.
    l_temp2_u32 = (static_cast<UInt32>(f_AF10_u16)) * (static_cast<UInt32>(f_AF10_u16));   /* F10 * F10 --> F20    */
    // max(A) is 10751 (see rbp_apgCalcClothoideParam_u16)
    // min(A) is 746   (see rbp_apgCalcClothoideParam_u16)
    // max(A^2/2^8) = 451500   min(A^2/2^8) = 2174
    l_temp2_u32 = rbp_mtl_ReducePrecision_u32(l_temp2_u32 , 8); /* F20 / F8  --> F12    */
    // max = 4294049792 / 2174 = 1975184

    // Division by zero cannot happen as the case that f_AF10_u16 = 0 is handeled in the first if-statement.
    if ((l_temp1_u32 > static_cast<UInt32>rbp_MAX_ds32) || (l_temp2_u32 > static_cast<UInt32>rbp_MAX_ds32))
    {
        l_temp1_u32 = (l_temp1_u32 / l_temp2_u32); /* F28 / F12 --> F16    */
    }
    else
    {
      l_temp1_u32 = static_cast<UInt32>(rbp_mtl_s32_Div_s32_s32(static_cast<SInt32>(l_temp1_u32), static_cast<SInt32>(l_temp2_u32)));/* F28 / F12 --> F16    */
    }

    if (l_temp1_u32 > rbp_apgKloA3PsiF16_u16[rbp_apgKloLUTlength_du16 - static_cast<UInt16>(1)])  /* coverage area 0 - 0.5rad*/
    {  /* error */
      // [defensive programming] as rbp_apgKloA3PsiF16_u16[rbp_apgKloLUTlength_du16-(UInt16) 1] = 0.5, this statement
      // cannot be reached as long as rbp_apgKloA3PsiF16_u16[rbp_apgKloLUTlength_du16-(UInt16) 1] is not changed.
      *f_PSIF16_pu16 = 0;
      l_RetVal_en = rbp_apgClothoidErrorPsiOutOfRange_enm;
    }
    else
    {
      *f_PSIF16_pu16 = (static_cast<UInt16>(l_temp1_u32));

      l_RetVal_en = rbp_apgClothoidOk_enm;
    }
  }

  return l_RetVal_en;
}


rbp_Type_tpcClothoidResult_en rbp_apgCalcClothoidPointByTau_en(
  const UInt16 f_TauF16_u16,
  const UInt16 f_AF10_u16,
  rbp_Type_Point_st * f_P_pst)
{
  UInt8  l_Schleife_u8;          /* Zaehler fuer die Schleife um die Suche */
  UInt8  l_IndexSuch_u8;         /* aktueller Index, bei dem gesucht wird */
  UInt8  l_IndexLow_u8  = 0;     /* Index der die untere Grenze angibt    */
  UInt8  l_IndexHigh_u8 = static_cast<UInt8> (rbp_apgKloLUTlength_du16 - static_cast<UInt16>(1)); /* Index der die obere Grenze angibt     */
  UInt16 l_DiffIndSuchPSI_u16;   /* Differenz des Wertes zwischen aktuellen und hoeheren Index */
  UInt16 l_DiffIdexLoIndHi_u16;  /* Differenz des Wertes zwischen aktuellen und hoeheren Index */
  UInt32 l_ProInterpol_u32;  // percentage [0 .. 100] of position of value in interval
  UInt32 l_Delta_u32;  // delta of X/Y from last support point to interpolated value
  UInt32 l_X_u32;
  UInt32 l_Y_u32;
  rbp_Type_tpcClothoidResult_en l_RetVal_en;

    if (f_P_pst == nullptr){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_P_pst " << XLOG_ENDL;
        return rbp_apgClothoidUndefined_enm;
    }

  /* Ueberpruefung A im Wertebereich */
  if ( (f_AF10_u16 > rbp_tpcMaxClothoidParamAF10_du16) ||  (f_AF10_u16 < rbp_tpcMinClothoidParamAF10_du16))
  {   /* error */
    f_P_pst->X_s16 = 0;
    f_P_pst->Y_s16 = 0;
    l_RetVal_en = rbp_apgClothoidErrorCloParaOutOfRange_enm;
  }
  /* Ueberpruefung PSI im Wertebereich */
  else if (f_TauF16_u16 > rbp_apgKloA3PsiF16_u16[rbp_apgKloLUTlength_du16-static_cast<UInt16>(1)])/* Abdeckungsbereich nur von 0 - 0.5rad*/
  {   /* error */
    f_P_pst->X_s16 = static_cast<SInt16> (rbp_apgKloA3XF10_u16[rbp_apgKloLUTlength_du16-static_cast<UInt16>(1)]); // set to max
    f_P_pst->Y_s16 = static_cast<SInt16> (rbp_apgKloA3YF10_u16[rbp_apgKloLUTlength_du16-static_cast<UInt16>(1)]); // set to max
    l_RetVal_en = rbp_apgClothoidErrorPsiOutOfRange_enm;
  }
  else
  {
    for ( l_Schleife_u8 = 0; l_Schleife_u8<8 ; l_Schleife_u8++ )
    {
      /* Neue Suchschleife */
      l_IndexSuch_u8 = l_IndexHigh_u8 - l_IndexLow_u8;
      l_IndexSuch_u8 = l_IndexSuch_u8 >> 1;    //PRQA S 3010             /* In die Mitte des Suchbereches Springen */
      l_IndexSuch_u8 = l_IndexSuch_u8 + l_IndexLow_u8;
      /* Unteren und oberen Suchbereich festlegen */
      if (f_TauF16_u16 < rbp_apgKloA3PsiF16_u16[l_IndexSuch_u8])
      {
        l_IndexHigh_u8 = l_IndexSuch_u8;
      }
      else
      {
        l_IndexLow_u8  = l_IndexSuch_u8;
      }
    }
    l_IndexSuch_u8 = l_IndexHigh_u8 - l_IndexLow_u8;
    l_IndexSuch_u8 = l_IndexSuch_u8 >> 1;  //PRQA S 3010              /* In die Mitte des Suchbereches Springen */
    l_IndexSuch_u8 = l_IndexSuch_u8 + l_IndexLow_u8;

    /* In l_IndexSuch_u8 ist nun immer der kleinere Index gespeichert. */
    /* D.h. Bei einem Suchwert von 17 und indizierung von 10 und 18 wird der Index von 10 angegeben. */

    /* Calculate linear interpolation */
    /* Differenz des Wertes zwischen aktuellen und hoeheren Index */
    l_DiffIdexLoIndHi_u16 = rbp_apgKloA3PsiF16_u16[l_IndexSuch_u8+1] - rbp_apgKloA3PsiF16_u16[l_IndexSuch_u8];
    /* Differenz zwischen unterem Index und gewuenschtem Wert */
    l_DiffIndSuchPSI_u16 = f_TauF16_u16 - rbp_apgKloA3PsiF16_u16[l_IndexSuch_u8];

    l_ProInterpol_u32 = rbp_mtl_u32_Div_u32_u32(l_DiffIndSuchPSI_u16 * 100u, l_DiffIdexLoIndHi_u16);

    l_Delta_u32 = rbp_apgKloA3XF10_u16[l_IndexSuch_u8+1] - rbp_apgKloA3XF10_u16[l_IndexSuch_u8];
    l_Delta_u32 = rbp_mtl_u32_Div_u32_u32(l_Delta_u32 * l_ProInterpol_u32, 100);
    l_X_u32 = rbp_apgKloA3XF10_u16[l_IndexSuch_u8] + l_Delta_u32;

    l_Delta_u32 = rbp_apgKloA3YF10_u16[l_IndexSuch_u8+1] - rbp_apgKloA3YF10_u16[l_IndexSuch_u8];
    l_Delta_u32 = rbp_mtl_u32_Div_u32_u32(l_Delta_u32 * l_ProInterpol_u32, 100);
    l_Y_u32 = rbp_apgKloA3YF10_u16[l_IndexSuch_u8] + l_Delta_u32;

    /* extension of the clothoid X values: */
    /* This is divided by the reference clothoid parameter and multiplied by the desired clothoid parameter. */
    f_P_pst->X_s16 = static_cast<SInt16> (rbp_mtl_u32_Div_u32_u32(l_X_u32 * f_AF10_u16, rbp_apgKloLUTRefA_du16)); // F10 * F10 / F10 = F10  //PRQA S 2857

    // extension of theclothoid Y values:
    f_P_pst->Y_s16 = static_cast<SInt16> (rbp_mtl_u32_Div_u32_u32(l_Y_u32 * f_AF10_u16, rbp_apgKloLUTRefA_du16)); // F10 * F10 / F10 = F10  //PRQA S 2857

    l_RetVal_en = rbp_apgClothoidOk_enm;
  }
  return l_RetVal_en;

}


rbp_Type_tpcClothoidResult_en rbp_apgCalcClothoidEnd_en(
  const UInt16 f_lengthF10_u16,
  const UInt16 f_AF10_u16,
  rbp_Type_Line_st * f_EndPoint_pst)
{
    if (f_EndPoint_pst == nullptr){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_EndPoint_pst " << XLOG_ENDL;
        return rbp_apgClothoidUndefined_enm;
    }

    UInt16                        l_TauF16_u16; // angle of tangent in clothoid endpoint
    rbp_Type_tpcClothoidResult_en l_ClothoidResult_en;

  // conversion of the length in the yaw angle, which is dependend on the clothoid parameter A
  l_ClothoidResult_en = rbp_tpc_CalcClothoidPsiViaLength_en(f_lengthF10_u16, f_AF10_u16, &l_TauF16_u16);

  // X und Y Werte in Abh. von Psi aus der LUT herausholen
  if (l_ClothoidResult_en == rbp_apgClothoidOk_enm)
  {
    // error bound analysis has shown that approximation is accurate to 0.5 mm for
    // all points of the clothoid, if A < 2.7, or L_clo < 2 m, and thus on average more accurate than LUT
    if ((f_AF10_u16 <= 2700) || (f_lengthF10_u16 < 2048))
    {
      rbp_apgCalcClothoidXYApprox_vd(f_lengthF10_u16, l_TauF16_u16, &f_EndPoint_pst->P_st);
    }
    else
    {
      l_ClothoidResult_en = rbp_apgCalcClothoidPointByTau_en(l_TauF16_u16, f_AF10_u16, &f_EndPoint_pst->P_st);
    }
  }

  if (l_ClothoidResult_en == rbp_apgClothoidOk_enm)
  {
    f_EndPoint_pst->Phi_s16 = static_cast<SInt16> (rbp_mtl_ReducePrecision_u16(l_TauF16_u16, 4)); /*F16 / F4  --> F12 */
  }
  else
  {
    (void) memset(f_EndPoint_pst, 0, sizeof(rbp_Type_Line_st));
  }

  return l_ClothoidResult_en;
}


void  rbp_geo_Rotate_vd(const rbp_Type_Point_st* f_P_cpst, const rbp_Type_Angle_st* f_Angle_cpst, rbp_Type_Point_st* f_P_rot_pst)
{
    if (f_P_cpst      == nullptr ||
        f_Angle_cpst  == nullptr ||
        f_P_rot_pst   == nullptr ){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_P_cpst || f_Angle_cpst || f_P_rot_pst" << XLOG_ENDL;
        return;
    }

  SInt32  l_CosTemp_s32;   // temporary variable a*cos(rotation angle)
  SInt32  l_SinTemp_s32;   // temporary variable a*sin(rotation angle)
  SInt32  l_temp_s32;    // helper variable to support P_rot == P

  assert((f_P_cpst != nullptr) && (f_Angle_cpst != nullptr) && (f_P_rot_pst != nullptr));
  l_CosTemp_s32 = f_P_cpst->X_s16 * f_Angle_cpst->Cos_s16; // InScale * F14 (e.g. F24)
  l_SinTemp_s32 = f_P_cpst->Y_s16 * f_Angle_cpst->Sin_s16;

  l_temp_s32 = rbp_mtl_ReducePrecision_s32(l_CosTemp_s32 - l_SinTemp_s32, static_cast<UInt8>(14)); // Input scale
  l_SinTemp_s32 = f_P_cpst->X_s16 * f_Angle_cpst->Sin_s16;
  l_CosTemp_s32 = f_P_cpst->Y_s16 * f_Angle_cpst->Cos_s16; // InScale * F14 (e.g. F24)

  f_P_rot_pst->X_s16 = static_cast<SInt16> (l_temp_s32);
  l_temp_s32 = rbp_mtl_ReducePrecision_s32(l_SinTemp_s32 + l_CosTemp_s32, static_cast<UInt8>(14)); // Input scale
  f_P_rot_pst->Y_s16 = static_cast<SInt16> (l_temp_s32);

  return;
}


/****************************************************************************
F U N C T I O N    D E S C R I P T I O N
-----------------------------------------------------------------------------
Function name  rbp_tpc_MirrorClothoidPoint_vd                              *//**
\brief         Transform clothoid point which has been computed in the first
               quadrant to the quadrant according to the driving direction
         (forward/backward) and the steering direction (left/right).
\details       [detailed description, formula]
*//*-------------------------------------------------------------------------
I N P U T                                                              *//**
\param[in]     f_dk_s16 \f$\Delta \kappa\f$
\param[in]     f_ds_s16 \f$\Delta s\f$
*//*-------------------------------------------------------------------------
O U T P U T                                                             *//**
\param[in,out] f_x_s16
\param[in,out] f_y_s16
\param[in,out] f_Psi_u32
\return        -
\throw         [error values]
*//*-------------------------------------------------------------------------
A U T H O R  I D E N T I T Y                                            *//**
\author        gra1lr
*//*************************************************************************/
static void rbp_tpc_MirrorClothoidPoint_vd(
          SInt16* f_x_s16,
          SInt16* f_y_s16,
          SInt16* f_Psi_s16,
          const SInt16 f_dk_s16,
          const SInt16 f_ds_s16)
{

    if (f_x_s16     == nullptr ||
        f_y_s16     == nullptr ||
        f_Psi_s16   == nullptr ){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_x_s16 || f_y_s16 || f_Psi_s16" << XLOG_ENDL;
        return;
    }

    // drive forward and turn left (first quadrant)
    if ((f_dk_s16 > 0) && (f_ds_s16 > 0))
    {
        // -> original clothoid do nothing
    }
    // drive forward and turn right (fourth quadrant)
    else if ((f_dk_s16 < 0) && (f_ds_s16 > 0))
    {
        // mirror Y
        *f_y_s16 = -(*f_y_s16);
        // mirror yaw angle at X axis:
        // Psi' = -Psi = 2pi - Psi
        *f_Psi_s16 = -*f_Psi_s16;
    }
    // drive backward and turn left (second quadrant)
    else if ((f_dk_s16 > 0) && (f_ds_s16 < 0))
    {
        // mirror X
        *f_x_s16 = -(*f_x_s16);
        // rotate yaw angle by 180 deg and mirror at X axis
        // Psi' = -Psi = 2pi - Psi
        *f_Psi_s16 = -*f_Psi_s16;
    }
    // drive backward and turn right (third quadrant)
    else if ((f_dk_s16 < 0) && (f_ds_s16 < 0))
    {
        // mirror X and Y
        *f_x_s16 = -(*f_x_s16);
        *f_y_s16 = -(*f_y_s16);
        // rotate yaw angle by 180 deg
        // Psi' = Psi + pi; -> Psi does not change!
        // *f_Psi_u32 = rbp_mtl_AngleAdd_u32((*f_Psi_u32),rbp_mtl_PI_du32);
    }
    else
    {
        // stop without steering -> do nothing
        // should never occur as function is not called in such segments
    }

  return;
}


SInt16  rbp_trig_SinCosMul_s16(const SInt16 f_Multiplier_cs16, const SInt16 f_SinCos_cs16)
{
  SInt32 l_retval_s32;

  l_retval_s32 = (static_cast<SInt32>(f_Multiplier_cs16))*(static_cast<SInt32>(f_SinCos_cs16)); // Fxx * F14
  l_retval_s32 = rbp_mtl_ReducePrecision_s32(l_retval_s32, static_cast<UInt8>(14)); // Fxx

  return static_cast<SInt16>(l_retval_s32);
}


SInt16 rbp_geo_DivideLengthByRadius_s16(SInt16 f_Length_s16, SInt16 f_Radius_s16)
{
  SInt32 l_PhiF12_s32 = rbp_MAX_ds16;

  if (f_Radius_s16 != 0)
  {
    // 2^15-1 * 2^12 / 2^0 ~= 2^27 -> overflow impossible
    l_PhiF12_s32 = rbp_mtl_s32_Div_s32_s32(static_cast<SInt32>(f_Length_s16 * 4096), static_cast<SInt32>(f_Radius_s16));//PRQA S 3133     // F12

    // reduce result to the representable range by normalizing it (if f_length_s16 is very large this may happen)
    while (l_PhiF12_s32 >= static_cast<SInt32> rbp_mtl_2_PI_du16)
    {
      l_PhiF12_s32 -= static_cast<SInt32> rbp_mtl_2_PI_du16;
    }
    while (l_PhiF12_s32 <= -static_cast<SInt32> rbp_mtl_2_PI_du16)
    {
      l_PhiF12_s32 += static_cast<SInt32> rbp_mtl_2_PI_du16;
    }
  }

  return static_cast<SInt16> (l_PhiF12_s32);
}


void rbp_vc_PredictPos_vd(
        rbp_Type_LineSinCos_st * f_PredictedPos_pst,
        rbp_Type_LineSinCos_st const * f_StartPos_pst,
        SInt16 f_Radius_s16,
        SInt16 f_DeltaRearF12_s16,
        SInt16 f_DeltaS_s16)
{
  SInt16 l_DeltaX_s16;
  SInt16 l_DeltaY_s16;
  SInt16 l_DeltaPhi_s16;
  rbp_Type_Angle_st PhiPlusDeltaRear_st; // yaw angle + deltaRear

  assert(nullptr != f_StartPos_pst);
  assert(nullptr != f_PredictedPos_pst);
  rbp_geo_Angle_Set_vd(&PhiPlusDeltaRear_st, f_StartPos_pst->Phi_st.Angle_s16 + f_DeltaRearF12_s16);  //PRQA S 3010

  if ((f_Radius_s16 != 0) && (f_DeltaS_s16 != 0)) // avoid division by zero or numerical inaccuracies
  {
    // handling of straight drive or crab steering
    if (static_cast<SInt16>rbp_mtl_Abs_mac(f_Radius_s16) >= rbp_geo_RadiusStraight_ds16)
    {
      l_DeltaX_s16 = rbp_mtl_SinCosMul_s16(f_DeltaS_s16, PhiPlusDeltaRear_st.Cos_s16);
      l_DeltaY_s16 = rbp_mtl_SinCosMul_s16(f_DeltaS_s16, PhiPlusDeltaRear_st.Sin_s16);

      f_PredictedPos_pst->Phi_st = f_StartPos_pst->Phi_st;
    }
    else
    {
      // calculate new pos by turning round on circlecenter. Circle arc function: ds = R * phi
      l_DeltaPhi_s16 = rbp_geo_DivideLengthByRadius_s16(f_DeltaS_s16, f_Radius_s16);

      l_DeltaY_s16 = rbp_mtl_SinCosMul_s16(f_Radius_s16,  PhiPlusDeltaRear_st.Cos_s16);
      l_DeltaX_s16 = rbp_mtl_SinCosMul_s16(-f_Radius_s16, PhiPlusDeltaRear_st.Sin_s16);  //PRQA S 3010
      rbp_geo_Angle_Set_vd(&PhiPlusDeltaRear_st, PhiPlusDeltaRear_st.Angle_s16 + l_DeltaPhi_s16);  //PRQA S 3010
      l_DeltaY_s16 -= rbp_mtl_SinCosMul_s16(f_Radius_s16,  PhiPlusDeltaRear_st.Cos_s16);
      l_DeltaX_s16 -= rbp_mtl_SinCosMul_s16(-f_Radius_s16, PhiPlusDeltaRear_st.Sin_s16);  //PRQA S 3010

      rbp_geo_Angle_Set_vd(&f_PredictedPos_pst->Phi_st, f_StartPos_pst->Phi_st.Angle_s16 + l_DeltaPhi_s16);  //PRQA S 3010
    }
    f_PredictedPos_pst->P_st.X_s16 = f_StartPos_pst->P_st.X_s16 + l_DeltaX_s16;
    f_PredictedPos_pst->P_st.Y_s16 = f_StartPos_pst->P_st.Y_s16 + l_DeltaY_s16;
  }
  else
  {
    // copy over source position. StartPos and predictedPos are not the same, s predictedPos is pointer to a usually just
    // instantiated Line
    *f_PredictedPos_pst = *f_StartPos_pst;
  }
}


void rbp_tpc_InterpolatedPositionWithinClothoid_vd(
          rbp_Type_LineSinCos_st* f_VehPos_pst,
          const rbp_Type_PathPoint_st* f_PrevPathPoint_pst,
          const rbp_Type_PathPoint_st* f_NextPathPoint_pst,
          const SInt16 f_S_s16)
{
    if (f_PrevPathPoint_pst==nullptr || f_NextPathPoint_pst==nullptr || f_VehPos_pst==nullptr){
        XLOG_WARN_OS(g_AppContext) << "!!!!!!!f_PrevPathPoint_pst || f_NextPathPoint_pst || f_VehPos_pst" << XLOG_ENDL;
        return;
    }

    // locally used results
    rbp_Type_Point_st l_PClo_st; // position on clothoid
    SInt16            psi_s16;

  // locally used variables
  SInt16 l_dsF10_s16;
  SInt16 l_dk_s16;
  SInt16 l_RF10_s16;
  SInt16 l_L1F10_s16;
  SInt16 l_L2F10_s16;
  SInt16 l_temp_s16;

  // variables used in interface function
  UInt16 l_LF10_u16;
  UInt16 l_AF10_u16;
  Boolean l_UseStraightLineOrCircleInterpolation_b = false;

  rbp_Type_PathPoint_st const * PathPointK1_pst;
  rbp_Type_PathPoint_st const * PathPointK2_pst;

  // kappas of opposite signs are not supported
  if(((f_PrevPathPoint_pst->Kappa_s16 >= 0) && (f_NextPathPoint_pst->Kappa_s16 >= 0)) ||
                 ((f_PrevPathPoint_pst->Kappa_s16 <= 0) && (f_NextPathPoint_pst->Kappa_s16 <= 0)))
  {

  }
  else
  {
  XLOG_WARN_OS(g_AppContext) << "!!!!!!!Kappa side is not support"<<XLOG_ENDL;  //PRQA S 4060
  }

  if (rbp_mtl_Abs_mac(f_PrevPathPoint_pst->Kappa_s16) < rbp_mtl_Abs_mac(f_NextPathPoint_pst->Kappa_s16))
  {
    PathPointK1_pst = f_PrevPathPoint_pst;
    PathPointK2_pst = f_NextPathPoint_pst;
  }
  else
  {
    // next path point has smaller curvature -> turn around the path here (does not hurt, but kappa
    // needs to be in ascending order for calculation of the egg clothoid
    PathPointK1_pst = f_NextPathPoint_pst;
    PathPointK2_pst = f_PrevPathPoint_pst;
  }

  // length between path points
  l_dsF10_s16 = PathPointK2_pst->S_s16 - PathPointK1_pst->S_s16;

  // change of curvature between path points
  l_dk_s16 = PathPointK2_pst->Kappa_s16 - PathPointK1_pst->Kappa_s16;

  if(l_dk_s16 == static_cast<SInt16>(0))
  {
    l_UseStraightLineOrCircleInterpolation_b = true;
  }

  // (curvature and) traveled distance must change in the segment
  if (l_dsF10_s16 == static_cast<SInt16>(0))
  {
    // segment is not a clothoid error! - do not modify f_VehPos_pst
    // rbp_xpgErrHdl_vd(APGErr_OutOfRange_Input_S_enm);
  }
  else
  {
    rbp_Type_Line_st l_CloEnd_st;   // F10
    rbp_Type_Angle_st l_VehPosAngle_st;
    rbp_Type_Line_st l_CloStart_st; // F10
    (void)memset(&l_CloStart_st, 0, sizeof(rbp_Type_Line_st)); // technically not needed but the compiler will ..
    (void)memset(&l_CloEnd_st, 0, sizeof(rbp_Type_Line_st)); //  .. yield a uninitialized variable warning otherwise

    // position of points with respect to clothoid -> L1, L2
    if (PathPointK1_pst->Kappa_s16 == static_cast<SInt16>(0))
    {
      l_L1F10_s16 = static_cast<SInt16>(0);
      l_L2F10_s16 = l_dsF10_s16;
    }
    else
    {
      SInt32 l_temp_L1F10_s32;
      SInt32 l_k1F15_s32;
      SInt32 l_k2F31_s32;
      SInt32 l_dsF26_s32;
      SInt32 l_temp_s32;

      // scale up
      l_k1F15_s32 = static_cast<SInt32>(PathPointK1_pst->Kappa_s16);
      l_k2F31_s32 = static_cast<SInt32>(PathPointK2_pst->Kappa_s16) * static_cast<SInt32> (1 << 16);
      l_dsF26_s32 = static_cast<SInt32>(l_dsF10_s16) * static_cast<SInt32> (1 << 16);

      // compute denominator (k2/k1 - 1)
      // division: 2^31/2^15 -> result has scale 2^16
      l_temp_s32 = rbp_mtl_s32_Div_s32_s32(l_k2F31_s32,l_k1F15_s32) - static_cast<SInt32> (1 << 16);

      // divide 2^26/2^16 -> result has scale 2^10
      l_temp_L1F10_s32 = rbp_mtl_s32_Div_s32_s32(l_dsF26_s32, l_temp_s32);

      if(rbp_mtl_Abs_mac(l_temp_L1F10_s32) > rbp_MAX_ds16)
      {
        l_UseStraightLineOrCircleInterpolation_b = true;
      }

      l_L1F10_s16 = static_cast<SInt16>(l_temp_L1F10_s32);
      l_L2F10_s16 = l_L1F10_s16 + l_dsF10_s16;
    }

    // length in clothoid coordinates
    l_temp_s16 = static_cast<SInt16>(f_S_s16 - PathPointK1_pst->S_s16) + l_L1F10_s16;
    l_LF10_u16 = static_cast<UInt16>rbp_mtl_Abs_mac(l_temp_s16);  //PRQA S 3135

    // radius of circle at PathPointK2_pst
    l_RF10_s16 = rbp_vc_Kappa2RadiusF10_s16(PathPointK2_pst->Kappa_s16);

    // Overflow protection nprod00207366
    if(l_RF10_s16 == rbp_geo_RadiusStraight_ds16)
    {
      l_UseStraightLineOrCircleInterpolation_b = true;
    }

    // compute clothoid parameter according to
    //
    // A^2 = R * L = L/kappa
    // A = \sqrt{L * R}
    //
    l_AF10_u16 = rbp_mtl_Sqrt_u32_u16(static_cast<UInt32>rbp_mtl_Abs_mac(l_L2F10_s16) * static_cast<UInt32>rbp_mtl_Abs_mac(l_RF10_s16));  //PRQA S 3135  //PRQA S 3133

    // Overflow protection nprod00207366
    if(l_AF10_u16 > rbp_tpcMaxClothoidParamAF10_du16)
    {
      l_UseStraightLineOrCircleInterpolation_b = true;
    }

    if (l_UseStraightLineOrCircleInterpolation_b == false)
    {
      // first call to  rbp_apgCalcClothoidEnd_en: compute end   point of clothoid
      Boolean l_EndOk_b = (rbp_apgCalcClothoidEnd_en(l_LF10_u16, l_AF10_u16, &l_CloEnd_st) == rbp_apgClothoidOk_enm);
      // second call to rbp_apgCalcClothoidEnd_en: compute start point of clothoid in case of egg type clothoid
      Boolean l_StartOk_b = true;
      if ((l_EndOk_b) && (PathPointK1_pst->Kappa_s16 != 0))
      {
        l_StartOk_b = (rbp_apgCalcClothoidEnd_en(static_cast<UInt16>rbp_mtl_Abs_mac(l_L1F10_s16), l_AF10_u16, &l_CloStart_st) == rbp_apgClothoidOk_enm); //PRQA S 3135
      }
      if ((!l_EndOk_b) || (!l_StartOk_b))
      {
        l_UseStraightLineOrCircleInterpolation_b = true;
      }
    }
    if (l_UseStraightLineOrCircleInterpolation_b == false)
    {
      // transform clothoid end point in case of egg type clothoid
      if (PathPointK1_pst->Kappa_s16 != 0)
      {
        rbp_Type_Angle_st l_CloStartAngle_st;
        l_CloEnd_st.P_st.X_s16 = l_CloEnd_st.P_st.X_s16 - l_CloStart_st.P_st.X_s16;
        l_CloEnd_st.P_st.Y_s16 = l_CloEnd_st.P_st.Y_s16 - l_CloStart_st.P_st.Y_s16;

        // transform end point into the coordinate system of start point
        // rotate end point
        rbp_geo_Angle_Set_vd(&l_CloStartAngle_st, -l_CloStart_st.Phi_s16);  //PRQA S 3010
        rbp_geo_Rotate_vd(&l_CloEnd_st.P_st, &l_CloStartAngle_st, &l_CloEnd_st.P_st);
        // update angle
        l_CloEnd_st.Phi_s16 -= l_CloStart_st.Phi_s16;
      }

      // mirror clothoid point coordinate(s) into correct quadrant
      rbp_tpc_MirrorClothoidPoint_vd(&l_CloEnd_st.P_st.X_s16, &l_CloEnd_st.P_st.Y_s16, &l_CloEnd_st.Phi_s16,
          l_dk_s16, l_dsF10_s16);

      // rotate by PathPointK1_pst->Psi_u32
      rbp_geo_Angle_Set_vd(&l_VehPosAngle_st, PathPointK1_pst->Pos_st.Phi_s16);
      rbp_geo_Rotate_vd(&l_CloEnd_st.P_st, &l_VehPosAngle_st, &l_PClo_st);

      psi_s16 = PathPointK1_pst->Pos_st.Phi_s16 + l_CloEnd_st.Phi_s16;

      // translate to vehicle position f_VehPos_pst->P_st
      f_VehPos_pst->P_st.X_s16 = l_PClo_st.X_s16 + PathPointK1_pst->Pos_st.P_st.X_s16;
      f_VehPos_pst->P_st.Y_s16 = l_PClo_st.Y_s16 + PathPointK1_pst->Pos_st.P_st.Y_s16;

      // assign yaw angle \Psi and compute sin and cos value
      rbp_geo_Angle_Set_vd(&f_VehPos_pst->Phi_st, psi_s16);
    }
    else // straight line or circle interpolation
    {
        rbp_Type_LineSinCos_st l_PathPointVehPos_st;

            // works for kappa = 0 and kappa != 0
            const SInt16 l_radius_s16 = rbp_vc_Kappa2RadiusF10_s16(
                (f_PrevPathPoint_pst->Kappa_s16 + f_NextPathPoint_pst->Kappa_s16) / 2);

        // create necessary format for function rbp_xpgPredictPos_st
        rbp_tpc_PathPoint2LineSinCos_vd(&l_PathPointVehPos_st, f_PrevPathPoint_pst);

        // definition of radius compatible with sign of kappa
        rbp_vc_PredictPos_vd(f_VehPos_pst, &l_PathPointVehPos_st, l_radius_s16, 0, f_S_s16 - f_PrevPathPoint_pst->S_s16);  //PRQA S 3010
    }
  }
}


void rbp_tpc_InterpolatedPosition_vd(
          rbp_Type_LineSinCos_st * f_VehPos_pst,
          const rbp_Type_PathPoint_st* f_PrevPathPoint_pst,
          const rbp_Type_PathPoint_st* f_NextPathPoint_pst,
          const SInt16 f_S_s16)
{
    if (f_PrevPathPoint_pst==nullptr || f_NextPathPoint_pst==nullptr){
        return;
    }
    //
    // steering while car stops
    //
    if (f_PrevPathPoint_pst->S_s16 == f_NextPathPoint_pst->S_s16)
    {
        // return position of next path point
        rbp_tpc_PathPoint2LineSinCos_vd(f_VehPos_pst, f_NextPathPoint_pst);
    }
    //
    // circle or straight segment
    //
    else if (f_PrevPathPoint_pst->Kappa_s16 == f_NextPathPoint_pst->Kappa_s16)
    {
        rbp_Type_LineSinCos_st   l_PathPointVehPos_st;
        rbp_Type_LineSinCosFl_st l_PathPointVehPosFl_st;
        rbp_Type_LineSinCosFl_st l_PredictedPosFl_st;

    // create necessary format for function rbp_xpgPredictPos_st
    rbp_tpc_PathPoint2LineSinCos_vd(&l_PathPointVehPos_st, f_PrevPathPoint_pst);

    rbp_tpc_ConvertPosFix2FL_vd(&l_PathPointVehPosFl_st, &l_PathPointVehPos_st, rbp_F10_du16);

    rbp_vc_PredictPos_Fl_vd(
      &l_PredictedPosFl_st,
      &l_PathPointVehPosFl_st,
      rbp_F15to1_f32(f_PrevPathPoint_pst->Kappa_s16),
      0.f, // 2WS
      rbp_F10toM_f32(f_S_s16 - f_PrevPathPoint_pst->S_s16));

    rbp_tpc_ConvertPosFL2Fix_vd(f_VehPos_pst,&l_PredictedPosFl_st, rbp_F10_du16);
  }
  //
  // clothoid segment
  //
  else
  {
    rbp_tpc_InterpolatedPositionWithinClothoid_vd(f_VehPos_pst, f_PrevPathPoint_pst, f_NextPathPoint_pst, f_S_s16);
  }
  return;
}


// resampleInterpolate(pathPoints, interpolateVec, 48, 9 or 13);
void resampleInterpolate(const cc::target::common::CAPGStripped& f_pathPoint, std::vector<cc::target::common::StrippedVehicleCoorResetVhm_st>& f_vehInterpolatedVec, const vfc::uint32_t f_numOfVertices, const vfc::uint32_t f_pathPointSize, bool f_isBackward)
{
  f_vehInterpolatedVec.clear();
    const vfc::uint32_t pathPointSize   = f_pathPointSize;
    const vfc::uint32_t numOfSegments   = f_numOfVertices - 1u;
    vfc::uint32_t l_numOfVertices = f_numOfVertices;

  vfc::int32_t extendedDistance = (f_isBackward) ?
    static_cast<vfc::int32_t>((-1.0f)*rbp_MtoF10_f32(std::abs(g_mechanicalData->m_axleToBumperDistanceRear))) : //PRQA S 3132  //PRQA S 3016
    static_cast<vfc::int32_t>(rbp_MtoF10_f32(std::abs(g_mechanicalData->m_wheelbase + g_mechanicalData->m_axleToBumperDistanceFront))); //PRQA S 3132  //PRQA S 3016

  vfc::int32_t totalDistance = f_pathPoint.m_Points_pst[pathPointSize-1].S_s16 - f_pathPoint.m_Points_pst[0u].S_s16 + extendedDistance;

  vfc::float32_t segmentLength = static_cast<vfc::float32_t>(totalDistance) / static_cast<vfc::float32_t>(numOfSegments);
  if ((std::abs(segmentLength) < rbp_MtoF10_f32(g_drivablePathSettings->m_thresholdDistance)))  //PRQA S 3132 // add safety ratio
  {
    segmentLength = (segmentLength/std::abs(segmentLength))*rbp_MtoF10_f32(g_drivablePathSettings->m_thresholdDistance);  //PRQA S 3132
    l_numOfVertices = static_cast<vfc::uint32_t>(std::abs(static_cast<vfc::float32_t>(totalDistance)/segmentLength));  //PRQA S 3016
  }

  std::array<vfc::int32_t, (RBP_CC_TPC_MAX_NUM_PATHPOINTS)> l_reDist_arr;         // RBP_CC_TPC_MAX_NUM_PATHPOINTS-1 + 1 for extended line
  for (vfc::uint32_t i = 0u; i < pathPointSize; i++)
  {
    l_reDist_arr[i] = std::abs(f_pathPoint.m_Points_pst[i].S_s16 - f_pathPoint.m_Points_pst[0].S_s16);
  }

  vfc::uint32_t APAverticesCount = 0u;
  osg::Vec2f   offsetVec = osg::Vec2(0.f,0.f);
  cc::target::common::StrippedVehicleCoorResetVhm_st l_vehPos = {};

  for (vfc::uint32_t i = 0u; i < l_numOfVertices; i++)
  {
        const vfc::int32_t currentVertexDistance = static_cast<vfc::int32_t>(
                                         static_cast<vfc::float32_t>(f_pathPoint.m_Points_pst[0u].S_s16) +
                                         static_cast<vfc::float32_t>(i) * segmentLength); //PRQA S 3132

        const vfc::int32_t  currentReDist           = static_cast<vfc::int32_t>(
            static_cast<vfc::float32_t>(i) * std::abs(segmentLength));
        vfc::uint32_t currentPathPointSegment = 0u;
        for (vfc::uint32_t j = 1u; j <= pathPointSize - 1; j++) // check lower and upper path point for current segment
        {
            const vfc::int32_t l_currentLenght = l_reDist_arr[j] - l_reDist_arr[j - 1u];

      if ((currentReDist >= l_reDist_arr[j-1u]) && (currentReDist <= l_reDist_arr[j]) && (l_currentLenght > 0))
      {
        currentPathPointSegment = j;
        break;
      }
    }

    if (currentPathPointSegment > 0)
    {
      // the APA segment has been found
      rbp_Type_LineSinCos_st interpolatedPoint;
      rbp_tpc_InterpolatedPosition_vd(
        &interpolatedPoint,
        reinterpret_cast<const rbp_Type_PathPoint_st*>(&f_pathPoint.m_Points_pst[currentPathPointSegment - 1u]),  //PRQA S 3030
        reinterpret_cast<const rbp_Type_PathPoint_st*>(&f_pathPoint.m_Points_pst[currentPathPointSegment]),  //PRQA S 3030
        static_cast<SInt16>(currentVertexDistance));

      l_vehPos = cc::target::common::StrippedVehicleCoorResetVhm_st {
      rbp_F10toM_f32(static_cast<vfc::float32_t>(interpolatedPoint.P_st.X_s16)),
      rbp_F10toM_f32(static_cast<vfc::float32_t>(interpolatedPoint.P_st.Y_s16)),
      rbp_F12to1_f32(static_cast<vfc::float32_t>(interpolatedPoint.Phi_st.Angle_s16))};

      f_vehInterpolatedVec.push_back(l_vehPos);
      APAverticesCount ++;
    }
    else
    {
      if ((offsetVec.length() <= 0.f) && (f_vehInterpolatedVec.size() > 0))
      {
        offsetVec = osg::Vec2f(f_vehInterpolatedVec[APAverticesCount-1u].m_VehicleCoor_X
                                - f_vehInterpolatedVec[APAverticesCount-2u].m_VehicleCoor_X,
                              f_vehInterpolatedVec[APAverticesCount-1u].m_VehicleCoor_Y
                                - f_vehInterpolatedVec[APAverticesCount-2u].m_VehicleCoor_Y);
      }

      if (offsetVec.length() > 0)
      {
        l_vehPos.m_VehicleCoor_X += offsetVec.x();
        l_vehPos.m_VehicleCoor_Y += offsetVec.y();
        f_vehInterpolatedVec.push_back(l_vehPos);
      }
    }

  }
}

} // namespace drivablepath
} // namespace assets
} // namespace cc
