//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BJEV AVAP
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  TileOverlay.cpp
/// @brief
//=============================================================================

#include "cc/assets/tileoverlay/inc/TileOverlay.h"
#include "cc/assets/tileoverlay/inc/TileSettings.h"
#include "cc/assets/tileoverlay/inc/TileSpline.h"
#include "cc/assets/tileoverlay/inc/TileTask.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/worker/core/inc/CustomTaskManager.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/worker/fusion/inc/FusionTask.h"


#include <cassert>
#include "osg/Depth"
#include "osg/Geometry"

#include "osg/LineWidth"
#include "osg/MatrixTransform"
#include "osg/Math"
#include "osgAnimation/EaseMotion"
#include "osgUtil/CullVisitor"


namespace cc
{
namespace assets
{
namespace tileoverlay
{

TileSectorData::TileSectorData()
  : m_leftBorderRefPoint   (0.0f, 0.0f)
  , m_leftBorderRefPointEnd(0.0f, 0.0f)
  , m_leftBorderDir        (0.0f, 0.0f)
  , m_currentDistance      (10.0f)
  , m_currentDistanceForPosDisp(0.0f)
  , m_previousDistance     (0.0f)
  , m_ObjMovingSts         (OBJ_MOVING_UNKNOW)
{
}

// TileSectorData::~TileSectorData()
// {
// }

pc::util::coding::Item<TileSettings> g_tileSettings("TileOverlay");

HeightInterpolator g_heightInterpolator;

void updateInterpolator()
{
  static vfc::uint32_t s_sequenceNumber = ~0u;
  if (g_tileSettings->getModifiedCount() != s_sequenceNumber)
  {
    s_sequenceNumber = g_tileSettings->getModifiedCount();
    g_heightInterpolator.clear();
    g_heightInterpolator.addSample(g_tileSettings->m_projectionTransitionCamAngle.x(), 1.0f);
    g_heightInterpolator.addSample(g_tileSettings->m_projectionTransitionCamAngle.y(), 0.0f);
    g_heightInterpolator.init();
  }
}

//!
//! VisibilityUpdateVisitor
//!
class VisibilityUpdateVisitor : public osg::NodeVisitor
{
public:

  VisibilityUpdateVisitor(bool f_flankAvailable, bool f_rearAvailable)
    : osg::NodeVisitor(osg::NodeVisitor::TRAVERSE_ALL_CHILDREN)
    , m_xMin(pc::vehicle::g_mechanicalData->getBumperCenterXRear() - g_tileSettings->m_extractorOffset)
    , m_xMax(pc::vehicle::g_mechanicalData->getBumperCenterXFront() + g_tileSettings->m_extractorOffset)
    , m_rearAvailable(f_rearAvailable)
    , m_flankAvailable(f_flankAvailable)
  {
  }

  void apply(osg::Geode& f_geode) override    // PRQA S 2120
  {
    const vfc::uint32_t l_numDrawables = f_geode.getNumDrawables();
    for (vfc::uint32_t i = 0u; i < l_numDrawables; ++i)
    {
      osg::Geometry* const l_geometry =f_geode.getDrawable(i)->asGeometry();
      if (l_geometry != nullptr)
      {
        apply(l_geometry);
      }
    }
  }

  void apply(osg::Geometry* f_geometry)
  {
    osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (f_geometry->getVertexArray());
    osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (f_geometry->getColorArray());
    const std::size_t l_numVertices = l_vertices->size();
    for (vfc::uint32_t i = 0u; i < l_numVertices; ++i)
    {
      const osg::Vec3f& l_vertex = (*l_vertices)[i];
      if (!m_flankAvailable)
      {
        if ((m_xMin < l_vertex.x()) && (m_xMax > l_vertex.x()))
        {
          (*l_colors)[i].a() = 0.0f;
        }
      }
      if (!m_rearAvailable)
      {
        if (m_xMin > l_vertex.x())
        {
          (*l_colors)[i].a() = 0.0f;
        }
      }
    }
    l_colors->dirty();
  }

private:

  const vfc::float32_t m_xMin;
  const vfc::float32_t m_xMax;
  bool m_rearAvailable;
  bool m_flankAvailable;
};


//!
//! TileOverlayComposite
//!
TileOverlayComposite::TileOverlayComposite(pc::core::Framework* f_framework, bool f_isPerspective)
  : m_framework(f_framework)
  , m_idxShadow(~0u)
  , m_idxSpline2D(~0u)
  , m_idxShield3D(~0u)
  , m_renderBinOrder(core::RENDERBIN_ORDER_OVERLAYS)
{
  setMatrix(osg::Matrix::translate(0.0f, 0.0f, g_tileSettings->m_heightOverGround));
  //! shield 3D
  osg::MatrixTransform* const l_transform3D = new osg::MatrixTransform;
  if(f_isPerspective)
  {
    //!Side squares with hairlines
    TileSideSquare3D* const l_square3D = new TileSideSquare3D;
    l_transform3D->addChild(l_square3D);

    //! shield single face with hairlines
    TileShield3DInner* const l_shield3DInner = new TileShield3DInner;
    l_transform3D->addChild(l_shield3DInner);

    TileShield3DOuter* const l_shield3DOuter = new TileShield3DOuter;
    l_transform3D->addChild(l_shield3DOuter);

    //! shield single face
    TileCoverTopBottom2D* const l_shieldCoverTop = new TileCoverTopBottom2D;
    l_shieldCoverTop->setOffset(g_tileSettings->m_shieldHeight);
    l_transform3D->addChild(l_shieldCoverTop);

    TileCoverTopBottom2D* const l_shieldCoverBottom = new TileCoverTopBottom2D;
    l_shieldCoverBottom->setOffset(0.0f);
    l_transform3D->addChild(l_shieldCoverBottom);  // PRQA S 3803

    m_idxShield3D = getNumChildren();
    osg::Group::addChild(l_transform3D);  // PRQA S 3803

    osg::StateSet* const l_commonStateSet = getOrCreateStateSet();
    l_commonStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
    l_commonStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_commonStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");

    //! disable depth buffer write
    osg::Depth* const l_depth = new osg::Depth;
    l_depth->setWriteMask(false);
    l_commonStateSet->setAttributeAndModes(l_depth, osg::StateAttribute::ON);
    l_commonStateSet->setAttribute(new osg::LineWidth(g_tileSettings->m_hairlineWidth));
  }
  else
  {
    Tile2DTopview* const l_tile2DTopview = new Tile2DTopview;
    l_transform3D->addChild(l_tile2DTopview);

    m_idxShield3D = getNumChildren();
    osg::Group::addChild(l_transform3D);  // PRQA S 3803

    osg::StateSet* const l_commonStateSet = getOrCreateStateSet();
    l_commonStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
    l_commonStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
    l_commonStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_commonStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");
    l_commonStateSet->setAttribute(new osg::LineWidth(g_tileSettings->m_hairlineWidth));
  }
}


TileOverlayComposite::TileOverlayComposite(const TileOverlayComposite& f_other, const osg::CopyOp& f_copyOp)
  : osg::MatrixTransform(f_other, f_copyOp)
  , m_framework(f_other.m_framework)
  , m_idxShadow(f_other.m_idxShadow)
  , m_idxSpline2D(f_other.m_idxSpline2D)
  , m_idxShield3D(f_other.m_idxShield3D)
  , m_renderBinOrder(core::RENDERBIN_ORDER_OVERLAYS)
{
}


TileOverlayComposite::~TileOverlayComposite() = default;


void TileOverlayComposite::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
  {
    // assert(nullptr != m_framework);
    // //! check if TILE overlay shall be displayed
    // cc::core::CustomFramework* l_framework = m_framework->asCustomFramework();
    // const cc::daddy::SVSOverlayDistStsDaddy_t* l_overlayDistState = l_framework->m_overlayDistStsDaddy_ReceiverPort.getData();
    // if (0 != l_overlayDistState)
    // {
    //   if (1u != l_overlayDistState->m_Data)
    //   {
    //     //! do not render if TILE in OFF state
    //     return;
    //   }
    // }
#if DISABLE_ROTATABLE_TRANSPARENT_MODEL
#else
    cc::core::CustomFramework* const l_framework = m_framework->asCustomFramework();
    if (nullptr != l_framework->m_VehTransparenceStsInternalReceiver.getData())
    {
      if (l_framework->m_VehTransparenceStsInternalReceiver.getData()->m_Data)
      {
        if (m_renderBinOrder != (core::RENDERBIN_ORDER_CAR_OPAQUE-1))
        {
          getOrCreateStateSet()->setRenderBinDetails(core::RENDERBIN_ORDER_CAR_OPAQUE-1, "RenderBin");
          m_renderBinOrder = core::RENDERBIN_ORDER_SPLINEOVERLAY-1;
        }

      }
      else
      {
        if (m_renderBinOrder != core::RENDERBIN_ORDER_OVERLAYS)
        {
          getOrCreateStateSet()->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");
          m_renderBinOrder = core::RENDERBIN_ORDER_OVERLAYS;
        }
      }
    }
#endif

    updateInterpolator();
    osgUtil::CullVisitor* const l_cv = static_cast<osgUtil::CullVisitor*> (&f_nv);
    osg::Vec3f const l_center(pc::vehicle::g_mechanicalData->getCenter(), 0.0f);
    osg::Camera* const l_cam = l_cv->getCurrentCamera();
    const osg::Vec3f l_eye = l_cam->getInverseViewMatrix().getTrans();
    osg::Vec3f l_ev = l_eye - l_center;
    l_ev.normalize();
    const vfc::float32_t l_camAngle = osg::RadiansToDegrees(static_cast<vfc::float32_t>(osg::PI_2) - std::acos(l_ev * osg::Z_AXIS));
    const vfc::float32_t l_height = g_heightInterpolator.getValue(l_camAngle);

    osg::MatrixTransform* const l_upperTransform = static_cast<osg::MatrixTransform*> (getChild(m_idxShield3D));
    l_upperTransform->setMatrix(osg::Matrix::scale(1.0f, 1.0f, l_height));
    l_upperTransform->accept(f_nv);
  }
  else
  {
    osg::MatrixTransform::traverse(f_nv);
  }
}

//!
//! TileOverlay
//!
TileOverlay::TileOverlay(pc::core::Framework* f_framework, bool f_isVehOffset, bool f_isPerspective)
{
  setName("TileOverlay");
  osg::CopyOp::CopyFlags const l_excludeMask =
    osg::CopyOp::DEEP_COPY_STATESETS |
    osg::CopyOp::DEEP_COPY_STATEATTRIBUTES |
    osg::CopyOp::DEEP_COPY_TEXTURES |
    osg::CopyOp::DEEP_COPY_IMAGES | // PRQA S 3143
    osg::CopyOp::DEEP_COPY_UNIFORMS;
  if (f_isPerspective)
  {
    setPrototype(new TileOverlayComposite(f_framework, true), static_cast<vfc::uint32_t>(osg::CopyOp::DEEP_COPY_ALL) & ~l_excludeMask); // PRQA S 3000
  }
  else
  {
    setPrototype(new TileOverlayComposite(f_framework, false), static_cast<vfc::uint32_t>(osg::CopyOp::DEEP_COPY_ALL) & ~l_excludeMask); // PRQA S 3000
  }


  ProcessingTask* const l_processingTask = new ProcessingTask(this, f_isVehOffset);
  pc::worker::core::enqueueEvent(new pc::worker::core::AddTaskEvent(l_processingTask));
}


TileOverlay::~TileOverlay() = default;

bool convergeDistance(vfc::float32_t& f_currentDistance, vfc::float32_t f_targetDistance)
{
  const vfc::float32_t l_distanceDelta = std::abs(f_targetDistance - f_currentDistance);
  if (l_distanceDelta < 0.005f)
  {
    //! converged
    return true;
  }
  else if(l_distanceDelta > 0.5f)
  {
    f_currentDistance = f_targetDistance;
  }
  else if (f_currentDistance < f_targetDistance)
  {
    f_currentDistance += (l_distanceDelta / 8.0f);
  }
  else
  {
    f_currentDistance -= (l_distanceDelta / 8.0f);
  }
  return false;
}



} // namespace tileoverlay
} // namespace assets
} // namespace cc
