#include "cc/assets/common/inc/StateSetUpdater.h"

#include "pc/svs/util/math/inc/FloatComp.h"

#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"













using pc::util::logging::g_AppContext;
using pc::util::logging::g_EngineContext;

namespace cc
{
namespace assets
{
namespace common
{

    static const std::string m_VertexShaderForPBR =
    "#version 300 es \n"
    " \n"
    "out vec3 vPos; \n"
    "out vec3 vNormal; \n"
    "//out vec2 vTexCoord; \n"
    " \n"
    "void main() \n"
    "{ \n"
    "    vPos = vec3(gl_ModelViewMatrix*gl_Vertex); \n"
    "    vNormal = gl_NormalMatrix*gl_Normal; \n"
    "    gl_Position = gl_ModelViewProjectionMatrix*gl_Vertex; \n"
    "    //vTexCoord = gl_MultiTexCoord0.xy; \n"
    "} \n";

        // "uniform vec4 specColor1;"
        // "uniform float specShininess1;"
        // "uniform vec4 specColor2;"
        // "uniform float specShininess2;"
        // "uniform float reflectionPower;"
        // "uniform float fresnel;"
        // "uniform vec4 diffuseColor;"
        // "uniform float modelAlpha;"
        // "uniform float brightness;"
        //"	const vec3 lightPos = vec3(0.0, 1.0, 1.0);"

        // "uniform vec3 diffuseColor; \n"     "uniform vec4 diffuseColor; \n"
        // "uniform vec3 lightColor; \n"       "uniform vec4 specColor1; \n"
        // "uniform vec3 lightPosition; \n"    "uniform vec3 lightPos; \n"
        // "uniform float metallic; \n"        "uniform float specShininess1; \n"
        // "uniform float roughness; \n"       "uniform float specShininess2; \n"
        // "uniform float lightPert; \n"       "uniform float brightness; \n"
        // "uniform float envPert; \n"         "uniform float fresnel; \n"
        // "uniform float rf; \n"              "uniform float reflectionPower; \n"

    static const std::string m_FragmentShaderForPBR =
        "#version 300 es \n"
        "precision highp float; \n"
        " \n"
        "in vec3 vPos; \n"
        "in vec3 vNormal; \n"
        "//in vec2 vTexCoord; \n"
        " \n"
        "uniform vec4 diffuseColor; \n"
        "uniform vec4 specColor1; \n"
        "uniform vec3 lightPos; \n"
        "uniform float specShininess1; \n"
        "uniform float specShininess2; \n"
        "uniform float brightness; \n"
        "uniform float fresnel; \n"
        "uniform float reflectionPower; \n"
        "uniform float modelAlpha; \n"
        "uniform float modelDefaultTransparency; \n"
        "uniform float specialColorIndexRendering; \n"
        " \n"
        "uniform samplerCube diffuseMap; \n"
        "uniform samplerCube diffuse2Map; \n"
        "uniform sampler2D lutMap; \n"
        " \n"
        "out vec4 FragColor; \n"
        " \n"
        "vec3 calculatefresnel(float cos, vec3 F, float ditheringValue) \n"
        "{ \n"
        "    return F + (max(vec3(1.0 - ditheringValue), F) - F) * pow(clamp(1.0 - cos, 0.0, 1.0), 5.0); \n"
        "} \n"
        " \n"
        "void main() \n"
        "{ \n"
        "vec3 m_diffuseColor = diffuseColor.rgb; \n"
        "vec3 lightColor = specColor1.rgb; \n"
        "vec3 lightPosition = lightPos; \n"
        "float metallic = specShininess1*0.001; \n"
        "float roughness = specShininess2*0.001; \n"
        "float lightPert = brightness; \n"
        "float envPert = fresnel; \n"
        "float rf = reflectionPower; \n"
        "if(specialColorIndexRendering == 1.0) \n"
        "{\n"
        "    if(diffuseColor.a == 0.2) \n"
        "    { \n"
        "       m_diffuseColor = vec3(0.03); \n"
        "       lightColor = vec3(0.3); \n"
        "       lightPosition = vec3(0.0, 10.0, 10.0); \n"
        "       metallic = 0.4; \n"
        "       roughness = 0.4; \n"
        "       lightPert = 0.3; \n"
        "       envPert = 0.5; \n"
        "       rf = 1.0; \n"
        "    } \n"
        "} \n"
        "    vec3 N = normalize(vNormal); \n"
        "    vec3 V = normalize(vec3(0.0) - vPos); \n"
        "    vec3 L = normalize(lightPosition - vPos); \n"
        "    vec3 H = normalize(V + L); \n"
        "    float NdotL = max(dot(N, L), 0.0); \n"
        "    float NdotV = dot(N, V); \n"
        "    vec3 F = mix(vec3(0.04), m_diffuseColor, metallic); \n"
        " \n"
        "    vec3 F1 = calculatefresnel(max(dot(H, V), 0.0), F, 0.0); \n"
        "    vec3 diffuseColorFactor1 = (1.0 - metallic)*(vec3(1.0) - F1); \n"
        "    vec3 finalLightColor = (diffuseColorFactor1*m_diffuseColor*0.3183)*lightColor*NdotL; \n"
        " \n"
        "    vec3 F2 = calculatefresnel(max(NdotV, 0.0), F, roughness); \n"
        "    vec3 diffuseColorFactor2 = (1.0 - metallic)*(vec3(1.0) - F2); \n"
        "    vec3 irradianceFactor = texture(diffuseMap, N).rgb; \n"
        "    vec3 diffuseColorReflect = irradianceFactor*m_diffuseColor; \n"
        "    vec3 R = reflect(-V, N); \n"
        "    vec3 prefilteredColor = texture(diffuse2Map, R).rgb; \n"
        "    vec2 brdf = texture(lutMap, vec2( max(NdotV, roughness)) ).rg; \n"
        "    vec3 specularColor = prefilteredColor * (F2*brdf.x + brdf.y); \n"
        "    vec3 finalReflectColor = (diffuseColorFactor2*diffuseColorReflect + specularColor); \n"
        " \n"
        "    vec3 color = finalLightColor*lightPert + finalReflectColor*envPert; \n"
        " \n"
        "    color = vec3(1.0) - exp(-color * rf); \n"
        "    color = pow(color, vec3(1.0/2.2)); \n"
        " \n"
        "  // vec3 specLightDir = normalize(vec3(0.0, 10.0, 10.0) - vPos);\n"
        "  // vec3 sepcHalf = normalize(specLightDir+V);\n"
        "  // vec3 spec = vec3(0.4) * pow(max(dot(N,sepcHalf),0.0), 60.0); \n"
        "  // spec = clamp(spec, 0.0, 1.0); \n"
        "  // color = color + spec; \n"
        "  // just add some spec color, not pbr \n"
        "  //vec3 spec = vec3(0.4) * pow(max(dot(N,H),0.0), 40.0); \n"
        "  vec3 spec = vec3(0.4) * pow(max(dot(N,H),0.0), 30.0); \n"
        "  spec = clamp(spec, 0.0, 1.0); \n"
        "  color = color + spec; \n"
        " \n"
        " \n"
        "   //vec3 specLightDir = normalize(vec3(1.0, 1.0, 1.0) - vPos);\n"
        "   //vec3 sepcHalf = normalize(specLightDir+V);\n"
        "   //vec3 spec = vec3(0.3) * pow(max(dot(N,H),0.0), 30.0); \n"
        "   //spec = clamp(spec, 0.0, 1.0); \n"
        "   //color = color + spec; \n"
        " \n"
        "   //vec4 Ispec = vec4(0.3) * pow(max(dot(N,H),0.0), 30.0); \n"
        "   //Ispec = clamp(Ispec, 0.0, 1.0);\n "
        "   //color = color + Ispec.rgb; \n"
        " \n"
        "    FragColor = vec4(color, min(1.0 - modelAlpha, modelDefaultTransparency)); \n"
        "    //FragColor = vec4(color, min(0.0, 1.0 - modelAlpha)); \n"
        "    //FragColor = vec4(1.0, 0.0, 0.0, 0.01); \n"
        "    //FragColor = vec4(color , 1.0); \n"
        "} \n";


    static const std::string m_VertexShaderForCrystal =
        "#version 300 es \n"
        " \n"
        "out vec3 vPos; \n"
        "out vec3 vNormal; \n"
        "//out vec2 vTexCoord; \n"
        " \n"
        "void main() \n"
        "{ \n"
        "    vPos = vec3(gl_ModelViewMatrix*gl_Vertex); \n"
        "    vNormal = gl_NormalMatrix*gl_Normal; \n"
        "    gl_Position = gl_ModelViewProjectionMatrix*gl_Vertex; \n"
        "    //vTexCoord = gl_MultiTexCoord0.xy; \n"
        "} \n";



    static const std::string m_FragmentShaderForCrystal =
        "#version 300 es \n"
        "precision highp float; \n"
        " \n"
        "in vec3 vPos; \n"
        "in vec3 vNormal; \n"
        "//in vec2 vTexCoord; \n"
        " \n"
        "uniform vec3 m_diffuseColor; \n"
        "uniform vec3 lightColor; \n"
        "uniform vec3 lightPosition; \n"
        "uniform float metallic; \n"
        "uniform float roughness; \n"
        "uniform float lightPert; \n"
        "uniform float envPert; \n"
        "uniform float rf; \n"
        " \n"
        "uniform samplerCube diffuseMap; \n"
        "uniform samplerCube diffuse2Map; \n"
        "uniform sampler2D lutMap; \n"
        " \n"
        "out vec4 FragColor; \n"
        " \n"
        "vec3 calculatefresnel(float cos, vec3 F, float ditheringValue) \n"
        "{ \n"
        "    return F + (max(vec3(1.0 - ditheringValue), F) - F) * pow(clamp(1.0 - cos, 0.0, 1.0), 5.0); \n"
        "} \n"
        " \n"
        "void main() \n"
        "{ \n"
        "    vec3 N = normalize(vNormal); \n"
        "    vec3 V = normalize(vec3(0.0) - vPos); \n"
        "    vec3 L = normalize(lightPosition - vPos); \n"
        "    vec3 H = normalize(V + L); \n"
        "    float NdotL = max(dot(N, L), 0.0); \n"
        "    float NdotV = dot(N, V); \n"
        "    vec3 F = mix(vec3(0.04), m_diffuseColor, metallic); \n"
        " \n"
        "    vec3 F1 = calculatefresnel(max(dot(H, V), 0.0), F, 0.0); \n"
        "    vec3 diffuseColorFactor1 = (1.0 - metallic)*(vec3(1.0) - F1); \n"
        "    vec3 finalLightColor = (diffuseColorFactor1*m_diffuseColor*0.3183)*lightColor*NdotL; \n"
        " \n"
        "    vec3 F2 = calculatefresnel(max(NdotV, 0.0), F, roughness); \n"
        "    vec3 diffuseColorFactor2 = (1.0 - metallic)*(vec3(1.0) - F2); \n"
        "    vec3 irradianceFactor = texture(diffuseMap, N).rgb; \n"
        "    vec3 diffuseColorReflect = irradianceFactor*m_diffuseColor; \n"
        "    vec3 R = reflect(-V, N); \n"
        "    vec3 prefilteredColor = texture(diffuse2Map, R).rgb; \n"
        "    vec2 brdf = texture(lutMap, vec2( max(NdotV, roughness)) ).rg; \n"
        "    vec3 specularColor = prefilteredColor * (F2*brdf.x + brdf.y); \n"
        "    vec3 finalReflectColor = (diffuseColorFactor2*diffuseColorReflect + specularColor); \n"
        " \n"
        "    vec3 color = finalLightColor*lightPert + finalReflectColor*envPert; \n"
        " \n"
        "    color = vec3(1.0) - exp(-color * rf); \n"
        "    color = pow(color, vec3(1.0/2.2)); \n"
        " \n"
        "    FragColor = vec4(color , 0.4); \n"
        "    //FragColor = vec4(color , 1.0); \n"
        "} \n";


StateSetUpdater::StateSetUpdater()
    :osg::NodeVisitor(osg::NodeVisitor::TRAVERSE_ALL_CHILDREN),
    m_NewStateSetForCrystal(),
    m_NewStateSetForMainCarpaint(),
    m_NewStateSetForBlackCarpaint(),
    m_NewStateSetForHoodCarpaint(),
    m_NewStateSetForMainWindowGlass(),
    m_NewStateSetForHeadWindowGlass{}
{
    XLOG_INFO(g_AppContext, "StateSet Updater Constructor");
}


void StateSetUpdater::apply(osg::Node & f_node)
{

    // osg::ref_ptr<osg::StateSet> m_NewStateSetForMainCarpaint;
    // osg::ref_ptr<osg::StateSet> m_NewStateSetForBlackCarpaint;
    // osg::ref_ptr<osg::StateSet> m_NewStateSetForMainWindowGlass;
    // osg::ref_ptr<osg::StateSet> m_NewStateSetForHeadWindowGlass;


    // std::string m_OldStateSetNameForMainCarpaint = "StateSet_carpaint_main";
    // std::string m_OldStateSetNameForBlackCarpaint = "StateSet_carpaint_black";
    // std::string m_OldStateSetNameForMainWindowGlass = "StateSet_roof_glass";
    // std::string m_OldStateSetNameForOtherWindowGlass= "StateSet_roof_glass_head";

    if( (nullptr != f_node.asGeode()) )
    {
        osg::Geode *const l_pGeode = f_node.asGeode();
        const unsigned int l_numGeometry = l_pGeode->getNumDrawables();
        if(l_numGeometry > 0)
        {
            //printf("Found Geode: %s, it has %u Geometry !\n", pGeode->getName().c_str(), l_numGeometry);
            for(unsigned int l_idx = 0; l_idx < l_numGeometry; l_idx++)
            {
                osg::Drawable *const l_pDrawable = l_pGeode->getDrawable(l_idx);
                if(l_pDrawable->asGeometry() != nullptr)
                {
                    osg::Geometry *const l_pGeometry = l_pDrawable->asGeometry();
                    osg::StateSet *const l_pStateSet = l_pGeometry->getStateSet();
                    if(l_pStateSet != nullptr)
                    {
                        if(l_pStateSet->getName() == m_OldStateSetNameForCrystal)
                        {
                            l_pGeometry->setStateSet(m_NewStateSetForCrystal);

                            XLOG_INFO(g_AppContext, "Update Carpint StateSet for Geode:" << l_pGeode->getName() << "Drawable-Index:" << l_idx);
                            //return;
                            continue;
                        }

                        if(l_pStateSet->getName() == m_OldStateSetNameForMainCarpaint)
                        {
                            l_pGeometry->setStateSet(m_NewStateSetForMainCarpaint);
                            XLOG_INFO(g_AppContext, "Update StateSet PBR Carpint Main for Geode:" << l_pGeode->getName() << "Drawable-Index:" << l_idx);
                            //return;
                            continue;
                        }

                        if(l_pStateSet->getName() == m_OldStateSetNameForBlackCarpaint)
                        {
                            l_pGeometry->setStateSet(m_NewStateSetForBlackCarpaint);
                            XLOG_INFO(g_AppContext, "Update StateSet PBR Carpint Black for Geode:" << l_pGeode->getName() << "Drawable-Index:" << l_idx);
                            //return;
                            continue;
                        }

                        if(l_pStateSet->getName() == m_OldStateSetNameForHoodCarpaint)
                        {
                            l_pGeometry->setStateSet(m_NewStateSetForHoodCarpaint);
                            XLOG_INFO(g_AppContext, "Update StateSet PBR Carpint Hood for Geode:" << l_pGeode->getName() << "Drawable-Index:" << l_idx);
                            //return;
                            continue;
                        }

                        if(l_pStateSet->getName() == m_OldStateSetNameForMainWindowGlass)
                        {
                            l_pGeometry->setStateSet(m_NewStateSetForMainWindowGlass);
                            XLOG_INFO(g_AppContext, "Update StateSet PBR Window Glass for Geode:" << l_pGeode->getName() << "Drawable-Index:" << l_idx);
                            //return;
                            continue;
                        }

                        if(l_pStateSet->getName() == m_OldStateSetNameForHeadWindowGlass)
                        {
                            l_pGeometry->setStateSet(m_NewStateSetForHeadWindowGlass);
                            XLOG_INFO(g_AppContext, "Update StateSet PBR Window Head Glass for Geode:" << l_pGeode->getName() << "Drawable-Index:" << l_idx);
                            //return;
                            continue;
                        }
                    }


                }
            }
        }
    }

    traverse(f_node);
}


void StateSetUpdater::initNewStateSetForCrystal()
{
    osg::ref_ptr<osg::Texture2D> const l_floatTextureBRFD = new osg::Texture2D;
    l_floatTextureBRFD->setDataVariance(osg::Object::STATIC);
    // l_floatTextureBRFD->setInternalFormat(GL_RGBA32F); // #define GL_RGBA32F                        0x8814
    // l_floatTextureBRFD->setInternalFormat(GL_RG16F);
    l_floatTextureBRFD->setInternalFormat(0x881B);// #define GL_RGB16F                         0x881B
    l_floatTextureBRFD->setSourceFormat(GL_RGB);
    l_floatTextureBRFD->setSourceType(GL_FLOAT);
    l_floatTextureBRFD->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureBRFD->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureBRFD->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_floatTextureBRFD->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_floatTextureBRFD->setUnRefImageDataAfterApply(true);
    // l_floatTextureBRFD->setUseHardwareMipMapGeneration(true);


    osg::ref_ptr<osg::TextureCubeMap> const l_floatTextureCubeIrradiance = new osg::TextureCubeMap;
    l_floatTextureCubeIrradiance->setDataVariance(osg::Object::STATIC);
    l_floatTextureCubeIrradiance->setInternalFormat(0x881B);// #define GL_RGB16F                         0x881B
    l_floatTextureCubeIrradiance->setSourceFormat(GL_RGB);
    l_floatTextureCubeIrradiance->setSourceType(GL_FLOAT);
    l_floatTextureCubeIrradiance->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureCubeIrradiance->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureCubeIrradiance->setWrap(osg::Texture::WRAP_R, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureCubeIrradiance->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_floatTextureCubeIrradiance->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_floatTextureCubeIrradiance->setUnRefImageDataAfterApply(true);
    // l_floatTextureCubeIrradiance->setUseHardwareMipMapGeneration(true);


    osg::ref_ptr<osg::TextureCubeMap> const l_floatTextureCubePrefilter = new osg::TextureCubeMap;
    l_floatTextureCubePrefilter->setDataVariance(osg::Object::STATIC);
    l_floatTextureCubePrefilter->setInternalFormat(0x881B);// #define GL_RGB16F                         0x881B
    l_floatTextureCubePrefilter->setSourceFormat(GL_RGB);
    l_floatTextureCubePrefilter->setSourceType(GL_FLOAT);
    l_floatTextureCubePrefilter->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureCubePrefilter->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureCubePrefilter->setWrap(osg::Texture::WRAP_R, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureCubePrefilter->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_floatTextureCubePrefilter->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_floatTextureCubePrefilter->setUnRefImageDataAfterApply(true);
    // l_floatTextureCubePrefilter->setUseHardwareMipMapGeneration(true);


    osg::ref_ptr<osg::Image> const l_imageBRFD = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imageIrradiance0 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imageIrradiance1 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imageIrradiance2 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imageIrradiance3 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imageIrradiance4 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imageIrradiance5 = new osg::Image;
    osg::Image * const l_pImageIrradiance[6] = {l_imageIrradiance0.get(), l_imageIrradiance1.get(),
                                       l_imageIrradiance2.get(), l_imageIrradiance3.get(),
                                       l_imageIrradiance4.get(), l_imageIrradiance5.get()};
    osg::ref_ptr<osg::Image> const l_imagePrefilter0 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imagePrefilter1 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imagePrefilter2 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imagePrefilter3 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imagePrefilter4 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imagePrefilter5 = new osg::Image;
    osg::Image * const l_pImagePrefilter[6] = { l_imagePrefilter0.get(), l_imagePrefilter1.get(),
                                       l_imagePrefilter2.get(), l_imagePrefilter3.get(),
                                       l_imagePrefilter4.get(), l_imagePrefilter5.get()};


    int l_size = 0;
    int l_readSize = 0;
    FILE *const l_fp = fopen(l_crystalResourcePath.c_str(), "rb");
    if(nullptr != l_fp)
    {
        XLOG_INFO(g_AppContext, "crystal bin ========== succeed to open bin file " << l_crystalResourcePath);

        l_size = 512*512*3*sizeof(GL_FLOAT);
        l_imageBRFD->allocateImage(512, 512, 1, GL_RGB, GL_FLOAT);
        l_readSize = fread(l_imageBRFD->data(), 1, l_size, l_fp);
        if(l_readSize != l_size)
        {
            XLOG_ERROR(g_AppContext, "crystal bin ========== load file error, need read:" << l_size <<"byte, but read: "<< l_readSize);
        }

        l_size = 32*32*3*sizeof(GL_FLOAT);
        for (unsigned int l_idx = 0; l_idx < 6; ++l_idx)
        {
            l_pImageIrradiance[l_idx]->allocateImage(32, 32, 1, GL_RGB, GL_FLOAT);

            l_readSize = fread(l_pImageIrradiance[l_idx]->data(), 1, l_size, l_fp);
            if(l_readSize != l_size)
            {
                XLOG_ERROR(g_AppContext, "crystal bin v load file error, need read:" << l_size <<"byte, but read: "<< l_readSize);
            }
        }

        l_size = 256*256*3*sizeof(GL_FLOAT);
        for (unsigned int l_idx = 0; l_idx < 6; ++l_idx)
        {
            l_pImagePrefilter[l_idx]->allocateImage(256, 256, 1, GL_RGB, GL_FLOAT);

            l_readSize = fread(l_pImagePrefilter[l_idx]->data(), 1, l_size, l_fp);
            if(l_readSize != l_size)
            {
                XLOG_ERROR(g_AppContext, "crystal bin ========== load file error, need read:" << l_size <<"byte, but read: "<< l_readSize);
            }
        }
    }
    else
    {
        XLOG_ERROR(g_AppContext, "crystal bin ========== fail to open bin file " << l_crystalResourcePath);
    }
    // l_imageBRFD->allocateImage(512, 512, 1, GL_RGB, GL_FLOAT);


    l_floatTextureBRFD->setImage(l_imageBRFD);
    l_floatTextureCubeIrradiance->setImage(0, l_imageIrradiance0);
    l_floatTextureCubeIrradiance->setImage(1, l_imageIrradiance1);
    l_floatTextureCubeIrradiance->setImage(2, l_imageIrradiance2);
    l_floatTextureCubeIrradiance->setImage(3, l_imageIrradiance3);
    l_floatTextureCubeIrradiance->setImage(4, l_imageIrradiance4);
    l_floatTextureCubeIrradiance->setImage(5, l_imageIrradiance5);
    l_floatTextureCubePrefilter->setImage(0, l_imagePrefilter0);
    l_floatTextureCubePrefilter->setImage(1, l_imagePrefilter1);
    l_floatTextureCubePrefilter->setImage(2, l_imagePrefilter2);
    l_floatTextureCubePrefilter->setImage(3, l_imagePrefilter3);
    l_floatTextureCubePrefilter->setImage(4, l_imagePrefilter4);
    l_floatTextureCubePrefilter->setImage(5, l_imagePrefilter5);

    osg::ref_ptr<osg::Program>  const mProgram = new osg::Program;
    // osg::ref_ptr<osg::Shader>   mVertexShader = new osg::Shader(osg::Shader::Type::VERTEX);
    // mVertexShader->loadShaderSourceFromFile(CONCAT_PATH_VEHICLE_MODEL("cc/vehicle_model/crystal_data/") + "shader.vs");
    osg::ref_ptr<osg::Shader>   const mVertexShader = new osg::Shader(osg::Shader::Type::VERTEX, m_VertexShaderForCrystal);
    // osg::ref_ptr<osg::Shader>   mFragmentShader = new osg::Shader(osg::Shader::Type::FRAGMENT);
    // mFragmentShader->loadShaderSourceFromFile(CONCAT_PATH_VEHICLE_MODEL("cc/vehicle_model/crystal_data/") + "shader.fs");
    osg::ref_ptr<osg::Shader>   const mFragmentShader = new osg::Shader(osg::Shader::Type::FRAGMENT, m_FragmentShaderForCrystal);
    mProgram->addShader( mVertexShader);
    mProgram->addShader( mFragmentShader);
    mProgram->setDataVariance(osg::Object::STATIC);


    m_NewStateSetForCrystal = new osg::StateSet;
    m_NewStateSetForCrystal->setName("StateSet_Crystal_new");
    m_NewStateSetForCrystal->setMode(GL_CULL_FACE, osg::StateAttribute::ON);

    // transparent
    m_NewStateSetForCrystal->setRenderingHint(osg::StateSet::RenderingHint::TRANSPARENT_BIN);
    m_NewStateSetForCrystal->setRenderBinDetails(10, "DepthSortedBin", osg::StateSet::RenderBinMode::USE_RENDERBIN_DETAILS);

    const osg::ref_ptr<osg::BlendFunc> l_BlendFunc = new osg::BlendFunc;
    l_BlendFunc->setFunction(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
    m_NewStateSetForCrystal->setAttribute(l_BlendFunc, osg::StateAttribute::ON);
    m_NewStateSetForCrystal->setMode(GL_BLEND, osg::StateAttribute::ON);

    const osg::ref_ptr<osg::Depth> l_depth = new osg::Depth;
    l_depth->setFunction(osg::Depth::Function::LEQUAL);
    // depth->setFunction(osg::Depth::Function::LESS);
    // depth->setWriteMask(false);
    m_NewStateSetForCrystal->setAttributeAndModes(l_depth, osg::StateAttribute::ON);


    m_NewStateSetForCrystal->setAttribute( mProgram, osg::StateAttribute::ON);

    // m_NewStateSetForCrystal->setTextureAttributeAndModes(0, l_floatTextureCubeIrradiance);
    // m_NewStateSetForCrystal->setTextureAttributeAndModes(1, l_floatTextureCubePrefilter);
    // m_NewStateSetForCrystal->setTextureAttributeAndModes(2, l_floatTextureBRFD);

    m_NewStateSetForCrystal->setTextureAttribute(0, l_floatTextureCubeIrradiance);
    m_NewStateSetForCrystal->setTextureAttribute(1, l_floatTextureCubePrefilter);
    m_NewStateSetForCrystal->setTextureAttribute(2, l_floatTextureBRFD);

#if 0
    const float r = pow((0.519f + 0.055f) / 1.055f, 2.4f);
    const float g = pow((0.815f + 0.055f) / 1.055f, 2.4f);
    const float b = pow((0.60f + 0.055f) / 1.055f, 2.4f);
    constexpr float r1 = 0.324f;
    constexpr float g1 = 0.518f;
    constexpr float b1 = 0.378f;
    constexpr float lx = 10.0f;
    constexpr float ly = 10.0f;
    constexpr float lz = 10.0f;
    constexpr float metallic = 0.3f;
    constexpr float roughness = 0.15f;
    constexpr float lightPert = 1.0f;
    constexpr float envPert = 1.0f;
    constexpr float rf = 1.0f;

    const float r1 = 1.0f;
    const float g1 = 1.0f;
    const float b1 = 1.0f;

    const float r = pow((1.0f + 0.055f) / 1.055f, 2.4f);
    const float g = pow((1.0f + 0.055f) / 1.055f, 2.4f);
    const float b = pow((1.0f + 0.055f) / 1.055f, 2.4f);
#else
    const float r = powf((1.0f + 0.055f) / 1.055f, 2.4f);
    const float g = powf((1.0f + 0.055f) / 1.055f, 2.4f);
    const float b = powf((1.0f + 0.055f) / 1.055f, 2.4f);
    constexpr float r1 = 1.0f;
    constexpr float g1 = 1.0f;
    constexpr float b1 = 1.0f;
    constexpr float lx = 10.0f;
    constexpr float ly = 10.0f;
    constexpr float lz = 10.0f;
    constexpr float metallic = 0.3f;
    constexpr float roughness = 0.15f;
    constexpr float lightPert = 1.0f;
    constexpr float envPert = 1.0f;
    constexpr float rf = 1.5f;
#endif

    osg::ref_ptr<osg::Uniform> const mUniformTexture2 = new osg::Uniform("diffuseMap", 0);
    m_NewStateSetForCrystal->addUniform(mUniformTexture2);

    osg::ref_ptr<osg::Uniform> const mUniformTexture3 = new osg::Uniform("diffuse2Map", 1);
    m_NewStateSetForCrystal->addUniform(mUniformTexture3);

    osg::ref_ptr<osg::Uniform> const mUniformTexture1 = new osg::Uniform("lutMap", 2);
    m_NewStateSetForCrystal->addUniform(mUniformTexture1);

    osg::ref_ptr<osg::Uniform> const mUniformColor = new osg::Uniform("m_diffuseColor", osg::Vec3(r, g, b));
    m_NewStateSetForCrystal->addUniform(mUniformColor);

    osg::ref_ptr<osg::Uniform> const mUniformLightColor = new osg::Uniform("lightColor", osg::Vec3(r1, g1, b1));
    m_NewStateSetForCrystal->addUniform(mUniformLightColor);

    osg::ref_ptr<osg::Uniform> const mUniformLightPosition = new osg::Uniform("lightPosition", osg::Vec3(lx, ly, lz));
    m_NewStateSetForCrystal->addUniform(mUniformLightPosition);

    osg::ref_ptr<osg::Uniform> const mUniformMetallic = new osg::Uniform("metallic", metallic);
    m_NewStateSetForCrystal->addUniform(mUniformMetallic);

    osg::ref_ptr<osg::Uniform> const mUniformRoughness = new osg::Uniform("roughness", roughness);
    m_NewStateSetForCrystal->addUniform(mUniformRoughness);

    osg::ref_ptr<osg::Uniform> const mUniformReflactFactor = new osg::Uniform("lightPert", 0.0f);
    m_NewStateSetForCrystal->addUniform(mUniformReflactFactor);

    osg::ref_ptr<osg::Uniform> const mUniformBrightnessFactor = new osg::Uniform("envPert", envPert);
    m_NewStateSetForCrystal->addUniform(mUniformBrightnessFactor);

    osg::ref_ptr<osg::Uniform> const mUniformReflactFactorFinal = new osg::Uniform("rf", rf);
    m_NewStateSetForCrystal->addUniform(mUniformReflactFactorFinal);
}


// osg::ref_ptr<osg::StateSet> m_NewStateSetForMainCarpaint;
// osg::ref_ptr<osg::StateSet> m_NewStateSetForBlackCarpaint;
// osg::ref_ptr<osg::StateSet> m_NewStateSetForMainWindowGlass;
// osg::ref_ptr<osg::StateSet> m_NewStateSetForHeadWindowGlass;

// std::string m_OldStateSetNameForCrystal = "";

// std::string m_OldStateSetNameForMainCarpaint = "";
// std::string m_OldStateSetNameForBlackCarpaint = "";
// std::string m_OldStateSetNameForMainWindowGlass = "";
// std::string m_OldStateSetNameForMainCarpaint = "";

// const std::string m_FragmentShaderForPBR =
// const std::string m_VertexShaderForPBR =

void StateSetUpdater::initNewStateSetForPBRCarpaint()
{
    osg::ref_ptr<osg::Texture2D> const l_floatTextureBRFD = new osg::Texture2D;
    l_floatTextureBRFD->setDataVariance(osg::Object::STATIC);
    // l_floatTextureBRFD->setInternalFormat(GL_RGBA32F); // #define GL_RGBA32F                        0x8814
    // l_floatTextureBRFD->setInternalFormat(GL_RG16F);
    l_floatTextureBRFD->setInternalFormat(0x881B); // #define GL_RGB16F                         0x881B
    l_floatTextureBRFD->setSourceFormat(GL_RGB);
    l_floatTextureBRFD->setSourceType(GL_FLOAT);
    l_floatTextureBRFD->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureBRFD->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureBRFD->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_floatTextureBRFD->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_floatTextureBRFD->setUnRefImageDataAfterApply(true);
    // l_floatTextureBRFD->setUseHardwareMipMapGeneration(true);


    osg::ref_ptr<osg::TextureCubeMap> const l_floatTextureCubeIrradiance = new osg::TextureCubeMap;
    l_floatTextureCubeIrradiance->setDataVariance(osg::Object::STATIC);
    l_floatTextureCubeIrradiance->setInternalFormat(0x881B);// #define GL_RGB16F                         0x881B
    l_floatTextureCubeIrradiance->setSourceFormat(GL_RGB);
    l_floatTextureCubeIrradiance->setSourceType(GL_FLOAT);
    l_floatTextureCubeIrradiance->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureCubeIrradiance->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureCubeIrradiance->setWrap(osg::Texture::WRAP_R, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureCubeIrradiance->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_floatTextureCubeIrradiance->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_floatTextureCubeIrradiance->setUnRefImageDataAfterApply(true);
    // l_floatTextureCubeIrradiance->setUseHardwareMipMapGeneration(true);


    osg::ref_ptr<osg::TextureCubeMap> const l_floatTextureCubePrefilter = new osg::TextureCubeMap;
    l_floatTextureCubePrefilter->setDataVariance(osg::Object::STATIC);
    l_floatTextureCubePrefilter->setInternalFormat(0x881B);// #define GL_RGB16F                         0x881B
    l_floatTextureCubePrefilter->setSourceFormat(GL_RGB);
    l_floatTextureCubePrefilter->setSourceType(GL_FLOAT);
    l_floatTextureCubePrefilter->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureCubePrefilter->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureCubePrefilter->setWrap(osg::Texture::WRAP_R, osg::Texture::CLAMP_TO_EDGE);
    l_floatTextureCubePrefilter->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_floatTextureCubePrefilter->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_floatTextureCubePrefilter->setUnRefImageDataAfterApply(true);
    // l_floatTextureCubePrefilter->setUseHardwareMipMapGeneration(true);


    osg::ref_ptr<osg::Image> const l_imageBRFD = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imageIrradiance0 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imageIrradiance1 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imageIrradiance2 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imageIrradiance3 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imageIrradiance4 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imageIrradiance5 = new osg::Image;
    osg::Image * const l_pImageIrradiance[6] = {l_imageIrradiance0.get(), l_imageIrradiance1.get(),
                                       l_imageIrradiance2.get(), l_imageIrradiance3.get(),
                                       l_imageIrradiance4.get(), l_imageIrradiance5.get()};

    osg::ref_ptr<osg::Image> const l_imagePrefilter0 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imagePrefilter1 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imagePrefilter2 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imagePrefilter3 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imagePrefilter4 = new osg::Image;
    osg::ref_ptr<osg::Image> const l_imagePrefilter5 = new osg::Image;
    osg::Image * const l_pImagePrefilter[6] = { l_imagePrefilter0.get(), l_imagePrefilter1.get(),
                                       l_imagePrefilter2.get(), l_imagePrefilter3.get(),
                                       l_imagePrefilter4.get(), l_imagePrefilter5.get()};



    int l_size = 0;
    int l_readSize = 0;
    FILE *const l_fp = fopen(l_mainCarpaintResourcePath.c_str(), "rb");
    if(l_fp != nullptr)
    {
        XLOG_INFO(g_AppContext, "main carpaint bin ========== succeed to open bin file " << l_mainCarpaintResourcePath);

        l_size = 512*512*3*sizeof(GL_FLOAT);
        l_imageBRFD->allocateImage(512, 512, 1, GL_RGB, GL_FLOAT);
        l_readSize = fread(l_imageBRFD->data(), 1, l_size, l_fp);
        if(l_readSize != l_size)
        {
            XLOG_ERROR(g_AppContext, "main carpaint bin ========== load file error, need read:" << l_size <<"byte, but read: "<< l_readSize);
        }

        l_size = 32*32*3*sizeof(GL_FLOAT);
        for (unsigned int l_idx = 0; l_idx < 6; ++l_idx)
        {
            l_pImageIrradiance[l_idx]->allocateImage(32, 32, 1, GL_RGB, GL_FLOAT);

            l_readSize = fread(l_pImageIrradiance[l_idx]->data(), 1, l_size, l_fp);
            if(l_readSize != l_size)
            {
                XLOG_ERROR(g_AppContext, "main carpaint bin v load file error, need read:" << l_size <<"byte, but read: "<< l_readSize);
            }
        }

        l_size = 256*256*3*sizeof(GL_FLOAT);
        for (unsigned int l_idx = 0; l_idx < 6; ++l_idx)
        {
            l_pImagePrefilter[l_idx]->allocateImage(256, 256, 1, GL_RGB, GL_FLOAT);

            l_readSize = fread(l_pImagePrefilter[l_idx]->data(), 1, l_size, l_fp);
            if(l_readSize != l_size)
            {
                XLOG_ERROR(g_AppContext, "main carpaint bin ========== load file error, need read:" << l_size <<"byte, but read: "<< l_readSize);
            }
        }
    }
    else
    {
        XLOG_ERROR(g_AppContext, "main carpaint bin ========== fail to open bin file " << l_mainCarpaintResourcePath);
    }
    // l_imageBRFD->allocateImage(512, 512, 1, GL_RGB, GL_FLOAT);


    l_floatTextureBRFD->setImage(l_imageBRFD);
    l_floatTextureCubeIrradiance->setImage(0, l_imageIrradiance0);
    l_floatTextureCubeIrradiance->setImage(1, l_imageIrradiance1);
    l_floatTextureCubeIrradiance->setImage(2, l_imageIrradiance2);
    l_floatTextureCubeIrradiance->setImage(3, l_imageIrradiance3);
    l_floatTextureCubeIrradiance->setImage(4, l_imageIrradiance4);
    l_floatTextureCubeIrradiance->setImage(5, l_imageIrradiance5);
    l_floatTextureCubePrefilter->setImage(0, l_imagePrefilter0);
    l_floatTextureCubePrefilter->setImage(1, l_imagePrefilter1);
    l_floatTextureCubePrefilter->setImage(2, l_imagePrefilter2);
    l_floatTextureCubePrefilter->setImage(3, l_imagePrefilter3);
    l_floatTextureCubePrefilter->setImage(4, l_imagePrefilter4);
    l_floatTextureCubePrefilter->setImage(5, l_imagePrefilter5);


    osg::ref_ptr<osg::Program>  const mProgram = new osg::Program;
    osg::ref_ptr<osg::Shader>   const mVertexShader = new osg::Shader(osg::Shader::Type::VERTEX, m_VertexShaderForPBR);
    osg::ref_ptr<osg::Shader>   const mFragmentShader = new osg::Shader(osg::Shader::Type::FRAGMENT, m_FragmentShaderForPBR);
    mProgram->addShader( mVertexShader);
    mProgram->addShader( mFragmentShader);
    mProgram->setDataVariance(osg::Object::STATIC);



    m_NewStateSetForMainCarpaint = new osg::StateSet;
    m_NewStateSetForMainCarpaint->setName("StateSet_carpaint_main_pbr_new");
    {
        m_NewStateSetForMainCarpaint->setMode(GL_CULL_FACE, osg::StateAttribute::ON);

        // transparent
        // m_NewStateSetForMainCarpaint->setRenderingHint(osg::StateSet::RenderingHint::TRANSPARENT_BIN);
        // m_NewStateSetForMainCarpaint->setRenderBinDetails(1000, "DepthSortedBin", osg::StateSet::RenderBinMode::USE_RENDERBIN_DETAILS);

        m_NewStateSetForMainCarpaint->setRenderingHint(osg::StateSet::RenderingHint::DEFAULT_BIN);


        const osg::ref_ptr<osg::BlendFunc> l_BlendFunc = new osg::BlendFunc;
        l_BlendFunc->setFunction(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
        m_NewStateSetForMainCarpaint->setAttribute(l_BlendFunc, osg::StateAttribute::ON);
        // m_NewStateSetForMainCarpaint->setMode(GL_BLEND, osg::StateAttribute::ON);

        // osg::ref_ptr<osg::Depth> l_depth = new osg::Depth;
        // l_depth->setFunction(osg::Depth::Function::LEQUAL);
        // depth->setFunction(osg::Depth::Function::LESS);
        // depth->setWriteMask(false);
        // m_NewStateSetForMainCarpaint->setAttributeAndModes(l_depth, osg::StateAttribute::ON);

        m_NewStateSetForMainCarpaint->setAttribute( mProgram, osg::StateAttribute::ON);

        // m_NewStateSetForMainCarpaint->setTextureAttributeAndModes(0, l_floatTextureCubeIrradiance);
        // m_NewStateSetForMainCarpaint->setTextureAttributeAndModes(1, l_floatTextureCubePrefilter);
        // m_NewStateSetForMainCarpaint->setTextureAttributeAndModes(2, l_floatTextureBRFD);

        m_NewStateSetForMainCarpaint->setTextureAttribute(0, l_floatTextureCubeIrradiance);
        m_NewStateSetForMainCarpaint->setTextureAttribute(1, l_floatTextureCubePrefilter);
        m_NewStateSetForMainCarpaint->setTextureAttribute(2, l_floatTextureBRFD);

        osg::ref_ptr<osg::Uniform> const mUniformTexture2 = new osg::Uniform("diffuseMap", 0);
        m_NewStateSetForMainCarpaint->addUniform(mUniformTexture2);

        osg::ref_ptr<osg::Uniform> const mUniformTexture3 = new osg::Uniform("diffuse2Map", 1);
        m_NewStateSetForMainCarpaint->addUniform(mUniformTexture3);

        osg::ref_ptr<osg::Uniform> const mUniformTexture1 = new osg::Uniform("lutMap", 2);
        m_NewStateSetForMainCarpaint->addUniform(mUniformTexture1);

        // osg::ref_ptr<osg::Uniform> mUniformColor = new osg::Uniform("diffuseColor", osg::Vec3(r, g, b));
        // m_NewStateSetForMainCarpaint->addUniform(mUniformColor);
        // osg::ref_ptr<osg::Uniform> mUniformLightColor = new osg::Uniform("lightColor", osg::Vec3(r1, g1, b1));
        // m_NewStateSetForMainCarpaint->addUniform(mUniformLightColor);
        // osg::ref_ptr<osg::Uniform> mUniformLightPosition = new osg::Uniform("lightPosition", osg::Vec3(lx, ly, lz));
        // m_NewStateSetForMainCarpaint->addUniform(mUniformLightPosition);
        // osg::ref_ptr<osg::Uniform> mUniformMetallic = new osg::Uniform("metallic", metallic);
        // m_NewStateSetForMainCarpaint->addUniform(mUniformMetallic);
        // osg::ref_ptr<osg::Uniform> mUniformRoughness = new osg::Uniform("roughness", roughness);
        // m_NewStateSetForMainCarpaint->addUniform(mUniformRoughness);
        // osg::ref_ptr<osg::Uniform> mUniformReflactFactor = new osg::Uniform("lightPert", lightPert);
        // m_NewStateSetForMainCarpaint->addUniform(mUniformReflactFactor);
        // osg::ref_ptr<osg::Uniform> mUniformBrightnessFactor = new osg::Uniform("envPert", envPert);
        // m_NewStateSetForMainCarpaint->addUniform(mUniformBrightnessFactor);
        // osg::ref_ptr<osg::Uniform> mUniformReflactFactorFinal = new osg::Uniform("rf", rf);
        // m_NewStateSetForMainCarpaint->addUniform(mUniformReflactFactorFinal);

        #if 0 // for debug, set default color
            float r = pow((0.519 + 0.055) / 1.055, 2.4);
            float g = pow((0.815 + 0.055) / 1.055, 2.4);
            float b = pow((0.60 + 0.055) / 1.055, 2.4);
            float r1 = 0.324f;
            float g1 = 0.518f;
            float b1 = 0.378f;
            float lx = 10.0f;
            float ly = 10.0f;
            float lz = 10.0f;
            float metallic = 0.3f;
            float roughness = 0.15f;
            float lightPert = 1.0f;
            float envPert = 1.0f;
            float rf = 1.0f;

            osg::ref_ptr<osg::Uniform> mUniformColor = new osg::Uniform("diffuseColor", osg::Vec4(r, g, b, 1.0));
            m_NewStateSetForMainCarpaint->addUniform(mUniformColor);
            osg::ref_ptr<osg::Uniform> mUniformLightColor = new osg::Uniform("specColor1", osg::Vec4(r1, g1, b1, 1.0));
            m_NewStateSetForMainCarpaint->addUniform(mUniformLightColor);
            osg::ref_ptr<osg::Uniform> mUniformLightPosition = new osg::Uniform("lightPos", osg::Vec3(lx, ly, lz));
            m_NewStateSetForMainCarpaint->addUniform(mUniformLightPosition);
            osg::ref_ptr<osg::Uniform> mUniformMetallic = new osg::Uniform("specShininess1", metallic);
            m_NewStateSetForMainCarpaint->addUniform(mUniformMetallic);
            osg::ref_ptr<osg::Uniform> mUniformRoughness = new osg::Uniform("specShininess2", roughness);
            m_NewStateSetForMainCarpaint->addUniform(mUniformRoughness);
            osg::ref_ptr<osg::Uniform> mUniformReflactFactor = new osg::Uniform("brightness", lightPert);
            m_NewStateSetForMainCarpaint->addUniform(mUniformReflactFactor);
            osg::ref_ptr<osg::Uniform> mUniformBrightnessFactor = new osg::Uniform("fresnel", envPert);
            m_NewStateSetForMainCarpaint->addUniform(mUniformBrightnessFactor);
            osg::ref_ptr<osg::Uniform> mUniformReflactFactorFinal = new osg::Uniform("reflectionPower", rf);
            m_NewStateSetForMainCarpaint->addUniform(mUniformReflactFactorFinal);
        #endif

        // osg::ref_ptr<osg::Uniform> mUniformModelAlpha = new osg::Uniform("modelAlpha", 0.0);
        // m_NewStateSetForMainCarpaint->addUniform(mUniformModelAlpha);


        osg::ref_ptr<osg::Uniform> const mUniformModelDefaultTransparency = new osg::Uniform("modelDefaultTransparency", 1.0f);
        m_NewStateSetForMainCarpaint->addUniform(mUniformModelDefaultTransparency);

        osg::ref_ptr<osg::Uniform> const mUniformSpecialColorIndexRendering = new osg::Uniform("specialColorIndexRendering", 0.0f);
        m_NewStateSetForMainCarpaint->addUniform(mUniformSpecialColorIndexRendering);
    }


    // ========================================= carpaint black start
    m_NewStateSetForBlackCarpaint = new osg::StateSet;
    m_NewStateSetForBlackCarpaint->setName("StateSet_carpaint_black_pbr_new");
    {
        m_NewStateSetForBlackCarpaint->setRenderingHint(osg::StateSet::RenderingHint::DEFAULT_BIN);
        m_NewStateSetForBlackCarpaint->setMode(GL_CULL_FACE, osg::StateAttribute::ON);

        // transparent
        // m_NewStateSetForBlackCarpaint->setRenderingHint(osg::StateSet::RenderingHint::TRANSPARENT_BIN);
        // m_NewStateSetForBlackCarpaint->setRenderBinDetails(1000, "DepthSortedBin", osg::StateSet::RenderBinMode::USE_RENDERBIN_DETAILS);

        osg::ref_ptr<osg::BlendFunc> const l_BlendFunc = new osg::BlendFunc;
        l_BlendFunc->setFunction(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
        m_NewStateSetForBlackCarpaint->setAttribute(l_BlendFunc, osg::StateAttribute::ON);
        m_NewStateSetForBlackCarpaint->setMode(GL_BLEND, osg::StateAttribute::ON);

        // osg::ref_ptr<osg::Depth> l_depth = new osg::Depth;
        // l_depth->setFunction(osg::Depth::Function::LEQUAL);
        // depth->setFunction(osg::Depth::Function::LESS);
        // depth->setWriteMask(false);
        // m_NewStateSetForBlackCarpaint->setAttributeAndModes(l_depth, osg::StateAttribute::ON);

        m_NewStateSetForBlackCarpaint->setAttribute( mProgram, osg::StateAttribute::ON);

        // m_NewStateSetForBlackCarpaint->setTextureAttributeAndModes(0, l_floatTextureCubeIrradiance);
        // m_NewStateSetForBlackCarpaint->setTextureAttributeAndModes(1, l_floatTextureCubePrefilter);
        // m_NewStateSetForBlackCarpaint->setTextureAttributeAndModes(2, l_floatTextureBRFD);

        m_NewStateSetForBlackCarpaint->setTextureAttribute(0, l_floatTextureCubeIrradiance);
        m_NewStateSetForBlackCarpaint->setTextureAttribute(1, l_floatTextureCubePrefilter);
        m_NewStateSetForBlackCarpaint->setTextureAttribute(2, l_floatTextureBRFD);

        osg::ref_ptr<osg::Uniform> const mUniformTexture2 = new osg::Uniform("diffuseMap", 0);
        m_NewStateSetForBlackCarpaint->addUniform(mUniformTexture2);

        osg::ref_ptr<osg::Uniform> const mUniformTexture3 = new osg::Uniform("diffuse2Map", 1);
        m_NewStateSetForBlackCarpaint->addUniform(mUniformTexture3);

        osg::ref_ptr<osg::Uniform> const mUniformTexture1 = new osg::Uniform("lutMap", 2);
        m_NewStateSetForBlackCarpaint->addUniform(mUniformTexture1);

        osg::ref_ptr<osg::Uniform> const mUniformColor = new osg::Uniform("diffuseColor", osg::Vec4(0.01f, 0.01f, 0.01f, 1.0f));
        //osg::ref_ptr<osg::Uniform> const mUniformColor = new osg::Uniform("diffuseColor", osg::Vec4(0.0f, 0.0f, 0.0f, 1.0f));
        m_NewStateSetForBlackCarpaint->addUniform(mUniformColor);
        osg::ref_ptr<osg::Uniform> const mUniformLightColor = new osg::Uniform("specColor1", osg::Vec4(0.3f, 0.3f, 0.3f, 1.0f));
        m_NewStateSetForBlackCarpaint->addUniform(mUniformLightColor);
        osg::ref_ptr<osg::Uniform> const mUniformLightPosition = new osg::Uniform("lightPos", osg::Vec3(0.0, 10.0f, 10.0f));
        m_NewStateSetForBlackCarpaint->addUniform(mUniformLightPosition);
        osg::ref_ptr<osg::Uniform> const mUniformMetallic = new osg::Uniform("specShininess1", 0.4f*1000.0f);
        m_NewStateSetForBlackCarpaint->addUniform(mUniformMetallic);
        osg::ref_ptr<osg::Uniform> const mUniformRoughness = new osg::Uniform("specShininess2", 0.9f*1000.0f);
        m_NewStateSetForBlackCarpaint->addUniform(mUniformRoughness);
        osg::ref_ptr<osg::Uniform> const mUniformReflactFactor = new osg::Uniform("brightness", 0.5f);
        m_NewStateSetForBlackCarpaint->addUniform(mUniformReflactFactor);
        osg::ref_ptr<osg::Uniform> const mUniformBrightnessFactor = new osg::Uniform("fresnel", 0.5f);
        m_NewStateSetForBlackCarpaint->addUniform(mUniformBrightnessFactor);
        osg::ref_ptr<osg::Uniform> const mUniformReflactFactorFinal = new osg::Uniform("reflectionPower", 1.0f);
        m_NewStateSetForBlackCarpaint->addUniform(mUniformReflactFactorFinal);


        // osg::ref_ptr<osg::Uniform> mUniformModelAlpha = new osg::Uniform("modelAlpha", 0.0f);
        // m_NewStateSetForBlackCarpaint->addUniform(mUniformModelAlpha);

        osg::ref_ptr<osg::Uniform> const mUniformModelDefaultTransparency = new osg::Uniform("modelDefaultTransparency", 1.0f);
        m_NewStateSetForBlackCarpaint->addUniform(mUniformModelDefaultTransparency);

        osg::ref_ptr<osg::Uniform> const mUniformSpecialColorIndexRendering = new osg::Uniform("specialColorIndexRendering", 0.0f);
        m_NewStateSetForBlackCarpaint->addUniform(mUniformSpecialColorIndexRendering);
    }
    // ========================================= carpaint black end

    // ========================================= carpaint hood start
    m_NewStateSetForHoodCarpaint = new osg::StateSet;
    m_NewStateSetForHoodCarpaint->setName("StateSet_carpaint_hood_pbr_new");
    {
        m_NewStateSetForHoodCarpaint->setRenderingHint(osg::StateSet::RenderingHint::DEFAULT_BIN);
        m_NewStateSetForHoodCarpaint->setMode(GL_CULL_FACE, osg::StateAttribute::ON);

        // transparent
        // m_NewStateSetForHoodCarpaint->setRenderingHint(osg::StateSet::RenderingHint::TRANSPARENT_BIN);
        // m_NewStateSetForHoodCarpaint->setRenderBinDetails(1000, "DepthSortedBin", osg::StateSet::RenderBinMode::USE_RENDERBIN_DETAILS);

        osg::ref_ptr<osg::BlendFunc> const l_BlendFunc = new osg::BlendFunc;
        l_BlendFunc->setFunction(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
        m_NewStateSetForHoodCarpaint->setAttribute(l_BlendFunc, osg::StateAttribute::ON);
        m_NewStateSetForHoodCarpaint->setMode(GL_BLEND, osg::StateAttribute::ON);

        // osg::ref_ptr<osg::Depth> l_depth = new osg::Depth;
        // l_depth->setFunction(osg::Depth::Function::LEQUAL);
        // depth->setFunction(osg::Depth::Function::LESS);
        // depth->setWriteMask(false);
        // m_NewStateSetForHoodCarpaint->setAttributeAndModes(l_depth, osg::StateAttribute::ON);

        m_NewStateSetForHoodCarpaint->setAttribute( mProgram, osg::StateAttribute::ON);

        // m_NewStateSetForHoodCarpaint->setTextureAttributeAndModes(0, l_floatTextureCubeIrradiance);
        // m_NewStateSetForHoodCarpaint->setTextureAttributeAndModes(1, l_floatTextureCubePrefilter);
        // m_NewStateSetForHoodCarpaint->setTextureAttributeAndModes(2, l_floatTextureBRFD);

        m_NewStateSetForHoodCarpaint->setTextureAttribute(0, l_floatTextureCubeIrradiance);
        m_NewStateSetForHoodCarpaint->setTextureAttribute(1, l_floatTextureCubePrefilter);
        m_NewStateSetForHoodCarpaint->setTextureAttribute(2, l_floatTextureBRFD);

        osg::ref_ptr<osg::Uniform> const mUniformTexture2 = new osg::Uniform("diffuseMap", 0);
        m_NewStateSetForHoodCarpaint->addUniform(mUniformTexture2);

        osg::ref_ptr<osg::Uniform> const mUniformTexture3 = new osg::Uniform("diffuse2Map", 1);
        m_NewStateSetForHoodCarpaint->addUniform(mUniformTexture3);

        osg::ref_ptr<osg::Uniform> const mUniformTexture1 = new osg::Uniform("lutMap", 2);
        m_NewStateSetForHoodCarpaint->addUniform(mUniformTexture1);

        // osg::ref_ptr<osg::Uniform> mUniformModelAlpha = new osg::Uniform("modelAlpha", 0.0f);
        // m_NewStateSetForHoodCarpaint->addUniform(mUniformModelAlpha);

        osg::ref_ptr<osg::Uniform> const mUniformModelDefaultTransparency = new osg::Uniform("modelDefaultTransparency", 1.0f);
        m_NewStateSetForHoodCarpaint->addUniform(mUniformModelDefaultTransparency);

        osg::ref_ptr<osg::Uniform> const mUniformSpecialColorIndexRendering = new osg::Uniform("specialColorIndexRendering", 1.0f);
        m_NewStateSetForHoodCarpaint->addUniform(mUniformSpecialColorIndexRendering);
    }
    // ========================================= carpaint hood end


    // ========================================= window  main glass start
    m_NewStateSetForMainWindowGlass = new osg::StateSet;
    m_NewStateSetForMainWindowGlass->setName("StateSet_roof_window_pbr_new");
    {
        m_NewStateSetForMainWindowGlass->setMode(GL_CULL_FACE, osg::StateAttribute::ON);

        // transparent
        m_NewStateSetForMainWindowGlass->setRenderingHint(osg::StateSet::RenderingHint::TRANSPARENT_BIN);
       // m_NewStateSetForMainWindowGlass->setRenderBinDetails(1000, "DepthSortedBin", osg::StateSet::RenderBinMode::USE_RENDERBIN_DETAILS);
        m_NewStateSetForMainWindowGlass->setRenderBinDetails(10, "DepthSortedBin", osg::StateSet::RenderBinMode::USE_RENDERBIN_DETAILS);

        osg::ref_ptr<osg::BlendFunc> const l_BlendFunc = new osg::BlendFunc;
        l_BlendFunc->setFunction(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
        m_NewStateSetForMainWindowGlass->setAttribute(l_BlendFunc, osg::StateAttribute::ON);
        m_NewStateSetForMainWindowGlass->setMode(GL_BLEND, osg::StateAttribute::ON);

        // osg::ref_ptr<osg::Depth> l_depth = new osg::Depth;
        // l_depth->setFunction(osg::Depth::Function::LEQUAL);
        // depth->setFunction(osg::Depth::Function::LESS);
        // depth->setWriteMask(false);
        // m_NewStateSetForMainWindowGlass->setAttributeAndModes(l_depth, osg::StateAttribute::ON);

        m_NewStateSetForMainWindowGlass->setAttribute( mProgram, osg::StateAttribute::ON);

        // m_NewStateSetForMainWindowGlass->setTextureAttributeAndModes(0, l_floatTextureCubeIrradiance);
        // m_NewStateSetForMainWindowGlass->setTextureAttributeAndModes(1, l_floatTextureCubePrefilter);
        // m_NewStateSetForMainWindowGlass->setTextureAttributeAndModes(2, l_floatTextureBRFD);

        m_NewStateSetForMainWindowGlass->setTextureAttribute(0, l_floatTextureCubeIrradiance);
        m_NewStateSetForMainWindowGlass->setTextureAttribute(1, l_floatTextureCubePrefilter);
        m_NewStateSetForMainWindowGlass->setTextureAttribute(2, l_floatTextureBRFD);

        osg::ref_ptr<osg::Uniform> const mUniformTexture2 = new osg::Uniform("diffuseMap", 0);
        m_NewStateSetForMainWindowGlass->addUniform(mUniformTexture2);

        osg::ref_ptr<osg::Uniform> const mUniformTexture3 = new osg::Uniform("diffuse2Map", 1);
        m_NewStateSetForMainWindowGlass->addUniform(mUniformTexture3);

        osg::ref_ptr<osg::Uniform> const mUniformTexture1 = new osg::Uniform("lutMap", 2);
        m_NewStateSetForMainWindowGlass->addUniform(mUniformTexture1);

        osg::ref_ptr<osg::Uniform> const mUniformColor = new osg::Uniform("diffuseColor", osg::Vec4(0.02f, 0.02f, 0.02f, 1.0f));
        m_NewStateSetForMainWindowGlass->addUniform(mUniformColor);
        osg::ref_ptr<osg::Uniform> const mUniformLightColor = new osg::Uniform("specColor1", osg::Vec4(0.03f, 0.03f, 0.03f, 1.0f));
        m_NewStateSetForMainWindowGlass->addUniform(mUniformLightColor);


        osg::ref_ptr<osg::Uniform> const mUniformLightPosition = new osg::Uniform("lightPos", osg::Vec3(0.0, 10.0f, 10.0f));
        m_NewStateSetForMainWindowGlass->addUniform(mUniformLightPosition);
        osg::ref_ptr<osg::Uniform> const mUniformMetallic = new osg::Uniform("specShininess1", 0.4f*1000.0f);
        m_NewStateSetForMainWindowGlass->addUniform(mUniformMetallic);
        osg::ref_ptr<osg::Uniform> const mUniformRoughness = new osg::Uniform("specShininess2", 0.8f*1000.0f);
        m_NewStateSetForMainWindowGlass->addUniform(mUniformRoughness);
        osg::ref_ptr<osg::Uniform> const mUniformReflactFactor = new osg::Uniform("brightness", 0.5f);
        m_NewStateSetForMainWindowGlass->addUniform(mUniformReflactFactor);
        osg::ref_ptr<osg::Uniform> const mUniformBrightnessFactor = new osg::Uniform("fresnel", 0.3f);
        m_NewStateSetForMainWindowGlass->addUniform(mUniformBrightnessFactor);
        osg::ref_ptr<osg::Uniform> const mUniformReflactFactorFinal = new osg::Uniform("reflectionPower", 1.0f);
        m_NewStateSetForMainWindowGlass->addUniform(mUniformReflactFactorFinal);

        // osg::ref_ptr<osg::Uniform> mUniformModelAlpha = new osg::Uniform("modelAlpha", 0.05f);
        // m_NewStateSetForMainWindowGlass->addUniform(mUniformModelAlpha);

        osg::ref_ptr<osg::Uniform> const mUniformModelDefaultTransparency = new osg::Uniform("modelDefaultTransparency", 0.98f);
        m_NewStateSetForMainWindowGlass->addUniform(mUniformModelDefaultTransparency);

        osg::ref_ptr<osg::Uniform> const mUniformSpecialColorIndexRendering = new osg::Uniform("specialColorIndexRendering", 0.0f);
        m_NewStateSetForMainWindowGlass->addUniform(mUniformSpecialColorIndexRendering);
    }
    // ========================================= window main glass end


    // ========================================= window  head glass start
    m_NewStateSetForHeadWindowGlass = new osg::StateSet;
    m_NewStateSetForHeadWindowGlass->setName("StateSet_roof_window_head_pbr_new");
    {
        m_NewStateSetForHeadWindowGlass->setMode(GL_CULL_FACE, osg::StateAttribute::ON);

        // transparent
        m_NewStateSetForHeadWindowGlass->setRenderingHint(osg::StateSet::RenderingHint::TRANSPARENT_BIN);
        // m_NewStateSetForHeadWindowGlass->setRenderBinDetails(1000, "DepthSortedBin", osg::StateSet::RenderBinMode::USE_RENDERBIN_DETAILS);
        m_NewStateSetForHeadWindowGlass->setRenderBinDetails(10, "DepthSortedBin", osg::StateSet::RenderBinMode::USE_RENDERBIN_DETAILS);

        osg::ref_ptr<osg::BlendFunc> const l_BlendFunc = new osg::BlendFunc;
        l_BlendFunc->setFunction(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
        m_NewStateSetForHeadWindowGlass->setAttribute(l_BlendFunc, osg::StateAttribute::ON);
        m_NewStateSetForHeadWindowGlass->setMode(GL_BLEND, osg::StateAttribute::ON);

        // osg::ref_ptr<osg::Depth> l_depth = new osg::Depth;
        // l_depth->setFunction(osg::Depth::Function::LEQUAL);
        // depth->setFunction(osg::Depth::Function::LESS);
        // depth->setWriteMask(false);
        // m_NewStateSetForHeadWindowGlass->setAttributeAndModes(l_depth, osg::StateAttribute::ON);

        m_NewStateSetForHeadWindowGlass->setAttribute( mProgram, osg::StateAttribute::ON);

        // m_NewStateSetForHeadWindowGlass->setTextureAttributeAndModes(0, l_floatTextureCubeIrradiance);
        // m_NewStateSetForHeadWindowGlass->setTextureAttributeAndModes(1, l_floatTextureCubePrefilter);
        // m_NewStateSetForHeadWindowGlass->setTextureAttributeAndModes(2, l_floatTextureBRFD);

        m_NewStateSetForHeadWindowGlass->setTextureAttribute(0, l_floatTextureCubeIrradiance);
        m_NewStateSetForHeadWindowGlass->setTextureAttribute(1, l_floatTextureCubePrefilter);
        m_NewStateSetForHeadWindowGlass->setTextureAttribute(2, l_floatTextureBRFD);

        osg::ref_ptr<osg::Uniform> const mUniformTexture2 = new osg::Uniform("diffuseMap", 0);
        m_NewStateSetForHeadWindowGlass->addUniform(mUniformTexture2);

        osg::ref_ptr<osg::Uniform> const mUniformTexture3 = new osg::Uniform("diffuse2Map", 1);
        m_NewStateSetForHeadWindowGlass->addUniform(mUniformTexture3);

        osg::ref_ptr<osg::Uniform> const mUniformTexture1 = new osg::Uniform("lutMap", 2);
        m_NewStateSetForHeadWindowGlass->addUniform(mUniformTexture1);

        osg::ref_ptr<osg::Uniform> const mUniformColor = new osg::Uniform("diffuseColor", osg::Vec4(0.02f, 0.02f, 0.02f, 1.0f));
        m_NewStateSetForHeadWindowGlass->addUniform(mUniformColor);
        osg::ref_ptr<osg::Uniform> const mUniformLightColor = new osg::Uniform("specColor1", osg::Vec4(0.03f, 0.03f, 0.03f, 1.0f));
        m_NewStateSetForHeadWindowGlass->addUniform(mUniformLightColor);


        osg::ref_ptr<osg::Uniform> const mUniformLightPosition = new osg::Uniform("lightPos", osg::Vec3(0.0, 10.0f, 10.0f));
        m_NewStateSetForHeadWindowGlass->addUniform(mUniformLightPosition);
        osg::ref_ptr<osg::Uniform> const mUniformMetallic = new osg::Uniform("specShininess1", 0.4f*1000.0f);
        m_NewStateSetForHeadWindowGlass->addUniform(mUniformMetallic);
        osg::ref_ptr<osg::Uniform> const mUniformRoughness = new osg::Uniform("specShininess2", 0.5f*1000.0f);
        m_NewStateSetForHeadWindowGlass->addUniform(mUniformRoughness);
        osg::ref_ptr<osg::Uniform> const mUniformReflactFactor = new osg::Uniform("brightness", 0.5f);
        m_NewStateSetForHeadWindowGlass->addUniform(mUniformReflactFactor);
        osg::ref_ptr<osg::Uniform> const mUniformBrightnessFactor = new osg::Uniform("fresnel", 0.3f);
        m_NewStateSetForHeadWindowGlass->addUniform(mUniformBrightnessFactor);
        osg::ref_ptr<osg::Uniform> const mUniformReflactFactorFinal = new osg::Uniform("reflectionPower", 1.0f);
        m_NewStateSetForHeadWindowGlass->addUniform(mUniformReflactFactorFinal);

        // osg::ref_ptr<osg::Uniform> mUniformModelAlpha = new osg::Uniform("modelAlpha", 0.05f);
        // m_NewStateSetForHeadWindowGlass->addUniform(mUniformModelAlpha);

        osg::ref_ptr<osg::Uniform> const mUniformModelDefaultTransparency = new osg::Uniform("modelDefaultTransparency", 0.93f);
        m_NewStateSetForHeadWindowGlass->addUniform(mUniformModelDefaultTransparency);

        osg::ref_ptr<osg::Uniform> const mUniformSpecialColorIndexRendering = new osg::Uniform("specialColorIndexRendering", 0.0f);
        m_NewStateSetForHeadWindowGlass->addUniform(mUniformSpecialColorIndexRendering);
    }
    // ========================================= window head glass end

} // end of void StateSetUpdater::initNewStateSetForPBRCarpaint()



} // end of common
} // end of assets
} // end of cc
