//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/assets/uielements/inc/TurnArroundOverlay.h"
#include "cc/imgui/inc/imgui_manager.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/assets/uielements/inc/Vehicle2DOverlay.h"
#include "cc/core/inc/CustomFramework.h"

#include "pc/generic/util/logging/inc/LoggingContexts.h"

#include <osgDB/ReadFile>
#include "cc/target/common/inc/commonInterface.h"

using pc::util::logging::g_AppContext;
using pc::vehicle::g_mechanicalData;

namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace uielements
{

pc::util::coding::Item<TurnArroundOverlaySettings> g_defaultSettings("TurnArroundSettings");

namespace
{

pc::assets::Icon* createIcon(const std::string& f_filepath)
{
    pc::assets::Icon* const l_icon = new pc::assets::Icon{f_filepath, true};
    // l_icon->setSize(osg::Vec2(l_image->s(), l_image->t()), pc::assets::Icon::UnitType::Pixel);
    l_icon->setOrigin(pc::assets::Icon::Origin::TopLeft);
    // l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Percentage);
    l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    // l_icon->setEnabled(false);
    return l_icon;
}

} // namespace

cc::assets::uielements::TurnArroundOverlay::TurnArroundOverlay(
    pc::core::Framework* f_framework,
    cc::core::AssetId                 f_assetId,
    const TurnArroundOverlaySettings& f_settings,
    std::string                       f_imagePath,
    osg::Vec2i                        f_iconPos,
    osg::Camera*                      f_referenceView)
    : pc::assets::ImageOverlays(f_assetId, f_referenceView) // PRQA S 2323
    , m_framework(f_framework)
    , m_settings(f_settings) // PRQA S 2323
    , m_icons() // PRQA S 2323
    , m_imagePath(f_imagePath)
    , m_imagePos(f_iconPos)
{
    init();
    m_modifiedCount = m_settings.getModifiedCount();
    const auto camera = static_cast<osg::Camera*>(getAsset());
    camera->setRenderOrder(osg::Camera::RenderOrder::NESTED_RENDER);
}

void TurnArroundOverlay::init()
{
    m_icons.clear(this);
    m_icons.addIcon(this, createIcon(CONCAT_PATH(m_imagePath)));

    m_icons.getIcon(CALIBRATE_VEHICLE)->setSize(osg::Vec2f{100.0f, 100.0f}, pc::assets::Icon::UnitType::Percentage);

     // ! Overlays
    //  m_icons.addIcon( this, createIcon(CONCAT_PATH(g_Vehicle2DSettings->m_gearStatusDrive)));
    //  m_icons.addIcon( this, createIcon(CONCAT_PATH(g_Vehicle2DSettings->m_gearStatusReverse)));
    //  m_icons.addIcon( this, createIcon(CONCAT_PATH(g_Vehicle2DSettings->m_gearStatusPark)));
    //  m_icons.addIcon( this, createIcon(CONCAT_PATH(g_Vehicle2DSettings->m_gearStatusNeutral)));
}

void TurnArroundOverlay::update(vfc::float64_t /*f_time*/)
{
    pc::daddy::EGear l_gear = pc::daddy::GEAR_INIT;
    // bool l_showGear = false;
    if (m_framework->asCustomFramework()->m_gearReceiver.hasData())
    {
        l_gear = static_cast<pc::daddy::EGear>(m_framework->m_gearReceiver.getData()->m_Data);
    }

    // if (m_framework->asCustomFramework()->m_CrabGuideline_ReceiverPort.hasData())
    // {
    //     const cc::daddy::CrabGuideline_t* const l_crabGuideline = m_framework->asCustomFramework()->m_CrabGuideline_ReceiverPort.getData();
    //     if (l_crabGuideline != nullptr)
    //     {
    //         l_showGear = l_crabGuideline->m_Data.m_isGearVisible;
    //     }
    // }
    m_icons.getIcon(CALIBRATE_VEHICLE)->setEnabled(true);
    // m_icons.getIcon(CALIBRATE_GEAR_D)->setEnabled(l_gear == pc::daddy::EGear::GEAR_D && l_showGear);
    // m_icons.getIcon(CALIBRATE_GEAR_R)->setEnabled(l_gear == pc::daddy::EGear::GEAR_R && l_showGear);
    // m_icons.getIcon(CALIBRATE_GEAR_P)->setEnabled(l_gear == pc::daddy::EGear::GEAR_P && l_showGear);
    // m_icons.getIcon(CALIBRATE_GEAR_N)->setEnabled(l_gear == pc::daddy::EGear::GEAR_N && l_showGear);
    osg::Vec2f position = {};
    if (m_referenceView != nullptr && m_referenceView->getViewport() != nullptr)
    {
        osg::Viewport* const l_viewport = m_referenceView->getViewport();
        position.x() = static_cast<vfc::float32_t>(m_imagePos.x());
        position.y() = static_cast<vfc::float32_t>(m_imagePos.y());
        position.x() += IMGUI_GET_SLIDER_INT("Settings", "TurnArroundIcon_X", -position.x(), position.x());
        position.y() += IMGUI_GET_SLIDER_INT("Settings", "TurnArroundIcon_Y", -position.y(), position.y());
        m_icons.getIcon(CALIBRATE_VEHICLE)->setPosition(position, pc::assets::Icon::UnitType::Pixel);
        // m_icons.getIcon(CALIBRATE_GEAR_D)->setPosition(position, pc::assets::Icon::UnitType::Pixel);
        // m_icons.getIcon(CALIBRATE_GEAR_R)->setPosition(position, pc::assets::Icon::UnitType::Pixel);
        // m_icons.getIcon(CALIBRATE_GEAR_P)->setPosition(position, pc::assets::Icon::UnitType::Pixel);
        // m_icons.getIcon(CALIBRATE_GEAR_N)->setPosition(position, pc::assets::Icon::UnitType::Pixel);
    }
    else
    {
        position.x() += IMGUI_GET_SLIDER_INT("Settings", "TurnArroundIcon_X", -5000, 5000);
        position.y() += IMGUI_GET_SLIDER_INT("Settings", "TurnArroundIcon_Y", -5000, 5000);
        m_icons.getIcon(CALIBRATE_VEHICLE)->setPosition(position, pc::assets::Icon::UnitType::Pixel);
        // m_icons.getIcon(CALIBRATE_GEAR_D)->setPosition(position, pc::assets::Icon::UnitType::Pixel);
        // m_icons.getIcon(CALIBRATE_GEAR_R)->setPosition(position, pc::assets::Icon::UnitType::Pixel);
        // m_icons.getIcon(CALIBRATE_GEAR_P)->setPosition(position, pc::assets::Icon::UnitType::Pixel);
        // m_icons.getIcon(CALIBRATE_GEAR_N)->setPosition(position, pc::assets::Icon::UnitType::Pixel);
    }
}

void TurnArroundOverlay::traverse(osg::NodeVisitor& f_nv)
{
    if (f_nv.getVisitorType() == osg::NodeVisitor::UPDATE_VISITOR)
    {
        if (m_modifiedCount != m_settings.getModifiedCount())
        {
            m_modifiedCount = m_settings.getModifiedCount();
            init();
        }
        update(f_nv.getFrameStamp()->getSimulationTime());
    }
    ImageOverlays::traverse(f_nv);
}

//!
//! ViewEnlargeCallback
//!
cc::assets::uielements::ViewEnlargeCallback::ViewEnlargeCallback(
    osg::Camera*               f_camera,
    pc::core::Framework*       /*f_framework*/,
    pc::virtcam::VirtualCamera f_camPos,
    const std::string&         f_name)
    : m_top()
    , m_bottom()
    , m_left()
    , m_right()
    , m_zNear()
    , m_zFar()
    , m_camPos(f_camPos)
{
    if (f_camera != nullptr)
    {
        setName(f_name);
        f_camera->getProjectionMatrixAsOrtho(m_left, m_right, m_bottom, m_top, m_zNear, m_zFar);
        IMGUI_SET_DEFAULT_SLIDER_FLOAT("Settings", "EnlargeFactor", 1.0f);
    }
}

void ViewEnlargeCallback::updatePlanView(osg::Camera* f_camera)
{
    if (nullptr == f_camera)
    {
        return;
    }
    double left = 0.0, right = 0.0, bottom = 0.0, top = 0.0, xOffset = 0.0, yOffset = 0.0;
    const float enlargeFactor = IMGUI_GET_SLIDER_FLOAT("Settings", "EnlargeFactor", 0.1f, 4.0f);
    if (IMGUI_GET_CHECKBOX_BOOL("Settings", "ResetEnlarge"))
    {
        left    = m_left;
        right   = m_right;
        bottom  = m_bottom;
        top     = m_top;
        xOffset = 0.0;
        yOffset = 0.0;
    }
    else
    {
        left    = m_left * enlargeFactor;
        right   = m_right * enlargeFactor;
        bottom  = m_bottom * enlargeFactor;
        top     = m_top * enlargeFactor;
        xOffset = IMGUI_GET_SLIDER_FLOAT("Settings", "Offset_X", -5.0f, 5.0f);
        yOffset = IMGUI_GET_SLIDER_FLOAT("Settings", "Offset_Y", -5.0f, 5.0f);
    }
    std::ostringstream os;
    os << "Left: " << left << ", "
       << "Right: " << right << ", "
       << "Top: " << top << ", "
       << "Bottom: " << bottom ;
    IMGUI_LOG("Settings", "Enlarge Status", os.str());

    f_camera->setProjectionMatrixAsOrtho2D(left, right, bottom, top);
    f_camera->setViewMatrix(
        osg::Matrixd::lookAt(m_camPos.m_eye, m_camPos.m_center, m_camPos.m_up) *
        osg::Matrixd::translate(xOffset, yOffset, 0.0));
}

void ViewEnlargeCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
    if (f_node == nullptr || f_nv == nullptr)
    {
        return;
    }
    if (f_nv->getVisitorType() == osg::NodeVisitor::UPDATE_VISITOR)
    {
        if (IMGUI_GET_CHECKBOX_BOOL("Settings", "TuningTurnArroundView"))
        {
            updatePlanView(f_node->asCamera());
        }
    }
    traverse(f_node, f_nv);
}

//!
//! TurnArroundProjectionUpdateVisitor
//!
TurnArroundProjectionUpdateVisitor::TurnArroundProjectionUpdateVisitor(const std::string f_imagePath)
    : osg::NodeVisitor(osg::NodeVisitor::TraversalMode::TRAVERSE_NONE) // PRQA S 2323 // PRQA S 4052
    , m_imagePath(f_imagePath) // PRQA S 2323
{
}

void TurnArroundProjectionUpdateVisitor::apply(osg::Node& f_node)
{
    osg::Camera* const camera = f_node.asCamera();
    if (camera == nullptr)
    {
        XLOG_ERROR(g_AppContext, "TurnArroundProjectionUpdateVisitor: invalid node");
        traverse(f_node);
        return;
    }
    osg::Viewport* const viewport = camera->getViewport();
    if (viewport == nullptr)
    {
        XLOG_ERROR(g_AppContext, "TurnArroundProjectionUpdateVisitor: invalid viewport");
        traverse(f_node);
        return;
    }

    osg::ref_ptr<osg::Image> const l_image = osgDB::readImageFile(CONCAT_PATH(m_imagePath));
    if (!l_image.valid())
    {
        XLOG_ERROR(g_AppContext, "TurnArroundProjectionUpdateVisitor: invalid image " << m_imagePath);
        traverse(f_node);
        return;
    }

    XLOG_INFO(g_AppContext, "TurnArroundProjectionUpdateVisitor: "
        << "Viewport Size: " << viewport->width() << ", " << viewport->height());

    XLOG_INFO(g_AppContext, "TurnArroundProjectionUpdateVisitor: "
        << "Image Size: " << l_image->s() << ", " << l_image->t());

    XLOG_INFO(g_AppContext, "TurnArroundProjectionUpdateVisitor: "
        << "Vehicle Width with mirror: " << g_mechanicalData->getWidthWithMirrors());

    const vfc::float64_t vehicleWidthPixel = static_cast<vfc::float64_t>(l_image->s()) * g_defaultSettings->m_scaleFactor;
    const vfc::float64_t vehicleWidthWithMirror = g_mechanicalData->getWidthWithMirrors();
    const vfc::float64_t viewportWidth = vehicleWidthWithMirror / vehicleWidthPixel * static_cast<vfc::float64_t>(viewport->width());
    const vfc::float64_t viewportHeight = viewportWidth * static_cast<vfc::float64_t>(viewport->height()) / static_cast<vfc::float64_t>(viewport->width());
    m_top = viewportHeight * 0.5f  + g_defaultSettings->m_viewportOffset;
    m_bottom = -viewportHeight * 0.5f + g_defaultSettings->m_viewportOffset;
    m_left = -viewportWidth * 0.5f ;
    m_right = viewportWidth * 0.5f ;

    camera->setProjectionMatrixAsOrtho2D(m_left, m_right, m_bottom, m_top);

    XLOG_INFO(g_AppContext, "TurnArroundProjectionUpdateVisitor: set view projection [" << camera->getName()
                               << "]: "
                               << "left: " << m_left << "right: " << m_right << "bottom: " << m_bottom
                               << "top: " << m_top);
}

//!
//! MeterPerPixelProjectionUpdateVisitor
//!
MeterPerPixelProjectionUpdateVisitor::MeterPerPixelProjectionUpdateVisitor(vfc::float64_t f_meterPerPixel)
    : osg::NodeVisitor(osg::NodeVisitor::TraversalMode::TRAVERSE_NONE) // PRQA S 2323 // PRQA S 4052
    , m_meterPerPixel(f_meterPerPixel)
{
}

void MeterPerPixelProjectionUpdateVisitor::apply(osg::Node& f_node)
{
    osg::Camera* const camera = f_node.asCamera();
    if (camera == nullptr)
    {
        XLOG_ERROR(g_AppContext, "MeterPerPixelProjectionUpdateVisitor: invalid node");
        traverse(f_node);
        return;
    }
    osg::Viewport* const viewport = camera->getViewport();
    if (viewport == nullptr)
    {
        XLOG_ERROR(g_AppContext, "MeterPerPixelProjectionUpdateVisitor: invalid viewport");
        traverse(f_node);
        return;
    }


    XLOG_INFO(g_AppContext, "MeterPerPixelProjectionUpdateVisitor: "
        << "Viewport Size: " << viewport->width() << ", " << viewport->height());

    const vfc::float64_t viewportWidth = m_meterPerPixel * static_cast<vfc::float64_t>(viewport->width());
    const vfc::float64_t viewportHeight = viewportWidth * static_cast<vfc::float64_t>(viewport->height()) / static_cast<vfc::float64_t>(viewport->width());
    m_top = viewportHeight * 0.5f  + g_defaultSettings->m_viewportOffset;
    m_bottom = -viewportHeight * 0.5f + g_defaultSettings->m_viewportOffset;
    m_left = -viewportWidth * 0.5f ;
    m_right = viewportWidth * 0.5f ;

    camera->setProjectionMatrixAsOrtho2D(m_left, m_right, m_bottom, m_top);

    XLOG_INFO(g_AppContext, "MeterPerPixelProjectionUpdateVisitor: set view projection [" << camera->getName()
                               << "]: "
                               << "left: " << m_left << "right: " << m_right << "bottom: " << m_bottom
                               << "top: " << m_top);
}


} // namespace uielements
} // namespace assets
} // namespace cc
