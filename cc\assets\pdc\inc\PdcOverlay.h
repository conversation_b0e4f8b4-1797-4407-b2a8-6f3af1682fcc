//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2021 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: NRCS2
//  Target systems: Cross platform
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: David Liepelt
//  Department: XC-DA/EPF5
//=============================================================================
/// @file  PdcOverlay.h
/// @brief PDC spline overlay geometry node implementation
//=============================================================================

#ifndef CC_ASSETS_PDC_PDCOVERLAY_H
#define CC_ASSETS_PDC_PDCOVERLAY_H

#include "cc/assets/pdc/inc/PdcModel.h"
#include "cc/assets/pdc/inc/PdcUpdateVisitor.h"
#include "pc/svs/core/inc/Asset.h"


#include <osg/Geode>
#include <osg/Array>

namespace cc
{
namespace assets
{
namespace pdc
{

///
/// PdcOverlay
///
class PdcOverlay : public osg::Geode
{
public:

  typedef osg::DrawElementsUShort IndexArray;
  typedef IndexArray::value_type IndexType;
  typedef osg::Vec3Array VertexArray;
  typedef osg::Vec4ubArray ColorArray;
  typedef ColorArray::value_type ColorVec;
  typedef ColorVec::value_type ColorComponent;


  /// @brief ASCII Diagram on sector information of the vehicle
  ///                 FRONT_ZONE_LEFT         FRONT_ZONE_RIGHT
  ///                              5    0 39    34
  ///                                   ^ Front of the Vehicle
  ///                             ______|______
  ///   LEFT_ZONE_START_INDEX  6 |      |      | 33     RIGHT_ZONE_END_INDEX
  ///                            |      |      |
  ///                            |      |      |
  ///                            |      |      |
  ///                            |      |      |
  ///   RIGHT_ZONE_END_INDEX  13 |______|______| 26     RIGHT_ZONE_START_INDEX
  ///                            14     |       25
  ///
  ///                  REAR_ZONE_LEFT         REAR_ZONE_RIGHT
  struct USSZones
  {
    enum PdcSectorStartingIndex : unsigned int
    {
      LEFT_ZONE_START_INDEX  = 6,
      LEFT_ZONE_END_INDEX    = 13,
      RIGHT_ZONE_START_INDEX = 26,
      RIGHT_ZONE_END_INDEX   = 33
    };
    static inline bool isFrontZone(unsigned int f_pdcSectorIndex)
    {
      return ((f_pdcSectorIndex<LEFT_ZONE_START_INDEX) || (f_pdcSectorIndex > RIGHT_ZONE_END_INDEX));
    }
    static inline bool isRearZone(unsigned int f_pdcSectorIndex)
    {
      return ((f_pdcSectorIndex>LEFT_ZONE_END_INDEX) && (f_pdcSectorIndex < RIGHT_ZONE_START_INDEX));
    }
    static inline bool isSideZone(unsigned int f_pdcSectorIndex)
    {
      return (((f_pdcSectorIndex>=LEFT_ZONE_START_INDEX) && (f_pdcSectorIndex <= LEFT_ZONE_END_INDEX))||((f_pdcSectorIndex>=RIGHT_ZONE_START_INDEX) && (f_pdcSectorIndex<=RIGHT_ZONE_END_INDEX)));
    }
  };

  enum : unsigned int
  {
    NUM_QUADS_BASE = PdcModel::NUM_SPLINE_POINTS_PER_SECTOR - 1,
    NUM_QUADS_UPRIGHT = NUM_QUADS_BASE,
    NUM_TRIANGLES_BASE = NUM_QUADS_BASE * 2, // each quad is made from two triangles
    NUM_TRIANGLES_UPRIGHT = NUM_QUADS_UPRIGHT *2,
    NUM_VERTICES_BASE = (2 * NUM_QUADS_BASE) + 2, // every added quad adds up two new vertices
    NUM_VERTICES_UPRIGHT = (2 * NUM_QUADS_UPRIGHT) + 2,
    NUM_VERTICES_PER_SECTOR = NUM_VERTICES_BASE + NUM_VERTICES_UPRIGHT,
    NUM_INDICES_BASE = NUM_TRIANGLES_BASE * 3,
    NUM_INDICES_UPRIGHT = NUM_TRIANGLES_UPRIGHT * 3,
    NUM_INDICES_PER_SECTOR = NUM_INDICES_BASE + NUM_INDICES_UPRIGHT
  };

  PdcOverlay() = default;
  PdcOverlay(const PdcOverlay& f_other, const osg::CopyOp& f_copyOp = osg::CopyOp::SHALLOW_COPY);

  META_Node(cc::assets::pdc, PdcOverlay);

  virtual void update(const PdcModel& f_model);

  ///
  /// Function to check if triangulation happens/ splines are drawn based on USS information
  /// Used only in unit tests
  /// Indices are filled if the splines are Visualised. Else they are not
  ///
  inline bool isSplineVisualised()
  {
    return (m_indicies->size()>0u);
  }


protected:

  virtual ~PdcOverlay() = default;

  osg::ref_ptr<VertexArray> m_vertices;
  osg::ref_ptr<IndexArray> m_indicies;
  osg::ref_ptr<ColorArray> m_colors;

  bool m_showOnlyFrontSectors{false};
  bool m_showOnlyRearSectors = false;
  vfc::float32_t m_vehicleTransparencyLevel = -1.0f;
  int  m_renderBinOrder;
private:

  PdcOverlay& operator = (const PdcOverlay&) = delete;

  void splineVisualization(const PdcModel& f_model);

  void sectorVisualization(const PdcModel& f_model);

  void init();


};

class PdcAsset : public pc::core::Asset
{
  public:
    PdcAsset(const cc::core::AssetId f_assetId, osg::Geode* f_asset);

  protected:
    virtual ~PdcAsset() = default;
};

} // namespace pdc
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PDC_PDCOVERLAY_H
