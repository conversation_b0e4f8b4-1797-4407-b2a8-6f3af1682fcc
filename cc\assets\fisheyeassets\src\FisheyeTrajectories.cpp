//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: JLR NFS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS JLR
/// @file  FisheyeWheelTrack.cpp
/// @brief
//=============================================================================

#include "cc/assets/fisheyeassets/inc/FisheyeTrajectories.h"
#include "cc/assets/trajectory/inc/TrajectoryAssets.h"
#include "cc/assets/trajectory/inc/MainLogic.h"
#include "cc/core/inc/CustomFramework.h"

#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/views/warpfisheyeview/inc/FisheyeModels.h"

namespace cc
{
namespace assets
{
namespace trajectory
{

const cc::assets::trajectory::DL1*           g_pDL1;
const cc::assets::trajectory::OutermostLine* g_leftOutermostLine;
const cc::assets::trajectory::OutermostLine* g_rightOutermostLine;
const cc::assets::trajectory::OutermostLine* g_leftOutermostLineColorful;
const cc::assets::trajectory::OutermostLine* g_rightOutermostLineColorful;

// constexpr vfc::uint32_t  g_numVerticesCoverPlate         = 48u;
// constexpr vfc::uint32_t  g_numLayoutPointsDL1            = 48u;
// constexpr vfc::uint32_t  g_numVerticesTrailerAssistLines = 48u;
} // namespace trajectory

namespace fisheyeassets
{

static osg::Texture2D* createFisheyeTexture(osg::Image* f_image)
{
    osg::Texture2D* const l_tex2D = new osg::Texture2D(f_image);
    l_tex2D->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_tex2D->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_tex2D->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_tex2D->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    l_tex2D->setResizeNonPowerOfTwoHint(false);
    l_tex2D->setUnRefImageDataAfterApply(true);
    return l_tex2D;
}

FisheyeWheelTrack::FisheyeWheelTrack( // PRQA S 6042
    cc::core::CustomFramework*                                           f_pCustomFramework,
    cc::assets::trajectory::commontypes::Side_en                         f_side,
    vfc::float32_t                                                       f_height,
    const cc::assets::trajectory::TrajectoryParams_st&                   f_trajParams,
    const cc::assets::trajectory::DL1* const                             f_distanceLine,
    vfc::uint32_t                                                        f_numOfVerts,
    WheelTrackType                                                       f_wheelTrackType,
    pc::core::sysconf::Cameras                                           f_camId,
    pc::views::warpfisheye::FisheyeModel*                                f_pModel,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
    : WheelTrack( // PRQA S 4050
          f_pCustomFramework,
          f_side,
          f_height,
          f_trajParams,
          f_distanceLine,
          f_numOfVerts,
          f_wheelTrackType)
    , m_camId(f_camId) // PRQA S 2323
    , m_pModel(f_pModel) // PRQA S 2323
    , m_framework(f_pCustomFramework) // PRQA S 2323
    , m_settings(f_settings) // PRQA S 2323
    , m_fisheyeVertices(nullptr) // PRQA S 2323
{
    // Extra 3D array containing fisheye coordinates
    m_fisheyeVertices = new osg::Vec3Array;
    m_geometry->setVertexArray(m_fisheyeVertices);

    osg::Image* const     l_texImage = WheelTrack::create1DTexture();
    osg::Texture2D* const l_tex2D    = createFisheyeTexture(l_texImage);

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setTextureAttribute(0u, l_tex2D);
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet); // PRQA S 3803
    osg::Depth* const l_depthStateAttrib = new osg::Depth();
    l_depthStateAttrib->setWriteMask(false);
    l_stateSet->setAttributeAndModes(l_depthStateAttrib);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);      // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(cc::assets::trajectory::RENDERBIN_ORDER_TRAJECTORY_WHEELTRACK, "RenderBin");
}

FisheyeWheelTrack::~FisheyeWheelTrack() = default;

void FisheyeWheelTrack::generateVertexData()
{
    // Generate vertex data in 3D (D70K)
    WheelTrack::generateVertexData_usingTexture();

    // Now overwrite the vertices to normalized virt cam coordinates
    if (true == m_framework->m_cameraCalibrationReceiver.isConnected())
    {
        const pc::daddy::CameraCalibrationDaddy* const l_pCalibDaddy = m_framework->m_cameraCalibrationReceiver.getData();
        if (nullptr != l_pCalibDaddy)
        {
            const vfc::uint32_t          l_camId_u = static_cast<vfc::uint32_t>(m_camId);
            const pc::c2w::SatCam& l_satCam  = l_pCalibDaddy->m_Data[l_camId_u];

            osg::Vec3f l_camTrans = l_satCam.getExtrinsicCalibration().getTranslation();

            osg::Vec3f l_virtCamRot(m_settings->m_virtualYaw, m_settings->m_virtualPitch, m_settings->m_virtualRoll);

            osg::Matrixf l_rotation;
            l_rotation.makeRotate(
                osg::inDegrees(-l_virtCamRot.x()),
                osg::Z_AXIS, // inverse of rotation matrix
                osg::inDegrees(-l_virtCamRot.y()),
                osg::Y_AXIS, // <=> rotation by opposite
                osg::inDegrees(-l_virtCamRot.z()),
                osg::X_AXIS); // angles in reverse order

            osg::Matrixf l_translate;
            l_translate.makeTranslate(-l_camTrans.x(), -l_camTrans.y(), -l_camTrans.z());

            const osg::Matrixf l_viewMatrix = l_translate * l_rotation;

            osg::Vec3Array* const l_vertices =
                dynamic_cast<osg::Vec3Array*>(m_geometry->getVertexArray()); // PRQA S 3077  // PRQA S 3400
            const vfc::uint32_t l_vSize = static_cast<vfc::uint32_t>(l_vertices->size());
            if (m_fisheyeVertices->size() != l_vSize)
            {
                m_fisheyeVertices->resize(l_vSize);
            }
            for (vfc::uint32_t i = 0u; i < l_vSize; ++i)
            {
                const osg::Vec3f l_vertex      = l_vertices->at(i);
                const osg::Vec3f l_vertexInCam = l_vertex * l_viewMatrix;

                osg::Vec2f l_normPxl     = m_pModel->getCam2Normalized(l_vertexInCam);
                m_fisheyeVertices->at(i) = osg::Vec3f(-0.5f, l_normPxl.x(), l_normPxl.y());
            }
            m_fisheyeVertices->dirty();
        }
    }
}

FisheyeTrailerAssistLine::FisheyeTrailerAssistLine( // PRQA S 6042
    cc::core::CustomFramework*                                           f_pCustomFramework,
    vfc::float32_t                                                       f_height,
    const cc::assets::trajectory::TrajectoryParams_st&                   f_trajParams,
    vfc::uint32_t                                                        f_numOfVerts,
    pc::core::sysconf::Cameras                                           f_camId,
    pc::views::warpfisheye::FisheyeModel*                                f_pModel,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
    : TrailerAssistLine(f_pCustomFramework, f_height, f_trajParams, f_numOfVerts)
    , m_camId(f_camId)
    , m_pModel(f_pModel)
    , m_framework(f_pCustomFramework)
    , m_settings(f_settings)
    , m_fisheyeVertices(nullptr)
{
    // Extra 3D array containing fisheye coordinates
    m_fisheyeVertices = new osg::Vec3Array;
    m_geometry->setVertexArray(m_fisheyeVertices);

    osg::Image* const     l_texImage = TrailerAssistLine::create1DTexture();
    osg::Texture2D* const l_tex2D    = createFisheyeTexture(l_texImage);

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setTextureAttribute(0u, l_tex2D);
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet); // PRQA S 3803
    osg::Depth* const l_depthStateAttrib = new osg::Depth();
    l_depthStateAttrib->setWriteMask(false);
    l_stateSet->setAttributeAndModes(l_depthStateAttrib);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);      // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(cc::assets::trajectory::RENDERBIN_ORDER_TRAJECTORY_WHEELTRACK, "RenderBin");
}

FisheyeTrailerAssistLine::~FisheyeTrailerAssistLine() = default;

void FisheyeTrailerAssistLine::generateVertexData()
{
    // Generate vertex data in 3D (D70K)
    TrailerAssistLine::generateVertexData_usingTexture();

    // Now overwrite the vertices to normalized virt cam coordinates
    if (true == m_framework->m_cameraCalibrationReceiver.isConnected())
    {
        const pc::daddy::CameraCalibrationDaddy* const l_pCalibDaddy = m_framework->m_cameraCalibrationReceiver.getData();
        if (nullptr != l_pCalibDaddy)
        {
            const vfc::uint32_t          l_camId_u = static_cast<vfc::uint32_t>(m_camId);
            const pc::c2w::SatCam& l_satCam  = l_pCalibDaddy->m_Data[l_camId_u];

            osg::Vec3f l_camTrans = l_satCam.getExtrinsicCalibration().getTranslation();

            osg::Vec3f l_virtCamRot(m_settings->m_virtualYaw, m_settings->m_virtualPitch, m_settings->m_virtualRoll);

            osg::Matrixf l_rotation;
            l_rotation.makeRotate(
                osg::inDegrees(-l_virtCamRot.x()),
                osg::Z_AXIS, // inverse of rotation matrix
                osg::inDegrees(-l_virtCamRot.y()),
                osg::Y_AXIS, // <=> rotation by opposite
                osg::inDegrees(-l_virtCamRot.z()),
                osg::X_AXIS); // angles in reverse order

            osg::Matrixf l_translate;
            l_translate.makeTranslate(-l_camTrans.x(), -l_camTrans.y(), -l_camTrans.z());

            const osg::Matrixf l_viewMatrix = l_translate * l_rotation;

            osg::Vec3Array* const l_vertices =
                dynamic_cast<osg::Vec3Array*>(m_geometry->getVertexArray()); // PRQA S 3077  // PRQA S 3400
            const vfc::uint32_t l_vSize = static_cast<vfc::uint32_t>(l_vertices->size());
            if (m_fisheyeVertices->size() != l_vSize)
            {
                m_fisheyeVertices->resize(l_vSize);
            }
            for (vfc::uint32_t i = 0u; i < l_vSize; ++i)
            {
                const osg::Vec3f l_vertex      = l_vertices->at(i);
                const osg::Vec3f l_vertexInCam = l_vertex * l_viewMatrix;

                osg::Vec2f l_normPxl     = m_pModel->getCam2Normalized(l_vertexInCam);
                m_fisheyeVertices->at(i) = osg::Vec3f(-0.5f, l_normPxl.x(), l_normPxl.y());
            }
            m_fisheyeVertices->dirty();
        }
    }
}

FisheyeOutermostLine::FisheyeOutermostLine( // PRQA S 6042
    cc::core::CustomFramework*                                           f_pCustomFramework,
    cc::assets::trajectory::commontypes::Side_en                         f_side,
    vfc::float32_t                                                       f_height,
    const cc::assets::trajectory::TrajectoryParams_st&                   f_trajParams,
    const cc::assets::trajectory::DIDescriptor_st&                       f_DIDescriptor,
    vfc::uint32_t                                                        f_numOfVerts_BeforeDIs,
    vfc::uint32_t                                                        f_numOfVerts_BetweenDIs,
    vfc::uint32_t                                                        f_numOfVerts_AfterDIs,
    pc::core::sysconf::Cameras                                           f_camId,
    pc::views::warpfisheye::FisheyeModel*                                f_pModel,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
    : OutermostLine( // PRQA S 4050
          f_pCustomFramework,
          f_side,
          f_height,
          f_trajParams,
          f_DIDescriptor,
          f_numOfVerts_BeforeDIs,
          f_numOfVerts_BetweenDIs,
          f_numOfVerts_AfterDIs)
    , m_camId(f_camId)
    , m_pModel(f_pModel)
    , m_framework(f_pCustomFramework)
    , m_settings(f_settings)
    , m_fisheyeVertices(nullptr) 
{
    // Extra 3D array containing fisheye coordinates
    m_fisheyeVertices = new osg::Vec3Array;
    m_geometry->setVertexArray(m_fisheyeVertices);

    osg::Image*     const l_texImage = OutermostLine::create1DTexture();
    osg::Texture2D* const l_tex2D    = createFisheyeTexture(l_texImage);

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setTextureAttribute(0u, l_tex2D);

    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet); // PRQA S 3803
    osg::Depth* const l_depthStateAttrib = new osg::Depth();
    l_depthStateAttrib->setWriteMask(false);
    l_stateSet->setAttributeAndModes(l_depthStateAttrib);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);      // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(cc::assets::trajectory::RENDERBIN_ORDER_TRAJECTORY_OUTERMOST_LINE, "RenderBin");
}

FisheyeOutermostLine::~FisheyeOutermostLine() = default;

void FisheyeOutermostLine::generateVertexData()
{
    // Generate vertex data in 3D (D70K)
    OutermostLine::generateVertexData_usingTexture();

    // Now overwrite the vertices to normalized virt cam coordinates
    if (true == m_framework->m_cameraCalibrationReceiver.isConnected())
    {
        const pc::daddy::CameraCalibrationDaddy* const l_pCalibDaddy = m_framework->m_cameraCalibrationReceiver.getData();
        if (nullptr != l_pCalibDaddy)
        {
            const vfc::uint32_t          l_camId_u = static_cast<vfc::uint32_t>(m_camId);
            const pc::c2w::SatCam& l_satCam  = l_pCalibDaddy->m_Data[l_camId_u];

            osg::Vec3f l_camTrans = l_satCam.getExtrinsicCalibration().getTranslation();

            osg::Vec3f l_virtCamRot(m_settings->m_virtualYaw, m_settings->m_virtualPitch, m_settings->m_virtualRoll);

            osg::Matrixf l_rotation;
            l_rotation.makeRotate(
                osg::inDegrees(-l_virtCamRot.x()),
                osg::Z_AXIS, // inverse of rotation matrix
                osg::inDegrees(-l_virtCamRot.y()),
                osg::Y_AXIS, // <=> rotation by opposite
                osg::inDegrees(-l_virtCamRot.z()),
                osg::X_AXIS); // angles in reverse order

            osg::Matrixf l_translate;
            l_translate.makeTranslate(-l_camTrans.x(), -l_camTrans.y(), -l_camTrans.z());

            const osg::Matrixf l_viewMatrix = l_translate * l_rotation;

            osg::Vec3Array* const l_vertices =
                dynamic_cast<osg::Vec3Array*>(m_geometry->getVertexArray()); // PRQA S 3077  // PRQA S 3400
            const vfc::uint32_t l_vSize = static_cast<vfc::uint32_t>(l_vertices->size());
            if (m_fisheyeVertices->size() != l_vSize)
            {
                m_fisheyeVertices->resize(l_vSize);
            }
            for (vfc::uint32_t i = 0u; i < l_vSize; ++i)
            {
                const osg::Vec3f l_vertex      = l_vertices->at(i);
                const osg::Vec3f l_vertexInCam = l_vertex * l_viewMatrix;

                osg::Vec2f l_normPxl     = m_pModel->getCam2Normalized(l_vertexInCam);
                m_fisheyeVertices->at(i) = osg::Vec3f(-0.5f, l_normPxl.x(), l_normPxl.y());
            }
            m_fisheyeVertices->dirty();
        }
    }
}

FisheyeOutermostLineColorful::FisheyeOutermostLineColorful( // PRQA S 6042
    cc::core::CustomFramework*                                           f_pCustomFramework,
    cc::assets::trajectory::commontypes::Side_en                         f_side,
    vfc::float32_t                                                       f_height,
    const cc::assets::trajectory::TrajectoryParams_st&                   f_trajParams,
    const cc::assets::trajectory::DIDescriptor_st&                       f_DIDescriptor,
    vfc::uint32_t                                                        f_numOfVerts_BeforeDIs,
    vfc::uint32_t                                                        f_numOfVerts_BetweenDIs,
    vfc::uint32_t                                                        f_numOfVerts_AfterDIs,
    pc::core::sysconf::Cameras                                           f_camId,
    pc::views::warpfisheye::FisheyeModel*                                f_pModel,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
    : OutermostLineColorful( // PRQA S 4050
          f_pCustomFramework,
          f_side,
          f_height,
          f_trajParams,
          f_DIDescriptor,
          f_numOfVerts_BeforeDIs,
          f_numOfVerts_BetweenDIs,
          f_numOfVerts_AfterDIs)
    , m_camId(f_camId)
    , m_pModel(f_pModel)
    , m_framework(f_pCustomFramework)
    , m_settings(f_settings)
    , m_fisheyeVertices(nullptr) 
{
    // Extra 3D array containing fisheye coordinates
    m_fisheyeVertices = new osg::Vec3Array;
    m_geometry->setVertexArray(m_fisheyeVertices);

    osg::Image*     const l_texImage = OutermostLineColorful::create1DTexture();
    osg::Texture2D* const l_tex2D    = createFisheyeTexture(l_texImage);

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setTextureAttribute(0u, l_tex2D);

    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet); // PRQA S 3803
    osg::Depth* const l_depthStateAttrib = new osg::Depth();
    l_depthStateAttrib->setWriteMask(false);
    l_stateSet->setAttributeAndModes(l_depthStateAttrib);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);      // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(cc::assets::trajectory::RENDERBIN_ORDER_TRAJECTORY_OUTERMOST_LINE, "RenderBin");
}

FisheyeOutermostLineColorful::~FisheyeOutermostLineColorful() = default;

void FisheyeOutermostLineColorful::generateVertexData()
{
    // Generate vertex data in 3D (D70K)
    OutermostLineColorful::generateVertexData_usingTexture();

    // Now overwrite the vertices to normalized virt cam coordinates
    if (true == m_framework->m_cameraCalibrationReceiver.isConnected())
    {
        const pc::daddy::CameraCalibrationDaddy* const l_pCalibDaddy = m_framework->m_cameraCalibrationReceiver.getData();
        if (nullptr != l_pCalibDaddy)
        {
            const vfc::uint32_t          l_camId_u = static_cast<vfc::uint32_t>(m_camId);
            const pc::c2w::SatCam& l_satCam  = l_pCalibDaddy->m_Data[l_camId_u];

            osg::Vec3f l_camTrans = l_satCam.getExtrinsicCalibration().getTranslation();

            osg::Vec3f l_virtCamRot(m_settings->m_virtualYaw, m_settings->m_virtualPitch, m_settings->m_virtualRoll);

            osg::Matrixf l_rotation;
            l_rotation.makeRotate(
                osg::inDegrees(-l_virtCamRot.x()),
                osg::Z_AXIS, // inverse of rotation matrix
                osg::inDegrees(-l_virtCamRot.y()),
                osg::Y_AXIS, // <=> rotation by opposite
                osg::inDegrees(-l_virtCamRot.z()),
                osg::X_AXIS); // angles in reverse order

            osg::Matrixf l_translate;
            l_translate.makeTranslate(-l_camTrans.x(), -l_camTrans.y(), -l_camTrans.z());

            const osg::Matrixf l_viewMatrix = l_translate * l_rotation;

            osg::Vec3Array* const l_vertices =
                dynamic_cast<osg::Vec3Array*>(m_geometry->getVertexArray()); // PRQA S 3077  // PRQA S 3400
            const vfc::uint32_t l_vSize = static_cast<vfc::uint32_t>(l_vertices->size());
            if (m_fisheyeVertices->size() != l_vSize)
            {
                m_fisheyeVertices->resize(l_vSize);
            }
            for (vfc::uint32_t i = 0u; i < l_vSize; ++i)
            {
                const osg::Vec3f l_vertex      = l_vertices->at(i);
                const osg::Vec3f l_vertexInCam = l_vertex * l_viewMatrix;

                osg::Vec2f l_normPxl     = m_pModel->getCam2Normalized(l_vertexInCam);
                m_fisheyeVertices->at(i) = osg::Vec3f(-0.5f, l_normPxl.x(), l_normPxl.y());
            }
            m_fisheyeVertices->dirty();
        }
    }
}

FisheyeCoverPlate::FisheyeCoverPlate(
    cc::core::CustomFramework*                                           f_pCustomFramework,
    vfc::float32_t                                                       f_height,
    const cc::assets::trajectory::TrajectoryParams_st&                   f_trajParams,
    vfc::uint32_t                                                        f_numOfVerts,
    pc::core::sysconf::Cameras                                           f_camId,
    pc::views::warpfisheye::FisheyeModel*                                f_pModel,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
    : CoverPlate( // PRQA S 4050
          f_pCustomFramework,
          f_height,
          f_trajParams,
          f_numOfVerts)
    , m_camId(f_camId)
    , m_pModel(f_pModel)
    , m_framework(f_pCustomFramework)
    , m_settings(f_settings)
    , m_fisheyeVertices(nullptr) 
{
    // Extra 3D array containing fisheye coordinates
    m_fisheyeVertices = new osg::Vec3Array;
    m_geometry->setVertexArray(m_fisheyeVertices);

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);      // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(cc::assets::trajectory::RENDERBIN_ORDER_TRAJECTORY_COVER_PLATE, "RenderBin");
}

FisheyeCoverPlate::~FisheyeCoverPlate() = default;

void FisheyeCoverPlate::generateVertexData()
{
    // Generate vertex data in 3D (D70K)
    CoverPlate::generateVertexData();

    // Now overwrite the vertices to normalized virt cam coordinates
    if (true == m_framework->m_cameraCalibrationReceiver.isConnected())
    {
        const pc::daddy::CameraCalibrationDaddy* const l_pCalibDaddy = m_framework->m_cameraCalibrationReceiver.getData();
        if (nullptr != l_pCalibDaddy)
        {
            const vfc::uint32_t          l_camId_u = static_cast<vfc::uint32_t>(m_camId);
            const pc::c2w::SatCam& l_satCam  = l_pCalibDaddy->m_Data[l_camId_u];

            osg::Vec3f l_camTrans = l_satCam.getExtrinsicCalibration().getTranslation();

            osg::Vec3f l_virtCamRot(m_settings->m_virtualYaw, m_settings->m_virtualPitch, m_settings->m_virtualRoll);

            osg::Matrixf l_rotation;
            l_rotation.makeRotate(
                osg::inDegrees(-l_virtCamRot.x()),
                osg::Z_AXIS, // inverse of rotation matrix
                osg::inDegrees(-l_virtCamRot.y()),
                osg::Y_AXIS, // <=> rotation by opposite
                osg::inDegrees(-l_virtCamRot.z()),
                osg::X_AXIS); // angles in reverse order

            osg::Matrixf l_translate;
            l_translate.makeTranslate(-l_camTrans.x(), -l_camTrans.y(), -l_camTrans.z());

            const osg::Matrixf l_viewMatrix = l_translate * l_rotation;

            osg::Vec3Array* const l_vertices =
                dynamic_cast<osg::Vec3Array*>(m_geometry->getVertexArray()); // PRQA S 3077  // PRQA S 3400
            const vfc::uint32_t l_vSize = static_cast<vfc::uint32_t>(l_vertices->size());
            if (m_fisheyeVertices->size() != l_vSize)
            {
                m_fisheyeVertices->resize(l_vSize);
            }
            for (vfc::uint32_t i = 0u; i < l_vSize; ++i)
            {
                const osg::Vec3f l_vertex      = l_vertices->at(i);
                const osg::Vec3f l_vertexInCam = l_vertex * l_viewMatrix;

                osg::Vec2f l_normPxl     = m_pModel->getCam2Normalized(l_vertexInCam);
                m_fisheyeVertices->at(i) = osg::Vec3f(-0.5f, l_normPxl.x(), l_normPxl.y());
            }
            m_fisheyeVertices->dirty();
        }
    }
}

FisheyeDL1::FisheyeDL1(
    cc::core::CustomFramework*                                           f_pCustomFramework,
    vfc::float32_t                                                       f_height,
    const cc::assets::trajectory::TrajectoryParams_st&                   f_trajParams,
    const assets::trajectory::OutermostLine* const                       f_leftOutermostLine,
    const assets::trajectory::OutermostLine* const                       f_rightOutermostLine,
    vfc::uint32_t                                                        f_numLayoutPoints,
    pc::core::sysconf::Cameras                                           f_camId,
    pc::views::warpfisheye::FisheyeModel*                                f_pModel,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
    : DL1(
          f_pCustomFramework,
          f_height,
          f_trajParams,
          f_leftOutermostLine,
          f_rightOutermostLine,
          f_numLayoutPoints)
    , m_camId(f_camId)
    , m_pModel(f_pModel)
    , m_framework(f_pCustomFramework)
    , m_settings(f_settings)
    , m_fisheyeVertices(nullptr) 
{
    // Extra 3D array containing fisheye coordinates
    m_fisheyeVertices = new osg::Vec3Array;
    m_geometry->setVertexArray(m_fisheyeVertices);

    osg::Image*     const l_texImage = DL1::create1DTexture();
    osg::Texture2D* const l_tex2D    = new osg::Texture2D(l_texImage);

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setTextureAttribute(0u, l_tex2D);
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet);                           // PRQA S 3803
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);       // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(cc::assets::trajectory::RENDERBIN_ORDER_TRAJECTORY_DL1, "RenderBin");
}

FisheyeDL1::~FisheyeDL1() = default;

void FisheyeDL1::generateVertexData()
{
    // Generate vertex data in 3D (D70K)
    DL1::generateVertexData_usingTexture();

    // Now overwrite the vertices to normalized virt cam coordinates
    if (true == m_framework->m_cameraCalibrationReceiver.isConnected())
    {
        const pc::daddy::CameraCalibrationDaddy* const l_pCalibDaddy = m_framework->m_cameraCalibrationReceiver.getData();
        if (nullptr != l_pCalibDaddy)
        {
            const vfc::uint32_t          l_camId_u = static_cast<vfc::uint32_t>(m_camId);
            const pc::c2w::SatCam& l_satCam  = l_pCalibDaddy->m_Data[l_camId_u];

            osg::Vec3f l_camTrans = l_satCam.getExtrinsicCalibration().getTranslation();

            osg::Vec3f l_virtCamRot(m_settings->m_virtualYaw, m_settings->m_virtualPitch, m_settings->m_virtualRoll);

            osg::Matrixf l_rotation;
            l_rotation.makeRotate(
                osg::inDegrees(-l_virtCamRot.x()),
                osg::Z_AXIS, // inverse of rotation matrix
                osg::inDegrees(-l_virtCamRot.y()),
                osg::Y_AXIS, // <=> rotation by opposite
                osg::inDegrees(-l_virtCamRot.z()),
                osg::X_AXIS); // angles in reverse order

            osg::Matrixf l_translate;
            l_translate.makeTranslate(-l_camTrans.x(), -l_camTrans.y(), -l_camTrans.z());

            const osg::Matrixf l_viewMatrix = l_translate * l_rotation;

            osg::Vec3Array* const l_vertices =
                dynamic_cast<osg::Vec3Array*>(m_geometry->getVertexArray()); // PRQA S 3077  // PRQA S 3400
            const vfc::uint32_t l_vSize = static_cast<vfc::uint32_t>(l_vertices->size());
            if (m_fisheyeVertices->size() != l_vSize)
            {
                m_fisheyeVertices->resize(l_vSize);
            }
            for (vfc::uint32_t i = 0u; i < l_vSize; ++i)
            {
                const osg::Vec3f l_vertex      = l_vertices->at(i);
                const osg::Vec3f l_vertexInCam = l_vertex * l_viewMatrix;

                osg::Vec2f l_normPxl     = m_pModel->getCam2Normalized(l_vertexInCam);
                m_fisheyeVertices->at(i) = osg::Vec3f(-0.5f, l_normPxl.x(), l_normPxl.y());
            }
            m_fisheyeVertices->dirty();
        }
    }
}

FisheyeRctaOverlay::FisheyeRctaOverlay(
    cc::core::CustomFramework*                                           f_pCustomFramework,
    pc::core::sysconf::Cameras                                           f_camId,
    pc::views::warpfisheye::FisheyeModel*                                f_pModel,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
    : cc::assets::rctaoverlay::RctaOverlay(f_pCustomFramework)
    , m_camId(f_camId)
    , m_pModel(f_pModel)
    , m_framework(f_pCustomFramework)
    , m_settings(f_settings)
    , m_fisheyeRctaLeftVertices(nullptr)
    , m_fisheyeRctaRightVertices(nullptr)
    , m_fisheyeFctaLeftVertices(nullptr)
    , m_fisheyeFctaRightVertices(nullptr)
{
    // Extra 3D array containing fisheye coordinates
    m_fisheyeRctaLeftVertices  = new osg::Vec3Array;
    m_fisheyeRctaRightVertices = new osg::Vec3Array;
    m_fisheyeFctaLeftVertices  = new osg::Vec3Array;
    m_fisheyeFctaRightVertices = new osg::Vec3Array;
}

FisheyeRctaOverlay::~FisheyeRctaOverlay() = default;

void FisheyeRctaOverlay::init() // PRQA S 6043
{
    RctaOverlay::init();

    osg::Geometry* const l_fctaLeft =
        dynamic_cast<osg::Geometry*>(m_FCTALeftGeode->getDrawable(0u)); // PRQA S 3077  // PRQA S 3400
    osg::Geometry* const l_fctaRight =
        dynamic_cast<osg::Geometry*>(m_FCTARightGeode->getDrawable(0u)); // PRQA S 3077  // PRQA S 3400
    osg::Geometry* const l_rctaLeft =
        dynamic_cast<osg::Geometry*>(m_RCTALeftGeode->getDrawable(0u)); // PRQA S 3077  // PRQA S 3400
    osg::Geometry* const l_rctaRight =
        dynamic_cast<osg::Geometry*>(m_RCTARightGeode->getDrawable(0u)); // PRQA S 3077  // PRQA S 3400
    // Now overwrite the vertices to normalized virt cam coordinates
    if (true == m_framework->m_cameraCalibrationReceiver.isConnected())
    {
        const pc::daddy::CameraCalibrationDaddy* const l_pCalibDaddy = m_framework->m_cameraCalibrationReceiver.getData();
        if (nullptr != l_pCalibDaddy)
        {
            const vfc::uint32_t          l_camId_u = static_cast<vfc::uint32_t>(m_camId);
            const pc::c2w::SatCam& l_satCam  = l_pCalibDaddy->m_Data[l_camId_u];

            osg::Vec3f l_camTrans = l_satCam.getExtrinsicCalibration().getTranslation();

            osg::Vec3f l_virtCamRot(m_settings->m_virtualYaw, m_settings->m_virtualPitch, m_settings->m_virtualRoll);

            osg::Matrixf l_rotation;
            l_rotation.makeRotate(
                osg::inDegrees(-l_virtCamRot.x()),
                osg::Z_AXIS, // inverse of rotation matrix
                osg::inDegrees(-l_virtCamRot.y()),
                osg::Y_AXIS, // <=> rotation by opposite
                osg::inDegrees(-l_virtCamRot.z()),
                osg::X_AXIS); // angles in reverse order

            osg::Matrixf l_translate;
            l_translate.makeTranslate(-l_camTrans.x(), -l_camTrans.y(), -l_camTrans.z());

            const osg::Matrixf l_viewMatrix = l_translate * l_rotation;

            if (pc::core::sysconf::REAR_CAMERA == m_camId)
            {
                // ! Transiform the RctaLeft
                osg::Vec3Array* const l_verticesRctaLeft =
                    dynamic_cast<osg::Vec3Array*>(l_rctaLeft->getVertexArray()); // PRQA S 3077  // PRQA S 3400
                const vfc::uint32_t l_vSizeRctaLeft = static_cast<vfc::uint32_t>(l_verticesRctaLeft->size());
                if (m_fisheyeRctaLeftVertices->size() != l_vSizeRctaLeft)
                {
                    m_fisheyeRctaLeftVertices->resize(l_vSizeRctaLeft);
                }
                for (vfc::uint32_t i = 0u; i < l_vSizeRctaLeft; ++i)
                {
                    const osg::Vec3f l_vertex      = l_verticesRctaLeft->at(i);
                    const osg::Vec3f l_vertexInCam = l_vertex * l_viewMatrix;

                    osg::Vec2f l_normPxl             = m_pModel->getCam2Normalized(l_vertexInCam);
                    m_fisheyeRctaLeftVertices->at(i) = osg::Vec3f(-0.5f, l_normPxl.x(), l_normPxl.y());
                }
                l_rctaLeft->setVertexArray(m_fisheyeRctaLeftVertices);

                // ! Transiform the RctaRight
                osg::Vec3Array* const l_verticesRctaRight =
                    dynamic_cast<osg::Vec3Array*>(l_rctaRight->getVertexArray()); // PRQA S 3077  // PRQA S 3400
                const vfc::uint32_t l_vSizeRctaRight = static_cast<vfc::uint32_t>(l_verticesRctaRight->size());
                if (m_fisheyeRctaRightVertices->size() != l_vSizeRctaRight)
                {
                    m_fisheyeRctaRightVertices->resize(l_vSizeRctaRight);
                }
                for (vfc::uint32_t i = 0u; i < l_vSizeRctaRight; ++i)
                {
                    const osg::Vec3f l_vertex      = l_verticesRctaRight->at(i);
                    const osg::Vec3f l_vertexInCam = l_vertex * l_viewMatrix;

                    osg::Vec2f l_normPxl              = m_pModel->getCam2Normalized(l_vertexInCam);
                    m_fisheyeRctaRightVertices->at(i) = osg::Vec3f(-0.5f, l_normPxl.x(), l_normPxl.y());
                }
                l_rctaRight->setVertexArray(m_fisheyeRctaRightVertices);
            }
            else if (pc::core::sysconf::FRONT_CAMERA == m_camId)
            {
                // ! Transiform the FctaLeft
                osg::Vec3Array* const l_verticesFctaLeft =
                    dynamic_cast<osg::Vec3Array*>(l_fctaLeft->getVertexArray()); // PRQA S 3077  // PRQA S 3400
                const vfc::uint32_t l_vSizeFctaLeft = static_cast<vfc::uint32_t>(l_verticesFctaLeft->size());
                if (m_fisheyeFctaLeftVertices->size() != l_vSizeFctaLeft)
                {
                    m_fisheyeFctaLeftVertices->resize(l_vSizeFctaLeft);
                }
                else
                {
                    // Do nothing
                }
                for (vfc::uint32_t i = 0u; i < l_vSizeFctaLeft; ++i)
                {
                    const osg::Vec3f l_vertex      = l_verticesFctaLeft->at(i);
                    const osg::Vec3f l_vertexInCam = l_vertex * l_viewMatrix;

                    osg::Vec2f l_normPxl             = m_pModel->getCam2Normalized(l_vertexInCam);
                    m_fisheyeFctaLeftVertices->at(i) = osg::Vec3f(-0.5f, l_normPxl.x(), l_normPxl.y());
                }
                l_fctaLeft->setVertexArray(m_fisheyeFctaLeftVertices);

                // ! Transiform the FctaRight
                osg::Vec3Array* const l_verticesFctaRight =
                    dynamic_cast<osg::Vec3Array*>(l_fctaRight->getVertexArray()); // PRQA S 3077  // PRQA S 3400
                const vfc::uint32_t l_vSizeFctaRight = static_cast<vfc::uint32_t>(l_verticesFctaRight->size());
                if (m_fisheyeFctaRightVertices->size() != l_vSizeFctaRight)
                {
                    m_fisheyeFctaRightVertices->resize(l_vSizeFctaRight);
                }
                else
                {
                    // Do nothing
                }
                for (vfc::uint32_t i = 0u; i < l_vSizeFctaRight; ++i)
                {
                    const osg::Vec3f l_vertex      = l_verticesFctaRight->at(i);
                    const osg::Vec3f l_vertexInCam = l_vertex * l_viewMatrix;

                    osg::Vec2f l_normPxl              = m_pModel->getCam2Normalized(l_vertexInCam);
                    m_fisheyeFctaRightVertices->at(i) = osg::Vec3f(-0.5f, l_normPxl.x(), l_normPxl.y());
                }
                l_fctaRight->setVertexArray(m_fisheyeFctaRightVertices);
            }
            else
            {
                // Do nothing
            }
        }
    }
}

FisheyeTrailerHitchTrajectory::FisheyeTrailerHitchTrajectory(
    cc::core::CustomFramework*                                           f_pCustomFramework,
    const cc::assets::trajectory::TrajectoryParams_st&                   f_trajParams,
    vfc::uint32_t                                                        f_numOfVerts,
    pc::core::sysconf::Cameras                                           f_camId,
    pc::views::warpfisheye::FisheyeModel*                                f_pModel,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
    : TrailerHitchTrajectory(
          f_trajParams,
          f_numOfVerts,
          f_pCustomFramework)
    , m_camId(f_camId)
    , m_pModel(f_pModel)
    , m_framework(f_pCustomFramework)
    , m_settings(f_settings)
    , m_fisheyeVertices(nullptr)
{
    // Extra 3D array containing fisheye coordinates
    m_fisheyeVertices = new osg::Vec3Array;
    m_geometry->setVertexArray(m_fisheyeVertices);

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);      // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(cc::assets::trajectory::RENDERBIN_ORDER_TRAJECTORY_TRAILER_HITCH_LINE, "RenderBin");
}

FisheyeTrailerHitchTrajectory::~FisheyeTrailerHitchTrajectory() = default;

void FisheyeTrailerHitchTrajectory::generateVertexData()
{
    // Generate vertex data in 3D (D70K)
    TrailerHitchTrajectory::generateVertexData();

    // Now overwrite the vertices to normalized virt cam coordinates
    if (true == m_framework->m_cameraCalibrationReceiver.isConnected())
    {
        const pc::daddy::CameraCalibrationDaddy* const l_pCalibDaddy = m_framework->m_cameraCalibrationReceiver.getData();
        if (nullptr != l_pCalibDaddy)
        {
            const vfc::uint32_t          l_camId_u = static_cast<vfc::uint32_t>(m_camId);
            const pc::c2w::SatCam& l_satCam  = l_pCalibDaddy->m_Data[l_camId_u];

            osg::Vec3f l_camTrans = l_satCam.getExtrinsicCalibration().getTranslation();

            osg::Vec3f l_virtCamRot(m_settings->m_virtualYaw, m_settings->m_virtualPitch, m_settings->m_virtualRoll);

            osg::Matrixf l_rotation;
            l_rotation.makeRotate(
                osg::inDegrees(-l_virtCamRot.x()),
                osg::Z_AXIS, // inverse of rotation matrix
                osg::inDegrees(-l_virtCamRot.y()),
                osg::Y_AXIS, // <=> rotation by opposite
                osg::inDegrees(-l_virtCamRot.z()),
                osg::X_AXIS); // angles in reverse order

            osg::Matrixf l_translate;
            l_translate.makeTranslate(-l_camTrans.x(), -l_camTrans.y(), -l_camTrans.z());

            const osg::Matrixf l_viewMatrix = l_translate * l_rotation;

            osg::Vec3Array* const l_vertices =
                dynamic_cast<osg::Vec3Array*>(m_geometry->getVertexArray()); // PRQA S 3077  // PRQA S 3400
            const vfc::uint32_t l_vSize = static_cast<vfc::uint32_t>(l_vertices->size());
            if (m_fisheyeVertices->size() != l_vSize)
            {
                m_fisheyeVertices->resize(l_vSize);
            }
            for (vfc::uint32_t i = 0u; i < l_vSize; ++i)
            {
                const osg::Vec3f l_vertex      = l_vertices->at(i);
                const osg::Vec3f l_vertexInCam = l_vertex * l_viewMatrix;

                osg::Vec2f l_normPxl     = m_pModel->getCam2Normalized(l_vertexInCam);
                m_fisheyeVertices->at(i) = osg::Vec3f(-0.5f, l_normPxl.x(), l_normPxl.y());
            }
            m_fisheyeVertices->dirty();
        }
    }
}

osg::Group* createFisheyeWheelTracks(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::WheelTrack* const l_left_WheelTrack_Whole = new assets::fisheyeassets::FisheyeWheelTrack(
        f_framework,
        cc::assets::trajectory::commontypes::Left_enm,
        trajectory::g_height,
        trajectory::g_trajParams,
        trajectory::g_pDL1,
        trajectory::g_numVerticesWheelTracks,
        assets::trajectory::WheelTrack::WHOLE,
        f_cam,
        f_model,
        f_settings);
    l_left_WheelTrack_Whole->setName("WheelTrack Left");

    assets::trajectory::WheelTrack* const l_right_WheelTrack_Whole = new assets::fisheyeassets::FisheyeWheelTrack(
        f_framework,
        cc::assets::trajectory::commontypes::Right_enm,
        trajectory::g_height,
        trajectory::g_trajParams,
        trajectory::g_pDL1,
        trajectory::g_numVerticesWheelTracks,
        assets::trajectory::WheelTrack::WHOLE,
        f_cam,
        f_model,
        f_settings);
    l_right_WheelTrack_Whole->setName("WheelTrack Right");

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_left_WheelTrack_Whole);  // PRQA S 3803
    l_group->addChild(l_right_WheelTrack_Whole); // PRQA S 3803

    l_group->setName("FisheyeWheelTracks");

    return l_group;
}

osg::Group* createFisheyeTrailerAssistLines(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::TrailerAssistLine* const l_left_TrailerAssistLine =
        new assets::fisheyeassets::FisheyeTrailerAssistLine(
            f_framework,
            trajectory::g_height,
            trajectory::g_trajParams,
            trajectory::g_numVerticesTrailerAssistLines,
            f_cam,
            f_model,
            f_settings);
    l_left_TrailerAssistLine->setName("TrailerAssistLine");

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_left_TrailerAssistLine); // PRQA S 3803
    l_group->setName("FisheyeTrailerAssistLines");

    return l_group;
}

osg::Group* createFisheyeTrailerHitchTrajectory(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::TrailerHitchTrajectory* const l_left_TrailerAssistLine =
        new assets::fisheyeassets::FisheyeTrailerHitchTrajectory(
            f_framework,
            trajectory::g_trajParams,
            trajectory::g_numVerticesTrailerAssistLines,
            f_cam,
            f_model,
            f_settings);
    l_left_TrailerAssistLine->setName("TrailerHitchTrajectory");

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_left_TrailerAssistLine); // PRQA S 3803
    l_group->setName("FisheyeTrailerHitchTrajectory");
    return l_group;
}

osg::Group* createFisheyeOutermostLines(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::OutermostLine* const l_left_OutermostLine = new assets::fisheyeassets::FisheyeOutermostLine(
        f_framework,
        cc::assets::trajectory::commontypes::Left_enm,
        trajectory::g_height,
        trajectory::g_trajParams,
        trajectory::g_DIDescriptor,
        22u,
        4u,
        25u,
        f_cam,
        f_model,
        f_settings);
    l_left_OutermostLine->setName("OutermostLineLeft");

    //! TODO remove global variable uses
    trajectory::g_leftOutermostLine = l_left_OutermostLine;

    assets::trajectory::OutermostLine* const l_right_OutermostLine = new assets::fisheyeassets::FisheyeOutermostLine(
        f_framework,
        cc::assets::trajectory::commontypes::Right_enm,
        trajectory::g_height,
        trajectory::g_trajParams,
        trajectory::g_DIDescriptor,
        22u,
        4u,
        25u,
        f_cam,
        f_model,
        f_settings);
    l_right_OutermostLine->setName("OutermostLineRight");

    //! TODO remove global variable uses
    trajectory::g_rightOutermostLine = l_right_OutermostLine;

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_left_OutermostLine);  // PRQA S 3803
    l_group->addChild(l_right_OutermostLine); // PRQA S 3803

    l_group->setName("FisheyeOutmostLines");

    return l_group;
}

osg::Group* createFisheyeOutermostLinesColorful(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::OutermostLineColorful* const l_left_OutermostLine =
        new assets::fisheyeassets::FisheyeOutermostLineColorful(
            f_framework,
            cc::assets::trajectory::commontypes::Left_enm,
            trajectory::g_height,
            trajectory::g_trajParams,
            trajectory::g_DIDescriptor,
            22u,
            4u,
            25u,
            f_cam,
            f_model,
            f_settings);
    l_left_OutermostLine->setName("OutermostLineLeftColorful");

    //! TODO remove global variable uses
    trajectory::g_leftOutermostLineColorful = l_left_OutermostLine;

    assets::trajectory::OutermostLineColorful* const l_right_OutermostLine =
        new assets::fisheyeassets::FisheyeOutermostLineColorful(
            f_framework,
            cc::assets::trajectory::commontypes::Right_enm,
            trajectory::g_height,
            trajectory::g_trajParams,
            trajectory::g_DIDescriptor,
            22u,
            4u,
            25u,
            f_cam,
            f_model,
            f_settings);
    l_right_OutermostLine->setName("OutermostLineRightColorful");

    //! TODO remove global variable uses
    trajectory::g_rightOutermostLineColorful = l_right_OutermostLine;

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_left_OutermostLine);  // PRQA S 3803
    l_group->addChild(l_right_OutermostLine); // PRQA S 3803

    l_group->setName("FisheyeOutmostLinesColorful");

    return l_group;
}

osg::Group* createFisheyeCoverPlate(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::CoverPlate* const l_fisheye_CoverPlate = new assets::fisheyeassets::FisheyeCoverPlate(
        f_framework,
        trajectory::g_height + 0.001f,
        trajectory::g_trajParams,
        trajectory::g_numVerticesCoverPlate,
        f_cam,
        f_model,
        f_settings);
    l_fisheye_CoverPlate->setName("CoverPlate");

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_fisheye_CoverPlate); // PRQA S 3803
    l_group->setName("FisheyeCoverPlate");

    return l_group;
}

osg::Group* createFisheyeDL1(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::DL1* const l_fisheye_DL1 = new assets::fisheyeassets::FisheyeDL1(
        f_framework,
        trajectory::g_height + 0.003f,
        trajectory::g_trajParams,
        trajectory::g_leftOutermostLine,
        trajectory::g_rightOutermostLine,
        trajectory::g_numLayoutPointsDL1,
        f_cam,
        f_model,
        f_settings);
    l_fisheye_DL1->setName("DistanceLine");

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_fisheye_DL1); // PRQA S 3803
    l_group->setName("FisheyeDistanceLine");

    return l_group;
}

osg::Group* createFisheyeDL1Colorful(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::DL1* const l_fisheye_DL1 = new assets::fisheyeassets::FisheyeDL1(
        f_framework,
        trajectory::g_height + 0.003f,
        trajectory::g_trajParams,
        trajectory::g_leftOutermostLineColorful,
        trajectory::g_rightOutermostLineColorful,
        trajectory::g_numLayoutPointsDL1,
        f_cam,
        f_model,
        f_settings);
    l_fisheye_DL1->setName("DistanceLineColorful");

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_fisheye_DL1); // PRQA S 3803
    l_group->setName("FisheyeDistanceLineColorful");

    return l_group;
}

osg::Group* createFisheyeRctaOverlay(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::rctaoverlay::RctaOverlay* const l_fisheye_RctaOverlay =
        new assets::fisheyeassets::FisheyeRctaOverlay(f_framework, f_cam, f_model, f_settings);
    l_fisheye_RctaOverlay->setName("RctaOverlay");

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_fisheye_RctaOverlay); // PRQA S 3803
    l_group->setName("FisheyeRctaOverlay");

    return l_group;
}

} // namespace fisheyeassets
} // namespace assets
} // namespace cc
