//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "ResizeHandler.h"

#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/target/common/inc/linux_svs_interface.hpp"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "pc/svs/animation/inc/Action.h"
#include "pc/svs/animation/inc/ViewportAnimation.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace core
{

class ResizeHandler::SetCameraUpdaterAction : public pc::animation::Action
{
public:
    SetCameraUpdaterAction(pc::core::View* f_view, bool f_enableCameraUpdater)
        : m_view(f_view)
        , m_enableCameraUpdater(f_enableCameraUpdater)
    {
    }

protected:
    void onAction() override
    {
        m_view->getCameraUpdater()->setEnabled(m_enableCameraUpdater);
        XLOG_INFO(g_AppContext, "SetCameraUpdaterAction: " << (m_enableCameraUpdater ? "Enabled" : "Disabled"));
    }

private:
    pc::core::View* m_view;
    bool            m_enableCameraUpdater;
};

ResizeHandler::ResizeHandler(
    pc::core::View*           f_view,
    const pc::core::Viewport& f_normalViewport,
    const pc::core::Viewport& f_fullViewport,
    int                       f_renderOrderNum,
    bool                      f_baseView)
    : m_view(f_view)
    , m_normalViewport(f_normalViewport)
    , m_fullViewport(f_fullViewport)
    , m_renderOrderNum(f_renderOrderNum)
    , m_baseView(f_baseView)
{
    // Save the original state
    m_originalRenderOrder    = m_view->getRenderOrder();
    m_originalRenderOrderNum = m_view->getRenderOrderNum();
    m_originalClearMask      = m_view->getClearMask();
}

class ResizeHandler::RestoreStateAction : public pc::animation::Action
{
public:
    RestoreStateAction(
        pc::core::View*          f_view,
        osg::Camera::RenderOrder f_renderOrder,
        int                      f_renderOrderNum,
        GLbitfield               f_clearMask)
        : m_view(f_view)
        , m_renderOrder(f_renderOrder)
        , m_renderOrderNum(f_renderOrderNum)
        , m_clearMask(f_clearMask)
    {
    }

    void onAction() override
    {
        // Restore default render order
        m_view->setRenderOrder(m_renderOrder, m_renderOrderNum);
        // Todo: Figure out the clear color
        XLOG_INFO(g_AppContext, "Restore State");
    }

private:
    pc::core::View*          m_view;
    osg::Camera::RenderOrder m_renderOrder;
    int                      m_renderOrderNum;
    GLbitfield               m_clearMask;
};

class ResizeHandler::SendSignalAction : public pc::animation::Action
{
public:
    SendSignalAction(cc::target::common::EViewAnimation f_viewAnimation)
        : m_viewAnimation(f_viewAnimation)
    {
    }

    void onAction() override
    {
        if (cc::daddy::CustomDaddyPorts::sm_animationDaddy_SenderPort.isConnected())
        {
            XLOG_INFO(g_AppContext, "Sending Signal " << static_cast<int>(m_viewAnimation));
            auto& l_container  = cc::daddy::CustomDaddyPorts::sm_animationDaddy_SenderPort.reserve();
            l_container.m_Data = m_viewAnimation;
            cc::daddy::CustomDaddyPorts::sm_animationDaddy_SenderPort.deliver();
        }
    }

private:
    cc::target::common::EViewAnimation m_viewAnimation;
};

pc::animation::Animation* ResizeHandler::animateNormalSize(float f_animationDuration)
{
    pc::animation::Animation* l_animation = pc::animation::parallel(
        new pc::animation::MoveViewportAnimation{m_view, m_normalViewport.m_origin, f_animationDuration},
        new pc::animation::ResizeViewportAnimation{m_view, m_normalViewport.m_size, f_animationDuration});

    if (m_baseView && (f_animationDuration > 0))
    {
        // Send start signal
        l_animation = pc::animation::parallel(
            new SendSignalAction(cc::target::common::EViewAnimation::AVM_ANI_FULL_DEFAULT_START), l_animation);
    }

    // Restore original state and send stop signal
    l_animation = pc::animation::serial(
        l_animation,
        new RestoreStateAction(m_view, m_originalRenderOrder, m_originalRenderOrderNum, m_originalClearMask));

    if (m_baseView && (f_animationDuration > 0))
    {
        // Send stop signal
        l_animation = pc::animation::serial(
            l_animation, new SendSignalAction(cc::target::common::EViewAnimation::AVM_ANI_FULL_DEFAULT_STOP));
    }

    if (m_enableCameraUpdater)
    {
        return l_animation;
    }
    else
    {
        return pc::animation::serial(
            new SetCameraUpdaterAction{m_view, false},
            l_animation,
            new SetCameraUpdaterAction{m_view, true});
    }
}

pc::animation::Animation* ResizeHandler::animateFullSize(float f_animationDuration)
{
    // Set render order to be on top
    m_view->setRenderOrder(osg::Camera::POST_RENDER, m_renderOrderNum);

    pc::animation::Animation* l_animation = pc::animation::parallel(
        new pc::animation::MoveViewportAnimation{m_view, m_fullViewport.m_origin, f_animationDuration},
        new pc::animation::ResizeViewportAnimation{m_view, m_fullViewport.m_size, f_animationDuration});

    if (m_baseView)
    {
        // Set clear mask if nothing rendered behind this view
        m_view->setClearMask(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        if (f_animationDuration > 0)
        {
            // Send start-stop signals
            l_animation = pc::animation::parallel(
                new SendSignalAction(cc::target::common::EViewAnimation::AVM_ANI_DEFAULT_FULL_START), l_animation);
            l_animation = pc::animation::serial(
                l_animation, new SendSignalAction(cc::target::common::EViewAnimation::AVM_ANI_DEFAULT_FULL_STOP));
        }
    }

    if (m_enableCameraUpdater)
    {
        return l_animation;
    }
    else
    {
        return pc::animation::serial(
            new SetCameraUpdaterAction{m_view, false},
            l_animation,
            new SetCameraUpdaterAction{m_view, true});
    }
}

} // namespace core
} // namespace cc
