//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#ifndef CC_CORE_VIEW_MODE_TOGGLE_H
#define CC_CORE_VIEW_MODE_TOGGLE_H

#include "ViewModeStateTransitionManager.h"

#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h"
#include "cc/virtcam/inc/CameraPositions.h"

#include "pc/svs/animation/inc/Action.h"
#include "pc/svs/animation/inc/Animation.h"

namespace pc
{
namespace core
{
class Framework;
class Scene;
} // namespace core

namespace daddy
{
struct ViewMode;
} // namespace daddy
} // namespace pc

namespace cc
{
namespace core
{

//======================================================
// DefaultViewToggleAction
//------------------------------------------------------
/// Action class that enables/disables predetermined views for a passed Screen ID.
/// <AUTHOR> David (CC-DA/EAV3)
/// @ingroup ViewModeStateTransition
//======================================================
class DefaultViewToggleAction : public pc::animation::Action
{
public:
    DefaultViewToggleAction(
        pc::core::Scene*           f_scene,
        const pc::daddy::ViewMode& f_mode,
        bool                       f_distortionOn,
        ScreenIdStruct             f_screenIdStruct)
        : m_scene(f_scene)
        , m_mode(f_mode)
        , m_distortionOn(f_distortionOn)
        , m_screenIdStruct(f_screenIdStruct)
    {
    }

    void setViewEnabled(vfc::int32_t f_view, bool f_enabled);

    void setFrontViewEnabled(bool f_enabled);
    void setRearViewEnabled(bool f_enabled);
    void setTopViewEnabled(bool f_enabled);
    void setSideViewEnabled(bool f_enabled);
    void setViewEnabledHorizontal(vfc::int32_t f_view, bool f_enabled);
    void setViewEnabledVertical(vfc::int32_t f_view, bool f_enabled);
    void setImageinImageViewportEnable(bool f_enabled);

    void onAction() override;

private:
    pc::core::Scene*              m_scene;
    pc::daddy::ViewMode           m_mode;
    vfc::int32_t                  m_preView      = EScreenID_NO_CHANGE;
    bool                          m_distortionOn = true;

    int m_isLeft  = false;
    int m_isRight = false;

    ScreenIdStruct m_screenIdStruct;
};

std::string getScreenName(vfc::int32_t f_screenId);

cc::virtcam::VirtualCamEnum convertCameraModeToVirtualCameraPosition(vfc::int32_t f_mode);
bool                        isSurroundView(vfc::int32_t f_mode);
bool                        isParkingView(vfc::int32_t f_mode);
bool                        isVertSurroundView(vfc::int32_t f_mode);
bool                        isVertParkingView(vfc::int32_t f_mode);
bool                        isKerbView(vfc::int32_t f_mode);
bool                        isFullscreenSurroundView(vfc::int32_t f_mode);
bool                        isEnlargeFront(vfc::int32_t f_mode);
bool                        isEnlargeRear(vfc::int32_t f_mode);
bool                        isFullscreen(vfc::int32_t f_mode);
bool                        isEnlarge(vfc::int32_t f_mode);
bool                        isFullscreenPlanView(vfc::int32_t f_mode);
bool                        isFrontView(vfc::int32_t f_mode);
bool                        isRearView(vfc::int32_t f_mode);
bool                        isFullscreenPlanOrSurroundView(vfc::int32_t f_mode);
bool                        isAVMRunBackground(vfc::int32_t f_mode);
bool                        isImageInImageView(vfc::int32_t f_view);
bool                        isWheelView(vfc::int32_t f_view);
bool                        isSingleView(vfc::int32_t f_view);
bool                        isSurroundViewAngleNeedKeep(vfc::int32_t f_mode);
bool                        isEnlargeFrontRearView(vfc::int32_t f_view);

enum class PlanViewType
{
    PICTURE_IN_PICTURE,
    NORMAL,
    FULLSCREEN
};
PlanViewType checkPlanViewType(vfc::int32_t f_view);

} // namespace core
} // namespace cc

#endif // CC_CORE_VIEW_MODE_TOGGLE_H
