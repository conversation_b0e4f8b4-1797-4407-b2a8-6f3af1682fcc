//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "ViewModeStateTransitionManager.h"
#include "ViewModeToggle.h"

#include "pc/svs/animation/inc/SerialAnimation.h"
#include "pc/svs/animation/inc/ViewportAnimation.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"

#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/views/customwarpfisheyeview/inc/CustomWarpFisheyeView.h"

#include "cc/imgui/inc/imgui_manager.h"

namespace cc
{
namespace core
{

using cc::util::logging::g_viewModeSMContext;

namespace
{
void updateViewport(osg::Camera* f_camera, const pc::core::Viewport& f_viewport)
{
    if (f_camera == nullptr)
    {
        return;
    }
    f_camera->setViewport(
        f_viewport.m_origin.x(), f_viewport.m_origin.y(), f_viewport.m_size.x(), f_viewport.m_size.y());

    // auto fisheyeView = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeView*>(f_camera);
    // if (fisheyeView)
    // {
    //     fisheyeView->customUpdate();
    // }
    // auto resizeAnimation = pc::animation::ResizeViewportAnimation(f_camera, f_viewport.m_size, 0.0f);
    // auto moveAnimation = pc::animation::MoveViewportAnimation(f_camera, f_viewport.m_origin, 0.0f);
    // while (!moveAnimation.update(std::numeric_limits<float>::max()) ||
    //        !resizeAnimation.update(std::numeric_limits<float>::max()))
    // {
    // }
}

void transformLeftViewport(osg::Camera* f_camera, const pc::core::Viewport& f_target, bool resize = true)
{
    if (f_camera == nullptr || f_camera->getViewport() == nullptr)
    {
        return;
    }

    const auto viewport = f_camera->getViewport();
    if (viewport->width() == static_cast<vfc::float64_t>(f_target.m_size.x()) &&
        viewport->height() == static_cast<vfc::float64_t>(f_target.m_size.y()))
    {
        // no change in size, skip
        // return;
    }

    osg::Vec2d targetViewportSize = {
        static_cast<vfc::float64_t>(f_target.m_size.x()), static_cast<vfc::float64_t>(f_target.m_size.y())};

    const vfc::float64_t l_ratio_trans = targetViewportSize.x() / viewport->width() - 1.0;
    const vfc::float64_t l_ratio_scale =
        (viewport->width() / viewport->height()) / (targetViewportSize.x() / targetViewportSize.y());

    if (resize)
    {
        f_camera->setViewport(f_target.m_origin.x(), f_target.m_origin.y(), f_target.m_size.x(), f_target.m_size.y());
    }

    const auto l_fisheyeCamera = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeView*>(f_camera);
    if (l_fisheyeCamera != nullptr && IMGUI_GET_CHECKBOX_BOOL("Settings", "Scale by projection"))
    {
        // l_fisheyeCamera->customUpdate();
        osg::Matrix l_p = f_camera->getProjectionMatrix();
        l_p.postMultTranslate(osg::Vec3d(l_ratio_trans, 0.0, 0.0));
        l_p.postMultScale(osg::Vec3d(l_ratio_scale, 1.0, 1.0));
        f_camera->setProjectionMatrix(l_p);
    }
}

void transformRightViewport(osg::Camera* f_camera, const pc::core::Viewport& f_target, bool resize = true)
{
    if (f_camera == nullptr || f_camera->getViewport() == nullptr)
    {
        return;
    }

    const auto viewport = f_camera->getViewport();
    if (viewport->width() == static_cast<vfc::float64_t>(f_target.m_size.x()) &&
        viewport->height() == static_cast<vfc::float64_t>(f_target.m_size.y()))
    {
        // no change in size, skip
        // return;
    }

    osg::Vec2d targetViewportSize = {
        static_cast<vfc::float64_t>(f_target.m_size.x()), static_cast<vfc::float64_t>(f_target.m_size.y())};
    const vfc::float64_t l_ratio_trans = 1.0 - targetViewportSize.x() / viewport->width();
    const vfc::float64_t l_ratio_scale =
        (viewport->width() / viewport->height()) / (targetViewportSize.x() / targetViewportSize.y());

    if (resize)
    {
        f_camera->setViewport(f_target.m_origin.x(), f_target.m_origin.y(), f_target.m_size.x(), f_target.m_size.y());
    }

    const auto l_fisheyeCamera = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeView*>(f_camera);
    if (l_fisheyeCamera != nullptr && IMGUI_GET_CHECKBOX_BOOL("Settings", "Scale by projection"))
    {
        // l_fisheyeCamera->customUpdate();
        osg::Matrix l_p = f_camera->getProjectionMatrix();
        l_p.postMultTranslate(osg::Vec3d(l_ratio_trans, 0.0, 0.0));
        l_p.postMultScale(osg::Vec3d(l_ratio_scale, 1.0, 1.0));
        f_camera->setProjectionMatrix(l_p);
    }
}

} // namespace

void cc::core::ViewModeStateTransitionManager::handleViewportSize()
{
    using cc::core::CustomViews;
    using cc::core::g_views;

    const bool l_isParking =
        (EScreenID_PARKING_TOP == m_currentScreenId || EScreenID_PARKING_FRONT == m_currentScreenId ||
         EScreenID_PARKING_REAR == m_currentScreenId || EScreenID_PARKING_FREEPARKING == m_currentScreenId);

    // bool l_isTopViewAtBottom =
    //     (EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL == m_currentScreenId ||
    //      EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL  == m_currentScreenId ||
    //      EScreenID_UPVIEWCOFIG_WHEEL_BOTH       == m_currentScreenId );

    //! Park Single Viewport
    {
        const auto& targetViewport = (l_isParking) ? g_views->m_parkSingleViewport : g_views->m_mainViewport;
        updateViewport(m_scene->getView(CustomViews::FRONT_VIEW), targetViewport);
        // updateViewport(m_scene->getView(CustomViews::FRONT_VIEW_PANO), targetViewport);
        updateViewport(m_scene->getView(CustomViews::REAR_VIEW), targetViewport);
        updateViewport(m_scene->getView(CustomViews::REAR_VIEW_PANO), targetViewport);
    }

    // //! Park Top Viewport
    {
        XLOG_INFO_OS(g_viewModeSMContext) << "Park Top Viewport Resize : l_isParking: " << static_cast<int>(l_isParking) << XLOG_ENDL;
        // const auto& targetViewport = (l_isParking) ? g_views->m_parkTop : g_views->m_imageInimagePlanviewViewport;
        // updateViewport(m_scene->getView(CustomViews::IMAGE_IMAGE_PLANVIEW), targetViewport);
        // updateViewport(m_scene->getView(CustomViews::IMAGE_IN_IMAGE_USS_OVERLAY), targetViewport);
        // updateViewport(m_scene->getView(CustomViews::IMAGE_IN_IMAGE_VEHICLE_2D), targetViewport);
    }

    const bool l_isNarrow = (EScreenID_APA_FRONT_HUBVIEW == m_currentScreenId);
    {
        const auto& targetViewportLeft  = (l_isNarrow) ? g_views->m_frontWheelLeftNarrow : g_views->m_frontWheelLeft;
        const auto& targetViewportRight = (l_isNarrow) ? g_views->m_frontWheelRightNarrow : g_views->m_frontWheelRight;
        updateViewport(m_scene->getView(CustomViews::FRONT_WHEEL_LEFT_VIEW), targetViewportLeft);
        updateViewport(m_scene->getView(CustomViews::FRONT_WHEEL_RIGHT_VIEW), targetViewportRight);
    }

    if ("st2" == g_dataContainerToSvs.m_vehicleInfo.vehicleType)
    {
        const bool l_isImageInImageWheelView =
            (EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL_WITHOUT_PLAN == m_currentScreenId ||
             EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL_WITHOUT_PLAN == m_currentScreenId ||
             EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL_WITHOUT_PLAN == m_currentScreenId ||
             EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL_WITHOUT_PLAN == m_currentScreenId);
        const bool l_WheelView =
            (EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL == m_currentScreenId ||
             EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL == m_currentScreenId ||
             EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL == m_currentScreenId ||
             EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL == m_currentScreenId);
        if (l_isImageInImageWheelView)
        {
            const auto& targetViewportLeft   = g_views->m_frontWheelLeftImageInImage;
            const auto& targetViewportRight  = g_views->m_frontWheelRightImageInImage;
            const auto& targetViewportSingle = g_views->m_singleViewImageInImage;

            switch (m_currentScreenId)
            {
            case EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL_WITHOUT_PLAN:
            {
                updateViewport(m_scene->getView(CustomViews::FRONT_WHEEL_LEFT_VIEW), targetViewportLeft);
                updateViewport(m_scene->getView(CustomViews::FRONT_WHEEL_RIGHT_VIEW), targetViewportRight);
                updateViewport(m_scene->getView(CustomViews::FRONT_VIEW), targetViewportSingle);
                break;
            }
            case EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL_WITHOUT_PLAN:
            {
                updateViewport(m_scene->getView(CustomViews::REAR_WHEEL_LEFT_VIEW), targetViewportLeft);
                updateViewport(m_scene->getView(CustomViews::REAR_WHEEL_RIGHT_VIEW), targetViewportRight);
                updateViewport(m_scene->getView(CustomViews::FRONT_VIEW), targetViewportSingle);
                break;
            }
            case EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL_WITHOUT_PLAN:
            {
                updateViewport(m_scene->getView(CustomViews::FRONT_WHEEL_LEFT_VIEW), targetViewportLeft);
                updateViewport(m_scene->getView(CustomViews::FRONT_WHEEL_RIGHT_VIEW), targetViewportRight);
                updateViewport(m_scene->getView(CustomViews::REAR_VIEW), targetViewportSingle);
                break;
            }
            case EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL_WITHOUT_PLAN:
            {
                updateViewport(m_scene->getView(CustomViews::REAR_WHEEL_LEFT_VIEW), targetViewportLeft);
                updateViewport(m_scene->getView(CustomViews::REAR_WHEEL_RIGHT_VIEW), targetViewportRight);
                updateViewport(m_scene->getView(CustomViews::REAR_VIEW), targetViewportSingle);
                break;
            }
            default:
            {
                break;
            }
            }
        }
        else if (l_WheelView)
        {
            const auto& targetViewportLeft   = g_views->m_frontWheelLeft;
            const auto& targetViewportRight  = g_views->m_frontWheelRight;
            const auto& targetViewportSingle = g_views->m_apaAssistViewport;

            switch (m_currentScreenId)
            {
            case EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL:
            {
                updateViewport(m_scene->getView(CustomViews::FRONT_WHEEL_LEFT_VIEW), targetViewportLeft);
                updateViewport(m_scene->getView(CustomViews::FRONT_WHEEL_RIGHT_VIEW), targetViewportRight);
                // updateViewport(m_scene->getView(CustomViews::FRONT_VIEW_PANO), targetViewportSingle);
                break;
            }
            case EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL:
            {
                updateViewport(m_scene->getView(CustomViews::REAR_WHEEL_LEFT_VIEW), targetViewportLeft);
                updateViewport(m_scene->getView(CustomViews::REAR_WHEEL_RIGHT_VIEW), targetViewportRight);
                // updateViewport(m_scene->getView(CustomViews::FRONT_VIEW_PANO), targetViewportSingle);
                break;
            }
            case EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL:
            {
                updateViewport(m_scene->getView(CustomViews::FRONT_WHEEL_LEFT_VIEW), targetViewportLeft);
                updateViewport(m_scene->getView(CustomViews::FRONT_WHEEL_RIGHT_VIEW), targetViewportRight);
                // updateViewport(m_scene->getView(CustomViews::REAR_VIEW_PANO), targetViewportSingle);
                break;
            }
            case EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL:
            {
                updateViewport(m_scene->getView(CustomViews::REAR_WHEEL_LEFT_VIEW), targetViewportLeft);
                updateViewport(m_scene->getView(CustomViews::REAR_WHEEL_RIGHT_VIEW), targetViewportRight);
                // updateViewport(m_scene->getView(CustomViews::REAR_VIEW_PANO), targetViewportSingle);
                break;
            }
            default:
            {
                break;
            }
            }
        }
        else
        {
            // const auto& targetViewportLeft   = g_views->m_frontWheelLeft; // fix qac, didn't use.
            // const auto& targetViewportRight  = g_views->m_frontWheelRight;
            const auto& targetViewportSingle = g_views->m_mainViewport;
            if (m_currentScreenId == EScreenID_SINGLE_FRONT_NORMAL)
            {
                updateViewport(m_scene->getView(CustomViews::FRONT_VIEW), targetViewportSingle);
            }
            else if (m_currentScreenId == EScreenID_SINGLE_REAR_NORMAL_ON_ROAD)
            {
                updateViewport(m_scene->getView(CustomViews::REAR_VIEW), targetViewportSingle);
            }
            else
            {
                // for qac
            }
        }
    }
}

} // namespace core
} // namespace cc
