//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/assets/superTransparentOverlay/inc/SuperTransparentOverlay.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "cc/target/common/inc/commonInterface.h"
#include "osg/Geometry"
#include "osg/Texture2D"
#include "osgDB/ReadFile"
#include "osg/State"
#include "cc/assets/parkingspots/inc/ParkingSpotUtil.h"

#include "cc/imgui/inc/imgui_manager.h"

namespace cc
{
namespace assets
{
namespace superTransparentOverlay
{

pc::util::coding::Item<SuperTransparentOverlaySetting> g_superTransparentSettings("SuperTransparent");

static osg::ref_ptr<osg::Texture2D> createSuperTransparentOverlayTexture(const std::string& f_filename)
{
  const osg::ref_ptr<osg::Image> l_image = osgDB::readImageFile(f_filename);
  if (nullptr != l_image)
  {
    const osg::ref_ptr<osg::Texture2D> l_texture = new osg::Texture2D(l_image);
    l_texture->setDataVariance(osg::Object::STATIC);
    l_texture->setUnRefImageDataAfterApply(true);
    l_texture->setResizeNonPowerOfTwoHint(false);
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP);
    l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP);
    return l_texture;
  }
  return nullptr;
}

SuperTransparentOverlay::SuperTransparentOverlay(cc::core::CustomFramework* f_customFramework,
                                                 osg::ref_ptr<assets::impostor::TransparentVehicleImpostor> f_trVehImpostor)  // PRQA S 2755
: MatrixTransform()
, m_framework(f_customFramework)
, m_planeGeode() // PRQA S 2323
, m_textureSelected(createSuperTransparentOverlayTexture(CONCAT_PATH_VEHICLE_MODEL("cc/vehicle_model/vehicle2d/superTransparent.png")))
, m_trVehImpostor(f_trVehImpostor)
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);

    osg::VertexBufferObject* const l_vbo = new osg::VertexBufferObject;
    l_vbo->setUsage(GL_DYNAMIC_DRAW_ARB);    // PRQA S 3143

    osg::Geometry* const l_geometry = new osg::Geometry;
    l_geometry->setUseDisplayList(false);
    l_geometry->setUseVertexBufferObjects(true);

    osg::Vec3Array* const l_vertices = new osg::Vec3Array(4u);
    l_vertices->setVertexBufferObject(l_vbo);

    (*l_vertices)[0u] = osg::Vec3(pc::vehicle::g_mechanicalData->getBumperCenterXRear(),   pc::vehicle::g_mechanicalData->getWidthWithMirrors()/2.0f, 0.05f);
    (*l_vertices)[1u] = osg::Vec3(pc::vehicle::g_mechanicalData->getBumperCenterXRear(),  -pc::vehicle::g_mechanicalData->getWidthWithMirrors()/2.0f, 0.05f);
    (*l_vertices)[2u] = osg::Vec3(pc::vehicle::g_mechanicalData->getBumperCenterXFront(), -pc::vehicle::g_mechanicalData->getWidthWithMirrors()/2.0f, 0.05f);
    (*l_vertices)[3u] = osg::Vec3(pc::vehicle::g_mechanicalData->getBumperCenterXFront(),  pc::vehicle::g_mechanicalData->getWidthWithMirrors()/2.0f, 0.05f);
    l_geometry->setVertexArray(l_vertices);

    osg::Vec2Array* const l_texCoords = new osg::Vec2Array(4u);

    (*l_texCoords)[0u] = osg::Vec2f(0.0f, 0.0f);
    (*l_texCoords)[1u] = osg::Vec2f(1.0f, 0.0f);
    (*l_texCoords)[2u] = osg::Vec2f(1.0f, 1.0f);
    (*l_texCoords)[3u] = osg::Vec2f(0.0f, 1.0f);
    l_geometry->setTexCoordArray(0u, l_texCoords);

    osg::Vec4Array* const l_colours = new osg::Vec4Array(1u);
    (*l_colours)[0u] = osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f);
    l_geometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);

    osg::Vec3Array* const l_normals = new osg::Vec3Array(1u);
    (*l_normals)[0u] = osg::Z_AXIS;
    l_geometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);

    osg::DrawElementsUByte* const l_indices = new osg::DrawElementsUByte(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), 6u);

    (*l_indices)[0u] = 1u;
    (*l_indices)[1u] = 0u;
    (*l_indices)[2u] = 2u;
    (*l_indices)[3u] = 2u;
    (*l_indices)[4u] = 0u;
    (*l_indices)[5u] = 3u;
    l_geometry->addPrimitiveSet(l_indices);    // PRQA S 3803

    m_planeGeode = new osg::Geode;
    m_planeGeode->addDrawable(l_geometry);    // PRQA S 3803

    osg::Group::addChild(m_planeGeode.get());    // PRQA S 3803

    const osg::observer_ptr<osg::StateSet> l_slotState     = new osg::StateSet;
    pc::core::TextureShaderProgramDescriptor l_slotShader("basicTex");

    l_slotState->setTextureAttribute(0u, m_textureSelected);
    l_slotState->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_slotState->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
    l_slotState->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_slotState->setRenderBinDetails(171, "RenderBin"); // render after wheel track & outermostline & pts overlay

    l_slotShader.apply(l_slotState.get());    // PRQA S 3803

    m_planeGeode->setStateSet(l_slotState.get());
}

void SuperTransparentOverlay::traverse(osg::NodeVisitor& f_nv)  // PRQA S 2755
{

    //! Super transparent mode
    auto& l_superTransparentModeReceiverPort = m_framework->m_HUvehTransLevelReceiver;
    // getOrCreateStateSet()->setRenderBinDetails(IMGUI_GET_SLIDER_INT("Settings", "SuperTransparentOverlayRenderBin", -10, 500), "RenderBin"); // render after wheel track & outermostline & pts overlay

    if (l_superTransparentModeReceiverPort.hasData())
    {
        const auto* const l_pData = l_superTransparentModeReceiverPort.getData().front();
        if (l_pData != nullptr)
        {
            //Not rendering when not received super transparent mode signal
            if (l_pData->m_Data != 2)
            {
                return;
            }
            if (nullptr != m_trVehImpostor)
            {
                if (m_trVehImpostor->getPRSSUpdateCallback()->getImpostorTransp() != 0.0f)
                {
                    return;
                }
            }
        }
    }

    osg::Group::traverse(f_nv);

}

} // namespace superTransparent
} // namespace assets
} // namespace cc
