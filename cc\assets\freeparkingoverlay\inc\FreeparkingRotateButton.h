//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#ifndef CC_ASSETS_FREEPARKINGOVERLAY_ROTATE_BUTTON_H
#define CC_ASSETS_FREEPARKINGOVERLAY_ROTATE_BUTTON_H

#include "cc/assets/freeparkingoverlay/inc/FreeparkingNode.h"
#include "cc/assets/parkingspots/inc/ParkingSpot.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/core/inc/Asset.h"

#include "cc/target/common/inc/commonInterface.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingUtils.h"

#include <osg/Geode>
#include <osg/MatrixTransform>
#include <osg/NodeCallback>
#include <osgAnimation/EaseMotion>

namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

class FreeparkingRotateButtonSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(FreeparkingRotateButtonSettings)
    {
        ADD_STRING_MEMBER(parkablePressableTexture);
        ADD_STRING_MEMBER(unparkablePressableTexture);
        ADD_STRING_MEMBER(parkablePressedTexture);
        ADD_STRING_MEMBER(unparkablePressedTexture);
        ADD_STRING_MEMBER(arrowTexture);
        ADD_FLOAT_MEMBER(groundLevel);
        ADD_FLOAT_MEMBER(mipmapBias);
        ADD_INT_MEMBER(step);
        ADD_MEMBER(osg::Vec2f, size);
        ADD_MEMBER(osg::Vec2f, arrowSize);
        ADD_FLOAT_MEMBER(offsetPercentage);
    }

    std::string m_parkablePressableTexture{CONCAT_PATH_VEHICLE_MODEL("cc/vehicle_model/ui/night/freeparking/rotatebutton_blue.png")};
    std::string m_unparkablePressableTexture{CONCAT_PATH_VEHICLE_MODEL("cc/vehicle_model/ui/night/freeparking/rotatebutton_white.png")};
    std::string m_parkablePressedTexture{CONCAT_PATH_VEHICLE_MODEL("cc/vehicle_model/ui/night/freeparking/rotatebutton_blue_pressed.png")};
    std::string m_unparkablePressedTexture{CONCAT_PATH_VEHICLE_MODEL("cc/vehicle_model/ui/night/freeparking/rotatebutton_white_pressed.png")};

    std::string m_arrowTexture{CONCAT_PATH_VEHICLE_MODEL("cc/vehicle_model/ui/night/freeparking/freeparking_rotate_arrow.png")};
    vfc::float32_t m_groundLevel{0.1f};
    vfc::float32_t m_mipmapBias{-0.2f};
    vfc::int32_t   m_step{4};
    osg::Vec2f     m_size      = {1.0f, 0.63f};
    osg::Vec2f     m_arrowSize = {1.0f, 0.15f};
    vfc::float32_t m_offsetPercentage{0.72f};
};

extern pc::util::coding::Item<FreeparkingRotateButtonSettings> g_freeparkingRotateButtonSettings;

class RotateButton : public FreeparkingNode, public osg::MatrixTransform
{
public:
    enum ButtonState : vfc::uint8_t
    {
        SELECTABLE = 0u,
        SELECTED   = 1u
    };

    enum ButtonDirection : vfc::uint8_t
    {
        TOP = 0u,
        BOTTOM = 1u
    };

public:
    RotateButton();

    ~RotateButton() override = default;

    void update(FreeparkingOverlay* f_freeparkingOverlay, const bool f_isLeft) override;

    void traverse(osg::NodeVisitor& f_nv) override;

    void setState(ButtonState f_state)
    {
        onStateChanged(f_state);
        m_state = f_state;
    }

    void setDirection(ButtonDirection f_direction)
    {
        m_direction = f_direction;
    }

    ButtonDirection getDirection() const
    {
        return m_direction;
    }

    void setStep(vfc::int32_t f_step)
    {
        m_step = f_step;
    }

    vfc::int32_t getStep() const
    {
        return m_step;
    }

    osg::Vec3f getRotateCenter()
    {
        return m_rotateCenter;
    }

    vfc::float32_t getOffsetToResponseCenter()
    {
        return m_offsetToResponseCenter;
    }

private:
    enum AnimationType
    {
        FADE_IN,  // from fully transparent to opaque
        FADE_OUT, // from fully opaque to transparent
    };

    enum AnimationState
    {
        TRANSITION, // either fade_in or fade_out
        OPAQUE,     // fully opaque
        TRANSPARENT // fully transparent
    };

private:
    RotateButton(const RotateButton& other);      // = delete
    RotateButton& operator=(const RotateButton&); // delete

    inline void     onStateChanged(ButtonState f_newState);
    void            updateState(FreeparkingOverlay* f_freeparkingOverlay);
    void            updateAnimation();
    void            updateTexture(ParkingPlaneState f_parkingPlaneState);
    void            fadeIn();
    void            fadeOut();
    osg::Texture2D* createTexture(const std::string& f_filename);

private:
    osg::ref_ptr<osg::Geode>            m_geode;
    bool                                m_visible;
    osg::Vec3f                          m_rotateCenter;
    vfc::float32_t                      m_offsetToResponseCenter;
    osg::Vec2f                          m_size;
    vfc::uint32_t                       m_spotType;
    static osg::ref_ptr<osg::Texture2D> sm_unparkableTexture;
    static osg::ref_ptr<osg::Texture2D> sm_parkableTexture;
    static osg::ref_ptr<osg::Texture2D> sm_arrowTexture;
    static osg::ref_ptr<osg::Texture2D> sm_parkablePressedTexture;
    static osg::ref_ptr<osg::Texture2D> sm_unparkablePressedTexture;
    ButtonState                         m_state;
    vfc::float32_t                      m_transparency;
    vfc::int32_t                        m_step;
    AnimationType                       m_animationType;
    AnimationState                      m_animationState;
    ButtonDirection                     m_direction{ButtonDirection::TOP};
};

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_FREEPARKINGOVERLAY_ROTATE_BUTTON_H
