//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/button/inc/FreeparkingButton.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/imgui/inc/imgui_manager.h"


namespace cc
{
namespace assets
{
namespace button
{
namespace freeparkingbutton
{


class FreeparkingButtonSettings : public pc::util::coding::ISerializable
{
public:

    SERIALIZABLE(FreeparkingButtonSettings) // PRQA S 2428
    {
        ADD_MEMBER(ButtonTextureSettings, startFreeparking);
        ADD_MEMBER(ButtonTextureSettings, stopFreeparking);
        ADD_MEMBER(osg::Vec2f, horiPos);
        ADD_MEMBER(osg::Vec2f, vertPos);
    }

    ButtonTextureSettings m_startFreeparking;
    ButtonTextureSettings m_stopFreeparking;

    osg::Vec2f m_horiPos{0.0f, 100.0f};
    osg::Vec2f m_vertPos{100.0f, 0.0f};

};

static pc::util::coding::Item<FreeparkingButtonSettings> g_freeparkingButtonSettings("FreeparkingButtonSettings");

FreeparkingButton::FreeparkingButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView)
: Button(f_assetId, f_referenceView) // PRQA S 2759 // PRQA S 2323
, m_framework(f_framework) // PRQA S 2323
{
    setIconAtMiddle(true);
    setPositionHori(g_freeparkingButtonSettings->m_horiPos);
    setPositionVert(g_freeparkingButtonSettings->m_vertPos);
}


void FreeparkingButton::onInvalid()
{
    setIconEnable(false);
}


void FreeparkingButton::onUnavailable()
{
    setIconEnable(true);
    if      (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY    && m_parkingMode == FREEPARKING) { setTexturePath(g_freeparkingButtonSettings->m_stopFreeparking.m_day.m_UnavailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT  && m_parkingMode == FREEPARKING) { setTexturePath(g_freeparkingButtonSettings->m_stopFreeparking.m_night.m_UnavailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY    && m_parkingMode == NONE       ) { setTexturePath(g_freeparkingButtonSettings->m_startFreeparking.m_day.m_UnavailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT  && m_parkingMode == NONE       ) { setTexturePath(g_freeparkingButtonSettings->m_startFreeparking.m_night.m_UnavailableTexturePath); }
    else
    {
        setTexturePath(g_freeparkingButtonSettings->m_startFreeparking.m_night.m_UnavailableTexturePath);
    }
}


void FreeparkingButton::onAvailable()
{
    setIconEnable(true);
    if      (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY    && m_parkingMode == FREEPARKING) { setTexturePath(g_freeparkingButtonSettings->m_stopFreeparking.m_day.m_AvailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT  && m_parkingMode == FREEPARKING) { setTexturePath(g_freeparkingButtonSettings->m_stopFreeparking.m_night.m_AvailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY    && m_parkingMode == NONE       ) { setTexturePath(g_freeparkingButtonSettings->m_startFreeparking.m_day.m_AvailableTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT  && m_parkingMode == NONE       ) { setTexturePath(g_freeparkingButtonSettings->m_startFreeparking.m_night.m_AvailableTexturePath); }
    else
    {
        setTexturePath(g_freeparkingButtonSettings->m_startFreeparking.m_night.m_AvailableTexturePath);
    }
}


void FreeparkingButton::onPressed()
{
    setIconEnable(true);
    if      (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY    && m_parkingMode == FREEPARKING) { setTexturePath(g_freeparkingButtonSettings->m_stopFreeparking.m_day.m_PressedTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT  && m_parkingMode == FREEPARKING) { setTexturePath(g_freeparkingButtonSettings->m_stopFreeparking.m_night.m_PressedTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEHE_TYPE_DAYNIGHT_DAY    && m_parkingMode == NONE       ) { setTexturePath(g_freeparkingButtonSettings->m_startFreeparking.m_day.m_PressedTexturePath); }
    else if (getDayNightTheme() == DayNightThemeType::ETHEME_TYPE_DAYNIGHT_NIGHT  && m_parkingMode == NONE       ) { setTexturePath(g_freeparkingButtonSettings->m_startFreeparking.m_night.m_PressedTexturePath); }
    else
    {
        setTexturePath(g_freeparkingButtonSettings->m_startFreeparking.m_night.m_PressedTexturePath);
    }
}


void FreeparkingButton::onReleased()
{
    if (checkTouchInsideResponseArea())
    {
        auto& freeparkingButtonPressContainer = cc::daddy::CustomDaddyPorts::sm_FreeParkingButtonPressDaddy_SenderPort.reserve();
        freeparkingButtonPressContainer.m_Data = true;
        cc::daddy::CustomDaddyPorts::sm_FreeParkingButtonPressDaddy_SenderPort.deliver();
    }
}


void FreeparkingButton::update()
{
    setSettingModifiedCount(g_freeparkingButtonSettings->getModifiedCount());

    bool allPortsHaveData = true;
    GET_PORT_DATA(touchStatusContainer,       m_framework->asCustomFramework()->m_HUTouchTypeReceiver, allPortsHaveData)
    GET_PORT_DATA(huRotateStatusContainer,    m_framework->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver, allPortsHaveData)
    GET_PORT_DATA(hmiDataContainer,           m_framework->asCustomFramework()->m_hmiDataReceiver, allPortsHaveData)
    GET_PORT_DATA(dayNightThemeContainer,     m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, allPortsHaveData)
    GET_PORT_DATA(parkStatusContainer,        m_framework->asCustomFramework()->m_parkHmiParkingStatusReceiver, allPortsHaveData)
    GET_PORT_DATA(freeparkingActiveContainer, m_framework->asCustomFramework()->m_freeparkingActiveReceiver, allPortsHaveData)
    GET_PORT_DATA(parkTypeContainer,          m_framework->asCustomFramework()->m_parkHmiParkParkngTypeSeldReceiver, allPortsHaveData)
    GET_PORT_DATA(odometryContainer,          m_framework->asCustomFramework()->m_odometryReceiver, allPortsHaveData)

    if (!allPortsHaveData)
    {
        return;
    }
    const bool touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
    const bool isNewMode = m_parkingMode != (freeparkingActiveContainer->m_Data ? FREEPARKING : NONE);
    setDayNightTheme(dayNightThemeContainer->m_Data);
    setRotateTheme(static_cast<cc::target::common::EThemeTypeHU>(huRotateStatusContainer->m_Data));
    setPositionHori(g_freeparkingButtonSettings->m_horiPos);
    setPositionVert(g_freeparkingButtonSettings->m_vertPos);
    setHuX(hmiDataContainer->m_Data.m_huX);
    setHuY(hmiDataContainer->m_Data.m_huY);
    setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    m_parkStatus = parkStatusContainer->m_Data;
    m_parkingMode = (freeparkingActiveContainer->m_Data ? FREEPARKING : NONE);
    const cc::target::common::EVehMoveDir l_parkingStandStill = static_cast<cc::target::common::EVehMoveDir>(odometryContainer->m_Data.m_vehMoveDir);
    const auto l_parkType = parkTypeContainer->m_Data;

    if (isNewMode)
    {
        setState(INVALID);
    }

    ButtonState currentState = INVALID;
    if (
      ((m_parkStatus == ParkStatusType::PARK_Searching && l_parkingStandStill == cc::target::common::EVehMoveDir::STANDSTILL) || m_parkStatus == ParkStatusType::PARK_AssistStandby) &&
      (l_parkType == cc::target::common::EParkngTypeSeld::PARKING_IN))
    {
      currentState = AVAILABLE;
    }

    if (currentState == INVALID)
    {
        setState(INVALID);
    }
    else
    {
        if (checkTouchInsideResponseArea() && (touchStatus() == TOUCH_DOWN || touchStatus() == TOUCH_MOVE)) // PRQA S 2759
        {
            currentState = PRESSED;
        }
        if (touchStatusChanged && touchStatus() == TOUCH_UP && getState() == PRESSED)
        {
            currentState = RELEASED;
        }
        setState(currentState);
    }

    if (currentState != RELEASED)
    {
        auto& freeparkingButtonPressContainer = cc::daddy::CustomDaddyPorts::sm_FreeParkingButtonPressDaddy_SenderPort.reserve();
        freeparkingButtonPressContainer.m_Data = false;
        cc::daddy::CustomDaddyPorts::sm_FreeParkingButtonPressDaddy_SenderPort.deliver();
    }
}

} // namespace freeparkingbutton
} // namespace button
} // namespace assets
} // namespace cc
