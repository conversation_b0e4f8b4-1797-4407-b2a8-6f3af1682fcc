//-------------------------------------------------------------------------------
// Copyright (c) 2021 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/pdc/inc/PdcSettings.h"
#include "pc/svs/util/math/inc/Interpolator.h"

namespace cc
{
namespace assets
{
namespace pdc
{
namespace
{

pc::util::math::LinearInterpolator<Color> g_distColorIn0;
pc::util::math::LinearInterpolator<Color> g_distColorIn1;
pc::util::math::LinearInterpolator<Color> g_distColorIn2;
pc::util::math::LinearInterpolator<Color> g_distColorOut0;
pc::util::math::LinearInterpolator<Color> g_distColorOut1;
pc::util::math::LinearInterpolator<Color> g_distColorOut2;

} // namespace

pc::util::coding::Item<PdcSettings> g_pdcSettings("PdcOverlaySettings");


void PdcSettings::updateInterpolators()
{
  static unsigned int s_lastUpdate = ~0u;
  if (g_pdcSettings->getModifiedCount() != s_lastUpdate)
  {
    s_lastUpdate = g_pdcSettings->getModifiedCount();

    g_distColorIn0.clear();
    g_distColorIn1.clear();
    g_distColorIn2.clear();
    g_distColorOut0.clear();
    g_distColorOut1.clear();
    g_distColorOut2.clear();
    for (std::size_t i = 0; i < ColorValues::NUM_COLORS; ++i)
    {
      g_distColorIn0.addSample(
        g_pdcSettings->m_colorDistances0.getDistance(i),
        g_pdcSettings->m_colorsInside0.getColor(i));
      g_distColorIn1.addSample(
        g_pdcSettings->m_colorDistances1.getDistance(i),
        g_pdcSettings->m_colorsInside1.getColor(i));
      g_distColorIn2.addSample(
        g_pdcSettings->m_colorDistances2.getDistance(i),
        g_pdcSettings->m_colorsInside2.getColor(i));
      g_distColorOut0.addSample(
        g_pdcSettings->m_colorDistances0.getDistance(i),
        g_pdcSettings->m_colorsOutside0.getColor(i));
      g_distColorOut1.addSample(
        g_pdcSettings->m_colorDistances1.getDistance(i),
        g_pdcSettings->m_colorsOutside1.getColor(i));
      g_distColorOut2.addSample(
        g_pdcSettings->m_colorDistances2.getDistance(i),
        g_pdcSettings->m_colorsOutside2.getColor(i));
    }
    g_distColorIn0.init();
    g_distColorIn1.init();
    g_distColorIn2.init();
    g_distColorOut0.init();
    g_distColorOut1.init();
    g_distColorOut2.init();
  }
}


Color PdcSettings::getDistColorIn0(float f_distance)
{
  return g_distColorIn0.getValue(f_distance);
}

Color PdcSettings::getDistColorIn1(float f_distance)
{
  return g_distColorIn1.getValue(f_distance);
}

Color PdcSettings::getDistColorIn2(float f_distance)
{
  return g_distColorIn2.getValue(f_distance);
}


Color PdcSettings::getDistColorOut0(float f_distance)
{
  return g_distColorOut0.getValue(f_distance);
}

Color PdcSettings::getDistColorOut1(float f_distance)
{
  return g_distColorOut1.getValue(f_distance);
}

Color PdcSettings::getDistColorOut2(float f_distance)
{
  return g_distColorOut2.getValue(f_distance);
}


} // namespace pdc
} // namespace assets
} // namespace cc
