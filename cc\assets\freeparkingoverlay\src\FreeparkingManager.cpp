//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EPF2-CN)
//  Department: CC-DA/EPF
//=============================================================================
/// @swcomponent CC DAI
/// @file  FreefreeparkingOverlayManager.cpp
/// @brief
//=============================================================================

#include "cc/assets/freeparkingoverlay/inc/FreeparkingManager.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"

#include "pc/svs/animation/inc/AnimationManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

#include "cc/assets/freeparkingoverlay/inc/FreeparkingOverlay.h"

#include "cc/assets/corner/inc/ViewportCorner.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/sm/viewmode/inc/ViewModeStateMachine.h"
#include "cc/views/planview/inc/PlanView.h"

#include "cc/imgui/inc/imgui_manager.h"

#ifdef TARGET_STANDALONE
#include "cc/imgui/inc/implot/implot.h"
#endif

using pc::util::logging::g_AppContext;

#define GET_PORT_DATA(dataDaddy, port, allPortHaveDataFlag)                                                            \
    const auto dataDaddy  = (port).getData();  /* PRQA S 1030 */                                                       \
    (allPortHaveDataFlag) = (allPortHaveDataFlag) && ((dataDaddy) != nullptr);                                         \
    if ((dataDaddy) == nullptr)                                                                                        \
    {                                                                                                                  \
        XLOG_ERROR(g_AppContext, #port << " doesn't have data!\n");                                                    \
    }

namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

pc::util::coding::Item<FreeparkingManagerSettings> g_managerSettings("FreeparkingManager");

void clampAngleDegree(vfc::float32_t& f_angle)
{
    while (f_angle < 0.0f)
    {
        f_angle += 360.0f;
    }
    while (f_angle > 360.0f)
    {
        f_angle -= 360.0f;
    }

    if (f_angle > 180.0f)
    {
        f_angle = -(360.0f - f_angle);
    }
}

void clampAngleRad(vfc::float32_t& f_angle)
{
    while (f_angle < 0.0f) // PRQA S 4234
    {
        f_angle += 2.f * static_cast<vfc::float32_t>(osg::PI);
    }
    while (f_angle > 2.f * static_cast<vfc::float32_t>(osg::PI)) // PRQA S 4234
    {
        f_angle -= 2.f * static_cast<vfc::float32_t>(osg::PI);
    }
}

//!
//! FreeparkingManager
//!
FreeparkingManager::FreeparkingManager(pc::core::Framework* const f_framework)
    : osg::Group()
    , m_framework{f_framework}
    , m_freeparkingOverlayAssets{}
    , m_lastUpdate{}
    , m_SlitheringFlag{}
    , m_MVPmatrix{}
    , m_MVPmatrixValid{}
    , m_spotSize{}        // slot size in meter
    , m_centerPosition{g_managerSettings->m_defaultPositionParallel}
    , m_rotateAngle{g_managerSettings->m_defaultAngle}
    , m_horizontalPad{g_managerSettings->m_horizontalPad_default}
    , m_viewport{}
    , m_viewportHori{cc::core::g_views->m_apaParkingPlanViewport}
    , m_viewportVert{cc::core::g_views->m_vertParkingPlanViewport}
    , m_isUserFinishedMoving{}
    , m_firstEntry{}
    , m_isSlotMovedByUser{false}
    , m_canReset{false}
    , m_360Rotate{false}
    , m_previousIsLeft{}
    , m_lastConfigUpdate{~0u}
    , m_SlitherPos{}
    , m_prevSlitherActionType{UNKOWN}
    , m_SlitherActionType{UNKOWN}
    , m_SlitherBeginPos{}
    , m_FrontLeft_vertex_screen{osg::Vec3f(0.f, 0.f, 0.f)}
    , m_FrontRight_vertex_sreen{osg::Vec3f(0.f, 0.f, 0.f)}
    , m_RearLeft_vertex_screen{osg::Vec3f(0.f, 0.f, 0.f)}
    , m_RearRight_vertex_screen{osg::Vec3f(0.f, 0.f, 0.f)}
    , m_Center_vertex_screen{osg::Vec3f(0.f, 0.f, 0.f)}
    , m_viewportTopLeft{osg::Vec2f(0.f, 0.f)}
    , m_viewportTopRight{osg::Vec2f(0.f, 0.f)}
    , m_viewportBottomLeft{osg::Vec2f(0.f, 0.f)}
    , m_viewportBottomRight{osg::Vec2f(0.f, 0.f)}
    , m_spotRect{g_managerSettings->m_defaultPositionParallel}
    , m_SlotOrientation{}
    , m_SlotOrientationStart{}
    , m_freeparkingActive{false}
    , m_freeparkingType{freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL}
    , m_hmiData{} // PRQA S 2427
    , m_touchStatus{}
    , m_twoFingerTouchStatus{}
    , m_parkable{}
    , m_twoFingerState{}
    , m_parkingSlotStateCtr{}
    , m_outputAngle{}
    , m_outputSlotCenter{}
    , m_outputRearAxleCenter{}
    , m_SlotOrientationTarget{}
{
    setName("FreeparkingManager");
    setNumChildrenRequiringUpdateTraversal(1u);
    setCullingActive(false);
    // setCullCallback(m_touchScreenCallback.get());

    //! setup parking spots
    m_freeparkingOverlayAssets = new osg::Switch;
    m_freeparkingOverlayAssets->setName("FreeparkingAssets");
    m_SlotOrientation.m_yawAngleRaw = 0.0f;
    m_SlotOrientation.m_CenterPos   = g_managerSettings->m_defaultPositionParallel;
    m_viewport                      = (m_horizontalPad) ? &m_viewportHori : &m_viewportVert;
    m_freeparkingOverlayAssets->addChild(new FreeparkingOverlay, false); // PRQA S 3803
    osg::Group::addChild(m_freeparkingOverlayAssets.get());              // PRQA S 3803
    // TODO: meterPerPixel should be obtained from mainViewport class
    const vfc::float32_t l_viewPortWidthSizePixel  = static_cast<vfc::float32_t>(m_viewport->m_size.x());
    const vfc::float32_t l_viewPortWidthSizeMeters = cc::views::planview::g_planView->m_widthMetersParkingHori;
    // m_meterPePixel                          = l_viewPortWidthSizeMeters / l_viewPortWidthSizePixel;
    updateFreeparkingSpotSize();
}

void FreeparkingManager::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        if (m_lastUpdate != f_nv.getFrameStamp()->getFrameNumber())
        {
            if (m_lastUpdate + 1 != f_nv.getFrameStamp()->getFrameNumber() && m_freeparkingActive )
            {
                reset();
                m_state     = State::Reset;
                m_startTime = static_cast<vfc::float32_t>(f_nv.getFrameStamp()->getSimulationTime());
            }
            m_lastUpdate = f_nv.getFrameStamp()->getFrameNumber();
            update(static_cast<vfc::float32_t>(f_nv.getFrameStamp()->getSimulationTime()));
            logInternal();
        }
    }
    if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
    {
        osgUtil::CullVisitor* const l_cv = static_cast<osgUtil::CullVisitor*>(&f_nv);
        m_MVPmatrix                = (*(l_cv->getModelViewMatrix())) * (*(l_cv->getProjectionMatrix()));
        m_MVPmatrixValid           = true;
    }
    osg::Group::traverse(f_nv);
}

void FreeparkingManager::reset()
{
    m_SlitherPos.StartPos                 = osg::Vec2f(0.0f, 0.0f);
    m_SlitherPos.EndPos                   = osg::Vec2f(0.0f, 0.0f);
    m_SlotOrientationStart.m_CenterPos    = pc::vehicle::g_mechanicalData->getCenter();
    m_SlotOrientationStart.m_yawAngleRaw  = (0.0f);
    switch (m_freeparkingType)
    {
    case freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_CROSS:
    {
        m_SlotOrientationTarget.m_CenterPos = g_managerSettings->m_defaultPositionCross;
        m_SlotOrientationTarget.m_yawAngleRaw = osg::DegreesToRadians(90.0f);
        break;
    }
    case freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_DIAGONAL:
    {
        m_SlotOrientationTarget.m_CenterPos = g_managerSettings->m_defaultPositionDiagonal;
        m_SlotOrientationTarget.m_yawAngleRaw = osg::DegreesToRadians(45.0f);
        break;
    }
    case freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL:
    default:
    {
        m_SlotOrientationTarget.m_CenterPos = g_managerSettings->m_defaultPositionParallel;
        m_SlotOrientationTarget.m_yawAngleRaw = osg::DegreesToRadians(0.0f);
        break;
    }
    }
    updateSpotSize();
    FreeparkingOverlay* const l_freeparkingOverlay = getFreeparkingOverlay(0u);
    l_freeparkingOverlay->setParkingPlanState(ParkingPlaneState::DEFAULT);
    m_isSlotMovedByUser                   = false;
    m_firstEntry                          = true;
}

void FreeparkingManager::updateInput(vfc::float32_t f_currentTime)
{
    using namespace cc::target::common;
    if (g_managerSettings->getModifiedCount() != m_lastConfigUpdate)
    {
        updateFreeparkingSpotSize();
        m_lastConfigUpdate = g_managerSettings->getModifiedCount();
    }

    const auto customFramework  = m_framework->asCustomFramework();
    bool allPortsHaveData = true;
    GET_PORT_DATA(hmiDataContainer, customFramework->m_hmiDataReceiver, allPortsHaveData)
    GET_PORT_DATA(huTouchContainer, customFramework->m_HUTouchTypeReceiver, allPortsHaveData)
    GET_PORT_DATA(parkhmiContainer, customFramework->m_ParkhmiToSvs_ReceiverPort, allPortsHaveData)
    GET_PORT_DATA(huTwoFingerTouchContainer, customFramework->m_HUTwoFingerTouchTypeReceiver, allPortsHaveData)

    if (!allPortsHaveData)
    {
        return;
    }

    m_previousIsLeft          = m_centerPosition.y() > 0.0f;
    bool l_parkingTypeChanged = false;
    bool l_freeparkingActive= false;
    l_freeparkingActive = (parkhmiContainer->m_Data.m_parkingStage == EParkingStage::FreeParking);
    // switch (parkhmiContainer->m_Data.m_freeParkingIn.m_parkStage)
    // {
    // case EFreeParkingStage::InFpMoving: // not show any slot
    // {
    //     m_freeparkingActive = false;
    //     m_canReset          = false;
    //     break;
    // }
    // case EFreeParkingStage::InFpStandstill: // show freeparking slot
    // {
    //     // case EFreeParkingStage::GuidanceStart:  // show target slot during guidance
    //     m_freeparkingActive = (parkhmiContainer->m_Data.m_parkingStage == EParkingStage::FreeParking);
    //     m_canReset          = false;
    //     break;
    // }
    // case EFreeParkingStage::None:           // not show any slot
    // case EFreeParkingStage::GuidanceFinish: // not show any slot
    // default:
    // {
    //     m_freeparkingActive = false;
    //     m_canReset          = true;
    // }
    // }

    m_parkable = parkhmiContainer->m_Data.m_freeParkingIn.m_isSlotParkable;

    const bool l_previous360Rotate = m_360Rotate;
    m_360Rotate              = parkhmiContainer->m_Data.m_freeParkingIn.m_is360FreeParking;

    const auto previousFreeparkingType = m_freeparkingType;
    // clang-format off
    switch (parkhmiContainer->m_Data.m_freeParkingIn.m_slotType)
    {
    case EFreeParkingSlotType::HorizontalSlot:
    {
        m_freeparkingType = freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_CROSS; break;
    }
    case EFreeParkingSlotType::VerticalSlot:
    {
        m_freeparkingType = freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL; break;
    }
    case EFreeParkingSlotType::DiagonalSlot:
    {
        m_freeparkingType = freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_DIAGONAL; break;
    }
    default:
    {
        m_freeparkingType = freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_CROSS; break;
    }
    }
    // clang-format on

    if (m_360Rotate)
    {
        m_freeparkingType = freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL;
    }
    if (previousFreeparkingType != m_freeparkingType)
    {
        l_parkingTypeChanged = true;
    }

    m_hmiData = hmiDataContainer->m_Data;
    const int l_newParkingSLotStateCtr = parkhmiContainer->m_Data.m_freeParkingIn.m_ctr;
    const TouchStatus          newTouchStatus          = static_cast<TouchStatus>(huTouchContainer->m_Data);
    const TwoFingerTouchStatus newTwoFingerTouchStatus = static_cast<TwoFingerTouchStatus>(huTwoFingerTouchContainer->m_Data);
    m_prevSlitherActionType                      = m_SlitherActionType;
    m_isUserFinishedMoving =
        (m_touchStatus == Down || m_touchStatus == Move) && (newTouchStatus == Invalid || newTouchStatus == Up);
    m_touchStatus          = newTouchStatus;
    m_twoFingerTouchStatus = newTwoFingerTouchStatus;
    if (newTwoFingerTouchStatus == TwoFingerTouchStatus::TwoFingerStart)
    {
        XLOG_INFO(g_AppContext, "TwoFingerStart!!!!!!!!!!!!!!!!!!!!!!!!!");
    }
    if (l_freeparkingActive && (m_360Rotate != l_previous360Rotate))
    {
        m_SlotOrientation.m_yawAngleRaw = 0.0f;
    }
    FreeparkingOverlay* const l_freeparkingOverlay = getFreeparkingOverlay(0u);

    if (l_freeparkingActive && l_parkingTypeChanged && !m_360Rotate)
    {
        if (!m_isSlotMovedByUser)
        {
            switch (m_freeparkingType)
            {
            case freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_CROSS:
            {
                m_SlotOrientation.m_CenterPos = g_managerSettings->m_defaultPositionCross;
                break;
            }
            case freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_DIAGONAL:
            {
                m_SlotOrientation.m_CenterPos = g_managerSettings->m_defaultPositionDiagonal;
                break;
            }
            case freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL:
            default:
            {
                m_SlotOrientation.m_CenterPos = g_managerSettings->m_defaultPositionParallel;
            }
            }
            // clang-format on
            l_freeparkingOverlay->setParkingPlanState(ParkingPlaneState::DEFAULT);
        }
        const bool l_isLeft = m_SlotOrientation.m_CenterPos.y() > 0.0f;
        if (m_freeparkingType == freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_CROSS)
        {
            m_SlotOrientation.m_yawAngleRaw = osg::DegreesToRadians((!l_isLeft) ? 90.0f : 90.0f);
        }
        else if (m_freeparkingType == freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_DIAGONAL)
        {
            m_SlotOrientation.m_yawAngleRaw = osg::DegreesToRadians((!l_isLeft) ? 45.0f : 45.0f);
        }
        else
        {
            m_SlotOrientation.m_yawAngleRaw = 0.0f;
        }
    }

    if (m_parkingSlotStateCtr != l_newParkingSLotStateCtr)
    {
        if (m_parkable == cc::target::common::EFreeParkingSlotState::AVAILABLE)
        {
            l_freeparkingOverlay->setParkingPlanState(ParkingPlaneState::AVAILABLE);
        }
        else
        {
            l_freeparkingOverlay->setParkingPlanState(ParkingPlaneState::UNAVAILABLE);
        }
        m_parkingSlotStateCtr = l_newParkingSLotStateCtr;
    }

    if (m_freeparkingActive && !l_freeparkingActive)
    {
        reset();
        m_SlotOrientation.m_CenterPos.x() = m_SlotOrientationStart.m_CenterPos.x();
        m_SlotOrientation.m_CenterPos.y() = m_SlotOrientationStart.m_CenterPos.y();
        m_SlotOrientation.m_yawAngleRaw   = m_SlotOrientationStart.m_yawAngleRaw ;
    }
    if (!m_freeparkingActive && l_freeparkingActive)
    {
        m_state     = State::Reset;
        m_startTime = f_currentTime;
    }
    m_freeparkingActive = l_freeparkingActive;
}

void FreeparkingManager::onInit(vfc::float32_t f_startupTime)
{
    using namespace cc::target::common;
    const auto customFramework = m_framework->asCustomFramework();

    bool allPortsHaveData = true;
    GET_PORT_DATA(parkhmiContainer, customFramework->m_ParkhmiToSvs_ReceiverPort, allPortsHaveData)
    if (!allPortsHaveData)
    {
        return;
    }
    m_freeparkingActive = (parkhmiContainer->m_Data.m_parkingStage == EParkingStage::FreeParking);
    if (!m_freeparkingActive)
    {
        m_freeparkingOverlayAssets->setValue(0u, false);
        FreeparkingOverlay* const l_freeparkingOverlay = getFreeparkingOverlay(0u);
        l_freeparkingOverlay->setVisibility(false);
        l_freeparkingOverlay->setRotateButtonVisibility(false);
        return;
    }
    // clang-format off
    switch (parkhmiContainer->m_Data.m_freeParkingIn.m_slotType)
    {
    case EFreeParkingSlotType::HorizontalSlot: { m_freeparkingType = freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_CROSS; break; }
    case EFreeParkingSlotType::VerticalSlot: { m_freeparkingType = freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL; break; }
    case EFreeParkingSlotType::DiagonalSlot: { m_freeparkingType = freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_DIAGONAL; break; }
    default:
    {
        m_freeparkingType = freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_CROSS; break;
    }
    }
    // clang-format on
    m_360Rotate = parkhmiContainer->m_Data.m_freeParkingIn.m_is360FreeParking;
    if (m_360Rotate)
    {
        m_freeparkingType = freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL;
    }
    reset();
    m_state     = State::Reset;
    m_startTime = f_startupTime;
}

void FreeparkingManager::onReset(vfc::float32_t f_currentTime)
{
    m_freeparkingOverlayAssets->setValue(0u, true);
    FreeparkingOverlay* const l_freeparkingOverlay = getFreeparkingOverlay(0u);
    l_freeparkingOverlay->setVisibility(true);
    l_freeparkingOverlay->setRotateButtonVisibility(true);
    vfc::float32_t elapsed     = 0.0f;
    vfc::float32_t t           = 0.0f;
    elapsed = f_currentTime - m_startTime;
    t = std::min(elapsed / g_managerSettings->m_startupTime, 1.0f);
    vfc::float32_t progress    = 0.0f;
    progress = pc::util::smootherstep(0.0f, 1.0f, t);

    m_SlotOrientation.m_CenterPos.x() = (1.0f - progress) * m_SlotOrientationStart.m_CenterPos.x() + (progress) * m_SlotOrientationTarget.m_CenterPos.x();
    m_SlotOrientation.m_CenterPos.y() = (1.0f - progress) * m_SlotOrientationStart.m_CenterPos.y() + (progress) * m_SlotOrientationTarget.m_CenterPos.y();
    m_SlotOrientation.m_yawAngleRaw   = (1.0f - progress) * m_SlotOrientationStart.m_yawAngleRaw + (progress) * m_SlotOrientationTarget.m_yawAngleRaw;
    updateFreeparkingOverlay();

    if (t >= 1.0f)
    {
        m_state = State::Active;
        m_firstEntry = true;
        m_isUserFinishedMoving = true; // send output at the first time
        m_isSlotMovedByUser = false;
        m_isSlotMovingByUser = false;
        m_SlitherActionType = UNKOWN;
        m_SlitherPos.StartPos.set(0.0f, 0.0f);
        m_SlitherPos.EndPos.set(0.0f, 0.0f);
        m_SlitherBeginPos.set(0.0f, 0.0f);
    }
}

void FreeparkingManager::onActive(vfc::float32_t f_currentTime)
{
    // TODO adapt logic
    if (IMGUI_GET_BUTTON_BOOL("FreeParking", "Reset"))
    {
        m_state = State::Init;
        return;
    }
    vfc::nop(f_currentTime); // not in use for now, but keep it for future use
    // using namespace cc::target::common;
    updateInput(f_currentTime);

    FreeparkingOverlay* const l_freeparkingOverlay = getFreeparkingOverlay(0u);
    l_freeparkingOverlay->setParkable(m_parkable);
    if (m_freeparkingActive)
    {
        updateViewPortLayoutInfo();
        getSlitherStartEndPoint();
        getSpotCornerCoorandSlitherType();
        UpdateSlotOrientation(); // update the orientation and rotate center
        updateSpotSize();
        updateFreeparkingOverlay();
        m_freeparkingOverlayAssets->setValue(0u, true);
        l_freeparkingOverlay->setVisibility(true);
        l_freeparkingOverlay->setRotateButtonVisibility(true);
    }
    else
    {
        m_freeparkingOverlayAssets->setValue(0u, false);
        l_freeparkingOverlay->setVisibility(false);
        l_freeparkingOverlay->setRotateButtonVisibility(false);
    }

    if (m_freeparkingActive && (m_SlitherActionType == ROTATION || m_SlitherActionType == TRANSLATION ||
                                m_SlitherActionType == TELEPORTATION) ||
        m_SlitherActionType == TWO_FINGER_ROTATION)
    {
        m_isSlotMovedByUser  = true;
        m_isSlotMovingByUser = true;
    }
    else
    {
        m_isSlotMovingByUser = false;
    }

    prepareOutput();
    if ((m_isUserFinishedMoving) || (m_freeparkingActive && m_firstEntry))
    {
        sendOutput();
        if (m_freeparkingActive && m_firstEntry)
        {
            m_firstEntry = false;
        }
    }

    if (m_isSlotMovingByUser)
    {
        l_freeparkingOverlay->setParkingPlanState(ParkingPlaneState::DEFAULT);
    }

#ifdef TARGET_STANDALONE
    {
        auto&      l_fpSlot = cc::daddy::CustomDaddyPorts::sm_FreeParkingSlotInternal_SenderPort.reserve();
        osg::Vec2f l_frontLeft;
        osg::Vec2f l_rearLeft;
        osg::Vec2f l_rearRight;
        osg::Vec2f l_frontRight;
        m_spotRect.updateRectPoints(l_frontLeft, l_rearLeft, l_rearRight, l_frontRight);
        if (m_outputSlotCenter.y() > 0.0f)
        {
            l_fpSlot.m_Data.m_frontLeft  = l_frontRight;
            l_fpSlot.m_Data.m_rearLeft   = l_frontLeft;
            l_fpSlot.m_Data.m_rearRight  = l_rearLeft;
            l_fpSlot.m_Data.m_frontRight = l_rearRight;
        }
        else
        {
            l_fpSlot.m_Data.m_frontLeft  = l_rearLeft;
            l_fpSlot.m_Data.m_rearLeft   = l_rearRight;
            l_fpSlot.m_Data.m_rearRight  = l_frontRight;
            l_fpSlot.m_Data.m_frontRight = l_frontLeft;
        }
        cc::daddy::CustomDaddyPorts::sm_FreeParkingSlotInternal_SenderPort.deliver();
    }
#endif
}

void FreeparkingManager::update(vfc::float32_t f_currentTime)
{
    switch (m_state)
    {
    case State::Init:
    {
        onInit(f_currentTime);
        break;
    }
    case State::Reset:
    {
        onReset(f_currentTime);
        break;
    }
    case State::Active:
    {
        onActive(f_currentTime);
        break;
    }
    default:
    {
        break;
    }
    }
}

void FreeparkingManager::updateSpotSize()
{
    m_spotSize = osg::Vec2f(
        pc::vehicle::g_mechanicalData->getLength() + g_managerSettings->m_lengthOffset,
        pc::vehicle::g_mechanicalData->getWidthWithMirrors() + g_managerSettings->m_widthOffset);
}

// Just init the spot size by default values
void FreeparkingManager::updateFreeparkingSpotSize()
{
    const vfc::float32_t l_slotWidthPixel              = g_managerSettings->m_imageProperties.m_slotWidthPixel;
    const vfc::float32_t l_slotHeightPixel             = g_managerSettings->m_imageProperties.m_slotHeightPixel;
    const vfc::float32_t l_rotateIconWidthExtendPixel  = g_managerSettings->m_imageProperties.m_rotateIconWidthExtendPixel;
    const vfc::float32_t l_rotateIconHeightExtendPixel = g_managerSettings->m_imageProperties.m_rotateIconHeightExtendPixel;
    const vfc::float32_t l_widthToHeight               = l_slotHeightPixel / l_slotWidthPixel;
    // In meter size
    m_spotSize.x() = g_managerSettings->m_freeparkingSlotWidth;
    m_spotSize.y() = m_spotSize.x() * l_widthToHeight;
}

void FreeparkingManager::updateFreeparkingOverlay()
{
    FreeparkingOverlay* const l_freeparkingOverlay = getFreeparkingOverlay(0u);
    const osg::Vec2f          l_spotOverlaySize    = osg::Vec2f(0.f, 0.f);
    m_centerPosition                         = m_SlotOrientation.m_CenterPos;
    m_rotateAngle                            = osg::RadiansToDegrees(m_SlotOrientation.m_yawAngleRaw);
    // clampAngleDegree(m_rotateAngle);

    const bool l_isLeft = m_centerPosition.y() > 0.0f;

    osg::Vec2f l_frontLeft;
    osg::Vec2f l_rearLeft;
    osg::Vec2f l_rearRight;
    osg::Vec2f l_frontRight;
    m_spotRect.setLength(m_spotSize.x());
    m_spotRect.setWidth(m_spotSize.y());
    m_spotRect.setAngle(m_rotateAngle);
    m_spotRect.setCenterPoint(m_centerPosition);
    m_spotRect.updateRectPoints(l_frontLeft, l_rearLeft, l_rearRight, l_frontRight);

    constexpr vfc::uint8_t l_parkSlotTypeRemap[4] = {
        4u, 1u, 0u, 2u}; // parkspot input define is different from park spot plane type define
    l_freeparkingOverlay->setType(l_parkSlotTypeRemap[static_cast<vfc::int32_t>(m_freeparkingType)]);
    l_freeparkingOverlay->setSize(m_spotSize);
    l_freeparkingOverlay->setPosition(m_centerPosition);
    l_freeparkingOverlay->setAngle(m_rotateAngle);
    l_freeparkingOverlay->setIsLeftSide(l_isLeft);
    l_freeparkingOverlay->setSelectionState(
        cc::assets::parkingspots::ParkingSpot::SELECTABLE, cc::assets::parkingspots::ParkingSpot::SELECTABLE);
    l_freeparkingOverlay->dirty();
}

void FreeparkingManager::updateViewPortLayoutInfo()
{
    if (m_framework->asCustomFramework()->m_HURotationPadReceiver.hasData())
    {
        const cc::daddy::HURotateStatusDaddy_t* const l_rotationPad =
            m_framework->asCustomFramework()->m_HURotationPadReceiver.getData();
        m_horizontalPad = (2u != l_rotationPad->m_Data);
    }
    m_viewport = (m_horizontalPad) ? &(m_viewportHori) : &(m_viewportVert);
}

void FreeparkingManager::prepareOutput()
{
    const bool           l_isLeft               = m_centerPosition.y() > 0.0f;
    const vfc::float32_t l_slotAngle            = m_spotRect.getAngle();
    const osg::Vec2f     l_slotCenter           = m_spotRect.getCenterPoint();
    const osg::Vec2f     l_rearAxleCenterOffset = pc::vehicle::g_mechanicalData->getCenter();
    m_outputAngle                         = l_slotAngle;
    m_outputSlotCenter                    = m_spotRect.getCenterPoint();
    m_outputRearAxleCenter                = l_slotCenter - pc::util::rotate(l_rearAxleCenterOffset, l_slotAngle);
}

void FreeparkingManager::sendOutput()
{
    IMGUI_LOG("FreeParking", "SendNewPort", "TRUE");
    auto& l_fpSlot        = cc::daddy::CustomDaddyPorts::sm_FreeParkingSlot_SenderPort.reserve();
    l_fpSlot.m_Data.m_x   = m_centerPosition.x() * 100.0f;
    l_fpSlot.m_Data.m_y   = m_centerPosition.y() * 100.0f;
    l_fpSlot.m_Data.m_yaw = osg::RadiansToDegrees(m_outputAngle);
    cc::daddy::CustomDaddyPorts::sm_FreeParkingSlot_SenderPort.deliver();
}

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc
