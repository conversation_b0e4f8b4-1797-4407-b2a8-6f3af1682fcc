//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
#include "cc/assets/uielements/inc/Vehicle2DOverlay.h"
#include "CustomSystemConf.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/osgx/inc/Utils.h"

#include "cc/assets/uielements/inc/HmiElementsSettings.h"
#include "cc/assets/impostor/inc/TransparentVehicleSettings.h"

#include "cc/imgui/inc/imgui_manager.h"
#include "cc/views/planview/inc/PlanViewEnlargeCallback.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/math/inc/CommonMath.h"

#include <algorithm>

using pc::util::logging::g_AppContext;

using cc::views::planview::g_planViewEnlargeSettings;

#define CHECK_PORT_DATA(containerName, port, flag)                                                                     \
    auto const containerName = port.getData();                                                                            \
    if (containerName == nullptr)                                                                                    \
    {                                                                                                                  \
        (flag) = false;                                                                                                \
    }
#define GET_PORT_DATA(dataDaddy, port, allPortHaveDataFlag)                                                            \
    const auto dataDaddy  = (port).getData();  /* PRQA S 1030 */                                                       \
    (allPortHaveDataFlag) = (allPortHaveDataFlag) && ((dataDaddy) != nullptr);                                         \
    if ((dataDaddy) == nullptr)                                                                                        \
    {                                                                                                                  \
        XLOG_ERROR(g_AppContext, #port << " doesn't have data!\n");                                                    \
    }

namespace cc
{
namespace assets
{
namespace uielements
{

pc::util::coding::Item<Vehicle2DSettings> g_Vehicle2DSettings("Vehicle2DSettings");

float Vehicle2DOverlayManager::sm_transparencyLevel = 1.0f;

void Vehicle2DOverlayManager::setVehicleTransparent(float f_transparent)
{
    f_transparent = pc::util::clamp(f_transparent, 0.0f, 1.0f);
    if (f_transparent != sm_transparencyLevel)
    {
        sm_transparencyLevel = f_transparent;
    }
}

Vehicle2DOverlayManager::Vehicle2DOverlayManager(
    cc::core::CustomFramework* f_framework,
    pc::assets::ImageOverlays* f_overlay)
    : m_framework(f_framework)
    , m_frameTick(osg::Timer::instance()->tick())
    , m_overlay(f_overlay)
    , m_Vehicle2DOverlays()
    , m_Vehicle2DTransparentOverlays()
    , m_doorMasks()
    , m_overlays()
    , m_frontArrows()
    , m_rearArrows()
    , m_colorIndex(cc::daddy::TIME_GRAY)
    , m_lastConfigUpdate(g_Vehicle2DSettings->getModifiedCount())
    , m_parkActive(false)
    , m_theme(cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    , m_viewId(std::numeric_limits<vfc::uint32_t>::max())
    , m_enlargeStatus(cc::daddy::NO_ENLARGE)
    , m_currentPosition()
    , m_startPosition()
    , m_targetPosition()
    , m_iconScaler(1.0f)
{
    init();
    // IMGUI_SET_DEFAULT_SLIDER_FLOAT("Settings", "MaskScaleHori", 1.0f);
    // IMGUI_SET_DEFAULT_SLIDER_FLOAT("Settings", "MaskScaleVert", 1.0f);
}

bool checkParkingActiveInVehicle2dOverlay(cc::target::common::EPARKStatusR2L f_parkStatus)
{
    using namespace cc::target::common;
    return f_parkStatus == PARK_Searching || f_parkStatus == PARK_Guidance_active ||
           f_parkStatus == PARK_Guidance_suspend || f_parkStatus == PARK_Terminated || f_parkStatus == PARK_Completed ||
           f_parkStatus == PARK_Failure || f_parkStatus == PARK_AssistStandby;
}

static void setFilterLinear(osg::Texture2D* f_texture)
{
    if (f_texture != nullptr)
    {
        f_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
        f_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    }
}

static void setArrowImage(pc::assets::Icon* f_icon, const std::string& f_image)
{
    if (f_icon == nullptr)
    {
        return; // todo: maybe add more loggings.
    }
    f_icon->setImage(f_image);
    const auto l_texture = const_cast<osg::Texture2D*>(f_icon->getTexture()); // PRQA S 3066
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
}

static pc::assets::Icon* createArrowIcon()
{
    pc::assets::Icon* const l_icon = new pc::assets::Icon{};
    setArrowImage(l_icon, CONCAT_PATH(g_Vehicle2DSettings->m_arrowTexturePath));
    l_icon->setOrigin(pc::assets::Icon::Origin::TopLeft);
    l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    l_icon->setEnabled(false);
    return l_icon;
}

static inline void setIconAlpha(pc::assets::Icon* f_icon, vfc::float32_t f_alpha)
{
    // f_icon->getOrCreateStateSet()->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(f_alpha);
    if (f_icon->getNumDrawables() == 0u || f_icon == nullptr)
    {
        return;
    }
    const auto geometry = f_icon->getDrawable(0u)->asGeometry();
    if (geometry == nullptr)
    {
        return;
    }
    const auto colorArray        = static_cast<osg::Vec4Array*>(geometry->getColorArray());
    colorArray->at(0u)[3u] = f_alpha;
    colorArray->dirty();
}

static void adaptFrontArrowIcon(pc::assets::Icon* f_icon, int /*f_index*/)
{
    if (f_icon == nullptr)
    {
        return; // todo: maybe add more loggings.
    }
    // const auto       frontStartPosition = -g_Vehicle2DSettings->m_arrowOffsetPosPercentage + 50.0f;
    // const auto       positionOffset     = -g_Vehicle2DSettings->m_arrowGapPercentage * f_index; // PRQA S 3011
    // const osg::Vec2f position           = {50.0f, frontStartPosition + positionOffset};
    // const osg::Vec2f size = {g_Vehicle2DSettings->m_arrowSizePercentage, g_Vehicle2DSettings->m_arrowSizePercentage};
    // f_icon->setPosition({50.0f, frontStartPosition + positionOffset}, pc::assets::Icon::UnitType::Percentage);
    // f_icon->setSize(size, pc::assets::Icon::UnitType::Percentage);
    f_icon->setFlipVertical(true);
}

static void adaptRearArrowIcon(pc::assets::Icon* f_icon, int /*f_index*/)
{
    if (f_icon == nullptr)
    {
        return; // todo: maybe add more loggings.
    }
	// auto       frontStartPosition = g_Vehicle2DSettings->m_arrowOffsetPos + 48.5f;
    // auto       positionOffset     = g_Vehicle2DSettings->m_arrowGap * f_index;
    // auto       arrowSizePixel     = g_Vehicle2DSettings->m_arrowSize;
    // osg::Vec2f size     = {g_Vehicle2DSettings->m_arrowSizePercentage, g_Vehicle2DSettings->m_arrowSizePercentage};
    // osg::Vec2f position = {50.0f, frontStartPosition + positionOffset};
    // f_icon->setPosition({50.0f, frontStartPosition + positionOffset}, pc::assets::Icon::UnitType::Percentage);
    // f_icon->setSize(size, pc::assets::Icon::UnitType::Percentage);
}

void Vehicle2DOverlayManager::init()
{
    // ! init Vehicle2DOverlay icons
    m_Vehicle2DOverlays.clear(m_overlay);
    m_Vehicle2DTransparentOverlays.clear(m_overlay);
    m_doorMasks.clear(m_overlay);
    m_overlays.clear(m_overlay);
    m_frontArrows.clear(m_overlay);
    m_rearArrows.clear(m_overlay);
    const bool l_isHori = (m_theme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI);
    const auto l_color  = getColorFromIndex(m_colorIndex);

    // clang-format off
    // ! Door Masks
    m_doorMasks.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_trunkOpenMaskTexturePath), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_doorMasks.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_frontLeftDoorOpenMaskTexturePath), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_doorMasks.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_frontRightDoorOpenMaskTexturePath), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));

    setFilterLinear(const_cast<osg::Texture2D*>(m_doorMasks.getIcon(VEHICLE2D_TRUNK_OPEN_MASK)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_doorMasks.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_OPEN_MASK)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_doorMasks.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN_MASK)->getTexture()));

    // ! Vehicle 2D Overlay
    m_Vehicle2DOverlays.addIcon( m_overlay, createIconTopLeft(CONCAT_PATH_VEHICLE_MODEL(l_color.m_vehicleBodyOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(l_color.m_frontLeftDoorOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(l_color.m_frontRightDoorOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DOverlays.addIcon( m_overlay, createIconTopLeft(CONCAT_PATH_VEHICLE_MODEL(l_color.m_hoodOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DOverlays.addIcon( m_overlay, createIconTopLeft(CONCAT_PATH_VEHICLE_MODEL(l_color.m_rearLeftDoorOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(l_color.m_rearRightDoorOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DOverlays.addIcon( m_overlay, createIconTopLeft(CONCAT_PATH_VEHICLE_MODEL(l_color.m_trunkOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(l_color.m_frontLeftDoorClose), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(l_color.m_frontRightDoorClose), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DOverlays.addIcon( m_overlay, createIconTopLeft(CONCAT_PATH_VEHICLE_MODEL(l_color.m_hoodClose), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(l_color.m_rearLeftDoorClose), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(l_color.m_rearRightDoorClose), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DOverlays.addIcon( m_overlay, createIconTopLeft(CONCAT_PATH_VEHICLE_MODEL(l_color.m_trunkClose), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));

    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_BODY_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_HOOD_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_TRUNK_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_CLOSE)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_CLOSE)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_HOOD_CLOSE)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_CLOSE)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_CLOSE)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_TRUNK_CLOSE)->getTexture()));

    // ! Vehicle 2D Transparent Overlay
    m_Vehicle2DTransparentOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_transparent.m_vehicleBodyOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DTransparentOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_transparent.m_frontLeftDoorOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DTransparentOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_transparent.m_frontRightDoorOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DTransparentOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_transparent.m_hoodOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DTransparentOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_transparent.m_rearLeftDoorOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DTransparentOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_transparent.m_rearRightDoorOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DTransparentOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_transparent.m_trunkOpen), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DTransparentOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_transparent.m_frontLeftDoorClose), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DTransparentOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_transparent.m_frontRightDoorClose), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DTransparentOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_transparent.m_hoodClose), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DTransparentOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_transparent.m_rearLeftDoorClose), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DTransparentOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_transparent.m_rearRightDoorClose), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_Vehicle2DTransparentOverlays.addIcon( m_overlay, createIconTopLeft( CONCAT_PATH_VEHICLE_MODEL(g_Vehicle2DSettings->m_transparent.m_trunkClose), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));

    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_BODY_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_HOOD_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_TRUNK_OPEN)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_CLOSE)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_CLOSE)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_HOOD_CLOSE)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_CLOSE)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_CLOSE)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_TRUNK_CLOSE)->getTexture()));

    // ! Overlays
    m_overlays.addIcon( m_overlay, createIconTopLeft(CONCAT_PATH(g_Vehicle2DSettings->m_gearStatusDrive), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_overlays.addIcon( m_overlay, createIconTopLeft(CONCAT_PATH(g_Vehicle2DSettings->m_gearStatusReverse), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_overlays.addIcon( m_overlay, createIconTopLeft(CONCAT_PATH(g_Vehicle2DSettings->m_gearStatusPark), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    m_overlays.addIcon( m_overlay, createIconTopLeft(CONCAT_PATH(g_Vehicle2DSettings->m_gearStatusNeutral), {0.0f, 0.0f}, {0.0f, 0.0f}, l_isHori));
    setFilterLinear(const_cast<osg::Texture2D*>(m_overlays.getIcon(GEAR_STATUS_D)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_overlays.getIcon(GEAR_STATUS_R)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_overlays.getIcon(GEAR_STATUS_P)->getTexture()));
    setFilterLinear(const_cast<osg::Texture2D*>(m_overlays.getIcon(GEAR_STATUS_N)->getTexture()));
    // clang-format on

    for (int i = 0; i < g_Vehicle2DSettings->m_numArrows; i++)
    {
        const auto arrow = createArrowIcon();
        adaptFrontArrowIcon(arrow, i);
        m_frontArrows.addIcon(m_overlay, arrow);
    }
    for (int i = 0; i < g_Vehicle2DSettings->m_numArrows; i++)
    {
        const auto arrow = createArrowIcon();
        adaptRearArrowIcon(arrow, i);
        m_rearArrows.addIcon(m_overlay, arrow);
    }

    // updateReferenceView();
    updateIconsSizePosition();



    setVehicleTransparency(m_transparencyLevel);
}

void Vehicle2DOverlayManager::setVehicleTransparency(const float f_alpha)
{
    const unsigned int l_iconCount = m_Vehicle2DOverlays.getNumIcons();

    for (size_t i = 0; i < l_iconCount; i++)
    {
        const auto l_vehicleIcon = m_Vehicle2DOverlays.getIcon(i);
        if (l_vehicleIcon != nullptr)
        {
            if (l_vehicleIcon->getNumDrawables() > 0u)
            {
                const auto geometry = l_vehicleIcon->getDrawable(0u)->asGeometry();
                if (geometry != nullptr)
                {
                    const auto colorArray = static_cast<osg::Vec4Array*>(geometry->getColorArray());
                    colorArray->at(0u)[3u] = f_alpha;
                    colorArray->dirty();
                }
            }
        }
    }
}

bool Vehicle2DOverlayManager::updateInput()
{
    bool allPortsHaveData = true;
    CHECK_PORT_DATA(themeContainer, m_framework->m_SVSRotateStatusDaddy_Receiver, allPortsHaveData)
    CHECK_PORT_DATA(colorContainer, m_framework->m_vehColorSts_ReceiverPort, allPortsHaveData)
    CHECK_PORT_DATA(parkStsContainer, m_framework->m_parkHmiParkingStatusReceiver, allPortsHaveData)
    CHECK_PORT_DATA(vehTransSwitchContainer, m_framework->m_HUvehTransCCReceiver, allPortsHaveData)
    CHECK_PORT_DATA(remoteReqContainer, m_framework->m_HURemoteScreenReceiver, allPortsHaveData)
    CHECK_PORT_DATA(doorContainer, m_framework->m_doorStateReceiver, allPortsHaveData)
    CHECK_PORT_DATA(mirrorContainer, m_framework->m_mirrorStateReceiver, allPortsHaveData)
    CHECK_PORT_DATA(enlargeContainer, m_framework->m_PlanViewEnlargeStatusReceiver, allPortsHaveData)
    CHECK_PORT_DATA(gearContainer, m_framework->m_gearReceiver, allPortsHaveData)
    CHECK_PORT_DATA(speedContainer, m_framework->m_speedReceiver, allPortsHaveData)
    CHECK_PORT_DATA(displayViewContainer, m_framework->m_displayedView_ReceiverPort, allPortsHaveData)

    if (!allPortsHaveData)
    {
        XLOG_WARN(g_AppContext, "Vehicle2DOverlayManager::update() some ports don't have data!");
        return false;
    }

    const vfc::int32_t                     l_viewId            = displayViewContainer->m_Data;
    const vfc::uint8_t                     l_colorIndex        = colorContainer->m_Data;
    const bool                             l_curParkActive     = checkParkingActiveInVehicle2dOverlay(parkStsContainer->m_Data);
    const vfc::uint8_t                     l_transparentSwitch = vehTransSwitchContainer->m_Data;
//    const ERemoteScreenMode                l_remoteReq         = static_cast<ERemoteScreenMode>(remoteReqContainer->m_Data);
//    const cc::daddy::PlanViewEnlargeStatus l_enlargeStatus     = enlargeContainer->m_Data;
    const cc::target::common::EThemeTypeHU l_currentTheme =
        static_cast<cc::target::common::EThemeTypeHU>(themeContainer->m_Data);

    m_gearStatus         = static_cast<pc::daddy::EGear>(gearContainer->m_Data);
    m_speed              = speedContainer->m_Data;
    m_frontRightDoorOpen = pc::daddy::CARDOORSTATE_CLOSED != doorContainer->m_Data[pc::daddy::CARDOOR_FRONT_RIGHT];
    m_frontLeftDoorOpen  = pc::daddy::CARDOORSTATE_CLOSED != doorContainer->m_Data[pc::daddy::CARDOOR_FRONT_LEFT];
    m_hoodOpen           = pc::daddy::CARDOORSTATE_CLOSED != doorContainer->m_Data[pc::daddy::CARDOOR_HOOD];
    m_trunkOpen          = pc::daddy::CARDOORSTATE_CLOSED != doorContainer->m_Data[pc::daddy::CARDOOR_TRUNK];
    m_rearRightDoorOpen  = pc::daddy::CARDOORSTATE_CLOSED != doorContainer->m_Data[pc::daddy::CARDOOR_REAR_RIGHT];
    m_rearLeftDoorOpen   = pc::daddy::CARDOORSTATE_CLOSED != doorContainer->m_Data[pc::daddy::CARDOOR_REAR_LEFT];
    m_leftSideMirrorFlapped = pc::daddy::MIRRORSTATE_FLAPPED == mirrorContainer->m_Data[static_cast<vfc::uint8_t>(pc::daddy::SIDEMIRROR_LEFT)];
    m_rightSideMirrorFlapped = pc::daddy::MIRRORSTATE_FLAPPED == mirrorContainer->m_Data[static_cast<vfc::uint8_t>(pc::daddy::SIDEMIRROR_RIGHT)];
    m_allDoorsAndMirrorsClosed =
        (pc::daddy::CARDOORSTATE_CLOSED == doorContainer->m_Data[pc::daddy::CARDOOR_FRONT_RIGHT]) &&
        (pc::daddy::CARDOORSTATE_CLOSED == doorContainer->m_Data[pc::daddy::CARDOOR_FRONT_LEFT]) &&
        (pc::daddy::CARDOORSTATE_CLOSED == doorContainer->m_Data[pc::daddy::CARDOOR_TRUNK]) &&
        (pc::daddy::CARDOORSTATE_CLOSED == doorContainer->m_Data[pc::daddy::CARDOOR_HOOD]) &&
        (pc::daddy::CARDOORSTATE_CLOSED == doorContainer->m_Data[pc::daddy::CARDOOR_REAR_RIGHT]) &&
        (pc::daddy::CARDOORSTATE_CLOSED == doorContainer->m_Data[pc::daddy::CARDOOR_REAR_LEFT]);
    // &&
    // (pc::daddy::MIRRORSTATE_NOT_FLAPPED ==
    //  mirrorContainer->m_Data[static_cast<vfc::uint8_t>(pc::daddy::SIDEMIRROR_LEFT)]) &&
    // (pc::daddy::MIRRORSTATE_NOT_FLAPPED ==
    //  mirrorContainer->m_Data[static_cast<vfc::uint8_t>(pc::daddy::SIDEMIRROR_RIGHT)]);

    if (m_overlay->getReferenceView() != nullptr)
    {
        const auto viewport = m_overlay->getReferenceView()->getViewport();
        if (viewport != nullptr)
        {
            osg::Vec2f newViewportSize = {
                static_cast<vfc::float32_t>(viewport->width()), static_cast<vfc::float32_t>(viewport->height())};
            if ((m_viewportSize.x() != newViewportSize.x()) || (m_viewportSize.y() != newViewportSize.y()))
            {
                m_dirty        = true;
                m_viewportSize = newViewportSize;
            }
        }
    }

    if ((m_theme != l_currentTheme) || (m_parkActive != l_curParkActive) ||
        (m_viewId != l_viewId) || IMGUI_GET_CHECKBOX_BOOL("Settings", "Vehicle2D Dirty") || (m_enlargeProgressPercentage != 1.0f))
    {
        m_dirty = true;
    }
    if ((m_colorIndex != l_colorIndex))
    {
        m_dirtyColor = true;
    }
    m_colorIndex    = l_colorIndex;
    m_theme         = l_currentTheme;
    m_parkActive    = l_curParkActive;
    m_viewId        = l_viewId;
    m_isImageInImage =
        (m_viewId == EScreenID_IMAGE_IN_IMAGE_PLANVIEW || m_viewId == EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE ||
         m_viewId == EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE);
    m_isFullscreen =
        (m_viewId == EScreenID_FULLSCREEN || m_viewId == EScreenID_FULLSCREEN_FRONT_ENLARGE ||
         m_viewId == EScreenID_FULLSCREEN_REAR_ENLARGE);
    if (m_isFullscreen && m_enlargeStatus != cc::daddy::NO_ENLARGE)
    {
        m_showTransparent = true;
    }
    else
    {
        m_showTransparent = (static_cast<vfc::uint8_t>(cc::target::common::ESvsVehTransSts::SVS_VEH_TRANS_OPEN) == l_transparentSwitch);
    }
    return true;
}

void Vehicle2DOverlayManager::updateGearAnimation()
{
    // arrows trajectory
    if (m_gearStatus == pc::daddy::EGear::GEAR_D && (m_speed <= 15.0f))
    {
        m_frontArrows.setAllEnabled(true);
        for (int i = 0; i < g_Vehicle2DSettings->m_numArrows; i++)
        {
            const auto icon   = m_frontArrows.getIcon(i);
            const auto factor = 2.0 * osg::PI / g_Vehicle2DSettings->m_arrowStepPeriod;
            setIconAlpha(icon, static_cast<vfc::float32_t>(std::sin(m_currentReferenceTime * factor - i * g_Vehicle2DSettings->m_arrowPhaseDiff)));

            const auto frontStartPosition = - m_arrowOffsetPos + m_currentPosition.y();
            auto positionOffset     = -m_arrowGap * static_cast<float>(i);
            if (m_enlargeStatus == cc::daddy::ENLARGE_FRONT)
            {
                positionOffset *= (1.0f + g_Vehicle2DSettings->m_arrowEnlargeOffsetPercentage);
            }
            if (m_enlargeStatus == cc::daddy::ENLARGE_REAR)
            {
                positionOffset *= (1.0f - g_Vehicle2DSettings->m_arrowEnlargeOffsetPercentage);
            }
            const osg::Vec2f size = {g_Vehicle2DSettings->m_arrowSize.x() * m_iconScaler, g_Vehicle2DSettings->m_arrowSize.y() * m_iconScaler};
            icon->setPosition({m_currentPosition.x(), frontStartPosition + positionOffset}, pc::assets::Icon::UnitType::Pixel);
            icon->setSize(size, pc::assets::Icon::UnitType::Pixel);
        }
    }
    if (m_gearStatus == pc::daddy::EGear::GEAR_R && (m_speed <= 15.0f))
    {
        m_rearArrows.setAllEnabled(true);
        for (int i = 0; i < g_Vehicle2DSettings->m_numArrows; i++)
        {
            const auto icon   = m_rearArrows.getIcon(i);
            const auto factor = 2.0 * osg::PI / g_Vehicle2DSettings->m_arrowStepPeriod;
            setIconAlpha(icon, static_cast<vfc::float32_t>(std::sin(m_currentReferenceTime * factor - i * g_Vehicle2DSettings->m_arrowPhaseDiff)));
            icon->setEnabled(true);
            const auto frontStartPosition = m_arrowOffsetPos + m_currentPosition.y();
            auto positionOffset     = m_arrowGap * static_cast<float>(i);
            if (m_enlargeStatus == cc::daddy::ENLARGE_FRONT)
            {
                positionOffset *= (1.0f + g_Vehicle2DSettings->m_arrowEnlargeOffsetPercentage);
            }
            if (m_enlargeStatus == cc::daddy::ENLARGE_REAR)
            {
                positionOffset *= (1.0f - g_Vehicle2DSettings->m_arrowEnlargeOffsetPercentage);
            }
            const osg::Vec2f size = {g_Vehicle2DSettings->m_arrowSize.x() * m_iconScaler, g_Vehicle2DSettings->m_arrowSize.y() * m_iconScaler};
            icon->setPosition({m_currentPosition.x(), frontStartPosition + positionOffset}, pc::assets::Icon::UnitType::Pixel);
            icon->setSize(size, pc::assets::Icon::UnitType::Pixel);
        }
    }
}

const Vehicle2DOverlaySettings Vehicle2DOverlayManager::getColorFromIndex(vfc::uint8_t f_colorIndex)
{
    switch (static_cast<vfc::int32_t>(f_colorIndex))
    {
    case cc::daddy::SILVERSAND_BLACK:
    case cc::daddy::XUANKONG_BLACK:
    case cc::daddy::YAO_SHI_BLACK:
    case cc::daddy::SGHC_OBSIDIAN_BLACK:
    {
        return g_Vehicle2DSettings->m_black;
    }
    case cc::daddy::AZURE:
    {
        return g_Vehicle2DSettings->m_azure;
    }
    case cc::daddy::WISDOM_BLUE:
    {
        return g_Vehicle2DSettings->m_blue;
    }
    case cc::daddy::TIME_GRAY:
    case cc::daddy::WINTER_GREY:
    case cc::daddy::HERMES_GREEN:
    case cc::daddy::SGHC_HERMES_GREEN:
    {
        return g_Vehicle2DSettings->m_gray;
    }
    case cc::daddy::MOUNTAIN_ASH:
    {
        return g_Vehicle2DSettings->m_mattegray;
    }
    case cc::daddy::MUSHAN_PINK:
    {
        return g_Vehicle2DSettings->m_mushanpink;
    }
    case cc::daddy::QIANSHAN_CUI:
    {
        return g_Vehicle2DSettings->m_qianshancui;
    }
    case cc::daddy::RED_EMPEROR:
    {
        return g_Vehicle2DSettings->m_red;
    }
    case cc::daddy::SNOWY_WHITE:
    case cc::daddy::MOON_WHITE:
    case cc::daddy::SGHC_SNOW_WHITHE:
    {
        return g_Vehicle2DSettings->m_white;
    }
    case cc::daddy::SILVER_GLAZE_WHITE:
    {
        return g_Vehicle2DSettings->m_silverglazewhite;
    }
    case cc::daddy::DU_DU_WHITE:
    {
        return g_Vehicle2DSettings->m_duduwhite;
    }
    case cc::daddy::WU_SONG_GOLDEN:
    case cc::daddy::SUNRISE_GOLD:
    {
        return g_Vehicle2DSettings->m_golden;
    }
    case cc::daddy::BLACK_GOLD:
    {
        return g_Vehicle2DSettings->m_blackgolden;
    }
    // case cc::daddy::JUN_WARE_GRAY:      return g_Vehicle2DSettings->m_junWareGray;
    default:
    {
        return g_Vehicle2DSettings->m_gray;
    }
    }
}

void Vehicle2DOverlayManager::update()
{
    m_Vehicle2DOverlays.setAllEnabled(false);
    m_Vehicle2DTransparentOverlays.setAllEnabled(false);
    m_doorMasks.setAllEnabled(false);
    m_overlays.setAllEnabled(false);
    m_frontArrows.setAllEnabled(false);
    m_rearArrows.setAllEnabled(false);

    // ! check if config has changed
    if ((g_Vehicle2DSettings->getModifiedCount() != m_lastConfigUpdate))
    {
        init();
        m_lastConfigUpdate = g_Vehicle2DSettings->getModifiedCount();
    }

    if (!updateInput())
    {
        return;
    }

    if (m_dirtyColor)
    {
        init();
        m_dirtyColor = false;
    }
    if (m_dirty)
    {
        updateIconsSizePosition();
        m_dirty = false;
    }

    const bool l_isForceTransparentMode =
        (m_viewId == EScreenID_FULLSCREEN_FRONT_ENLARGE) || (m_viewId == EScreenID_FULLSCREEN_REAR_ENLARGE);

    const bool l_isFrobidTransparentMode =
        (m_viewId == AVM3D_VIEW_FULL_SCREEN_2D_VSW);
    // auto currentView = m_framework->getScene()->getView(m_veh2dViewId);
    // if (currentView && (currentView->getNodeMask() == 0u))
    // {
    //     // m_Vehicle2DOverlays.setAllEnabled(false);
    // }
    // else
    {
        if (l_isFrobidTransparentMode)
        {
            setVehicleTransparency(1.0f);
        }
        else if (m_transparencyLevel != sm_transparencyLevel)
        {
            setVehicleTransparency(sm_transparencyLevel);
            m_transparencyLevel = sm_transparencyLevel;
        }
        else
        {
        }
        constexpr float transparentToggleThreshold = 0.25f;
        if ((m_transparencyLevel > transparentToggleThreshold && !l_isForceTransparentMode) || l_isFrobidTransparentMode)
        {
            // const float l_elapsedFrameTime = osg::Timer::instance()->delta_s(m_frameTick, osg::Timer::instance()->tick());
            // m_impostorTransp = cc::assets::impostor::g_transpVMData->calculateTransparencyLevel(l_elapsedFrameTime, m_impostorTransp, m_transpLevel);
            // m_frameTick = osg::Timer::instance()->tick();
            // setVehicleTransparency(m_impostorTransp);
            m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_OPEN)->setEnabled(m_frontLeftDoorOpen);
            m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_CLOSE)->setEnabled(!m_frontLeftDoorOpen);
            m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN)->setEnabled(m_frontRightDoorOpen);
            m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_CLOSE)->setEnabled(!m_frontRightDoorOpen);
            m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_OPEN)->setEnabled(m_rearLeftDoorOpen);
            m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_CLOSE)->setEnabled(!m_rearLeftDoorOpen);
            m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_OPEN)->setEnabled(m_rearRightDoorOpen);
            m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_CLOSE)->setEnabled(!m_rearRightDoorOpen);
            m_Vehicle2DOverlays.getIcon(VEHICLE2D_TRUNK_OPEN)->setEnabled(m_trunkOpen);
            m_Vehicle2DOverlays.getIcon(VEHICLE2D_TRUNK_CLOSE)->setEnabled(!m_trunkOpen);
            m_Vehicle2DOverlays.getIcon(VEHICLE2D_HOOD_OPEN)->setEnabled(m_hoodOpen);
            m_Vehicle2DOverlays.getIcon(VEHICLE2D_HOOD_CLOSE)->setEnabled(!m_hoodOpen);
            m_Vehicle2DOverlays.getIcon(VEHICLE2D_BODY_OPEN)->setEnabled(true); //! VEHICLE BODY WITHOUT DOORS
        }
        if ((l_isForceTransparentMode || (m_showTransparent && (m_transparencyLevel <= transparentToggleThreshold)) /* && m_allDoorsAndMirrorsClosed */) && !l_isFrobidTransparentMode)
        {
            m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_OPEN)->setEnabled(m_frontLeftDoorOpen);
            m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_CLOSE)->setEnabled(!m_frontLeftDoorOpen);
            m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN)->setEnabled(m_frontRightDoorOpen);
            m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_CLOSE)->setEnabled(!m_frontRightDoorOpen);
            m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_OPEN)->setEnabled(m_rearLeftDoorOpen);
            m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_CLOSE)->setEnabled(!m_rearLeftDoorOpen);
            m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_OPEN)->setEnabled(m_rearRightDoorOpen);
            m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_CLOSE)->setEnabled(!m_rearRightDoorOpen);
            m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_TRUNK_OPEN)->setEnabled(m_trunkOpen);
            m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_TRUNK_CLOSE)->setEnabled(!m_trunkOpen);
            m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_HOOD_OPEN)->setEnabled(m_hoodOpen);
            m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_HOOD_CLOSE)->setEnabled(!m_hoodOpen);
            m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_BODY_OPEN)
                ->setEnabled(true); //! VEHICLE BODY WITHOUT DOORS
        }
    }
    m_doorMasks.getIcon(VEHICLE2D_TRUNK_OPEN_MASK)->setEnabled(m_trunkOpen);


    if (m_viewId == EScreenID_PARKING_TOP)
    {
        m_doorMasks.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_OPEN_MASK)->setEnabled(m_frontLeftDoorOpen||m_leftSideMirrorFlapped);
        m_doorMasks.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN_MASK)->setEnabled(m_frontRightDoorOpen||m_rightSideMirrorFlapped);
    }
    else
    {
        m_doorMasks.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_OPEN_MASK)->setEnabled(m_frontLeftDoorOpen);
        m_doorMasks.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN_MASK)->setEnabled(m_frontRightDoorOpen);
    }
    // gear status
    if (!m_parkActive && isLessEqual(m_speed, 5.f) &&
        (m_viewId != EScreenID_REMOTE_SCREEN_FRONT && m_viewId != EScreenID_REMOTE_SCREEN_REAR &&
         m_viewId != EScreenID_REMOTE_SCREEN_LEFT && m_viewId != EScreenID_REMOTE_SCREEN_RIGHT))
    {
        updateGearAnimation();
        m_overlays.getIcon(GEAR_STATUS_D)->setEnabled(m_gearStatus == pc::daddy::EGear::GEAR_D);
        m_overlays.getIcon(GEAR_STATUS_R)->setEnabled(m_gearStatus == pc::daddy::EGear::GEAR_R);
        m_overlays.getIcon(GEAR_STATUS_P)->setEnabled(m_gearStatus == pc::daddy::EGear::GEAR_P);
        m_overlays.getIcon(GEAR_STATUS_N)->setEnabled(m_gearStatus == pc::daddy::EGear::GEAR_N);
    }
}

Vehicle2DOverlay::Vehicle2DOverlay(
    cc::core::CustomFramework* f_customFramework,
    cc::core::AssetId          f_assetId,
    osg::Camera*               f_referenceView)
    : cc::assets::uielements::CustomImageOverlays(f_assetId, f_referenceView) // PRQA S 4050
    , m_customFramework(f_customFramework)
    , m_manager(f_customFramework, this)
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
    getAsset()->asCamera()->setRenderOrder(osg::Camera::RenderOrder::NESTED_RENDER, 0);
}

void Vehicle2DOverlay::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        m_manager.updateReferenceTime(static_cast<vfc::float32_t>(f_nv.getFrameStamp()->getSimulationTime()));
        m_manager.update();
    }

    pc::assets::ImageOverlays::traverse(f_nv);
}

} // namespace uielements
} // namespace assets
} // namespace cc

