//-------------------------------------------------------------------------------
// Copyright (c) 2019 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/ptsoverlay/inc/PtsAnimation.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "cc/core/inc/CustomScene.h"
#include <cassert> // PRQA S 1060

namespace cc
{
namespace assets
{
namespace ptsoverlay
{

pc::util::coding::Item<PtsPositions> g_ptsPositions("PtsPositions");

//!
//! PtsAnimation
//!
PtsAnimation::PtsAnimation(
  pc::core::View* f_defaultView,
  PtsPositions::PtsStates f_startState,
  PtsPositions::PtsStates f_targetState,
  bool f_animateParkView)
  : m_defaultView(f_defaultView) // PRQA S 2323 // PRQA S 4052
  , m_startState(f_startState) // PRQA S 2323
  , m_targetState(f_targetState) // PRQA S 2323
  , m_animateParkView(f_animateParkView) // PRQA S 2323
{
  initAnimation();
}

void PtsAnimation::initAnimation() // PRQA S 4211
{
  pc::core::Asset* const l_ptsOverlayAsset = m_defaultView->getAsset(cc::core::AssetId::EASSETS_OBSTACLE_OVERLAY_DAI);
  assets::ptsoverlay::PtsOverlay* l_ptsOverlay = nullptr;
  if(m_animateParkView)
  {
    l_ptsOverlay = dynamic_cast<assets::ptsoverlay::PtsOverlay*>(dynamic_cast<osg::MatrixTransform*>(l_ptsOverlayAsset->getAsset())->getChild(0)); // PRQA S 3400
  }
  else
  {
    l_ptsOverlay = dynamic_cast<assets::ptsoverlay::PtsOverlay*>(dynamic_cast<osg::MatrixTransform*>(l_ptsOverlayAsset->getAsset())); // PRQA S 3400
  }

  if (l_ptsOverlay != nullptr)
  {
    bool l_is3D = true;
    // transition from or to the TopView the shield will not be rendered - only animated 2D View
    // if ParkIn RV to ParkOut RV will be possible it is neccessary to catch this transition here, so the overlay will be rendered just 2D
    if ((m_targetState == PtsPositions::PTSPOSITIONS_DEFAULT2D) || (m_startState == PtsPositions::PTSPOSITIONS_DEFAULT2D))
    {
      l_is3D = false;
    }
    l_ptsOverlay->setIs3D(l_is3D);
    l_ptsOverlay->setTransitionAnimationActive(true);
  }

}

float PtsAnimation::getFixedDuration() const // PRQA S 2446
{
  return g_ptsPositions->m_animationDuration;
}


bool PtsAnimation::hasFixedDuration() const
{
  return true;
}

bool PtsAnimation::supportsCancellation() const
{
  return true;
}

void PtsAnimation::onBegin()
{
  // needs to be done here again if a new animation is triggered before the first one finished
  initAnimation();
}

bool PtsAnimation::onUpdate(float f_animationTime) // PRQA S 2446
{
  if( f_animationTime >= g_ptsPositions->m_animationDuration)
  {
    f_animationTime = g_ptsPositions->m_animationDuration;
  }
  pc::core::Asset* const l_ptsOverlayAsset = m_defaultView->getAsset(cc::core::AssetId::EASSETS_OBSTACLE_OVERLAY_DAI);
  assets::ptsoverlay::PtsOverlay* l_ptsOverlay = nullptr;
  if(m_animateParkView)
  {
    l_ptsOverlay = dynamic_cast<assets::ptsoverlay::PtsOverlay*>(dynamic_cast<osg::MatrixTransform*>(l_ptsOverlayAsset->getAsset())->getChild(0)); // PRQA S 3400
  }
  else
  {
    l_ptsOverlay = dynamic_cast<assets::ptsoverlay::PtsOverlay*>(dynamic_cast<osg::MatrixTransform*>(l_ptsOverlayAsset->getAsset())); // PRQA S 3400
  }

  if(l_ptsOverlay != nullptr)
  {
    osg::Vec4f l_startPosition = getStatePosition(true);    // scaleX, scaleY, translationX, translationY
    osg::Vec2f l_startScale = osg::Vec2f(l_startPosition.x(), l_startPosition.y());
    osg::Vec2f l_startTranslate = osg::Vec2f(l_startPosition.z(), l_startPosition.w());

    osg::Vec4f l_targetPosition = getStatePosition(false);
    osg::Vec2f l_targetScale = osg::Vec2f(l_targetPosition.x(), l_targetPosition.y());
    osg::Vec2f l_targetTranslate = osg::Vec2f(l_targetPosition.z(), l_targetPosition.w());

    const float l_animationFactor = pc::util::smoothstep(0.0f, 1.0f, f_animationTime / g_ptsPositions->m_animationDuration); // PRQA S 2446

    float l_startHeight = 0.f; // PRQA S 2446
    if((m_targetState == PtsPositions::PTSPOSITIONS_DEFAULT3D) || (m_startState == PtsPositions::PTSPOSITIONS_DEFAULT3D))
    {
      l_startHeight = 1.f;
    }

    // animation will be from start to the target for the scale and translation in X-Direction
    const float l_diffScaleX       = l_targetScale.x() - l_startScale.x(); // PRQA S 2446
    const float l_diffTranslationX = l_targetTranslate.x() - l_startTranslate.x(); // PRQA S 2446

    const float l_scaleX     = l_diffScaleX * l_animationFactor; // PRQA S 2446
    const float l_translateX = l_diffTranslationX * l_animationFactor; // PRQA S 2446

    // animation will be from start to the target for the scale and translation in Y-Direction
    const float l_diffScaleY       = l_targetScale.y() - l_startScale.y(); // PRQA S 2446
    const float l_diffTranslationY = l_targetTranslate.y() - l_startTranslate.y(); // PRQA S 2446

    const float l_scaleY     = l_diffScaleY * l_animationFactor; // PRQA S 2446
    const float l_translateY = l_diffTranslationY * l_animationFactor; // PRQA S 2446

    float l_height    = l_startHeight * (1.f - l_animationFactor); // PRQA S 2446
    // if it is an animation back to the default view3D, the animation for the height has to happen the other way around
    if((m_targetState == PtsPositions::PTSPOSITIONS_DEFAULT3D))
    {
      l_height    = l_startHeight * l_animationFactor;
    }

    // never scale with 0.0 else it will result in a crash
    if(l_height < g_ptsPositions->m_heightMinThreshold)
    {
      l_height = g_ptsPositions->m_heightMinThreshold;
    }

    if(m_animateParkView)
    {
      static_cast<osg::MatrixTransform*>(l_ptsOverlayAsset->getAsset())->setMatrix( // PRQA S 3076
        osg::Matrix::scale(l_startScale.x() + l_scaleX, l_startScale.y() + l_scaleY, l_height) * osg::Matrix::translate(l_startTranslate.x() + l_translateX, l_startTranslate.y() + l_translateY, 0.0f));
    }
    else
    {
      static_cast<osg::MatrixTransform*>(l_ptsOverlay)->setMatrix(
        osg::Matrix::scale(l_startScale.x() + l_scaleX, l_startScale.y() + l_scaleY, l_height) * osg::Matrix::translate(l_startTranslate.x() + l_translateX, l_startTranslate.y() + l_translateY, 0.0f));
    }

    bool l_is3D = true;
    // if the height is smaller than the threshold and it will transition from or to the TopView the shield will not be rendered - only animated 2D View
    if ((l_height <= g_ptsPositions->m_heightMinThreshold) || ((m_targetState == PtsPositions::PTSPOSITIONS_DEFAULT2D) || (m_startState == PtsPositions::PTSPOSITIONS_DEFAULT2D)))
    {
      l_is3D = false;
    }
    l_ptsOverlay->setIs3D(l_is3D);
  }
  return f_animationTime >= g_ptsPositions->m_animationDuration;
}


osg::Vec4f PtsAnimation::getStatePosition(bool f_start) const
{
  PtsPositions::PtsStates const l_state = f_start ? m_startState : m_targetState;

  switch (l_state)
  {
  case PtsPositions::PTSPOSITIONS_PARKIN:
  {
      return g_ptsPositions->m_parkIn;
  }
  case PtsPositions::PTSPOSITIONS_PARKOUT_PARA_LEFT:
  {
      return g_ptsPositions->m_parkOutParaLeft;
  }
  case PtsPositions::PTSPOSITIONS_PARKOUT_PARA_RIGHT:
  {
      return g_ptsPositions->m_parkOutParaRight;
  }
  case PtsPositions::PTSPOSITIONS_PARKOUT_CROSS_FRONT:
  {
      return g_ptsPositions->m_parkOutCrossFront;
  }
  case PtsPositions::PTSPOSITIONS_PARKOUT_CROSS_REAR:
  {
      return g_ptsPositions->m_parkOutCrossRear;
  }
  case PtsPositions::PTSPOSITIONS_DEFAULT3D:
  {
      return g_ptsPositions->m_default3D;
  }
  case PtsPositions::PTSPOSITIONS_DEFAULT2D:
  {
      return g_ptsPositions->m_default2D;
  }
  default:
  {
      return g_ptsPositions->m_default2D;
  }
  }
}


// PtsPositions::PtsStates PtsAnimation::determinePtsState(cc::views::parkview::ParkOutView::ParkoutStates f_parkOutState)
// {
//   switch (f_parkOutState)
//   {
//   case cc::views::parkview::ParkOutView::PARK_OUT_PARA_R:
//     return g_ptsPositions->PTSPOSITIONS_PARKOUT_PARA_RIGHT;
//   case cc::views::parkview::ParkOutView::PARK_OUT_PARA_L:
//     return g_ptsPositions->PTSPOSITIONS_PARKOUT_PARA_LEFT;
//   case cc::views::parkview::ParkOutView::PARK_OUT_CROSS_F:
//     return g_ptsPositions->PTSPOSITIONS_PARKOUT_CROSS_FRONT;
//   case cc::views::parkview::ParkOutView::PARK_OUT_CROSS_R:
//     return g_ptsPositions->PTSPOSITIONS_PARKOUT_CROSS_REAR;
//   default:
//     return g_ptsPositions->PTSPOSITIONS_PARKOUT_PARA_LEFT;
//   }
// }


void PtsAnimation::onCameraFlightEnded() // PRQA S 4211
{
  pc::core::Asset* const l_ptsOverlayAsset = m_defaultView->getAsset(cc::core::AssetId::EASSETS_OBSTACLE_OVERLAY_DAI);
  assets::ptsoverlay::PtsOverlay* l_ptsOverlay = nullptr;
  if(m_animateParkView)
  {
    l_ptsOverlay = dynamic_cast<assets::ptsoverlay::PtsOverlay*>(dynamic_cast<osg::MatrixTransform*>(l_ptsOverlayAsset->getAsset())->getChild(0)); // PRQA S 3400
  }
  else
  {
    l_ptsOverlay = dynamic_cast<assets::ptsoverlay::PtsOverlay*>(dynamic_cast<osg::MatrixTransform*>(l_ptsOverlayAsset->getAsset())); // PRQA S 3400
  }

  if (l_ptsOverlay != nullptr)
  {
    // set after the animation the ptsOverlay matrix back to identity
    static_cast<osg::MatrixTransform*>(l_ptsOverlay)->setMatrix(osg::Matrix::identity());
    l_ptsOverlay->setTransitionAnimationActive(false);
  }
}

///
/// CameraFlightEndedAction
///
CameraFlightEndedAction::CameraFlightEndedAction(PtsAnimation* f_ptsAnimation)
  : m_ptsAnimation(f_ptsAnimation) // PRQA S 2323 // PRQA S 4052
{
}

void CameraFlightEndedAction::onAction()
{
  if (m_ptsAnimation.valid())
  {
    m_ptsAnimation->onCameraFlightEnded();
  }
}


} // namespace ptsoverlay
} // namespace assets
} // namespace cc
