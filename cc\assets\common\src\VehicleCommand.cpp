//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  VehicleCommand.cpp
/// @brief
//=============================================================================

#include "pc/generic/util/cli/inc/CommandRegistry.h"
#include "pc/svs/util/cli/inc/BaseCommands.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"

namespace cc
{
namespace assets
{
namespace common
{

//======================================================
// SetAlphaCommand
//------------------------------------------------------
/// Handle vehicle alpha signal.
/// Send the new apha value as a signal.
/// <AUTHOR>
//======================================================
class SetAlphaCommand : public pc::util::cli::NumericSignalSetter<vfc::float32_t>
{
public:

    SetAlphaCommand()
      : pc::util::cli::NumericSignalSetter<vfc::float32_t>("vehicle alpha") // PRQA S 2323
    {
    }

    void sendSignal(vfc::float32_t f_value) override
    {
        cc::daddy::VehicleAlphaDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_VehicleAlpha_Socket_SenderPort.reserve();
        l_container.m_Data = f_value;
        cc::daddy::CustomDaddyPorts::sm_VehicleAlpha_Socket_SenderPort.deliver();
    }
};

static SetAlphaCommand g_setAlphaCommand;

//======================================================
// SetVehicleColorBase
//------------------------------------------------------
/// Handle vehicle color signal.
/// Switch vehicle color.
/// <AUTHOR>
//======================================================
class SetVehicleColorBase : public pc::util::cli::CommandCallback
{
public:

    explicit SetVehicleColorBase(const std::string& f_colorName)
      : m_colorName(f_colorName) // PRQA S 4052
    {
    }

    bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
    {
        cc::daddy::color_t l_color;
        f_input >> l_color.R >> l_color.G >> l_color.B >> l_color.A;  // PRQA S 3803
        if (f_input.fail())
        {
            f_output << parseError;  // PRQA S 3803
            getHelp(f_output);
            return false;
        }
        f_output << "Setting vehicle " << m_colorName << " color to (" << l_color.R << ", " << l_color.G << ", " << l_color.B << ", " << l_color.A << ")" << newline;  // PRQA S 3803
        sendSignal(l_color);
        return true;
    }

    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Set the vehicle model " << m_colorName << " color" << newline;  // PRQA S 3803
    }

    void getHelp(std::ostream& f_output) const override
    {
        f_output << "<R> <G> <B> <A> (byte)" << newline;  // PRQA S 3803
    }

    virtual void sendSignal(const cc::daddy::color_t& f_color) = 0;

private:

    std::string m_colorName;
};

//======================================================
// SetDiffuseColorCommand
//------------------------------------------------------
/// Handle vehicle diffuse color signal.
/// Send vehicle diffuse color.
/// <AUTHOR>
//======================================================
class SetDiffuseColorCommand : public SetVehicleColorBase
{
public:

    SetDiffuseColorCommand()
      : SetVehicleColorBase("diffuse")
    {
    }

    void sendSignal(const cc::daddy::color_t& f_color) override
    {
        cc::daddy::ColorDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_VehicleDiffuseColor_Socket_SenderPort.reserve();
        l_container.m_Data = f_color;
        cc::daddy::CustomDaddyPorts::sm_VehicleDiffuseColor_Socket_SenderPort.deliver();
    }
};

static SetDiffuseColorCommand g_setDiffuseColorCommand;

//======================================================
// SetDiffuseColorIndexCommand
//------------------------------------------------------
/// Handle vehicle diffuse color signal.
/// Send vehicle diffuse color.
/// <AUTHOR>
//======================================================

class SetDiffuseColorIndexCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    SetDiffuseColorIndexCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("color index")
    {
    }

    void sendSignal(vfc::int32_t f_color) override
    {
        cc::daddy::ColorIndexDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_VehicleDiffuseColorIndex_SenderPort.reserve();
        l_container.m_Data = static_cast<vfc::uint8_t>(f_color);
        cc::daddy::CustomDaddyPorts::sm_VehicleDiffuseColorIndex_SenderPort.deliver();
    }
};

static SetDiffuseColorIndexCommand g_setDiffuseColorIndexCommand;

//======================================================
// SetSpec1ColorCommand
//------------------------------------------------------
/// Handle vehicle specular color 1 signal.
/// Send vehicle specular color 1.
/// <AUTHOR>
//======================================================
class SetSpec1ColorCommand : public SetVehicleColorBase
{
public:

    SetSpec1ColorCommand()
      : SetVehicleColorBase("specular 1")
    {
    }

    void sendSignal(const cc::daddy::color_t& f_color) override
    {
        cc::daddy::ColorDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_VehicleSpecColor1_Socket_SenderPort.reserve();
        l_container.m_Data = f_color;
        cc::daddy::CustomDaddyPorts::sm_VehicleSpecColor1_Socket_SenderPort.deliver();
    }
};

static SetSpec1ColorCommand g_setSpec1ColorCommand;

//======================================================
// SetSpec2ColorCommand
//------------------------------------------------------
/// Handle vehicle specular color 2 signal.
/// Send vehicle specular color 2.
/// <AUTHOR>
//======================================================
class SetSpec2ColorCommand : public SetVehicleColorBase
{
public:

    SetSpec2ColorCommand()
      : SetVehicleColorBase("specular 2")
    {
    }

    void sendSignal(const cc::daddy::color_t& f_color) override
    {
        cc::daddy::ColorDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_VehicleSpecColor2_Socket_SenderPort.reserve();
        l_container.m_Data = f_color;
        cc::daddy::CustomDaddyPorts::sm_VehicleSpecColor2_Socket_SenderPort.deliver();
    }
};

static SetSpec2ColorCommand g_setSpec2ColorCommand;

//======================================================
// SetRough1Command
//------------------------------------------------------
/// Handle vehicle roughtness 1 signal.
/// Send vehicle roughtness 1.
/// <AUTHOR>
//======================================================
class SetRough1Command : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    SetRough1Command()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("vehicle color roughness 1")
    {
    }

    void sendSignal(vfc::int32_t f_value) override
    {
        cc::daddy::VehicleModelParamDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_VehicleModelParam_Socket_SenderPort.reserveLastDelivery();
        l_container.m_Data.roughness1 = f_value;
        cc::daddy::CustomDaddyPorts::sm_VehicleModelParam_Socket_SenderPort.deliver();
    }
};

static SetRough1Command g_setRough1Command;

//======================================================
// SetRough2Command
//------------------------------------------------------
/// Handle vehicle roughtness 2 signal.
/// Send vehicle roughtness 2.
/// <AUTHOR>
//======================================================
class SetRough2Command : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    SetRough2Command()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("vehicle color roughness 2")
    {
    }

    void sendSignal(vfc::int32_t f_value) override
    {
        cc::daddy::VehicleModelParamDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_VehicleModelParam_Socket_SenderPort.reserveLastDelivery();
        l_container.m_Data.roughness2 = f_value;
        cc::daddy::CustomDaddyPorts::sm_VehicleModelParam_Socket_SenderPort.deliver();
    }
};

static SetRough2Command g_setRough2Command;

//======================================================
// SetSpecPow1Command
//------------------------------------------------------
/// Handle vehicle power 1 signal.
/// Send vehicle power 1.
/// <AUTHOR>
//======================================================
class SetSpecPow1Command : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    SetSpecPow1Command()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("vehicle color specular power 1")
    {
    }

    void sendSignal(vfc::int32_t f_value) override
    {
        cc::daddy::VehicleModelParamDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_VehicleModelParam_Socket_SenderPort.reserveLastDelivery();
        l_container.m_Data.specPower1 = f_value;
        cc::daddy::CustomDaddyPorts::sm_VehicleModelParam_Socket_SenderPort.deliver();
    }
};

static SetSpecPow1Command g_setSpecPow1Command;

//======================================================
// SetSpecPow2Command
//------------------------------------------------------
/// Handle vehicle power 2 signal.
/// Send vehicle power 2.
/// <AUTHOR>
//======================================================
class SetSpecPow2Command : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    SetSpecPow2Command()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("vehicle color specular power 2")
    {
    }

    void sendSignal(vfc::int32_t f_value) override
    {
        cc::daddy::VehicleModelParamDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_VehicleModelParam_Socket_SenderPort.reserveLastDelivery();
        l_container.m_Data.specPower2 = f_value;
        cc::daddy::CustomDaddyPorts::sm_VehicleModelParam_Socket_SenderPort.deliver();
    }
};

static SetSpecPow2Command g_setSpecPow2Command;

//======================================================
// SetReflectionPowerCommand
//------------------------------------------------------
/// Handle vehicle reflection signal.
/// Send vehicle reflection.
/// <AUTHOR>
//======================================================
class SetReflectionPowerCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    SetReflectionPowerCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("vehicle color reflection power")
    {
    }

    void sendSignal(vfc::int32_t f_value) override
    {
        cc::daddy::VehicleModelParamDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_VehicleModelParam_Socket_SenderPort.reserveLastDelivery();
        l_container.m_Data.reflectionPower = f_value;
        cc::daddy::CustomDaddyPorts::sm_VehicleModelParam_Socket_SenderPort.deliver();
    }
};

static SetReflectionPowerCommand g_setReflectionPowerCommand;

//======================================================
// SetFresnelCommand
//------------------------------------------------------
/// Handle vehicle frensel signal.
/// Send vehicle frensel.
/// <AUTHOR>
//======================================================
class SetFresnelCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    SetFresnelCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("vehicle color Fresnel")
    {
    }

    void sendSignal(vfc::int32_t f_value) override
    {
        cc::daddy::VehicleModelParamDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_VehicleModelParam_Socket_SenderPort.reserveLastDelivery();
        l_container.m_Data.fresnel = f_value;
        cc::daddy::CustomDaddyPorts::sm_VehicleModelParam_Socket_SenderPort.deliver();
    }
};

static SetFresnelCommand g_setFresnelCommand;

//======================================================
// VehicleColorCommand
//------------------------------------------------------
/// Handle all vehicle color commands.
/// Collect all color commands and make them reachable.
/// <AUTHOR>
//======================================================
class VehicleColorCommand : public pc::util::cli::CommandCallbackGroup
{
public:

    VehicleColorCommand()
    {
        addCommand("alpha",      &g_setAlphaCommand);
        addCommand("diffuse",    &g_setDiffuseColorCommand);
        addCommand("colorindex", &g_setDiffuseColorIndexCommand);
        addCommand("spec1",      &g_setSpec1ColorCommand);
        addCommand("spec2",      &g_setSpec2ColorCommand);
        addCommand("rough1",     &g_setRough1Command);
        addCommand("rough2",     &g_setRough2Command);
        addCommand("specpow1",   &g_setSpecPow1Command);
        addCommand("specpow2",   &g_setSpecPow2Command);
        addCommand("reflpow",    &g_setReflectionPowerCommand);
        addCommand("fresnel",    &g_setFresnelCommand);
    }

    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Set the vehicle color parameters" << newline;  // PRQA S 3803
    }
};

static pc::util::cli::Command<VehicleColorCommand> g_vehicleColorCommand("vehcol");

class SetCustomLightCommand : public pc::util::cli::CommandCallback
{
public:

    bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
    {
        vfc::uint32_t l_light0 = 0u;
        vfc::uint32_t l_light1 = 0u;
        vfc::uint32_t l_light2 = 0u;
        vfc::uint32_t l_light3 = 0u;
        vfc::uint32_t l_light4 = 0u;
        vfc::uint32_t l_light5 = 0u;
        bool  l_leftDayLight  = 0u;
        bool  l_rightDayLight = 0u;
        vfc::uint32_t l_Foglight = 0u;

        vfc::uint32_t l_frontClearanceLight = 0u;
        vfc::uint32_t l_rearClearanceLight = 0u;
        vfc::uint32_t l_frontLeftCornerLight = 0u;
        vfc::uint32_t l_frontRightCornerLight = 0u;
        vfc::uint32_t l_frontPosLightState = 0u;
        vfc::uint32_t l_reverseLightState = 0u;


        f_input >> l_light0 >> l_light1 >> l_light2 >> l_light3 >> l_light4>> l_light5>>l_leftDayLight>>l_rightDayLight;  // PRQA S 3803

        if (!f_input.fail())
        {
            f_input >> l_Foglight >> l_frontClearanceLight >> l_rearClearanceLight >> l_frontLeftCornerLight >> l_frontRightCornerLight >> l_frontPosLightState >> l_reverseLightState;
            if(f_input.fail())
            {
                f_output << "byd sghl some lights are not set, check below"<< newline;
                getHelp(f_output);
            }

            if ( true == cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.isConnected() )
            {
                cc::daddy::CustomVehicleLightsDaddy& l_vehicleLightState = cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.reserve();

                l_vehicleLightState.m_Data.m_lowBeamIndication    = l_light0;
                l_vehicleLightState.m_Data.m_mainBeamIndication   = l_light1;
                l_vehicleLightState.m_Data.m_daytimeRunningLights = static_cast<vfc::uint8_t>(l_light2);
                l_vehicleLightState.m_Data.m_hazardLightState     = static_cast<vfc::uint8_t>(l_light3);
                l_vehicleLightState.m_Data.m_rearPosLightState    = static_cast<vfc::uint8_t>(l_light4);
                l_vehicleLightState.m_Data.m_turnSignalStatus     = static_cast<vfc::uint8_t>(l_light5);
                l_vehicleLightState.m_Data.m_leftHeadLightState   = static_cast<bool>(l_leftDayLight);
                l_vehicleLightState.m_Data.m_rightHeadLightState  = static_cast<bool>(l_rightDayLight);
                l_vehicleLightState.m_Data.m_fogLightStatus       = static_cast<vfc::uint8_t>(l_Foglight);
                l_vehicleLightState.m_Data.m_frontClearanceLightState     = static_cast<vfc::uint8_t>(l_frontClearanceLight);
                l_vehicleLightState.m_Data.m_rearClearanceLightState      = static_cast<vfc::uint8_t>(l_rearClearanceLight);
                l_vehicleLightState.m_Data.m_frontLeftCornerLightState    = static_cast<vfc::uint8_t>(l_frontLeftCornerLight);
                l_vehicleLightState.m_Data.m_frontRightCornerLightState   = static_cast<vfc::uint8_t>(l_frontRightCornerLight);
                l_vehicleLightState.m_Data.m_frontPosLightState           = static_cast<vfc::uint8_t>(l_frontPosLightState);
                l_vehicleLightState.m_Data.m_reverseLightState            = static_cast<vfc::uint8_t>(l_reverseLightState);
                cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.deliver();
            }
            return true;
        }

        f_output << parseError;  // PRQA S 3803
        getHelp(f_output);
        return false;
    }

    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Set customer lights" << newline;  // PRQA S 3803
    }

    void getHelp(std::ostream& f_output) const override
    {
        f_output << "Set customer lights: " << newline
        << "low beam:                   bit0" << newline
        << "high beam:                  bit1" << newline
        << "daylight:                   bit2" << newline
        << "hazard light:               bit3" << newline
        << "rearpositionlight:          bit4" << newline
        << "turnSignalStatus:           bit5" << newline
        << "LeftDayLight:               bit6" << newline
        << "RightDayLight:              bit7" << newline  // PRQA S 3803
        << "FogLight:                   bit8" << newline
        << "FrontClearanceLight:        bit9" << newline
        << "RearClearanceLight:         bit10" << newline
        << "FrontLeftCornerLight:       bit11" << newline
        << "FrontRightCornerLight:      bit12" << newline
        << "FrontPositionLightState:    bit13" << newline
        << "ReverseLightState:          bit14" << newline;
    }
};

static pc::util::cli::Command<SetCustomLightCommand> g_custLightCommand("custlight");


class SetTrailerAssistModeCommand : public pc::util::cli::CommandCallback
{
public:

    bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
    {
        bool  l_trailerAssistMode = 0u;

        f_input >> l_trailerAssistMode;  // PRQA S 3803

        if (!f_input.fail())
        {
            if ( true == cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.isConnected() )
            {
                cc::daddy::CustomVehicleLightsDaddy& l_vehicleLightState = cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.reserve();

                l_vehicleLightState.m_Data.m_Trailer_Assist_Mode_S  = static_cast<bool>(l_trailerAssistMode);
                cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.deliver();
            }
            return true;
        }

        f_output << parseError;  // PRQA S 3803
        getHelp(f_output);
        return false;
    }

    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Set Trailer Assist Mode" << newline;  // PRQA S 3803
    }

    void getHelp(std::ostream& f_output) const override
    {
        f_output << "Set Trailer Assist Mode: " << newline
        << "ON: 1" << newline
		<< "OFF: 0" << newline;  // PRQA S 3803
    }
};

static pc::util::cli::Command<SetTrailerAssistModeCommand> g_trailerAssistModeCommand("trailermode");



} // namespace common
} // namespace assets
} // namespace cc

