//-------------------------------------------------------------------------------
// Copyright (c) 2021 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/pdc/inc/PdcHelper.h"

#include <cmath>
namespace cc
{
namespace assets
{
namespace pdc
{

void Color::fromString(const std::string& /*f_str*/) // PRQA S 4212
{
  // todo
}

std::string Color::toString() const // PRQA S 4212
{
  return std::string(); // todo
}

///
/// SvgWriter
///
SvgWriter::SvgWriter(std::ostream& f_os, float f_viewBoxMinX, float f_viewBoxMinY, float f_viewBoxMaxX, float f_viewBoxMaxY) // PRQA S 2446
  : SvgWriter(f_os, osg::Vec2(f_viewBoxMinX, f_viewBoxMinY), osg::Vec2(f_viewBoxMaxX, f_viewBoxMaxY)) // PRQA S 2323
{
}


SvgWriter::SvgWriter(std::ostream& f_os, const osg::Vec2& f_viewBoxMin, const osg::Vec2& f_viewBoxMax)
  : m_os(f_os) // PRQA S 2323
{
  m_os << "<svg" <<
    " viewBox=\"" << f_viewBoxMin.x() << " " << f_viewBoxMin.y() << " " << f_viewBoxMax.x() << " " << f_viewBoxMax.y() << "\"" <<
    //width=\"" << f_size.x() << "\" height=\"" << f_size.y() << "\"" <<
    " xmlns=\"http://www.w3.org/2000/svg\">" << std::endl;
}


SvgWriter::~SvgWriter() = default;
// SvgWriter::~SvgWriter()
// {
//   // close SVG root node
//   // m_os << "</svg>" << std::endl;
// }


} // namespace pdc
} // namespace assets
} // namespace cc
