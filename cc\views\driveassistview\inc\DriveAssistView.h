//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  DriveAssistView.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_DRIVEASSISTVIEW_H_
#define CC_ASSETS_DRIVEASSISTVIEW_H_

#include "pc/svs/core/inc/View.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"


namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc


namespace cc
{
namespace views
{
namespace driveassistview
{


//!
//! SingleDriveAssistView
//!
class SingleDriveAssistView : public pc::util::coding::ISerializable
{
public:

  SingleDriveAssistView()
    : m_eye(0.0f, 0.0f, 0.0f)
    , m_deltaCenter(1.0f, 0.0f, 0.0f)
    , m_up(0.0f, 0.0f, 1.0f)
    , m_fovy(30.0f)
  {
  }

  SERIALIZABLE(SingleDriveAssistView)
  {
    ADD_MEMBER(osg::Vec3f, eye);
    ADD_MEMBER(osg::Vec3f, deltaCenter);
    ADD_MEMBER(osg::Vec3f, up);
    ADD_FLOAT_MEMBER(fovy);
  }

  const std::string asString() const
  {
    std::ostringstream l_stream;
    l_stream << std::fixed << std::setprecision(3) << "SingleDriveAssistView:"
                           << "\n    m_eye:         "   << m_eye.x() << ", " << m_eye.y() << ", " << m_eye.z() 
                           << "\n    m_deltaCenter: "   << m_deltaCenter.x() << ", " << m_deltaCenter.y() << ", " << m_deltaCenter.z() 
                           << "\n    m_up:          "   << m_up.x() << ", " << m_up.y() << ", " << m_up.z() 
                           << "\n    m_fovy:        "   << m_fovy;  // PRQA S 3803
    return l_stream.str();
  }  

  osg::Vec3f m_eye;
  osg::Vec3f m_deltaCenter;
  osg::Vec3f m_up;
  vfc::float32_t m_fovy;
};

//!
//! DriveAssistViewSettings
//!
class DriveAssistViewSettings : public pc::util::coding::ISerializable
{
public:

  DriveAssistViewSettings()
  {
  }

  SERIALIZABLE(DriveAssistViewSettings)
  {
    ADD_MEMBER( SingleDriveAssistView, LeftCam );
    ADD_MEMBER( SingleDriveAssistView, RightCam );
  }

  const std::string asString() const
  {
    std::ostringstream l_stream;
    l_stream << std::fixed << std::setprecision(3) << "DriveAssistViewSettings:"
                           << "\n  m_LeftCam:         "   << m_LeftCam.asString()
                           << "\n  m_RightCam:        "   << m_RightCam.asString();  // PRQA S 3803
    return l_stream.str();
  }    

  SingleDriveAssistView m_LeftCam;
  SingleDriveAssistView m_RightCam;
};

extern pc::util::coding::Item<DriveAssistViewSettings> g_settings;

//======================================================
// DriveAssistView
//------------------------------------------------------
/// (DEPRECATED) A view with a fake perspective. 
/// <AUTHOR> Jose
//======================================================
class DriveAssistView: public pc::core::View
{
public:

    DriveAssistView(
        pc::core::Framework* f_framework,
        const std::string& f_name,
        const pc::core::Viewport& f_viewport,
        pc::core::sysconf::Cameras f_cam );

protected:

  virtual ~DriveAssistView();

private:

  //! Copy constructor is not permitted.
  DriveAssistView (const DriveAssistView& other); // = delete
  //! Copy assignment operator is not permitted.
  DriveAssistView& operator=(const DriveAssistView& other); // = delete

  pc::core::Framework *m_pFramework;
  pc::core::sysconf::Cameras m_cam;


};

} //namespace driveassistview
} //namespace assets
} //namespace cc



#endif /* CC_ASSETS_DRIVEASSISTVIEW_H_ */
