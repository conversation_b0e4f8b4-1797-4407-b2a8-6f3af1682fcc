//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/trajectory/inc/ActionPoint.h"

#include <array>
#include "osg/Texture2D"
#include "osgDB/ReadFile"
#include "osgDB/WriteFile"

#include "cc/assets/trajectory/inc/Helper.h"

#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Quantization.h"

/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3804
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace trajectory
{

// This multiplier is to make the quad stripe bigger to have enough room for the blur on the downsampled mipmaps
constexpr vfc::float32_t g_blurMul = 1.2f; // (1 <= )


/**
 * Calculates the parameter for each actionpoint on the f_side it is and pass them to Frame.cpp to generate the vertices for the geometry
 * @param f_side: If it is for the tyres on the right or left side
 * @param f_renderBinOrder: RenderOrdere so it will be rendered on top of the baseplate
 * @param f_trajParams: General const parameters for all trackTypes that are given by TrajectoryCodingParams with CodingParamters.xml
 * @param f_numOfVerts: Number of vertices to generate the geometry afterwards
 */
ActionPoint::ActionPoint(
  pc::core::Framework* f_framework,
  cc::assets::trajectory::commontypes::Side_en f_side,
  vfc::float32_t f_height,
  const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
  const cc::assets::trajectory::DL1* const f_distanceLine,
  vfc::uint32_t f_numOfVerts)
  : GeneralTrajectoryLine( // PRQA S 2323
      f_framework,
      f_side,
      3u,
      f_height,
      f_trajParams,
      true)
  , mc_numOfVerts(f_numOfVerts) // PRQA S 2323
  , m_currActionPointDist(0.f) // PRQA S 2323
  , m_lastActionPointDist(0.f) // PRQA S 2323
  , m_actionPointNearX(0.f) // PRQA S 2323
  , m_startDist(0.f) // PRQA S 2323
  , m_targetDist(0.f) // PRQA S 2323
  , m_startTime(-1.f) // PRQA S 2323
  , m_animate(false) // PRQA S 2323
  , m_distanceLine(f_distanceLine) // PRQA S 2323
{
  m_geometry->addPrimitiveSet(new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES)));  // PRQA S 3804  // PRQA S 3803

  //! init static data
  osg::Vec2ubArray* const l_texCoords = new osg::Vec2ubArray;
  l_texCoords->setNormalize(true);
  l_texCoords->reserve(3u * mc_numOfVerts);
  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < mc_numOfVerts; l_vertexIndex++)
  {
    const vfc::float32_t l_v = 1.0f - static_cast<vfc::float32_t>(l_vertexIndex) / (static_cast<vfc::float32_t>(mc_numOfVerts) - 1.0f);
    l_texCoords->push_back(osg::Vec2ub(0u, pc::util::osgx::toUByte(l_v)));
  }
  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < mc_numOfVerts; l_vertexIndex++)
  {
    const vfc::float32_t l_v = 1.0f - static_cast<vfc::float32_t>(l_vertexIndex) / (static_cast<vfc::float32_t>(mc_numOfVerts) - 1.0f);
    l_texCoords->push_back(osg::Vec2ub(255u, pc::util::osgx::toUByte(l_v)));
  }
  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < mc_numOfVerts; l_vertexIndex++)
  {
    const vfc::float32_t l_v = 1.0f - static_cast<vfc::float32_t>(l_vertexIndex) / (static_cast<vfc::float32_t>(mc_numOfVerts) - 1.0f);
    l_texCoords->push_back(osg::Vec2ub(0u, pc::util::osgx::toUByte(l_v)));
  }
  m_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray());
  l_colors->setBinding(osg::Array::BIND_PER_VERTEX);

  const vfc::float32_t lc_halfGradientWidth   = std::abs(m_trajParams.GradientWidth) * 0.5f; // GradientWidth = 0.007
  const vfc::float32_t lc_halfWheelTrackWidth = m_trajParams.WheelTrack_Width_Whole * 0.5f; // WheelTrack_Width_Whole = 0.228

  const vfc::float32_t l_geomHalfWidth = (lc_halfWheelTrackWidth + lc_halfGradientWidth);// * g_blurMul; // removed the blur for the ActionPoint in the width
  m_lineGeometryWidth = l_geomHalfWidth * 2.0f;
  m_geomHeight = (m_trajParams.ActionPoint_Length + m_trajParams.GradientWidth) * g_blurMul; // ActionPoint_Length = 0.1
  const vfc::float32_t l_blurHeight    = (m_geomHeight - (m_trajParams.ActionPoint_Length + m_trajParams.GradientWidth)) * 0.5f;
  m_actionPointFarToMidDist = l_blurHeight + m_trajParams.GradientWidth * 0.5f;
}


ActionPoint::~ActionPoint() = default;


vfc::float32_t ActionPoint::getActionPointNearX() const
{
  return m_actionPointNearX;
}

struct TilePoint
{
  osg::Vec2f  Pos;
  osg::Vec2ui TopLeftNeighbor;
  osg::Vec2ui BottomRightNeighbor;
  osg::Vec4f  Color;
};


enum class GradType  :  vfc::uint8_t // GradientType
{
  UNIFORM,
  HORIZONTAL,
  VERTICAL,
  TWO_DIMENSIONAL
};


struct Tile
{
  osg::Vec2ui  TopLeftTilePointIndex;
  osg::Vec2ui  BottomRightTilePointIndex;
  GradType     GradientType;
};


osg::Image* ActionPoint::create2DTexture() const  // PRQA S 6040  // PRQA S 6041  // PRQA S 6043  // PRQA S 6044
{
  // TODO: Connect to the WheelTrack asset!
  constexpr vfc::float32_t lc_wheelTrackImageWidth_geom = 256.0f - 1.0f;

  constexpr vfc::uint32_t  lc_imageWidth_px    = 128u; // Image width in pixels
  constexpr vfc::float32_t         lc_imageWidth_geom  = static_cast<vfc::float32_t>(lc_imageWidth_px - 1); // Geometric image width
  constexpr vfc::float32_t         lc_invImageWidth_geom  = 1.0f / lc_imageWidth_geom;
  constexpr vfc::uint8_t lc_channelsPerPixel = 4u;

  const vfc::float32_t lc_halfGradientWidth   = std::abs(m_trajParams.GradientWidth) * 0.5f; // GradientWidth = 0.007
  const vfc::float32_t lc_halfWheelTrackWidth = m_trajParams.WheelTrack_Width_Whole * 0.5f; // WheelTrack_Width_Whole = 0.228

  const vfc::float32_t l_geomHalfWidth = (lc_halfWheelTrackWidth + lc_halfGradientWidth);// * g_blurMul; // removed the blur for the ActionPoint in the width
  const vfc::float32_t l_invGeomHalfWidth = 1.0f / l_geomHalfWidth;

  const vfc::float32_t l_invGeomHeight = 1.0f / m_geomHeight;
  const vfc::float32_t l_blurWidth     = l_geomHalfWidth - (lc_halfWheelTrackWidth + lc_halfGradientWidth);
  const vfc::float32_t l_blurHeight = (m_geomHeight - (m_trajParams.ActionPoint_Length + m_trajParams.GradientWidth)) * 0.5f;


  // Image height in pixels
  const vfc::float32_t l_lc_imageHeight_px = lc_imageWidth_geom * m_geomHeight / l_geomHalfWidth + 1.0f;
  const vfc::uint32_t lc_imageHeight_px = static_cast<vfc::uint32_t>(l_lc_imageHeight_px) + 1u;  //PRQA S 3016
  const vfc::float32_t        lc_imageHeight_geom = static_cast<vfc::float32_t>(lc_imageHeight_px) - 1.0f; // Geometric image height
  const vfc::float32_t        lc_invImageHeight_geom = 1.0f / lc_imageHeight_geom;

  // This is needed because the texture for the action point is half the size IN PIXELS of the wheel track, NOT GEOMETRICALLY
  constexpr vfc::float32_t lc_geometricUpScaler = lc_wheelTrackImageWidth_geom * 0.5f / lc_imageWidth_geom;

  // Lines for the gradient tiles
  std::array<vfc::float32_t, 6> l_verticalLinePos_Norm_X;   // From left to right
  l_verticalLinePos_Norm_X[0u] = 0.0f;
  l_verticalLinePos_Norm_X[1u] = l_blurWidth;
  l_verticalLinePos_Norm_X[2u] = l_verticalLinePos_Norm_X[1u] + m_trajParams.GradientWidth;
  l_verticalLinePos_Norm_X[4u] = l_verticalLinePos_Norm_X[2u] + m_trajParams.WheelTrack_Width_BorderLine;
  l_verticalLinePos_Norm_X[3u] = l_verticalLinePos_Norm_X[4u] - m_trajParams.GradientWidth;
  l_verticalLinePos_Norm_X[5u] = l_geomHalfWidth;

  for (vfc::uint32_t i = 0u; i < 6u; i++)
  {
    l_verticalLinePos_Norm_X[i] *= l_invGeomHalfWidth * lc_geometricUpScaler;

    if (1.0f < l_verticalLinePos_Norm_X[i])
    {
      l_verticalLinePos_Norm_X[i] = 1.0f;
    }
  }

  std::array<vfc::float32_t, 8> l_horizontalLinePos_Norm_Y; // From top to bottom
  l_horizontalLinePos_Norm_Y[0u] = 0.0f;
  l_horizontalLinePos_Norm_Y[1u] = l_blurHeight;
  l_horizontalLinePos_Norm_Y[2u] = l_horizontalLinePos_Norm_Y[1u] + m_trajParams.GradientWidth;
  l_horizontalLinePos_Norm_Y[4u] = l_horizontalLinePos_Norm_Y[2u] + m_trajParams.WheelTrack_Width_BorderLine;
  l_horizontalLinePos_Norm_Y[3u] = l_horizontalLinePos_Norm_Y[4u] - m_trajParams.GradientWidth;
  l_horizontalLinePos_Norm_Y[6u] = m_geomHeight - l_blurHeight;
  l_horizontalLinePos_Norm_Y[5u] = l_horizontalLinePos_Norm_Y[6u] - m_trajParams.GradientWidth;
  l_horizontalLinePos_Norm_Y[7u] = m_geomHeight;

  for (vfc::uint32_t i = 0u; i < 8u; i++)
  {
    l_horizontalLinePos_Norm_Y[i] *= l_invGeomHeight * lc_geometricUpScaler;

    if (1.0f < l_horizontalLinePos_Norm_Y[i])
    {
      l_horizontalLinePos_Norm_Y[i] = 1.0f;
    }
  }


  // Defining the tile points
  typedef std::array< std::array<TilePoint, 8>, 6> TilePointArray;
  TilePointArray l_tilePoints;

  for (vfc::uint32_t x = 0u; x < 6u; x++)
  {
    for (vfc::uint32_t y = 0u; y < 8u; y++)
    {
      l_tilePoints[x][y].Pos.x() = l_verticalLinePos_Norm_X[x];
      l_tilePoints[x][y].Pos.y() = l_horizontalLinePos_Norm_Y[y];

      if (0u == x)
      {
        l_tilePoints[x][y].TopLeftNeighbor.x()     = 0u;
        l_tilePoints[x][y].BottomRightNeighbor.x() = 0u;
      }
      else if (5u == x)
      {
        l_tilePoints[x][y].TopLeftNeighbor.x()     = lc_imageWidth_px - 1u;
        l_tilePoints[x][y].BottomRightNeighbor.x() = lc_imageWidth_px - 1u;
      }
      else
      {
        l_tilePoints[x][y].TopLeftNeighbor.x() = pc::util::round2uInt(l_tilePoints[x][y].Pos.x() * lc_imageWidth_geom);
        l_tilePoints[x][y].BottomRightNeighbor.x() = l_tilePoints[x][y].TopLeftNeighbor.x() + 1u;
      }

      if (0u == y)
      {
        l_tilePoints[x][y].TopLeftNeighbor.y()     = 0u;
        l_tilePoints[x][y].BottomRightNeighbor.y() = 0u;
      }
      else if (7u == y)
      {
        l_tilePoints[x][y].TopLeftNeighbor.y()     = lc_imageHeight_px - 1u;
        l_tilePoints[x][y].BottomRightNeighbor.y() = lc_imageHeight_px - 1u;
      }
      else
      {
        l_tilePoints[x][y].TopLeftNeighbor.y() = pc::util::round2uInt(l_tilePoints[x][y].Pos.y() * lc_imageHeight_geom);
        l_tilePoints[x][y].BottomRightNeighbor.y() = l_tilePoints[x][y].TopLeftNeighbor.y() + 1u;
      }
    }
  }

  // Left transparent gray part
  l_tilePoints[0u][0u].Color = m_trajParams.WheelTrack_Color_Auto_Far_BorderLine;
  l_tilePoints[0u][0u].Color.a() = 0.0f;
  l_tilePoints[1u][0u].Color = l_tilePoints[0u][0u].Color;
  l_tilePoints[0u][1u].Color = l_tilePoints[0u][0u].Color;
  l_tilePoints[1u][1u].Color = l_tilePoints[0u][0u].Color;

  // Gray border
  l_tilePoints[2u][0u].Color = m_trajParams.WheelTrack_Color_Auto_Far_BorderLine;
  l_tilePoints[3u][0u].Color = l_tilePoints[2u][0u].Color;
  l_tilePoints[2u][1u].Color = l_tilePoints[2u][0u].Color;
  l_tilePoints[3u][1u].Color = l_tilePoints[2u][0u].Color;

  // Gray inner part
  l_tilePoints[4u][0u].Color = m_trajParams.WheelTrack_Color_Auto_Far_Inside;
  l_tilePoints[5u][0u].Color = l_tilePoints[4u][0u].Color;
  l_tilePoints[4u][1u].Color = l_tilePoints[4u][0u].Color;
  l_tilePoints[5u][1u].Color = l_tilePoints[4u][0u].Color;

  // Transparent part to the left from the red part
  l_tilePoints[0u][2u].Color = m_trajParams.ActionPoint_Color;
  l_tilePoints[0u][2u].Color.a() = 0.0f;
  l_tilePoints[1u][2u].Color = l_tilePoints[0u][2u].Color;
  l_tilePoints[0u][3u].Color = l_tilePoints[0u][2u].Color;
  l_tilePoints[1u][3u].Color = l_tilePoints[0u][2u].Color;
  l_tilePoints[0u][4u].Color = l_tilePoints[0u][2u].Color;
  l_tilePoints[1u][4u].Color = l_tilePoints[0u][2u].Color;
  l_tilePoints[0u][5u].Color = l_tilePoints[0u][2u].Color;
  l_tilePoints[1u][5u].Color = l_tilePoints[0u][2u].Color;

  // Red outer part
  l_tilePoints[2u][2u].Color = m_trajParams.ActionPoint_Color;
  l_tilePoints[3u][2u].Color = l_tilePoints[2u][2u].Color;
  l_tilePoints[2u][3u].Color = l_tilePoints[2u][2u].Color;
  l_tilePoints[3u][3u].Color = l_tilePoints[2u][2u].Color;
  l_tilePoints[2u][4u].Color = l_tilePoints[2u][2u].Color;
  l_tilePoints[3u][4u].Color = l_tilePoints[2u][2u].Color;
  l_tilePoints[2u][5u].Color = l_tilePoints[2u][2u].Color;
  l_tilePoints[3u][5u].Color = l_tilePoints[2u][2u].Color;

  l_tilePoints[4u][2u].Color = l_tilePoints[2u][2u].Color;
  l_tilePoints[5u][2u].Color = l_tilePoints[2u][2u].Color;
  l_tilePoints[4u][3u].Color = l_tilePoints[2u][2u].Color;
  l_tilePoints[5u][3u].Color = l_tilePoints[2u][2u].Color;

  // Red inner part
  l_tilePoints[4u][4u].Color = m_trajParams.ActionPoint_Color;
  l_tilePoints[5u][4u].Color = m_trajParams.ActionPoint_Color;
  l_tilePoints[4u][5u].Color = m_trajParams.ActionPoint_Color;
  l_tilePoints[5u][5u].Color = m_trajParams.ActionPoint_Color;

  // Transition from green closer part to red actionpoint
  l_tilePoints[4u][6u].Color = m_trajParams.WheelTrack_Color_Auto_Close_Inside;
  l_tilePoints[5u][6u].Color = m_trajParams.WheelTrack_Color_Auto_Close_Inside;
  l_tilePoints[4u][7u].Color = m_trajParams.WheelTrack_Color_Auto_Close_Inside;
  l_tilePoints[5u][7u].Color = m_trajParams.WheelTrack_Color_Auto_Close_Inside;

  // Left transparent green part
  l_tilePoints[0u][6u].Color = m_trajParams.WheelTrack_Color_Auto_Close_BorderLine;
  l_tilePoints[0u][6u].Color.a() = 0.0f;
  l_tilePoints[1u][6u].Color = l_tilePoints[0u][6u].Color;
  l_tilePoints[0u][7u].Color = l_tilePoints[0u][6u].Color;
  l_tilePoints[1u][7u].Color = l_tilePoints[0u][6u].Color;

  // Green border
  l_tilePoints[2u][6u].Color = m_trajParams.WheelTrack_Color_Auto_Close_BorderLine;
  l_tilePoints[3u][6u].Color = l_tilePoints[2u][6u].Color;
  l_tilePoints[2u][7u].Color = l_tilePoints[2u][6u].Color;
  l_tilePoints[3u][7u].Color = l_tilePoints[2u][6u].Color;

  typedef std::array< std::array<Tile, 7>, 5> TileArray;
  TileArray l_tiles; // PRQA S 4102


  // Fill up the tile data
  for (vfc::uint32_t x = 0u; x < 5u; x++)
  {
    for (vfc::uint32_t y = 0u; y < 7u; y++)
    {
      l_tiles[x][y].TopLeftTilePointIndex.x() = x;
      l_tiles[x][y].TopLeftTilePointIndex.y() = y;
      l_tiles[x][y].BottomRightTilePointIndex.x() = x + 1u;
      l_tiles[x][y].BottomRightTilePointIndex.y() = y + 1u;

      bool l_horizontallyConstant = false;
      bool l_verticallyConstant   = false;

      if ( (l_tilePoints[l_tiles[x][y].TopLeftTilePointIndex.x()]
                        [l_tiles[x][y].TopLeftTilePointIndex.y()].Color ==
            l_tilePoints[l_tiles[x][y].BottomRightTilePointIndex.x()]
                        [l_tiles[x][y].TopLeftTilePointIndex.y()].Color)
            &&
           (l_tilePoints[l_tiles[x][y].TopLeftTilePointIndex.x()]
                        [l_tiles[x][y].BottomRightTilePointIndex.y()].Color ==
            l_tilePoints[l_tiles[x][y].BottomRightTilePointIndex.x()]
                        [l_tiles[x][y].BottomRightTilePointIndex.y()].Color) )
      {
        l_horizontallyConstant = true;
      }

      if ( (l_tilePoints[l_tiles[x][y].TopLeftTilePointIndex.x()]
                        [l_tiles[x][y].TopLeftTilePointIndex.y()].Color ==
            l_tilePoints[l_tiles[x][y].TopLeftTilePointIndex.x()]
                        [l_tiles[x][y].BottomRightTilePointIndex.y()].Color)
            &&
           (l_tilePoints[l_tiles[x][y].BottomRightTilePointIndex.x()]
                        [l_tiles[x][y].TopLeftTilePointIndex.y()].Color ==
            l_tilePoints[l_tiles[x][y].BottomRightTilePointIndex.x()]
                        [l_tiles[x][y].BottomRightTilePointIndex.y()].Color) )
      {
        l_verticallyConstant = true;
      }

      if (l_horizontallyConstant && l_verticallyConstant)
      {
        l_tiles[x][y].GradientType = GradType::UNIFORM;
      }
      else if (l_horizontallyConstant)
      {
        l_tiles[x][y].GradientType =  GradType::VERTICAL;
      }
      else if (l_verticallyConstant)
      {
        l_tiles[x][y].GradientType =  GradType::HORIZONTAL;
      }
      else
      {
        l_tiles[x][y].GradientType =  GradType::TWO_DIMENSIONAL;
      }
    }
  }

  osg::Image* const l_image = new osg::Image;
  l_image->allocateImage(static_cast<vfc::int32_t>(lc_imageWidth_px), static_cast<vfc::int32_t>(lc_imageHeight_px), 1, GL_RGBA, GL_UNSIGNED_BYTE); //PRQA S 3143
  vfc::uint8_t* const l_data = l_image->data();
  constexpr vfc::uint32_t l_imageWidthTimesChannelsPerPixel = lc_imageWidth_px * lc_channelsPerPixel;
  // Rendering the tiles
  for (vfc::uint32_t tile_x = 0u; tile_x < 5u; tile_x++)
  {
    for (vfc::uint32_t tile_y = 0u; tile_y < 7u; tile_y++)
    {
      if (GradType::UNIFORM == l_tiles[tile_x][tile_y].GradientType)
      {
        for (vfc::uint32_t x  = l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                                           [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].BottomRightNeighbor.x();
                          x <= l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                                           [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].TopLeftNeighbor.x();
                          x++)
        {
          const vfc::uint32_t l_channelsPerPixelTimesX = lc_channelsPerPixel * x;
          for (vfc::uint32_t y  = l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                                             [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].BottomRightNeighbor.y();
                            y <= l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                                             [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].TopLeftNeighbor.y();
                            y++)
          {
            const vfc::uint32_t l_temp = l_imageWidthTimesChannelsPerPixel * y + l_channelsPerPixelTimesX;
            l_data[l_temp + static_cast<vfc::uint32_t>(helper::R)] =
                pc::util::osgx::toUByte(l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                                                       [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].Color.r());
            l_data[l_temp + static_cast<vfc::uint32_t>(helper::G)] =
                pc::util::osgx::toUByte(l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                                                       [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].Color.g());
            l_data[l_temp + static_cast<vfc::uint32_t>(helper::B)] =
                pc::util::osgx::toUByte(l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                                                       [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].Color.b());
            l_data[l_temp + static_cast<vfc::uint32_t>(helper::A)] =
                pc::util::osgx::toUByte(l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                                                       [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].Color.a());
          }
        }
      }
      else if ( GradType::HORIZONTAL == l_tiles[tile_x][tile_y].GradientType)
      {
        for (vfc::uint32_t x  = l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                                           [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].BottomRightNeighbor.x();
                          x <= l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                                           [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].TopLeftNeighbor.x();
                          x++)
        {
          const vfc::float32_t l_texCoord_x = static_cast<vfc::float32_t>(x) * lc_invImageWidth_geom;

          osg::Vec4ub l_interpolatedColor = cc::assets::trajectory::helper::TrajectoryHelper::smoothstep_Vec4fIn_Vec4ubOut( // PRQA S 2759
              l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                          [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].Color,
              l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                          [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].Color,
              l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                          [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].Pos.x(),
              l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                          [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].Pos.x(),
              l_texCoord_x);

          const vfc::uint32_t l_channelsPerPixelTimesX = lc_channelsPerPixel * x;

          for (vfc::uint32_t y  = l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                                             [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].BottomRightNeighbor.y();
                            y <= l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                                             [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].TopLeftNeighbor.y();
                            y++)
          {
            const vfc::uint32_t l_temp = l_imageWidthTimesChannelsPerPixel * y + l_channelsPerPixelTimesX;

            l_data[l_temp + static_cast<vfc::uint32_t>(helper::R)] = l_interpolatedColor.r();
            l_data[l_temp + static_cast<vfc::uint32_t>(helper::G)] = l_interpolatedColor.g();
            l_data[l_temp + static_cast<vfc::uint32_t>(helper::B)] = l_interpolatedColor.b();
            l_data[l_temp + static_cast<vfc::uint32_t>(helper::A)] = l_interpolatedColor.a();
          }
        }
      }
      else if ( GradType::VERTICAL == l_tiles[tile_x][tile_y].GradientType)
      {
        for (vfc::uint32_t x  = l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                                           [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].BottomRightNeighbor.x();
                          x <= l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                                           [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].TopLeftNeighbor.x();
                          x++)
        {
          const vfc::uint32_t l_channelsPerPixelTimesX = lc_channelsPerPixel * x;

          for (vfc::uint32_t y  = l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                                             [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].BottomRightNeighbor.y();
                            y <= l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                                             [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].TopLeftNeighbor.y();
                            y++)
          {
            const vfc::float32_t l_texCoord_y = static_cast<vfc::float32_t>(y) * lc_invImageHeight_geom;

            osg::Vec4ub l_interpolatedColor = cc::assets::trajectory::helper::TrajectoryHelper::smoothstep_Vec4fIn_Vec4ubOut( // PRQA S 2759
                l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                            [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].Color,
                l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                            [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].Color,
                l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                            [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].Pos.y(),
                l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                            [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].Pos.y(),
                l_texCoord_y);

            const vfc::uint32_t l_temp = l_imageWidthTimesChannelsPerPixel * y + l_channelsPerPixelTimesX;

            l_data[l_temp + static_cast<vfc::uint32_t>(helper::R)] = l_interpolatedColor.r();
            l_data[l_temp + static_cast<vfc::uint32_t>(helper::G)] = l_interpolatedColor.g();
            l_data[l_temp + static_cast<vfc::uint32_t>(helper::B)] = l_interpolatedColor.b();
            l_data[l_temp + static_cast<vfc::uint32_t>(helper::A)] = l_interpolatedColor.a();
          }
        }
      }
      else if ( GradType::TWO_DIMENSIONAL == l_tiles[tile_x][tile_y].GradientType)
      {
        for (vfc::uint32_t x  = l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                                           [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].BottomRightNeighbor.x();
                          x <= l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                                           [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].TopLeftNeighbor.x();
                          x++)
        {
          const vfc::float32_t l_texCoord_x = static_cast<vfc::float32_t>(x) * lc_invImageWidth_geom;

          osg::Vec4ub const l_interpolatedColor_Top = cc::assets::trajectory::helper::TrajectoryHelper::smoothstep_Vec4fIn_Vec4ubOut( // PRQA S 2759
              l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                          [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].Color,
              l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                          [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].Color,
              l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                          [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].Pos.x(),
              l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                          [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].Pos.x(),
              l_texCoord_x);

          osg::Vec4ub const l_interpolatedColor_Bottom = cc::assets::trajectory::helper::TrajectoryHelper::smoothstep_Vec4fIn_Vec4ubOut( // PRQA S 2759
              l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                          [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].Color,
              l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                          [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].Color,
              l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                          [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].Pos.x(),
              l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                          [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].Pos.x(),
              l_texCoord_x);

          const vfc::uint64_t l_channelsPerPixelTimesX = lc_channelsPerPixel * x;

          for (vfc::uint32_t y  = l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                                             [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].BottomRightNeighbor.y();
                            y <= l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                                             [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].TopLeftNeighbor.y();
                            y++)
          {
            const vfc::float32_t l_texCoord_y = static_cast<vfc::float32_t>(y) * lc_invImageHeight_geom;

            osg::Vec4ub l_interpolatedColor = cc::assets::trajectory::helper::TrajectoryHelper::smoothstep_Vec4ub( // PRQA S 2759
                l_interpolatedColor_Top,
                l_interpolatedColor_Bottom,
                l_tilePoints[l_tiles[tile_x][tile_y].TopLeftTilePointIndex.x()]
                            [l_tiles[tile_x][tile_y].TopLeftTilePointIndex.y()].Pos.y(),
                l_tilePoints[l_tiles[tile_x][tile_y].BottomRightTilePointIndex.x()]
                            [l_tiles[tile_x][tile_y].BottomRightTilePointIndex.y()].Pos.y(),
                l_texCoord_y);

            const vfc::uint64_t l_temp = l_imageWidthTimesChannelsPerPixel * y + l_channelsPerPixelTimesX;

            l_data[l_temp + static_cast<vfc::uint32_t>(helper::R)] = l_interpolatedColor.r();
            l_data[l_temp + static_cast<vfc::uint32_t>(helper::G)] = l_interpolatedColor.g();
            l_data[l_temp + static_cast<vfc::uint32_t>(helper::B)] = l_interpolatedColor.b();
            l_data[l_temp + static_cast<vfc::uint32_t>(helper::A)] = l_interpolatedColor.a();
          }
        }
      }
      else
      {
        //Do nothing
      }
    }
  }
  return l_image;
}


void ActionPoint::generateVertexData()
{
  generateVertexData_usingTexture();
}


void ActionPoint::generateVertexData_usingTexture()  // PRQA S 6041  // PRQA S 6044
{
  // *** 1. Create frame ***
  m_frame.removeAllPoints();

  vfc::float32_t l_wheelCenterAngle = 0.0f;
  vfc::float32_t l_wheelCenterLongitudinalPos = 0.0f;
  const vfc::float32_t lc_halfGeometryWidth = m_lineGeometryWidth * 0.5f;

  if (cc::assets::trajectory::commontypes::Rotation_enm == sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType)
  {
    // When it's rotation
    if (cc::assets::trajectory::commontypes::Left_enm == mc_side)
    {
      // Left wheel track:
      m_frameRadiuses[0u] = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Radius
                             + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
      m_frameRadiuses[1u] = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Radius;
      m_frameRadiuses[2u] = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Radius
                             + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

      m_frame.setVertexLineRadius(0u, m_frameRadiuses[0u]);
      m_frame.setVertexLineRadius(1u, m_frameRadiuses[1u]);
      m_frame.setVertexLineRadius(2u, m_frameRadiuses[2u]);
      l_wheelCenterAngle = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Angle;
    }
    else
    {
      // Right wheel track:
      m_frameRadiuses[0u] = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Radius
                             + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
      m_frameRadiuses[1u] = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Radius;
      m_frameRadiuses[2u] = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Radius
                             + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;

      m_frame.setVertexLineRadius(0u, m_frameRadiuses[2u]);
      m_frame.setVertexLineRadius(1u, m_frameRadiuses[1u]);
      m_frame.setVertexLineRadius(2u, m_frameRadiuses[0u]);
      l_wheelCenterAngle = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Angle;
    }
  }
  else
  {
    // When it's translation
    if (cc::assets::trajectory::commontypes::Left_enm == mc_side)
    {
      // Left wheel track:
      m_frameLateralOffsets[0u] = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y()
                                   + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
      m_frameLateralOffsets[1u] = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y();
      m_frameLateralOffsets[2u] = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y()
                                   + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

      m_frame.setVertexLineOffset(0u, m_frameLateralOffsets[0u]);
      m_frame.setVertexLineOffset(1u, m_frameLateralOffsets[1u]);
      m_frame.setVertexLineOffset(2u, m_frameLateralOffsets[2u]);
      l_wheelCenterLongitudinalPos = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.x();
    }
    else
    {
      // Right wheel track:
      m_frameLateralOffsets[0u] = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y()
                                   + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
      m_frameLateralOffsets[1u] = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y();
      m_frameLateralOffsets[2u] = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y()
                                   + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;

      m_frame.setVertexLineOffset(0u, m_frameLateralOffsets[2u]);
      m_frame.setVertexLineOffset(1u, m_frameLateralOffsets[1u]);
      m_frame.setVertexLineOffset(2u, m_frameLateralOffsets[0u]);
      l_wheelCenterLongitudinalPos = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.x();
    }
  }
  m_frame.setBumperLineAngle(0u, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Angle);
  m_frame.setBumperLinePos  (0u, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Pos.x());
  const osg::Vec4f l_lineColor = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f); // With texturing just the alpha matters in the shader.
  cc::assets::trajectory::commontypes::ControlPoint_st l_controlPoint;

  vfc::float32_t l_actionPointMidPos = 0.0f;
  // front and rear is the same
  if (cc::assets::trajectory::mainlogic::Forward_enm == sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection)
  {
    l_actionPointMidPos = sm_mainLogicRefPtr->getInputDataRef().External.Parking.ActionPointDist_Front;
  }
  else
  {
    l_actionPointMidPos = sm_mainLogicRefPtr->getInputDataRef().External.Parking.ActionPointDist_Rear;
  }

  if (m_trajParams.Length < l_actionPointMidPos)
  {
    l_actionPointMidPos = m_trajParams.Length;
  }

  m_currActionPointDist = l_actionPointMidPos;
  // check if a new remainingDistance is coming in that is smaller so there is no animation and it will be shown on the screen
  if(m_currActionPointDist <= m_lastActionPointDist)
  {
    m_animate = false;
  }
  else
  {
    m_animate = true;
    l_actionPointMidPos = m_lastActionPointDist;
  }

  // determine the actionpointHeight with l_geomHeight
  const vfc::float32_t l_actionPointFarPos  = l_actionPointMidPos + m_actionPointFarToMidDist;
  const vfc::float32_t l_geomHeight = 3.0f * m_actionPointFarToMidDist;
  m_actionPointNearX = l_actionPointFarPos - l_geomHeight;

  // calculation for ROTATION
  // vfc::float32_t l_newXNear = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPointDefault - sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint.x()
  //                 + (m_actionPointNearX + m_trajParams.RenderOffset + m_distanceLine->getDistanceLineWidth()) * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul;
  vfc::float32_t l_newXNear = 0.0f;
  if (cc::assets::trajectory::mainlogic::Forward_enm == sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection)
  {
    l_newXNear = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPointDefault - sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint.x()
              + (m_actionPointNearX + m_trajParams.RenderOffset_Front + m_distanceLine->getDistanceLineWidth()) * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul;
  }
  else
  {
    l_newXNear = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPointDefault - sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint.x()
              + (m_actionPointNearX + m_trajParams.RenderOffset_Rear + m_distanceLine->getDistanceLineWidth()) * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul;
  }

  // for the case Back- or FrontSteering is getting larger or smaller than the other so Left and RightTurn is changing it must be calculated which radius will be used
  vfc::float32_t l_radiusLeft = 1.0f;
  vfc::float32_t l_radiusRight = 1.0f;
  vfc::float32_t l_radius = 1.0f;

  if(cc::assets::trajectory::commontypes::Left_enm == mc_side)
  {
    l_radiusLeft = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Radius
                    + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
    l_radiusRight = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Radius
                    + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
  }
  else// if(cc::assets::trajectory::commontypes::Right_enm == mc_side)
  {
    l_radiusLeft = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Radius
                    + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
    l_radiusRight = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Radius
                    + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
  }

  vfc::float32_t l_startPointLeft = std::cos(-std::acos(l_newXNear / l_radiusLeft) * sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul) * m_frameRadiuses[1u];
  vfc::float32_t l_startPointRight = std::cos(-std::acos(l_newXNear / l_radiusRight) * sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul) * m_frameRadiuses[1u];
  l_startPointLeft += sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint.x();
  l_startPointRight += sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint.x();

  if(sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * l_startPointRight > sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * l_startPointLeft)
  {
    l_radius = l_radiusRight;
  }
  else
  {
    l_radius = l_radiusLeft;
  }

  vfc::float32_t l_startAngle = 0.0f;
  vfc::float32_t l_endAngle = 0.0f;
  // if the actionPoint far is already INF then the actionpoint shouldnt be rendered anymore - at the moment is the angle just set to 0.0
  if(helper::TrajectoryHelper::isInfinity(l_startPointLeft) || helper::TrajectoryHelper::isInfinity(l_startPointRight))
  {
    l_startAngle = 0.0f;
    l_endAngle = 0.0f;
  }
  else
  {
    l_startAngle = - std::acos(l_newXNear / l_radius) * sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul;
    l_endAngle = l_startAngle + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * std::atan(l_geomHeight / l_radius);
  }


  // calculation of TRANSLATION
  // e.g. if it is a forward left turn, the offset must be choose to the left for the right and the left track
  vfc::float32_t l_offset = 0.0f;
  if(isEqual(sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul , 1.0f))
  {
    if(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle >= 0.0f)
    {
      if(cc::assets::trajectory::commontypes::Left_enm == mc_side)
      {
        l_offset = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y()
                                    + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
      }
      else// if(cc::assets::trajectory::commontypes::Right_enm == mc_side)
      {
        l_offset = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y()
                                    + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
      }
    }
    // right turn
    else //if(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle < 0.0)
    {
      if(cc::assets::trajectory::commontypes::Left_enm == mc_side)
      {
        l_offset = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y()
                                    + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
      }
      else// if(cc::assets::trajectory::commontypes::Right_enm == mc_side)
      {
        l_offset = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y()
                                    + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
      }
    }
  }
  else // if(sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul == -1.0)
  {
    if(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle >= 0.0f)
    {
      if(cc::assets::trajectory::commontypes::Left_enm == mc_side)
      {
        l_offset = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y()
                                    + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
      }
      else// if(cc::assets::trajectory::commontypes::Right_enm == mc_side)
      {
        l_offset = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y()
                                    + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
      }
    }
    // right turn
    else //if(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle < 0.0)
    {
      if(cc::assets::trajectory::commontypes::Left_enm == mc_side)
      {
        l_offset = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y()
                                    + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
      }
      else// if(cc::assets::trajectory::commontypes::Right_enm == mc_side)
      {
        l_offset = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y()
                                    + lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
      }
    }
  }



  osg::Vec2f l_startPoint;
  // l_startPoint.x() = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPointDefault
  //                     + sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
  //                       * (m_actionPointNearX + m_trajParams.RenderOffset + m_distanceLine->getDistanceLineWidth());
  if (cc::assets::trajectory::mainlogic::Forward_enm == sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection)
  {
    l_startPoint.x() = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPointDefault
                      + sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
                        * (m_actionPointNearX + m_trajParams.RenderOffset_Front + m_distanceLine->getDistanceLineWidth());
  }
  else
  {
    l_startPoint.x() = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPointDefault
                      + sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
                        * (m_actionPointNearX + m_trajParams.RenderOffset_Rear + m_distanceLine->getDistanceLineWidth());
  }
  l_startPoint.y() = l_offset;

  const vfc::float32_t l_angleRad = osg::DegreesToRadians(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle);
  const cc::assets::trajectory::helper::RotationFunctor l_translationAngle(l_angleRad);
  // rotate the offset(y) and the length(x) seperately around 0/0
  osg::Vec2f l_rotatedOffset = osg::Vec2f(0.0f, l_startPoint.y());
  osg::Vec2f l_rotatedLongitud = osg::Vec2f(l_startPoint.x(), 0.0f);
  l_translationAngle.rotate(l_rotatedOffset);
  l_translationAngle.rotate(l_rotatedLongitud);

  // factor to multiply the old x value with - substract before that the rotatedOffset
  const vfc::float32_t l_factor = (l_startPoint.x() - l_rotatedOffset.x()) / l_rotatedLongitud.x();
  l_startPoint.x() *= l_factor;

  // multiply old xValue with the factor
  const vfc::float32_t l_startPos = l_startPoint.x();
  const vfc::float32_t l_endPos = l_startPoint.x() + l_geomHeight * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul;


  // pass the calculated controlPoints to the frame so the vertices can be calculated
  // Controllpoint left(0),mid(1),right(2)
  l_controlPoint.Angle = l_startAngle;
  l_controlPoint.LongitudinalPos = l_startPos;
  l_controlPoint.Color = l_lineColor;
  l_controlPoint.Index = 0u; // Dummy value. Will be calculated later.
  m_frame.addControlPoint(0u, l_controlPoint);
  m_frame.addControlPoint(1u, l_controlPoint);
  m_frame.addControlPoint(2u, l_controlPoint);


  const vfc::float32_t l_fadeInStartAngle =
      l_wheelCenterAngle
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * (0.3f / m_frame.getVertexLineRadius(0u));

  const vfc::float32_t l_fadeInStartPos =
      l_wheelCenterLongitudinalPos
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * 0.3f;

  const vfc::float32_t l_fadeInEndAngle = l_wheelCenterAngle;

  const vfc::float32_t l_fadeInEndPos = l_wheelCenterLongitudinalPos;


  m_frame.setFadeInStartAngle(0u, l_fadeInStartAngle);
  m_frame.setFadeInStartAngle(1u, l_fadeInStartAngle);
  m_frame.setFadeInStartAngle(2u, l_fadeInStartAngle);

  m_frame.setFadeInStartPos(0u, l_fadeInStartPos);
  m_frame.setFadeInStartPos(1u, l_fadeInStartPos);
  m_frame.setFadeInStartPos(2u, l_fadeInStartPos);

  m_frame.setFadeInEndAngle(0u, l_fadeInEndAngle);
  m_frame.setFadeInEndAngle(1u, l_fadeInEndAngle);
  m_frame.setFadeInEndAngle(2u, l_fadeInEndAngle);

  m_frame.setFadeInEndPos(0u, l_fadeInEndPos);
  m_frame.setFadeInEndPos(1u, l_fadeInEndPos);
  m_frame.setFadeInEndPos(2u, l_fadeInEndPos);

  // Controllpoint left(0), mid(1), right(2)
  l_controlPoint.Angle = l_endAngle;
  l_controlPoint.LongitudinalPos = l_endPos;
  l_controlPoint.Color = l_lineColor;
  m_frame.addControlPoint(0u, l_controlPoint);
  m_frame.addControlPoint(1u, l_controlPoint);
  m_frame.addControlPoint(2u, l_controlPoint);


  const vfc::float32_t l_trajEndAngle =
        m_frame.getBumperLineAngle(0u)
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        * (m_trajParams.Length / sm_mainLogicRefPtr->getModelDataRef().BumperCenterPoint.Radius);

  const vfc::float32_t l_fadeOutStartAngle = m_frame.getBumperLineAngle(0u)
      + 0.5f * (l_trajEndAngle - m_frame.getBumperLineAngle(0u));

  const vfc::float32_t l_trajEndPos =
      m_frame.getBumperLinePos(0u)
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        * m_trajParams.Length;

  const vfc::float32_t l_fadeOutStartPos = m_frame.getBumperLinePos(0u)
      + 0.5f * (l_trajEndPos - m_frame.getBumperLinePos(0u));

  const vfc::float32_t l_fadeOutEndAngle = l_trajEndAngle;

  const vfc::float32_t l_fadeOutEndPos = l_trajEndPos;


  m_frame.setFadeOutStartAngle(0u, l_fadeOutStartAngle);
  m_frame.setFadeOutStartAngle(1u, l_fadeOutStartAngle);
  m_frame.setFadeOutStartAngle(2u, l_fadeOutStartAngle);

  m_frame.setFadeOutStartPos(0u, l_fadeOutStartPos);
  m_frame.setFadeOutStartPos(1u, l_fadeOutStartPos);
  m_frame.setFadeOutStartPos(2u, l_fadeOutStartPos);

  m_frame.setFadeOutEndAngle(0u, l_fadeOutEndAngle);
  m_frame.setFadeOutEndAngle(1u, l_fadeOutEndAngle);
  m_frame.setFadeOutEndAngle(2u, l_fadeOutEndAngle);

  m_frame.setFadeOutEndPos(0u, l_fadeOutEndPos);
  m_frame.setFadeOutEndPos(1u, l_fadeOutEndPos);
  m_frame.setFadeOutEndPos(2u, l_fadeOutEndPos);


  // *** 2. Create vertices (and colors) ***
  // Generate line vertices
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (m_geometry->getVertexArray());
  l_vertices->clear();

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray());
  l_colors->clear();

  // pass the current steering angle if front and back steering has the same angle
  const vfc::float32_t l_translationAngle_Rad = osg::DegreesToRadians(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle);
  m_frame.generateVertices(  // PRQA S 3803
      0u, 0u, 1u, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
      l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm,
      mc_numOfVerts, sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
      l_translationAngle_Rad);

  m_frame.generateVertices(  // PRQA S 3803
      1u, 0u, 1u, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
      l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm,
      mc_numOfVerts, sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
      l_translationAngle_Rad);

  m_frame.generateVertices(  // PRQA S 3803
      2u, 0u, 1u, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
      l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm,
      mc_numOfVerts, sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
      l_translationAngle_Rad);



  // *** 3. Create indices ***
  osg::DrawElementsUShort* const l_indices = static_cast<osg::DrawElementsUShort*> (m_geometry->getPrimitiveSet(0u));
  l_indices->clear();
  m_frame.generateIndices(0u, 0u, 1u, 0u, mc_numOfVerts, l_indices);
  m_frame.generateIndices(1u, 0u, 2u, 0u, mc_numOfVerts, l_indices);

  l_vertices->dirty();
  l_colors->dirty();

  l_indices->dirty();
  m_geometry->dirtyBound();

  setCull(false);
}

void ActionPoint::animate()
{
  if(m_currActionPointDist > m_lastActionPointDist)
  {
    // check if the animation starts or check if the target distance changed so the animation can adapt
    if((m_startTime < 0.0) || isNotEqual(m_currActionPointDist , m_targetDist))
    {
      m_startTime = static_cast<vfc::float32_t>(osg::Timer::instance()->tick());
      m_startDist = m_lastActionPointDist;
      m_targetDist = m_currActionPointDist;
    }
    else // first time there is nothing to animate cause there is no delta in time
    {
      const vfc::float32_t l_deltaTime = static_cast<vfc::float32_t>(osg::Timer::instance()->delta_m(static_cast<osg::Timer_t>(m_startTime), osg::Timer::instance()->tick()));  //PRQA S 3016
      m_lastActionPointDist = m_startDist + (m_currActionPointDist - m_startDist) * l_deltaTime / m_trajParams.AnimDurRemainingDistance;

      // also make sure if the value is bigger to cut it to the maximum
      if(m_lastActionPointDist >= m_currActionPointDist)
      {
        m_lastActionPointDist = m_currActionPointDist;
      }
      generateVertexData();
    }
  }
  else
  {
    m_startTime = -1.0f;
    m_animate = false;
    m_lastActionPointDist = m_currActionPointDist;
  }
}


} // namespace trajectory
} // namespace assets
} // namespace cc
