//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC BYD_DENZA&MR
/// @file  Vehicle2DCrabWheels.h
/// @brief
//=============================================================================

#include "cc/assets/vehicle2dwheels/inc/Vehicle2DCrabWheels.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/FloatComp.h" //for isZero vfc::float32_t compare
// #include "pc/svs/util/osgx/inc/Utils.h"
// #include "pc/svs/util/osgx/inc/Quantization.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

#include "cc/imgui/inc/imgui_manager.h" // PRQA S 1060

#include "osgDB/ReadFile"
#include "osg/Depth"
#include "osg/Geode"
#include "osg/Geometry"
#include "osg/MatrixTransform"
#include "osg/Texture2D"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace crabwheel
{

pc::util::coding::Item<VehicleCrabWheelSettings> g_crabSettings("CrabWheel");

static osg::Texture2D* loadTexture(const std::string& f_filename)
{
    osg::Image* const l_image = osgDB::readImageFile(f_filename);
    if (l_image == nullptr)
    {
        XLOG_ERROR(g_AppContext, "Vehicle2DCrabWheels::loadTexture(): Could not load " << f_filename);
    }
    osg::Texture2D* const l_texture = new osg::Texture2D(l_image);
    l_texture->setDataVariance(osg::Object::STATIC);
    l_texture->setUnRefImageDataAfterApply(true);
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    return l_texture;
}

Vehicle2DCrabWheels::Vehicle2DCrabWheels(pc::core::Framework* f_framework, bool f_enableAnimation)
    : osg::MatrixTransform{}
    , m_framework{f_framework} // PRQA S 2323 // PRQA S 4052
    , m_vehicle2DWheelsFL{}    // PRQA S 2323
    , m_vehicle2DWheelsFR{}    // PRQA S 2323
    , m_vehicle2DWheelsRL{}    // PRQA S 2323
    , m_vehicle2DWheelsRR{}    // PRQA S 2323
    , m_steeringWheelAngleFront{0}
    , m_steeringWheelAngleRearLeft{0}
    , m_steeringWheelAngleRearRight{0}
    , m_settingsModifiedCount{~0u} // PRQA S 2323
    , m_enableAnimation{f_enableAnimation}
    , m_sharedVehicle2DWheelStateSetLeft{nullptr}
    , m_sharedVehicle2DWheelStateSetRight{nullptr}
    , sm_wheelTextureLeft{nullptr}
    , sm_wheelTextureRight{nullptr}
    , sm_animateWheelTexture{nullptr}
{
    setName("Vehicle2DCrabWheels");
    setNumChildrenRequiringUpdateTraversal(1u);
}

void Vehicle2DCrabWheels::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        if (g_crabSettings->getModifiedCount() != m_settingsModifiedCount)
        {
            init();
            m_settingsModifiedCount = g_crabSettings->getModifiedCount();
        }
        update(f_nv.getFrameStamp()->getSimulationTime());
    }
    osg::Group::traverse(f_nv);
}

void Vehicle2DCrabWheels::init()
{
    removeChildren(0u, getNumChildren()); // PRQA S 3803

    sm_wheelTextureLeft        = loadTexture(CONCAT_PATH(g_crabSettings->m_crabwheelLeft));
    sm_wheelTextureRight        = loadTexture(CONCAT_PATH(g_crabSettings->m_crabwheelRight));
    sm_animateWheelTexture = loadTexture(CONCAT_PATH(g_crabSettings->m_crabWheelAnimation));

    m_sharedVehicle2DWheelStateSetLeft = new osg::StateSet;
    m_sharedVehicle2DWheelStateSetRight = new osg::StateSet;

    // osg::StateSet* l_wheelStateSet = new osg::StateSet;
    if (m_enableAnimation)
    {
        m_sharedVehicle2DWheelStateSetLeft->setTextureAttribute(0u, sm_animateWheelTexture);
        m_sharedVehicle2DWheelStateSetRight->setTextureAttribute(0u, sm_animateWheelTexture);
        pc::core::TextureShaderProgramDescriptor l_vehicel2DWheelsShader("wipingTex");
        l_vehicel2DWheelsShader.apply(m_sharedVehicle2DWheelStateSetLeft.get()); // PRQA S 3803
        l_vehicel2DWheelsShader.apply(m_sharedVehicle2DWheelStateSetRight.get()); // PRQA S 3803
    }
    else
    {
        m_sharedVehicle2DWheelStateSetLeft->setTextureAttribute(0u, sm_wheelTextureLeft);
        m_sharedVehicle2DWheelStateSetLeft->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)
            ->set(osg::Vec4f(1.0f, 1.0f, 0.0f, 0.0f));                                                 // PRQA S 3803
        m_sharedVehicle2DWheelStateSetLeft->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(1.0f); // PRQA S 3803
        m_sharedVehicle2DWheelStateSetLeft->getOrCreateUniform("u_bias", osg::Uniform::FLOAT)->set(0.0f);  // PRQA S 3803
        pc::core::TextureShaderProgramDescriptor l_vehicel2DWheelsShaderLeft("advancedTex");
        l_vehicel2DWheelsShaderLeft.apply(m_sharedVehicle2DWheelStateSetLeft.get()); // PRQA S 3803
        m_sharedVehicle2DWheelStateSetRight->setTextureAttribute(0u, sm_wheelTextureLeft);
        m_sharedVehicle2DWheelStateSetRight->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)
            ->set(osg::Vec4f(1.0f, 1.0f, 0.0f, 0.0f));                                                 // PRQA S 3803
        m_sharedVehicle2DWheelStateSetRight->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(1.0f); // PRQA S 3803
        m_sharedVehicle2DWheelStateSetRight->getOrCreateUniform("u_bias", osg::Uniform::FLOAT)->set(0.0f);  // PRQA S 3803
        pc::core::TextureShaderProgramDescriptor l_vehicel2DWheelsShaderRight("advancedTex");
        l_vehicel2DWheelsShaderRight.apply(m_sharedVehicle2DWheelStateSetRight.get()); // PRQA S 3803
    }
    m_sharedVehicle2DWheelStateSetLeft->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
    m_sharedVehicle2DWheelStateSetLeft->setMode(GL_BLEND, osg::StateAttribute::ON);
    m_sharedVehicle2DWheelStateSetLeft->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    m_sharedVehicle2DWheelStateSetLeft->setRenderBinDetails(600u, "RenderBin");
    m_sharedVehicle2DWheelStateSetRight->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
    m_sharedVehicle2DWheelStateSetRight->setMode(GL_BLEND, osg::StateAttribute::ON);
    m_sharedVehicle2DWheelStateSetRight->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    m_sharedVehicle2DWheelStateSetRight->setRenderBinDetails(600u, "RenderBin");

    osg::ref_ptr<osg::Geode> const l_wheelsFLGeode = new osg::Geode;
    osg::ref_ptr<osg::Geode> const l_wheelsFRGeode = new osg::Geode;
    osg::ref_ptr<osg::Geode> const l_wheelsRLGeode = new osg::Geode;
    osg::ref_ptr<osg::Geode> const l_wheelsRRGeode = new osg::Geode;

    osg::ref_ptr<osg::Geometry> const l_wheelsFLGeometry = new osg::Geometry;
    osg::ref_ptr<osg::Geometry> const l_wheelsFRGeometry = new osg::Geometry;
    osg::ref_ptr<osg::Geometry> const l_wheelsRLGeometry = new osg::Geometry;
    osg::ref_ptr<osg::Geometry> const l_wheelsRRGeometry = new osg::Geometry;

    osg::Vec3Array* const l_vertices = new osg::Vec3Array(4u);
    (*l_vertices)[0u]                = osg::Vec3(
        -0.5f * g_crabSettings->m_wheelSize.x(),
        -0.5f * g_crabSettings->m_wheelSize.y(),
        g_crabSettings->m_groundLevel); // create a high planar to make it placed upper than vehicle model
    (*l_vertices)[1u] = osg::Vec3(
        0.5f * g_crabSettings->m_wheelSize.x(),
        -0.5f * g_crabSettings->m_wheelSize.y(),
        g_crabSettings->m_groundLevel); // just set the height as 0.1 also be fine
    (*l_vertices)[2u] = osg::Vec3(
        0.5f * g_crabSettings->m_wheelSize.x(),
        0.5f * g_crabSettings->m_wheelSize.y(),
        g_crabSettings->m_groundLevel); // 14.5 ~ maximum value of height that we can we see thought planview camera
    (*l_vertices)[3u] =
        osg::Vec3(-0.5f * g_crabSettings->m_wheelSize.x(), 0.5f * g_crabSettings->m_wheelSize.y(), g_crabSettings->m_groundLevel);
    l_wheelsFLGeometry->setVertexArray(l_vertices);
    l_wheelsFRGeometry->setVertexArray(l_vertices);
    l_wheelsRLGeometry->setVertexArray(l_vertices);
    l_wheelsRRGeometry->setVertexArray(l_vertices);

    osg::Vec2Array* const l_texCoords = new osg::Vec2Array(4u);
    (*l_texCoords)[0u]                = osg::Vec2(1.0f, 0.0f);
    (*l_texCoords)[1u]                = osg::Vec2(1.0f, 1.0f);
    (*l_texCoords)[2u]                = osg::Vec2(0.0f, 1.0f);
    (*l_texCoords)[3u]                = osg::Vec2(0.0f, 0.0f);
    l_wheelsFLGeometry->setTexCoordArray(0u, l_texCoords);
    l_wheelsFRGeometry->setTexCoordArray(0u, l_texCoords);
    l_wheelsRLGeometry->setTexCoordArray(0u, l_texCoords);
    l_wheelsRRGeometry->setTexCoordArray(0u, l_texCoords);

    osg::Vec4Array* const l_colours = new osg::Vec4Array(1u);
    (*l_colours)[0u]                = osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f);
    l_wheelsFLGeometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);
    l_wheelsFRGeometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);
    l_wheelsFLGeometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);
    l_wheelsRRGeometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);

    osg::Vec3Array* const l_normals = new osg::Vec3Array(1u);
    (*l_normals)[0u]                = osg::Z_AXIS;
    l_wheelsFLGeometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);
    l_wheelsFRGeometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);
    l_wheelsRLGeometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);
    l_wheelsRRGeometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);

    osg::DrawElementsUByte* const l_indices = new osg::DrawElementsUByte(osg::PrimitiveSet::TRIANGLES, 6u);
    (*l_indices)[0u]                        = 1u;
    (*l_indices)[1u]                        = 0u;
    (*l_indices)[2u]                        = 2u;
    (*l_indices)[3u]                        = 2u;
    (*l_indices)[4u]                        = 0u;
    (*l_indices)[5u]                        = 3u;
    l_wheelsFLGeometry->addPrimitiveSet(l_indices); // PRQA S 3803
    l_wheelsFRGeometry->addPrimitiveSet(l_indices); // PRQA S 3803
    l_wheelsRLGeometry->addPrimitiveSet(l_indices); // PRQA S 3803
    l_wheelsRRGeometry->addPrimitiveSet(l_indices); // PRQA S 3803

    l_wheelsFLGeode->addDrawable(l_wheelsFLGeometry); // PRQA S 3803
    l_wheelsFRGeode->addDrawable(l_wheelsFRGeometry); // PRQA S 3803
    l_wheelsRLGeode->addDrawable(l_wheelsRLGeometry); // PRQA S 3803
    l_wheelsRRGeode->addDrawable(l_wheelsRRGeometry); // PRQA S 3803

    m_vehicle2DWheelsFL = new osg::MatrixTransform();
    m_vehicle2DWheelsFR = new osg::MatrixTransform();
    m_vehicle2DWheelsRL = new osg::MatrixTransform();
    m_vehicle2DWheelsRR = new osg::MatrixTransform();

    m_vehicle2DWheelsFL->addChild(l_wheelsFLGeode); // PRQA S 3803
    m_vehicle2DWheelsFR->addChild(l_wheelsFRGeode); // PRQA S 3803
    m_vehicle2DWheelsRL->addChild(l_wheelsRLGeode); // PRQA S 3803
    m_vehicle2DWheelsRR->addChild(l_wheelsRRGeode); // PRQA S 3803
    m_vehicle2DWheelsFL->setName("wheelsFLNode");
    m_vehicle2DWheelsFR->setName("wheelsFRNode");
    m_vehicle2DWheelsRL->setName("wheelsRLNode");
    m_vehicle2DWheelsRR->setName("wheelsRRNode");

    m_vehicle2DWheelsFL->setStateSet(m_sharedVehicle2DWheelStateSetLeft.get());
    m_vehicle2DWheelsFR->setStateSet(m_sharedVehicle2DWheelStateSetLeft.get());
    m_vehicle2DWheelsRL->setStateSet(m_sharedVehicle2DWheelStateSetRight.get());
    m_vehicle2DWheelsRR->setStateSet(m_sharedVehicle2DWheelStateSetRight.get());

    addChild(m_vehicle2DWheelsFL); // PRQA S 3803
    addChild(m_vehicle2DWheelsFR); // PRQA S 3803
    addChild(m_vehicle2DWheelsRL); // PRQA S 3803
    addChild(m_vehicle2DWheelsRR); // PRQA S 3803

}

void Vehicle2DCrabWheels::update(vfc::float64_t f_time)
{
    m_time = f_time;
    // updateWheelSteering();
    updateCrabWheelAngle();
    setupCommonState();
    if (m_enableAnimation)
    {
        updateCrabAnimation();
    }
}

// void Vehicle2DCrabWheels::updateWheelSteering()
// {
//     const pc::daddy::SteeringAngleDaddy* const l_pFrontWheelSteeringData =
//         m_framework->asCustomFramework()->m_steeringAngleFrontReceiver.getData();
//     if (nullptr != l_pFrontWheelSteeringData)
//     {
//         vfc::CSI::si_radian_f32_t l_value_rad = l_pFrontWheelSteeringData->m_Data;
//         vfc::float32_t            l_value     = l_value_rad.value();
//         if (false == isZero(m_steeringWheelAngleFront - l_value))
//         {
//             m_steeringWheelAngleFront = l_value;
//         }
//     }
//     m_steeringWheelAngleRear = m_steeringWheelAngleFront;
// }

void Vehicle2DCrabWheels::setupCommonState() // PRQA S 4211
{
    const osg::Vec3 l_positionFL(
        g_crabSettings->m_wheelBaseCrab,
        g_crabSettings->m_trackFrontCrab * 0.5f,
        g_crabSettings->m_groundLevel);
    m_vehicle2DWheelsFL->setMatrix(
        osg::Matrix::rotate(m_steeringWheelAngleFront, osg::Z_AXIS) * osg::Matrix::translate(l_positionFL));

    const osg::Vec3 l_positionFR(
        g_crabSettings->m_wheelBaseCrab,
        -(g_crabSettings->m_trackFrontCrab * 0.5f),
        g_crabSettings->m_groundLevel);
    m_vehicle2DWheelsFR->setMatrix(
        osg::Matrix::rotate(m_steeringWheelAngleFront, osg::Z_AXIS) * osg::Matrix::translate(l_positionFR));

    osg::Vec3 l_positionRL(g_crabSettings->m_rearAxlePosition.x(), g_crabSettings->m_trackRearCrab * 0.5f, g_crabSettings->m_groundLevel);
    m_vehicle2DWheelsRL->setMatrix(
        osg::Matrix::rotate(m_steeringWheelAngleRearLeft, osg::Z_AXIS) * osg::Matrix::translate(l_positionRL));

    osg::Vec3 l_positionRR(g_crabSettings->m_rearAxlePosition.x(), -(g_crabSettings->m_trackRearCrab * 0.5f), g_crabSettings->m_groundLevel);
    m_vehicle2DWheelsRR->setMatrix(
        osg::Matrix::rotate(m_steeringWheelAngleRearRight, osg::Z_AXIS) * osg::Matrix::translate(l_positionRR));
}

void Vehicle2DCrabWheels::updateCrabAnimation()
{
    bool             dirty{false};
    pc::daddy::EGear l_gear = pc::daddy::GEAR_INIT;

    if (m_framework->m_gearReceiver.hasData())
    {
        l_gear = static_cast<pc::daddy::EGear>(m_framework->m_gearReceiver.getData()->m_Data);
    }
    if (l_gear == pc::daddy::EGear::GEAR_D)
    {
        m_vehicle2DWheelsFL->setNodeMask(~0u);
        m_vehicle2DWheelsFR->setNodeMask(~0u);
        m_vehicle2DWheelsRL->setNodeMask(~0u);
        m_vehicle2DWheelsRR->setNodeMask(~0u);
        if (m_drivingDirection != DrivingDirection::Forward)
        {
            dirty              = true;
            m_drivingDirection = DrivingDirection::Forward;
        }
    }
    else if (l_gear == pc::daddy::EGear::GEAR_R)
    {
        m_vehicle2DWheelsFL->setNodeMask(~0u);
        m_vehicle2DWheelsFR->setNodeMask(~0u);
        m_vehicle2DWheelsRL->setNodeMask(~0u);
        m_vehicle2DWheelsRR->setNodeMask(~0u);
        if (m_drivingDirection != DrivingDirection::Backward)
        {
            dirty              = true;
            m_drivingDirection = DrivingDirection::Backward;
        }
    }
    else
    {
        m_vehicle2DWheelsFL->setNodeMask(0u);
        m_vehicle2DWheelsFR->setNodeMask(0u);
        m_vehicle2DWheelsRL->setNodeMask(0u);
        m_vehicle2DWheelsRR->setNodeMask(0u);

    }

    const vfc::float32_t time =
        std::fmod(static_cast<vfc::float32_t>(m_time), g_crabSettings->m_fadePeriod) / g_crabSettings->m_fadePeriod;

    auto timeUniformLeft = m_sharedVehicle2DWheelStateSetLeft->getOrCreateUniform("u_time", osg::Uniform::FLOAT);
    auto timeUniformRight = m_sharedVehicle2DWheelStateSetRight->getOrCreateUniform("u_time", osg::Uniform::FLOAT);

    timeUniformLeft->set(time);
    timeUniformRight->set(time);
    if (!dirty)
    {
        return;
    }

    osg::Geometry* const l_wheelsFLGeometry = m_vehicle2DWheelsFL->getChild(0u)->asGeode()->getDrawable(0u)->asGeometry();
    osg::Geometry* const l_wheelsFRGeometry = m_vehicle2DWheelsFR->getChild(0u)->asGeode()->getDrawable(0u)->asGeometry();
    osg::Geometry* const l_wheelsRLGeometry = m_vehicle2DWheelsRL->getChild(0u)->asGeode()->getDrawable(0u)->asGeometry();
    osg::Geometry* const l_wheelsRRGeometry = m_vehicle2DWheelsRR->getChild(0u)->asGeode()->getDrawable(0u)->asGeometry();
    if ((l_wheelsFLGeometry == nullptr) || (l_wheelsFRGeometry == nullptr) || (l_wheelsRLGeometry == nullptr) ||
        (l_wheelsRRGeometry == nullptr))
    {
        XLOG_ERROR(
            g_AppContext,
            "Vehicle2DCrabWheels - updateCrabAnimation() - (l_wheelsFLGeometry == nullptr) || (l_wheelsFRGeometry == "
            "nullptr) || (l_wheelsRLGeometry == nullptr) || (l_wheelsRRGeometry == nullptr)");
        return;
    }
    osg::Vec2Array* const l_texCoords = new osg::Vec2Array(4u);
    if (m_drivingDirection == DrivingDirection::Backward)
    {
        (*l_texCoords)[0u]    = osg::Vec2(1.0f, 0.0f); // Bottom Right
        (*l_texCoords)[1u]    = osg::Vec2(1.0f, 1.0f); // Top Right
        (*l_texCoords)[2u]    = osg::Vec2(0.0f, 1.0f); // Top Left
        (*l_texCoords)[3u]    = osg::Vec2(0.0f, 0.0f); // Bottom Left
        auto directionUniformLeft = m_sharedVehicle2DWheelStateSetLeft->getOrCreateUniform("u_direction", osg::Uniform::FLOAT);
        auto directionUniformRight = m_sharedVehicle2DWheelStateSetRight->getOrCreateUniform("u_direction", osg::Uniform::FLOAT);

        directionUniformLeft->set(0.0);
        directionUniformRight->set(0.0);
    }
    if (m_drivingDirection == DrivingDirection::Forward)
    {
        (*l_texCoords)[0u]    = osg::Vec2(1.0f, 1.0f); // Top Right
        (*l_texCoords)[1u]    = osg::Vec2(1.0f, 0.0f); // Bottom Right
        (*l_texCoords)[2u]    = osg::Vec2(0.0f, 0.0f); // Bottom Left
        (*l_texCoords)[3u]    = osg::Vec2(0.0f, 1.0f); // Top Left
        auto directionUniformLeft = m_sharedVehicle2DWheelStateSetLeft->getOrCreateUniform("u_direction", osg::Uniform::FLOAT);
        auto directionUniformRight =  m_sharedVehicle2DWheelStateSetRight->getOrCreateUniform("u_direction", osg::Uniform::FLOAT);

        directionUniformLeft->set(1.0);
        directionUniformRight->set(1.0);
    }
    l_wheelsFLGeometry->setTexCoordArray(0u, l_texCoords);
    l_wheelsFRGeometry->setTexCoordArray(0u, l_texCoords);
    l_wheelsRLGeometry->setTexCoordArray(0u, l_texCoords);
    l_wheelsRRGeometry->setTexCoordArray(0u, l_texCoords);

    l_texCoords->dirty();
}

void Vehicle2DCrabWheels::updateCrabWheelAngle()
{
    if (m_framework->asCustomFramework()->m_CrabGuideline_ReceiverPort.hasData())
    {
        const cc::daddy::CrabGuideline_t* const l_crabGuideline = m_framework->asCustomFramework()->m_CrabGuideline_ReceiverPort.getData();
        if (l_crabGuideline != nullptr)
        {
            m_steeringWheelAngleFront = osg::DegreesToRadians(l_crabGuideline->m_Data.m_angle);
            m_steeringWheelAngleRearLeft  = osg::DegreesToRadians(l_crabGuideline->m_Data.m_leftRearWheelAngle);
            m_steeringWheelAngleRearRight  = osg::DegreesToRadians(l_crabGuideline->m_Data.m_rightRearWheelAngle);
        }
    }
}

} // namespace crabwheel
} // namespace assets
} // namespace cc
