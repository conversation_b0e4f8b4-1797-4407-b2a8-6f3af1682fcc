//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"

namespace cc
{
namespace assets
{
namespace trajectory
{

//!
//! TrajectoryLineCullCallback
//!
class TrajectoryLineCullCallback : public osg::Drawable::CullCallback
{
public:

  explicit TrajectoryLineCullCallback(GeneralTrajectoryLine* f_trajectoryLine)
    : m_trajectoryLine(f_trajectoryLine)
  {
  }

  bool cull(osg::NodeVisitor* /* f_nv */, osg::Drawable* /* f_drawable */, osg::RenderInfo* /* f_renderInfo */) const override    // PRQA S 2120
  {
    return m_trajectoryLine->isCulled();
  }

protected:

  GeneralTrajectoryLine* m_trajectoryLine;

};

//!
//! TrajectoryLineHideCallback
//!
class TrajectoryLineHideCallback : public TrajectoryLineCullCallback
{
public:

  explicit TrajectoryLineHideCallback(GeneralTrajectoryLine* f_trajectoryLine)
    : TrajectoryLineCullCallback(f_trajectoryLine) // PRQA S 2323 // PRQA S 4052
  {
  }

  bool cull(osg::NodeVisitor* /* f_nv */, osg::Drawable* /* f_drawable */, osg::RenderInfo* /* f_renderInfo */) const override
  {
    if(m_trajectoryLine->isCulled() || m_trajectoryLine->isHidden())
    {
      return true;
    }
    else
    {
      return false;
    }
  }

};


osg::ref_ptr<cc::assets::trajectory::mainlogic::MainLogic> GeneralTrajectoryLine::sm_mainLogicRefPtr;

//!
//! GeneralTrajectoryLine
//!
GeneralTrajectoryLine::GeneralTrajectoryLine(
  pc::core::Framework* f_framework,
  cc::assets::trajectory::commontypes::Side_en f_side,
  vfc::uint32_t f_numOfVertexLines,
  vfc::float32_t f_height,
  const TrajectoryParams_st& f_trajParams,
  bool f_hideCallback)
  : m_cull(true) // PRQA S 2323 // PRQA S 4052
  , m_hide(false) // PRQA S 2323
  , mc_side(f_side) // PRQA S 2323
  , mc_numOfVertexLines(f_numOfVertexLines) // PRQA S 2323
  , m_height(f_height) // PRQA S 2323
  , m_frame() // PRQA S 2323
  , m_frameRadiuses(f_numOfVertexLines)
  , m_frameLateralOffsets(f_numOfVertexLines)
  , m_trajParams(f_trajParams) // PRQA S 2323
  , m_lineGeometryWidth(0.2f) // Default value // PRQA S 2323
  , m_lastUpdate(0u) // PRQA S 2323
  , m_geometry{nullptr}
{
  if (true != sm_mainLogicRefPtr.valid())
  {
    sm_mainLogicRefPtr = new cc::assets::trajectory::mainlogic::MainLogic(f_framework);
  }
  addUpdateCallback(sm_mainLogicRefPtr.get());

  m_geometry = new osg::Geometry;
  if(f_hideCallback)
  {
    m_geometry->setCullCallback(new TrajectoryLineHideCallback(this));
  }
  else
  {
    m_geometry->setCullCallback(new TrajectoryLineCullCallback(this));
  }
  m_geometry->setUseDisplayList(false);
  m_geometry->setUseVertexBufferObjects(true);
  m_geometry->setVertexArray(new osg::Vec3Array);
  m_geometry->setColorArray(new osg::Vec4Array);
  osg::Geode::addDrawable(m_geometry);  // PRQA S 3803
  m_frame.addVertexLines(f_numOfVertexLines);
}


GeneralTrajectoryLine::~GeneralTrajectoryLine() = default;

cc::assets::trajectory::commontypes::Side_en GeneralTrajectoryLine::getSide()
{
  return mc_side;
}

cc::assets::trajectory::mainlogic::MainLogic* GeneralTrajectoryLine::getMainLogicPtr()
{
  return sm_mainLogicRefPtr.get();
}

} // namespace trajectory
} // namespace assets
} // namespace cc
