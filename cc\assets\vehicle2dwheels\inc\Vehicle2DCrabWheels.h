//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC BYD_DENZA&MR
/// @file  Vehicle2DCrabWheels.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_VEHICLE2DWHEEL_CRABWHEEL_H
#define CC_ASSETS_VEHICLE2DWHEEL_CRABWHEEL_H

#include "cc/daddy/inc/CustomDaddyTypes.h"

#include "cc/target/common/inc/commonInterface.h"
#include <osg/Group>
#include <osg/MatrixTransform>
#include <osg/Texture2D>

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace crabwheel
{

//!
//! VehicleCrabWheelSettings
//!
class VehicleCrabWheelSettings : public pc::util::coding::ISerializable
{
public:
    VehicleCrabWheelSettings()
        : m_wheelSize(1.2f, 0.6f)
        , m_crabwheelLeft(CONCAT_PATH("cc/resources/icons/crabWheelLeft.png"))
        , m_crabwheelRight(CONCAT_PATH("cc/resources/icons/crabWheelRight.png"))
        , m_crabWheelAnimation(CONCAT_PATH("cc/resources/icons/crabWheelAnimation.png"))
        , m_trackFrontCrab(1.0f)
        , m_trackRearCrab(1.0f)
        , m_wheelBaseCrab(1.0f)
        , m_rearAxlePosition(0.0f, 0.0f)
        , m_groundLevel(0.2f)
    {
    }

    SERIALIZABLE(VehicleCrabWheelSettings)
    {
        ADD_MEMBER(osg::Vec2f, wheelSize);
        ADD_STRING_MEMBER(crabwheelLeft);
        ADD_STRING_MEMBER(crabwheelRight);
        ADD_STRING_MEMBER(crabWheelAnimation);
        ADD_FLOAT_MEMBER(groundLevel);
        ADD_FLOAT_MEMBER(fadePeriod);
        ADD_FLOAT_MEMBER(trackFrontCrab);
        ADD_FLOAT_MEMBER(trackRearCrab);
        ADD_FLOAT_MEMBER(wheelBaseCrab);
        ADD_MEMBER(osg::Vec2f, rearAxlePosition);
    }

    osg::Vec2      m_wheelSize;
    std::string    m_crabwheelLeft;
    std::string    m_crabwheelRight;
    std::string    m_crabWheelAnimation;
    vfc::float32_t m_groundLevel;
    vfc::float32_t m_fadePeriod{0.5f};
    vfc::float32_t m_trackFrontCrab;
    vfc::float32_t m_trackRearCrab;
    vfc::float32_t m_wheelBaseCrab;
    osg::Vec2f     m_rearAxlePosition;
};

extern pc::util::coding::Item<VehicleCrabWheelSettings> g_crabSettings;

// class Vehicle2DCrabWheelsUpdateCallback : public osg::NodeCallback
// {
// public:
//     Vehicle2DCrabWheelsUpdateCallback(pc::core::Framework* f_pFramework, const bool f_isLeft);

//     virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

// protected:
//     virtual ~Vehicle2DCrabWheelsUpdateCallback() = default;

// private:
//     //! Copy constructor is not permitted.
//     Vehicle2DCrabWheelsUpdateCallback(const Vehicle2DCrabWheelsUpdateCallback& other); // = delete
//     //! Copy assignment operator is not permitted.
//     Vehicle2DCrabWheelsUpdateCallback& operator=(const Vehicle2DCrabWheelsUpdateCallback& other); // = delete

//     osg::ref_ptr<osg::Geode> m_geodeWheelFL;
//     osg::ref_ptr<osg::Geode> m_geodeWheelFR;
//     osg::ref_ptr<osg::Geode> m_geodeWheelRL;
//     osg::ref_ptr<osg::Geode> m_geodeWheelRR;
//     pc::core::Framework*     m_pFramework;
// };

//!
//! vehicle2dwheels
//!
class Vehicle2DCrabWheels : public osg::MatrixTransform
{
public:
    enum class DrivingDirection : vfc::uint8_t
    {
        Backward,
        Forward,
    };

public:
    // Vehicle2DCrabWheels(pc::core::Framework* f_framework);
    Vehicle2DCrabWheels(pc::core::Framework* f_framework, bool f_enableAnimation = false);

    void setupCommonState();

    // void updateWheelSteering();

    virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:
    void init();

    void updateCrabWheelAngle();

    virtual ~Vehicle2DCrabWheels() = default;

    pc::core::Framework*               m_framework;
    osg::ref_ptr<osg::MatrixTransform> m_vehicle2DWheelsFL;
    osg::ref_ptr<osg::MatrixTransform> m_vehicle2DWheelsFR;
    osg::ref_ptr<osg::MatrixTransform> m_vehicle2DWheelsRL;
    osg::ref_ptr<osg::MatrixTransform> m_vehicle2DWheelsRR;
    vfc::float32_t                     m_steeringWheelAngleFront;
    vfc::float32_t                     m_steeringWheelAngleRearLeft;
    vfc::float32_t                     m_steeringWheelAngleRearRight;
    vfc::uint32_t                      m_settingsModifiedCount;
    const bool                         m_enableAnimation;
    DrivingDirection                   m_drivingDirection{DrivingDirection::Backward};
    vfc::float64_t                     m_time;

    osg::ref_ptr<osg::StateSet>  m_sharedVehicle2DWheelStateSetLeft;
    osg::ref_ptr<osg::StateSet>  m_sharedVehicle2DWheelStateSetRight;
    osg::ref_ptr<osg::Texture2D> sm_wheelTextureLeft;
    osg::ref_ptr<osg::Texture2D> sm_wheelTextureRight;
    osg::ref_ptr<osg::Texture2D> sm_animateWheelTexture;

private:
    //! Copy constructor is not permitted.
    Vehicle2DCrabWheels(const Vehicle2DCrabWheels& other); // = delete
    //! Copy assignment operator is not permitted.
    Vehicle2DCrabWheels& operator=(const Vehicle2DCrabWheels& other); // = delete

    void update(vfc::float64_t f_time);
    void updateCrabAnimation();
};

osg::Texture2D* loadcrabWheelTexture(const std::string& f_filename);

} // namespace crabwheel
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_VEHICLE2DWHEEL_CRABWHEEL_H
