//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/assets/splineoverlay/inc/DistanceOverlay.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/svs/util/osgx/inc/Utils.h"

#include "cc/imgui/inc/imgui_manager.h"
// #include "cc/imgui/inc/implot/implot.h"

#include <algorithm>

#include "osgDB/ReadFile"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace distanceoverlay
{

pc::util::coding::Item<DistanceOverlaySettings> g_distanceOverlaySettings("DistanceOverlay");

namespace
{

osg::Texture2D* loadDistanceOverlayTexture(const std::string& f_filename)
{
    osg::ref_ptr<osg::Image> const l_image = osgDB::readImageFile(f_filename);
    if (!l_image.valid())
    {
        XLOG_ERROR(g_AppContext, "DistanceOverlay: invalid image: " << f_filename);
        return nullptr;
    }
    osg::Texture2D* const l_texture = new osg::Texture2D(l_image);
    l_texture->setDataVariance(osg::Object::STATIC);
    l_texture->setUnRefImageDataAfterApply(true);
    l_texture->setResizeNonPowerOfTwoHint(true);
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP);
    l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP);
    return l_texture;
}

} // namespace

bool DistanceOverlay::checkDistanceThreshold(int f_index, vfc::float32_t f_distance) // PRQA S 2427
{
    static constexpr std::array<int, 12> rearIndex{{14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25}}; // PRQA S 2427 // PRQA S 4209
    static constexpr std::array<int, 12> frontIndex{{5, 4, 3, 2, 1, 0, 39, 38, 37, 36, 35, 34}}; // PRQA S 2427 // PRQA S 4209
    bool l_sideSector = false;
    bool l_cornerSector = false;
    switch (f_index) // PRQA S 4018
    {
    //! Left Side
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    //! Right Side
    case 26:
    case 27:
    case 28:
    case 29:
    case 30:
    case 31:
    case 32:
    case 33:
    {
        l_sideSector = true;
        break;
    }
    case 3:
    case 4:
    case 5:
    case 14:
    case 15:
    case 16:
    case 23:
    case 24:
    case 25:
    case 34:
    case 35:
    case 36:
    {
        l_cornerSector = true;
        break;
    }
    default:
    {
        break;
    }
    }

    if (l_sideSector)
    {
        return (f_distance <= g_distanceOverlaySettings->m_displayThresholdSide);
    } else if (l_cornerSector) {
        return (f_distance <= g_distanceOverlaySettings->m_displayThresholdCorner);
    } else {

    }

    return (f_distance <= g_distanceOverlaySettings->m_displayThreshold);
}


DistanceOverlay::DistanceOverlay(cc::core::CustomZoneLayout* f_zoneLayout, vfc::float32_t f_characterSize, pc::core::Framework* f_framework)
    : osg::Group()
    , m_zoneLayout(f_zoneLayout)
    , m_framework(f_framework)
    , m_characterSize(f_characterSize)
    , m_modifiedCount(g_distanceOverlaySettings->getModifiedCount())
{
    setName("DistanceOverlay");
    setNumChildrenRequiringUpdateTraversal(1u);
    for (int i = 0; i < NUM_ZONES; i++)
    {
        const pc::vehicle::LineData line = m_zoneLayout->getMiddleLine(i);
        m_innerPoints[i]           = line.m_innerPoint;
        m_outerPoints[i]           = line.m_outerPoint;
    }
    init();
}

void DistanceOverlay::init()
{
    removeChildren(0u, getNumChildren());

    //! Front Sector
    {
        m_frontTextGeode              = new osg::Geode;
        m_frontText                   = new osgText::Text;
        osg::StateSet* const l_textStateSet = m_frontTextGeode->getOrCreateStateSet();
        l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
        l_textStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
        l_textStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
        l_textStateSet->setRenderBinDetails(pc::core::g_renderOrder->m_overlays, "RenderBin");
        m_frontText->setFont( CONCAT_PATH(g_distanceOverlaySettings->m_font));
        XLOG_ERROR(g_AppContext, "Font path: " << g_distanceOverlaySettings->m_font);
        m_frontText->setFont(CONCAT_PATH(g_distanceOverlaySettings->m_font));
        m_frontText->setColor(g_distanceOverlaySettings->m_color);
        m_frontText->setCharacterSize(m_characterSize);
        m_frontText->setDrawMode(osgText::TextBase::TEXT);
        m_frontText->setCharacterSizeMode(osgText::TextBase::OBJECT_COORDS);
        m_frontText->setAxisAlignment(osgText::Text::XY_PLANE);
        m_frontText->setAlignment(osgText::Text::CENTER_CENTER);
        m_frontText->setText("cm");
        m_frontTextGeode->addDrawable(m_frontText);
        addChild(m_frontTextGeode);
    }

    //! Rear Sector
    {
        m_rearTextGeode               = new osg::Geode;
        m_rearText                    = new osgText::Text;
        osg::StateSet* const l_textStateSet = m_rearTextGeode->getOrCreateStateSet();
        l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
        l_textStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
        l_textStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
        l_textStateSet->setRenderBinDetails(pc::core::g_renderOrder->m_overlays, "RenderBin");
        m_rearText->setFont(CONCAT_PATH(g_distanceOverlaySettings->m_font));
        XLOG_ERROR(g_AppContext, "Font path: " << g_distanceOverlaySettings->m_font);
        m_rearText->setFont(CONCAT_PATH(g_distanceOverlaySettings->m_font));
        m_rearText->setColor(g_distanceOverlaySettings->m_color);
        m_rearText->setCharacterSize(m_characterSize);
        m_rearText->setDrawMode(osgText::TextBase::TEXT);
        m_rearText->setCharacterSizeMode(osgText::TextBase::OBJECT_COORDS);
        m_rearText->setAxisAlignment(osgText::Text::XY_PLANE);
        m_rearText->setAlignment(osgText::Text::CENTER_CENTER);
        m_rearText->setText("cm");
        m_rearTextGeode->addDrawable(m_rearText);
        addChild(m_rearTextGeode);
    }

    //! Right Sector
    {
        m_leftTextGeode              = new osg::Geode;
        m_leftText                   = new osgText::Text;
        osg::StateSet* const l_textStateSet = m_leftTextGeode->getOrCreateStateSet();
        l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
        l_textStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
        l_textStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
        l_textStateSet->setRenderBinDetails(pc::core::g_renderOrder->m_overlays, "RenderBin");
        m_leftText->setFont( CONCAT_PATH(g_distanceOverlaySettings->m_font));
        XLOG_ERROR(g_AppContext, "Font path: " << g_distanceOverlaySettings->m_font);
        m_leftText->setFont(CONCAT_PATH(g_distanceOverlaySettings->m_font));
        m_leftText->setColor(g_distanceOverlaySettings->m_color);
        m_leftText->setCharacterSize(m_characterSize);
        m_leftText->setDrawMode(osgText::TextBase::TEXT);
        m_leftText->setCharacterSizeMode(osgText::TextBase::OBJECT_COORDS);
        m_leftText->setAxisAlignment(osgText::Text::XY_PLANE);
        m_leftText->setAlignment(osgText::Text::CENTER_CENTER);
        m_leftText->setText("cm");
        m_leftTextGeode->addDrawable(m_leftText);
        addChild(m_leftTextGeode);
    }

    //! Right Sector
    {
        m_rightTextGeode              = new osg::Geode;
        m_rightText                   = new osgText::Text;
        osg::StateSet* const l_textStateSet = m_rightTextGeode->getOrCreateStateSet();
        l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
        l_textStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
        l_textStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
        l_textStateSet->setRenderBinDetails(pc::core::g_renderOrder->m_overlays, "RenderBin");
        m_rightText->setFont( CONCAT_PATH(g_distanceOverlaySettings->m_font));
        XLOG_ERROR(g_AppContext, "Font path: " << g_distanceOverlaySettings->m_font);
        m_rightText->setFont(CONCAT_PATH(g_distanceOverlaySettings->m_font));
        m_rightText->setColor(g_distanceOverlaySettings->m_color);
        m_rightText->setCharacterSize(m_characterSize);
        m_rightText->setDrawMode(osgText::TextBase::TEXT);
        m_rightText->setCharacterSizeMode(osgText::TextBase::OBJECT_COORDS);
        m_rightText->setAxisAlignment(osgText::Text::XY_PLANE);
        m_rightText->setAlignment(osgText::Text::CENTER_CENTER);
        m_rightText->setText("cm");
        m_rightTextGeode->addDrawable(m_rightText);
        addChild(m_rightTextGeode);
    }

    //! FRONT SECTOR IMAGE
    {
        m_frontImageGeode                 = new osg::Geode;
        osg::Texture2D* const l_stopTextTexture = loadDistanceOverlayTexture(CONCAT_PATH(g_distanceOverlaySettings->m_stopTexture));
        const osg::Vec3f      l_corner = osg::Vec3f(g_distanceOverlaySettings->m_frontStopCorner, -g_distanceOverlaySettings->m_stopSize.x() * 0.5f, 0.0f);
        const osg::Vec3f      l_width  = osg::Vec3f(0.0f, g_distanceOverlaySettings->m_stopSize.x(), 0.0f);
        const osg::Vec3f      l_height = osg::Vec3f(g_distanceOverlaySettings->m_stopSize.y(), 0.0f, 0.0f);
        osg::Geometry*  const l_stopText =
            pc::util::osgx::createTexturePlane(l_corner, l_width, l_height, 1, 1, 1.0f, 0.0f, 0.0f, 1.0f);
        osg::StateSet* const l_stopTextStateSet = l_stopText->getOrCreateStateSet();
        l_stopTextStateSet->setTextureAttribute(0u, l_stopTextTexture);
        l_stopTextStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
        l_stopTextStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);       // PRQA S 3143
        l_stopTextStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");
        pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
        l_basicTexShader.apply(l_stopTextStateSet);
        m_frontImageGeode->addDrawable(l_stopText);
        addChild(m_frontImageGeode);
    }

    //! REAR SECTOR IMAGE
    {
        m_rearImageGeode                  = new osg::Geode;
        osg::Texture2D* const l_stopTextTexture = loadDistanceOverlayTexture(CONCAT_PATH(g_distanceOverlaySettings->m_stopTexture));
        const osg::Vec3f      l_corner = osg::Vec3f(g_distanceOverlaySettings->m_rearStopCorner, -g_distanceOverlaySettings->m_stopSize.x() * 0.5f, 0.0f);
        const osg::Vec3f      l_width  = osg::Vec3f(0.0f, g_distanceOverlaySettings->m_stopSize.x(), 0.0f);
        const osg::Vec3f      l_height = osg::Vec3f(g_distanceOverlaySettings->m_stopSize.y(), 0.0f, 0.0f);
        osg::Geometry*  const l_stopText =
            pc::util::osgx::createTexturePlane(l_corner, l_width, l_height, 1, 1, 1.0f, 0.0f, 0.0f, 1.0f);
        osg::StateSet* const l_stopTextStateSet = l_stopText->getOrCreateStateSet();
        l_stopTextStateSet->setTextureAttribute(0u, l_stopTextTexture);
        l_stopTextStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
        l_stopTextStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);       // PRQA S 3143
        l_stopTextStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");
        pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
        l_basicTexShader.apply(l_stopTextStateSet);
        m_rearImageGeode->addDrawable(l_stopText);
        addChild(m_rearImageGeode);
    }

    //! LEFT SECTOR IMAGE
    {
        const vfc::float32_t cornerX =
            (g_distanceOverlaySettings->m_frontStopCorner + g_distanceOverlaySettings->m_rearStopCorner + g_distanceOverlaySettings->m_stopSize.y() * 0.5f) * 0.5f;
        m_leftImageGeode                  = new osg::Geode;
        osg::Texture2D* const l_stopTextTexture = loadDistanceOverlayTexture(CONCAT_PATH(g_distanceOverlaySettings->m_stopTexture));
        const osg::Vec3f      l_corner = osg::Vec3f(cornerX, g_distanceOverlaySettings->m_leftStopCorner - g_distanceOverlaySettings->m_stopSize.x() * 0.5f, 0.0f);
        const osg::Vec3f      l_width  = osg::Vec3f(0.0f, g_distanceOverlaySettings->m_stopSize.x(), 0.0f);
        const osg::Vec3f      l_height = osg::Vec3f(g_distanceOverlaySettings->m_stopSize.y(), 0.0f, 0.0f);
        osg::Geometry*  const l_stopText =
            pc::util::osgx::createTexturePlane(l_corner, l_width, l_height, 1, 1, 1.0f, 0.0f, 0.0f, 1.0f);
        osg::StateSet* const l_stopTextStateSet = l_stopText->getOrCreateStateSet();
        l_stopTextStateSet->setTextureAttribute(0u, l_stopTextTexture);
        l_stopTextStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
        l_stopTextStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);       // PRQA S 3143
        l_stopTextStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");
        pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
        l_basicTexShader.apply(l_stopTextStateSet);
        m_leftImageGeode->addDrawable(l_stopText);
        addChild(m_leftImageGeode);
    }

    //! RIGHT SECTOR IMAGE
    {
        const vfc::float32_t cornerX =
            (g_distanceOverlaySettings->m_frontStopCorner + g_distanceOverlaySettings->m_rearStopCorner + g_distanceOverlaySettings->m_stopSize.y() * 0.5f) * 0.5f;
        m_rightImageGeode                  = new osg::Geode;
        osg::Texture2D* const l_stopTextTexture = loadDistanceOverlayTexture(CONCAT_PATH(g_distanceOverlaySettings->m_stopTexture));
        const osg::Vec3f      l_corner = osg::Vec3f(cornerX, g_distanceOverlaySettings->m_rightStopCorner - g_distanceOverlaySettings->m_stopSize.x() * 0.5f, 0.0f);
        const osg::Vec3f      l_width  = osg::Vec3f(0.0f, g_distanceOverlaySettings->m_stopSize.x(), 0.0f);
        const osg::Vec3f      l_height = osg::Vec3f(g_distanceOverlaySettings->m_stopSize.y(), 0.0f, 0.0f);
        osg::Geometry*  const l_stopText =
            pc::util::osgx::createTexturePlane(l_corner, l_width, l_height, 1, 1, 1.0f, 0.0f, 0.0f, 1.0f);
        osg::StateSet* const l_stopTextStateSet = l_stopText->getOrCreateStateSet();
        l_stopTextStateSet->setTextureAttribute(0u, l_stopTextTexture);
        l_stopTextStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
        l_stopTextStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);       // PRQA S 3143
        l_stopTextStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");
        pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
        l_basicTexShader.apply(l_stopTextStateSet);
        m_rightImageGeode->addDrawable(l_stopText);
        addChild(m_rightImageGeode);
    }

    m_frontTextGeode->setNodeMask(0u);
    m_rearTextGeode->setNodeMask(0u);
    m_leftTextGeode->setNodeMask(0u);
    m_rightTextGeode->setNodeMask(0u);

    m_frontImageGeode->setNodeMask(0u);
    m_rearImageGeode->setNodeMask(0u);
    m_leftImageGeode->setNodeMask(0u);
    m_rightImageGeode->setNodeMask(0u);
}

void DistanceOverlay::traverse(osg::NodeVisitor& f_nv)
{
    // if (m_framework->asCustomFramework()->m_HURadarWallButtonReceiver.hasData())
    // {
    //     const cc::daddy::HURadarWallButtonDaddy_t* l_radarWallButton =
    //         m_framework->asCustomFramework()->m_HURadarWallButtonReceiver.getData();
    //     // l_huRadarWallButton = l_radarWallButton->m_Data;
    //     if (l_radarWallButton != nullptr)
    //     {
    //         if (l_radarWallButton->m_Data == cc::target::common::EhuRadarWallButton::CLOSERADARWALL)
    //         {
    //             return;
    //         }
    //     }
    // }

    if (f_nv.getVisitorType() == osg::NodeVisitor::UPDATE_VISITOR)
    {
        if (m_modifiedCount != g_distanceOverlaySettings->getModifiedCount())
        {
            init();
            m_modifiedCount = g_distanceOverlaySettings->getModifiedCount();
            m_dirty         = true;
        }
        update();
    }
    osg::Group::traverse(f_nv);
}

void DistanceOverlay::updateInput()
{
    if (m_framework->m_ultrasonicDataReceiver.hasNewData())
    {
        m_dirty = true;
    }
}

void DistanceOverlay::updateFrontSector()
{
    // static const std::array<int, 12> frontIndex = {34, 35, 36, 37, 38, 39, 0, 1, 2, 3, 4, 5};
    static constexpr std::array<int, 12> frontIndex = {5, 4, 3, 2, 1, 0, 39, 38, 37, 36, 35, 34};
    if (!m_framework->m_ultrasonicDataReceiver.hasData())
    {
        m_frontTextGeode->setNodeMask(0u);
        return;
    }
    const auto ussData = &m_framework->m_ultrasonicDataReceiver.getData()->m_Data;

    vfc::float32_t closestDistance      = 2.0f;
    int            closestDistanceIndex = 0;
    osg::Vec2f     textPosition         = {};
    vfc::float32_t textOrientation      = 0.0f;
    bool           foundClosestDistance = false;
    // Determine processing order based on gear
    std::array<int, 4> processingOrder = {9, 6, 3, 0}; // Reverse order for D gear: front right first 4321
    for (const int i : processingOrder) 
    {
        const int             currentIndex = frontIndex[i + 1];
        pc::vehicle::LineData line         = m_zoneLayout->getMiddleLine(currentIndex);
        m_distances[currentIndex]          = ussData->at(currentIndex).getDistance();
        m_positions[currentIndex] = line.m_innerPoint + line.m_direction * (ussData->at(currentIndex).getDistance() +
                                                                            g_distanceOverlaySettings->m_offsetDistance);
        if (m_distances[currentIndex] < closestDistance && checkDistanceThreshold(currentIndex, m_distances[currentIndex]))
        {
            foundClosestDistance = true;
            closestDistance      = m_distances[currentIndex];
            closestDistanceIndex = currentIndex;
            textPosition         = m_positions[currentIndex];
            textOrientation      = std::atan2(line.m_direction.y(), line.m_direction.x());
        }
    }
    if (!foundClosestDistance)
    {
        closestDistanceIndex = 0;
        textPosition = m_positions[0];
        m_frontTextGeode->setNodeMask(0u);
    }

    m_frontText->setPosition({textPosition.x(), textPosition.y(), 1.0f});
    m_frontText->setRotation(osg::Quat(-osg::PI_2 + textOrientation, osg::Z_AXIS));
    m_frontText->setText(std::to_string(static_cast<int>(std::round(closestDistance * 100.0f))) + "cm");

    if (checkDistanceThreshold(closestDistanceIndex, closestDistance) && foundClosestDistance)
    {
        m_lastClosestDistanceFront = closestDistance;
        m_frontTextGeode->setNodeMask(~0u);
    }
    if (closestDistance <= g_distanceOverlaySettings->m_stopDistanceThreshold)
    {
        m_frontTextGeode->setNodeMask(0u);
        m_frontImageGeode->setNodeMask((m_stopDistanceEnabled) ? ~0u : 0u);
    }
    else
    {
        m_frontImageGeode->setNodeMask(0u);
    }
}

void DistanceOverlay::updateRearSector()
{
    static constexpr std::array<int, 12> rearIndex = {14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25};
    if (!m_framework->m_ultrasonicDataReceiver.hasData())
    {
        m_rearTextGeode->setNodeMask(0u);
        return;
    }
    const auto ussData = &m_framework->m_ultrasonicDataReceiver.getData()->m_Data;

    vfc::float32_t closestDistance      = 2.0f;
    int            closestDistanceIndex = 0;
    osg::Vec2f     textPosition         = {};
    vfc::float32_t textOrientation      = 0.0f;
    bool           foundClosestDistance = false;
    std::array<int, 4> processingOrder = {6, 3, 9, 0}; // Reverse order for R gear: middle first 3241
    for (const int i : processingOrder) 
    {
        const int             currentIndex = rearIndex[i + 1];
        pc::vehicle::LineData line         = m_zoneLayout->getMiddleLine(currentIndex);
        m_distances[currentIndex]          = ussData->at(currentIndex).getDistance();
        m_positions[currentIndex] = line.m_innerPoint + line.m_direction * (ussData->at(currentIndex).getDistance() +
                                                                            g_distanceOverlaySettings->m_offsetDistance);
        if (m_distances[currentIndex] < closestDistance && checkDistanceThreshold(currentIndex, m_distances[currentIndex]))
        {
            foundClosestDistance = true;
            closestDistance      = m_distances[currentIndex];
            closestDistanceIndex = currentIndex;
            textPosition         = m_positions[currentIndex];
            textOrientation      = std::atan2(line.m_direction.y(), line.m_direction.x());
        }
    }

    if (!foundClosestDistance)
    {
        closestDistanceIndex = 0;
        textPosition = m_positions[0];
        m_rearTextGeode->setNodeMask(0u);
    }

    m_rearText->setPosition({textPosition.x(), textPosition.y(), 1.0f});
    m_rearText->setRotation(osg::Quat(osg::PI_2 + textOrientation, osg::Z_AXIS));
    m_rearText->setText(std::to_string(static_cast<int>(std::round(closestDistance * 100.0f))) + "cm");

    if (checkDistanceThreshold(closestDistanceIndex, closestDistance) && foundClosestDistance)
    {
        m_lastClosestDistanceRear = closestDistance;
        m_rearTextGeode->setNodeMask(~0u);
    }
    if (closestDistance <= g_distanceOverlaySettings->m_stopDistanceThreshold)
    {
        m_rearTextGeode->setNodeMask(0u);
        m_rearImageGeode->setNodeMask((m_stopDistanceEnabled) ? ~0u : 0u);
    }
    else
    {
        m_rearImageGeode->setNodeMask(0u);
    }
}

void DistanceOverlay::updateLeftSector()
{
    // static const std::array<int, 12> frontIndex = {5, 4, 3, 2, 1, 0, 39, 38, 37, 36, 35, 34};
    static constexpr std::array<int, 8> leftIndex = {6, 7, 8, 9, 10, 11, 12, 13};
    if (!m_framework->m_ultrasonicDataReceiver.hasData())
    {
        m_leftTextGeode->setNodeMask(0u);
        return;
    }
    const auto ussData = &m_framework->m_ultrasonicDataReceiver.getData()->m_Data;

    vfc::float32_t closestDistance      = 2.0f;
    int            closestDistanceIndex = 0;
    osg::Vec2f     textPosition         = {};
    vfc::float32_t textOrientation      = 0.0f;
    bool           foundClosestDistance = false;
    // 0 2 4 6
    for (int i = 0; i < leftIndex.size(); i = i + 2)
    {
        const int             currentIndex = leftIndex[i + 1];
        const int             previousIndex = leftIndex[i];
        pc::vehicle::LineData line0         = m_zoneLayout->getMiddleLine(currentIndex);
        pc::vehicle::LineData line1         = m_zoneLayout->getMiddleLine(previousIndex);
        m_distances[currentIndex]          = ussData->at(currentIndex).getDistance();
        m_positions[currentIndex] =
            ((line0.m_innerPoint + line1.m_innerPoint) * 0.5f) +
            (line0.m_direction) * (ussData->at(currentIndex).getDistance() + g_distanceOverlaySettings->m_sideOffsetDistance);
        if (m_distances[currentIndex] < closestDistance && checkDistanceThreshold(currentIndex, m_distances[currentIndex]))
        {
            foundClosestDistance = true;
            closestDistance      = m_distances[currentIndex];
            closestDistanceIndex = currentIndex;
            textPosition         = m_positions[currentIndex];
            textOrientation      = std::atan2(
                (line0.m_direction.y() + line1.m_direction.y()) * 0.5f,
                (line0.m_direction.x() + line1.m_direction.x()) * 0.5f);
        }
    }

    if (!foundClosestDistance)
    {
        closestDistanceIndex = 0;
        textPosition = m_positions[0];
        m_leftTextGeode->setNodeMask(0u);
    }

    m_leftText->setPosition({textPosition.x(), textPosition.y(), 1.0f});
    m_leftText->setRotation(osg::Quat(-osg::PI_2 + 0.f, osg::Z_AXIS));
    m_leftText->setText(std::to_string(static_cast<int>(std::round(closestDistance * 100.0f))) + "cm");

    if (checkDistanceThreshold(closestDistanceIndex, closestDistance) && foundClosestDistance)
    {
        m_lastClosestDistanceLeft = closestDistance;
        m_leftTextGeode->setNodeMask(~0u);
    }
    if (closestDistance <= g_distanceOverlaySettings->m_stopDistanceThreshold)
    {
        m_leftTextGeode->setNodeMask(0u);
        m_leftImageGeode->setNodeMask((m_stopDistanceEnabled) ? ~0u : 0u);
    }
    else
    {
        m_leftImageGeode->setNodeMask(0u);
    }

}

void DistanceOverlay::updateRightSector()
{
    // static const std::array<int, 12> frontIndex = {5, 4, 3, 2, 1, 0, 39, 38, 37, 36, 35, 34};
    static constexpr std::array<int, 8> rightIndex = {26, 27, 28, 29, 30, 31, 32, 33};
    if (!m_framework->m_ultrasonicDataReceiver.hasData())
    {
        m_rightTextGeode->setNodeMask(0u);
        return;
    }
    const auto ussData = &m_framework->m_ultrasonicDataReceiver.getData()->m_Data;

    vfc::float32_t closestDistance      = 2.0f;
    int            closestDistanceIndex = 0;
    osg::Vec2f     textPosition         = {};
    vfc::float32_t textOrientation      = 0.0f;
    bool           foundClosestDistance = false;
    // 0 2 4 6
    for (int i = 0; i < rightIndex.size(); i = i + 2)
    {
        const int             currentIndex = rightIndex[i + 1];
        const int             previousIndex = rightIndex[i];
        pc::vehicle::LineData line0         = m_zoneLayout->getMiddleLine(currentIndex);
        pc::vehicle::LineData line1         = m_zoneLayout->getMiddleLine(previousIndex);
        m_distances[currentIndex]          = ussData->at(currentIndex).getDistance();
        m_positions[currentIndex] =
            ((line0.m_innerPoint + line1.m_innerPoint) * 0.5f) +
            (line0.m_direction) * (ussData->at(currentIndex).getDistance() + g_distanceOverlaySettings->m_sideOffsetDistance);
        if (m_distances[currentIndex] < closestDistance && checkDistanceThreshold(currentIndex, m_distances[currentIndex]))
        {
            foundClosestDistance = true;
            closestDistance      = m_distances[currentIndex];
            closestDistanceIndex = currentIndex;
            textPosition         = m_positions[currentIndex];
            textOrientation      = std::atan2(
                (line0.m_direction.y() + line1.m_direction.y()) * 0.5f,
                (line0.m_direction.x() + line1.m_direction.x()) * 0.5f);
        }
    }

    if (!foundClosestDistance)
    {
        closestDistanceIndex = 0;
        textPosition = m_positions[0];
        m_rightTextGeode->setNodeMask(0u);
    }

    m_rightText->setPosition({textPosition.x(), textPosition.y(), 1.0f});
    m_rightText->setRotation(osg::Quat(-osg::PI_2 + 0.f, osg::Z_AXIS));
    m_rightText->setText(std::to_string(static_cast<int>(std::round(closestDistance * 100.0f))) + "cm");

    if (checkDistanceThreshold(closestDistanceIndex, closestDistance) && foundClosestDistance)
    {
        m_lastClosestDistanceRight = closestDistance;
        m_rightTextGeode->setNodeMask(~0u);
    }
    if (closestDistance <= g_distanceOverlaySettings->m_stopDistanceThreshold)
    {
        m_rightTextGeode->setNodeMask(0u);
        m_rightImageGeode->setNodeMask((m_stopDistanceEnabled) ? ~0u : 0u);
    }
    else
    {
        m_rightImageGeode->setNodeMask(0u);
    }
}

void DistanceOverlay::updateImgui()
{
}

void DistanceOverlay::update()
{
    updateInput();

    if (m_dirty)
    {
        updateFrontSector();
        updateRearSector();
        updateLeftSector();
        updateRightSector();
        m_dirty = false;
    }

    // if (m_framework->asCustomFramework()->isImguiEnabled())
    // {
    //     updateImgui();
    // }
}

} // namespace distanceoverlay
} // namespace assets
} // namespace cc