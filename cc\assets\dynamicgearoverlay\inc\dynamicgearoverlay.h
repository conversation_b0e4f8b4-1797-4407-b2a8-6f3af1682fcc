//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: NVA2HC NGUYEN DUC THIEN Van (CN/ESC-EPA1)
//  Department: CN/ESC
//=============================================================================
/// @swcomponent SVS GAC
/// @file  DynamicGearOverlays.h
/// @brief
//=============================================================================

#ifndef CC_VIEWS_ENGINEERINGVIEW_DynamicGearOverlays_H
#define CC_VIEWS_ENGINEERINGVIEW_DynamicGearOverlays_H

#include "pc/svs/core/inc/Asset.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "cc/target/common/inc/linux_svs_interface.hpp"
#include <osg/MatrixTransform>

namespace cc
{
namespace assets
{
namespace dynamicgearoverlay
{

//======================================================
// DynamicGearOverlays
//------------------------------------------------------
/// Class responsible for the DynamicGearOverlays view
/// Receives the APG step and travel distance desired and showing on the circle progess bar
/// <AUTHOR> DUC THIEN Van (CN/ESC-EPA1)
/// @ingroup DynamicGearOverlays
//======================================================

class DynamicGearSettings : public pc::util::coding::ISerializable
{
public:

  DynamicGearSettings()
  :m_positionHori(osg::Vec3f(600.0f, -250.0f, 0.0f))
  ,m_positionVert(osg::Vec3f(600.0f, -250.0f, 0.0f))
  ,m_color(osg::Vec4f(0.0f, 1.0f, 1.0f, 0.5f))
  ,m_outerRadius(20.0f)
  ,m_innerRadius(14.0f)
  ,m_horiToVertScaling(1.0f)
  ,m_clockwise(true)
  {
  }

  SERIALIZABLE(DynamicGearSettings)
  {
    ADD_MEMBER(osg::Vec3f, positionHori);
    ADD_MEMBER(osg::Vec3f, positionVert);
    ADD_MEMBER(osg::Vec4f, color);
    ADD_FLOAT_MEMBER(outerRadius);
    ADD_FLOAT_MEMBER(innerRadius);
    ADD_FLOAT_MEMBER(horiToVertScaling);
    ADD_BOOL_MEMBER(clockwise);
  }

  osg::Vec3f m_positionHori;
  osg::Vec3f m_positionVert;
  osg::Vec4f m_color;
  vfc::float32_t m_outerRadius;
  vfc::float32_t m_innerRadius;
  vfc::float32_t m_horiToVertScaling;
  bool m_clockwise;
};

extern pc::util::coding::Item<DynamicGearSettings> g_dynamicgearSettings;

class DynamicGear : public osg::Geode // PRQA S 2617 // PRQA S 2614
{
public:

    enum : vfc::uint32_t
    {
        MARKER_NUM_SEGMENTS = 10u,
        CIRCLE_NUM_SEGMENTS = 50u
    };
    DynamicGear(cc::core::CustomFramework* f_customFramework);

    void drawCircle();

    const osg::Vec3f& getPosition() const
    {
        return m_position;
    }

    void setPosition(const osg::Vec3f& f_position)
    {
        m_position = f_position;
    }

    void update();

    const osg::Vec4f& getColor() const
    {
        return m_color;
    }

    void setColor(const osg::Vec4f& f_color)
    {
        m_color = f_color;
    }

    bool isCulled() const
    {
        return m_cull;
    }

    void setCull(bool f_isCulled)
    {
        m_cull = f_isCulled;
    }

    bool isHidden() const
    {
        return m_hide;
    }

    void setHide(bool f_isHidden)
    {
        m_hide = f_isHidden;
    }

protected:

    virtual ~DynamicGear();

private:

  //! Copy constructor is not permitted.
  DynamicGear (const DynamicGear& other); // = delete
  //! Copy assignment operator is not permitted.
  DynamicGear& operator=(const DynamicGear& other); // = delete

    bool m_cull;
    bool m_hide;
    cc::target::common::EThemeTypeHU m_theme;
    bool  m_requireUpdateGeode;
    osg::ref_ptr<osg::Vec3Array> m_DynamicGearVertexArray;
    osg::Vec3f m_position;
    osg::ref_ptr<osg::Vec4Array> m_PtrColor;
    osg::Vec4f m_color;

    cc::core::CustomFramework* m_customFramework;
    vfc::float32_t    m_progress_percentage;
    bool     m_isNewStep;
    vfc::uint8_t  m_APG_Output_MoveNumber; // 0..1..2..3..4...
    vfc::int16_t  m_APG_Output_TravelDistDesired_max; // cm
    vfc::uint32_t m_modifiedCount;
};

class DynamicGearOverlays : public osg::MatrixTransform
{
public:

    DynamicGearOverlays(cc::core::CustomFramework* f_customFramework);

    virtual bool update(pc::core::Framework* f_framework);


    virtual void traverse(osg::NodeVisitor& f_nv) override;

    bool isCulled() const
    {
        return m_cull;
    }

    void setCull(bool f_isCulled)
    {
        m_cull = f_isCulled;
    }

    bool isHidden() const
    {
        return m_hide;
    }

    void setHide(bool f_isHidden)
    {
        m_hide = f_isHidden;
    }


protected:

    virtual ~DynamicGearOverlays();

private:

  //! Copy constructor is not permitted.
  DynamicGearOverlays (const DynamicGearOverlays& other); // = delete
  //! Copy assignment operator is not permitted.
  DynamicGearOverlays& operator=(const DynamicGearOverlays& other); // = delete


  bool m_cull;
  bool m_hide;

  vfc::uint32_t m_sequenceNumber;

  osg::ref_ptr<osg::Vec3Array> m_DynamicGearVertexArray;

  osg::Timer_t m_lastUpdate;

  cc::core::CustomFramework* m_customFramework;

};


} // namespace dynamicgearoverlay
} // namespace asset
} // namespace cc

#endif // CC_VIEWS_ENGINEERINGVIEW_DynamicGearOverlays_H