#ifndef PASHMI_TYPES_C_HPP_INCLUDED
#define PASHMI_TYPES_C_HPP_INCLUDED

#ifndef SLK_CODE_GEN
#include "vfc/core/vfc_types.hpp"
#endif   // SLK_CODE_GEN


#ifndef SLK_CODE_GEN
namespace pashmi
{
#endif
    typedef enum class
    #ifndef SLK_CODE_GEN
    EPtsHmiState : vfc::uint8_t
    #endif   // SLK_CODE_GEN
    {
        PTS_HMI_SYSTEM_OFF              = 0,
        PTS_HMI_SWITCHED_OFF            = 1,
        PTS_HMI_ERROR                   = 2,
        PTS_HMI_ERROR_FEEDBACK          = 3,
        PTS_HMI_DISTURBANCE_FRONT       = 4,
        PTS_HMI_DISTURBANCE_REAR        = 5,
        PTS_HMI_DISTURBANCE_FRONT_REAR  = 6,
        PTS_HMI_GEAR_P                  = 7,
        PTS_HMI_NO_WARNING              = 8,
        PTS_HMI_WARNING_ACTIVE          = 9,
    } EPtsHmiState;


    typedef enum class
    #ifndef SLK_CODE_GEN
    EPtsSndWrn : vfc::uint8_t
    #endif   // SLK_CODE_GEN
    {
        PTS_SND_WRN_OFF     = 0,
        PTS_SND_WRN_INT1    = 1,
        PTS_SND_WRN_INT2    = 2,
        PTS_SND_WRN_INT3    = 3,
        PTS_SND_WRN_INT4    = 4,
        PTS_SND_WRN_INT5    = 5,
        PTS_SND_WRN_INT6    = 6,
        PTS_SND_WRN_INT7    = 7,
        PTS_SND_WRN_INT8    = 8,
        PTS_SND_WRN_INT9    = 9,
        PTS_SND_WRN_INT10   = 10,
        PTS_SND_WRN_INT11   = 11,
        PTS_SND_WRN_INT12   = 12,
        PTS_SND_WRN_INT13   = 13,
        PTS_SND_WRN_INT14   = 14,
        PTS_SND_WRN_CONT    = 15,
    } EPtsSndWrn;


    typedef enum class
    #ifndef SLK_CODE_GEN
    EPtsSwLedRq : vfc::uint8_t
    #endif   // SLK_CODE_GEN
    {
        PTS_SW_LED_OFF      = 0,
        PTS_SW_LED_ON       = 1,
        PTS_SW_LED_BLINK    = 2,
    } EPtsSwLedRq;

    typedef enum class
    #ifndef SLK_CODE_GEN
    EPtsSwState : vfc::uint8_t
    #endif   // SLK_CODE_GEN
    {
        PTS_SW_STATE_OFF    = 0,
        PTS_SW_STATE_ON     = 1,
    } EPtsSwState;

    typedef enum class
    #ifndef SLK_CODE_GEN
    EPtsPopUpRq : vfc::uint8_t
    #endif   // SLK_CODE_GEN
    {
        PTS_POP_UP_RQ_NONE          = 0,
        PTS_POP_UP_RQ_OBST_PRSNT    = 1, // Obstacle present
        PTS_POP_UP_RQ_OBST_APPROACH = 2, // approach to Obstacle
    } EPtsPopUpRq;

    typedef enum class
    #ifndef SLK_CODE_GEN
    EPtsColorCoding : vfc::uint8_t
    #endif   // SLK_CODE_GEN
    {
        PTS_GREY        = 0,
        PTS_BLUE        = 1,
        PTS_YELLOW_ONE  = 2,
        PTS_YELLOW_TWO  = 3,
        PTS_ORANGE_ONE  = 4,
        PTS_ORANGE_TWO  = 5,
        PTS_RED_ONE     = 6,
        PTS_RED_TWO     = 7,
    } EPtsColorCoding;

    typedef struct
    {
        EPtsColorCoding     m_ptsDispIndicationEntry[16];
        bool                m_objOnCourse[16];
        EPtsColorCoding     m_ptsDispIndicationMini[6];
    }CPtsDispInformation;



#ifndef SLK_CODE_GEN
}
#endif

#endif
