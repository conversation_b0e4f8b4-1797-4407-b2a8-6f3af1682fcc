//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomIcon.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/CustomIcon.h"
#include "cc/assets/uielements/inc/Utils.h"

#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "cc/util/logging/inc/LoggingContexts.h"

using pc::util::logging::g_OSGContext;
using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

//!
//! CustomIcon
//!
CustomIcon::CustomIcon(const std::string& f_filename, bool f_isLeft, bool f_isParkSpace, bool f_isParkUI, bool f_isHoriScreen)
: Icon::Icon(f_filename, false) // PRQA S 2323
, m_rotAngle(0.f) // PRQA S 2323
, m_isLeft(f_isLeft) // PRQA S 2323
, m_isParkSpace(f_isParkSpace) // PRQA S 2323
, m_isParkUI(f_isParkUI) // PRQA S 2323
, m_isHoriScreen(f_isHoriScreen) // PRQA S 2323
, m_animationStyle(AnimationStyle::NONE_EFFECT) // PRQA S 2323
, m_animationDir(AnimationDir::START_FROM_TOP) // PRQA S 2323
, m_iconSize(osg::Vec2f{0.0f, 0.0f}) // PRQA S 2323
{
  init(f_filename);
}

CustomIcon::CustomIcon(
  const std::string& f_filename, bool f_isLeft, bool f_isParkSpace, bool f_isParkUI, bool f_isHoriScreen,
  const AnimationStyle f_animationStyle, const AnimationDir f_animationDir)
: Icon::Icon(f_filename, false)
, m_rotAngle(0.f)
, m_isLeft(f_isLeft)
, m_isParkSpace(f_isParkSpace)
, m_isParkUI(f_isParkUI)
, m_isHoriScreen(f_isHoriScreen)
, m_animationStyle(f_animationStyle)
, m_animationDir(f_animationDir)
, m_iconSize(osg::Vec2f{0.0f, 0.0f})
{
  init(f_filename);
}

void CustomIcon::init(const std::string& f_filename)
{
  auto const texture = Icon::getTexture();
  if (texture == nullptr)
  {
    XLOG_WARN(g_AppContext, "CustomIcon: " << f_filename << " not found!");
    return;
  }
  osg::Vec2i l_textureSize(texture->getTextureWidth(), texture->getTextureHeight());
  if ((0 == l_textureSize.x()) || (0 == l_textureSize.y()))
  {
    //! Texture size is only available after the texture has been applied for the first time,
    //! so check underlying image size in case of invalid texture size
    const osg::Image* const l_image = texture->getImage();
    // assert(l_image);
    l_textureSize.x() = l_image->s();
    l_textureSize.y() = l_image->t();
  }
  m_iconSize = osg::Vec2f{static_cast<vfc::float32_t>(l_textureSize.x()), static_cast<vfc::float32_t>(l_textureSize.y())};
  setSize(m_iconSize, pc::assets::Icon::UnitType::Pixel);
  if (m_animationStyle == AnimationStyle::AUGMENTED_WAVE_EFFECT)
  {
    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    pc::core::TextureShaderProgramDescriptor l_iconAnimationShader("iconAnimationAugmentedWave");
    osg::Uniform* const l_augmentedWaveUniform = l_stateSet->getOrCreateUniform("isAugmentedWave", osg::Uniform::BOOL);
    osg::Uniform* const l_alphaUniform = l_stateSet->getOrCreateUniform("alpha", osg::Uniform::FLOAT);
    l_augmentedWaveUniform->set(false); // PRQA S 3803
    l_alphaUniform->set(1.0f);  // PRQA S 3803
    l_iconAnimationShader.apply(l_stateSet);  // PRQA S 3803
  }
  else if (m_animationStyle == AnimationStyle::FADEIN_FADEOUT_EFFECT)
  {
    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    pc::core::TextureShaderProgramDescriptor l_iconAnimationShader("iconAnimationFadeInFadeOut");
    osg::Uniform* const l_fadingUniform = l_stateSet->getOrCreateUniform("isFadeInOut", osg::Uniform::BOOL);
    osg::Uniform* const l_alphaUniform = l_stateSet->getOrCreateUniform("alpha", osg::Uniform::FLOAT);
    l_fadingUniform->set(false); // PRQA S 3803
    l_alphaUniform->set(1.0f);  // PRQA S 3803
    l_iconAnimationShader.apply(l_stateSet);  // PRQA S 3803
  }
  else {}
}


void CustomIcon::setTexture(osg::Texture2D* f_texture)
{
  osg::StateSet* const l_stateSet = getOrCreateStateSet();
  l_stateSet->setTextureAttribute(0u, f_texture);
}


void CustomIcon::setRotateAngle(const vfc::float32_t& f_rotAngle)
{
  m_rotAngle = f_rotAngle;
  // magic to set dirty
  osg::Vec2f originalPos;
  pc::assets::Icon::UnitType originalUnitType{pc::assets::Icon::UnitType::Pixel};
  pc::assets::Icon::getPosition(originalPos, originalUnitType);
  pc::assets::Icon::setPosition(originalPos * 0.5f + osg::Vec2f{1.0f, 1.0f}, originalUnitType);
  pc::assets::Icon::setPosition(originalPos, originalUnitType);
}


void CustomIcon::setAnimation(const AnimationStyle f_animationStyle)
{
  m_animationStyle = f_animationStyle;
}


const CustomIcon::AnimationStyle CustomIcon::getAnimation() const
{
  return m_animationStyle;
}

void CustomIcon::setHorizontalScreen(const bool f_isHoriScreen)
{
  m_isHoriScreen = f_isHoriScreen;
}


osg::Geometry* CustomIcon::createGeometry() const
{
  osg::Geometry* const l_geometry = new osg::Geometry;
  l_geometry->setUseDisplayList(false);
  l_geometry->setUseVertexBufferObjects(true);
  l_geometry->setVertexArray(new osg::Vec3Array(4));

  osg::Vec4Array* const l_colors = new osg::Vec4Array(1);
  (*l_colors)[0] = osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f);
  l_geometry->setColorArray(l_colors, osg::Array::BIND_OVERALL);

  osg::Vec2Array* const l_texCoords = new osg::Vec2Array(4);
  l_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);

  osg::DrawElementsUByte* const l_indices = new osg::DrawElementsUByte(osg::PrimitiveSet::TRIANGLES, 6u);
  (*l_indices)[0] = 0u;
  (*l_indices)[1] = 1u;
  (*l_indices)[2] = 2u;
  (*l_indices)[3] = 2u;
  (*l_indices)[4] = 1u;
  (*l_indices)[5] = 3u;
  l_geometry->addPrimitiveSet(l_indices);  // PRQA S 3803

  return l_geometry;
}


void CustomIcon::updateGeometry(
    osg::Geometry* f_geometry,
    const osg::Vec2f& f_origin,
    const osg::Vec2f& f_size,
    vfc::float32_t f_left,
    vfc::float32_t f_bottom,
    vfc::float32_t f_right,
    vfc::float32_t f_top) const
{
  if (f_geometry == nullptr)
  {
      return;
  }
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (f_geometry->getVertexArray()); // PRQA S 3076
  if (m_isParkSpace)  // for parking space
  {
    if (m_isLeft)
    {
      if (false == m_isHoriScreen)  // left && vertical screen
      {
        (*l_vertices)[0u] = osg::Vec3f(f_origin.x() + f_size.x() * std::cos(m_rotAngle), f_origin.y() - f_size.x() * std::sin(m_rotAngle), 0.0f);
        (*l_vertices)[1u] = osg::Vec3f(f_origin.x() + f_size.x() * std::cos(m_rotAngle) - f_size.y() * std::sin(m_rotAngle), f_origin.y() - f_size.x() * std::sin(m_rotAngle) - f_size.y() * std::cos(m_rotAngle), 0.0f);
        (*l_vertices)[2u] = osg::Vec3f(f_origin, 0.0f);
        (*l_vertices)[3u] = osg::Vec3f(f_origin.x() - f_size.y() * std::sin(m_rotAngle), f_origin.y() - f_size.y() * std::cos(m_rotAngle), 0.0f);
      }
      else  // left && horizontal screen
      {
        (*l_vertices)[0u] = osg::Vec3f(f_origin, 0.0f);
        (*l_vertices)[1u] = osg::Vec3f(f_origin.x() - f_size.x() * std::cos(m_rotAngle), f_origin.y() + f_size.x() * std::sin(m_rotAngle), 0.0f);
        (*l_vertices)[2u] = osg::Vec3f(f_origin.x() - f_size.y() * std::sin(m_rotAngle), f_origin.y() - f_size.y() * std::cos(m_rotAngle), 0.0f);
        (*l_vertices)[3u] = osg::Vec3f(f_origin.x() - f_size.y() * std::sin(m_rotAngle) - f_size.x() * std::cos(m_rotAngle), f_origin.y() - f_size.y() * std::cos(m_rotAngle) + f_size.x() * std::sin(m_rotAngle), 0.0f);
      }
    }
    else if (false == m_isHoriScreen)  // right && vertical screen
    {
      (*l_vertices)[0u] = osg::Vec3f(f_origin.x() + f_size.x() * std::cos(m_rotAngle),                                     f_origin.y() - f_size.x() * std::sin(m_rotAngle), 0.0f);
      (*l_vertices)[1u] = osg::Vec3f(f_origin.x() + f_size.x() * std::cos(m_rotAngle) + f_size.y() * std::sin(m_rotAngle), f_origin.y() - f_size.x() * std::sin(m_rotAngle) + f_size.y() * std::cos(m_rotAngle), 0.0f);
      (*l_vertices)[2u] = osg::Vec3f(f_origin, 0.0f);
      (*l_vertices)[3u] = osg::Vec3f(f_origin.x() + f_size.y() * std::sin(m_rotAngle),                                     f_origin.y() + f_size.y() * std::cos(m_rotAngle), 0.0f);
    }
    else  // right && horizontal screen
    {
      (*l_vertices)[0u] = osg::Vec3f(f_origin, 0.0f);
      (*l_vertices)[1u] = osg::Vec3f(f_origin.x() + f_size.x() * std::cos(m_rotAngle), f_origin.y() - f_size.x() * std::sin(m_rotAngle), 0.0f);
      (*l_vertices)[2u] = osg::Vec3f(f_origin.x() - f_size.y() * std::sin(m_rotAngle), f_origin.y() - f_size.y() * std::cos(m_rotAngle), 0.0f);
      (*l_vertices)[3u] = osg::Vec3f(f_origin.x() - f_size.y() * std::sin(m_rotAngle) + f_size.x() * std::cos(m_rotAngle), f_origin.y() - f_size.y() * std::cos(m_rotAngle) - f_size.x() * std::sin(m_rotAngle), 0.0f);
    }
  }
  else if (m_isParkUI && (false == m_isHoriScreen))  // vertical layout , and rotate 90degree
  {
    //        3 (d)    1 (b)
    //        -------------
    //        |   size.y  |
    //        |           |
    //        |           |    size.x
    //        |           |
    //        |           |
    //        |           |
    //        -------------
    //        2 (c)     0 (a)

    // if pic size is already rotated, take below calculation
    (*l_vertices)[0u] = osg::Vec3f(f_origin.x() + f_size.x(), f_origin.y(), 0.0f);
    (*l_vertices)[1u] = osg::Vec3f(f_origin.x() + f_size.x(), f_origin.y() + f_size.y(), 0.0f);
    (*l_vertices)[2u] = osg::Vec3f(f_origin, 0.0f);
    (*l_vertices)[3u] = osg::Vec3f(f_origin.x(), f_origin.y() + f_size.y(), 0.0f);

    // (*l_vertices)[0u] = osg::Vec3f(f_origin.x() + f_size.y(), f_origin.y(), 0.0f);
    // (*l_vertices)[1u] = osg::Vec3f(f_origin.x() + f_size.y(), f_origin.y() + f_size.x(), 0.0f);
    // (*l_vertices)[2u] = osg::Vec3f(f_origin, 0.0f);
    // (*l_vertices)[3u] = osg::Vec3f(f_origin.x(), f_origin.y() + f_size.x(), 0.0f);
  }
  else  // default, no rotate
  {
    //        2 (c)            3 (d)
    //        ----------------------
    //        |       size.x       |
    //        |                    |   size.y
    //        |                    |
    //        ----------------------
    //        0 (a)            1 (b)
    (*l_vertices)[0u] = osg::Vec3f(f_origin, 0.0f);
    (*l_vertices)[1u] = osg::Vec3f(f_origin.x() + f_size.x(), f_origin.y(), 0.0f);
    (*l_vertices)[2u] = osg::Vec3f(f_origin.x(), f_origin.y() + f_size.y(), 0.0f);
    (*l_vertices)[3u] = osg::Vec3f(f_origin + f_size, 0.0f);
  }
  l_vertices->dirty();

  osg::Vec2Array* const l_texCoords = static_cast<osg::Vec2Array*> (f_geometry->getTexCoordArray(0u)); // PRQA S 3076
  (*l_texCoords)[0u] = osg::Vec2f(f_left, f_bottom);
  (*l_texCoords)[1u] = osg::Vec2f(f_right, f_bottom);
  (*l_texCoords)[2u] = osg::Vec2f(f_left, f_top);
  (*l_texCoords)[3u] = osg::Vec2f(f_right, f_top);
  l_texCoords->dirty();

  f_geometry->dirtyBound();
}

void CustomIcon::updateShaderUniform()
{
  if (m_animationStyle == AnimationStyle::AUGMENTED_WAVE_EFFECT)
  {
    static vfc::float32_t g_alpha = 1.f; // TODO add this as private member
    static AnimationDir g_animationDir = AnimationDir::START_FROM_TOP;
    static constexpr vfc::float32_t g_step = 0.03f;

    if (g_animationDir != m_animationDir)
    {
      g_alpha = (m_animationDir == START_FROM_TOP) ? 1.0f : 0.0f;
      g_animationDir = m_animationDir;
    }
    if (m_animationDir == AnimationDir::START_FROM_TOP)
    {
      g_alpha = (g_alpha < 0.0f) ? 1.0f : g_alpha - g_step;
    }
    if (m_animationDir == AnimationDir::START_FROM_BOTTOM)
    {
      g_alpha = (g_alpha > 1.0f) ? 0.0f : g_alpha + g_step;
    }
    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->getOrCreateUniform("isAugmentedWave", osg::Uniform::BOOL)->set(true);
    l_stateSet->getOrCreateUniform("isFadeInOut", osg::Uniform::BOOL)->set(false);
    l_stateSet->getOrCreateUniform("alpha", osg::Uniform::FLOAT)->set(g_alpha);
  }

  if (m_animationStyle == AnimationStyle::FADEIN_FADEOUT_EFFECT)
  {
    static vfc::uint32_t g_animationCounter = 0u;
    const vfc::float32_t l_alpha = std::abs(std::cos(osg::DegreesToRadians(static_cast<vfc::float32_t>(g_animationCounter))));
    g_animationCounter = g_animationCounter + 3u;
    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->getOrCreateUniform("isAugmentedWave", osg::Uniform::BOOL)->set(false);
    l_stateSet->getOrCreateUniform("isFadeInOut", osg::Uniform::BOOL)->set(true);
    l_stateSet->getOrCreateUniform("alpha", osg::Uniform::FLOAT)->set(l_alpha);
  }
}


} // namespace uielements
} // namespace assets
} // namespace cc
