//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CameraPositions.cpp
/// @brief
//=============================================================================

#include "cc/virtcam/inc/CameraPositions.h"
#include <cassert>

namespace cc
{
namespace virtcam
{

pc::util::coding::Item<CameraPositions> g_positions("VirtCam");

//!
//! CameraPositions
//!
CameraPositions::CameraPositions() = default;

const pc::virtcam::VirtualCamera& CameraPositions::getPosition(vfc::uint32_t f_index) const  // PRQA S 6040
{
  assert(f_index < static_cast<vfc::uint32_t>(NUMBER_OF_VIRT_CAMS));

  switch (f_index)
  {
    case VCAM_PLAN_VIEW:
    {
      return m_plan;
    }
    case VCAM_FRONT_VIEW:
    {
      return m_front;
    }
    case VCAM_REAR_VIEW:
    {
      return m_rear;
    }
    case VCAM_FRONT_JUNCTION_VIEW:
    {
      return m_frontJunction;
    }
    case VCAM_REAR_JUNCTION_VIEW:
    {
      return m_rearJunction;
    }
    case VCAM_TRAILER_VIEW:
    {
      return m_trailer;
    }
    case VCAM_TRAILER_ZOOM_VIEW:
    {
      return m_trailerZoom;
    }
    case VCAM_BONNET_VIEW:
    {
      return m_bonnet;
    }
    case VCAM_DRIVE_ASSIST_LEFT_VIEW:
    {
      return m_driveAssistLeft;
    }
    case VCAM_DRIVE_ASSIST_MAIN_VIEW:
    {
      return m_driveAssistMain;
    }
    case VCAM_DRIVE_ASSIST_RIGHT_VIEW:
    {
      return m_driveAssistRight;
    }
    case VCAM_FRONT_LEFT_VIEW:
    {
      return m_frontLeft;
    }
    case VCAM_REAR_LEFT_VIEW:
    {
      return m_rearLeft;
    }
    case VCAM_FRONT_RIGHT_VIEW:
    {
      return m_frontRight;
    }
    case VCAM_REAR_RIGHT_VIEW:
    {
      return m_rearRight;
    }
    case VCAM_KERB_LEFT_VIEW:
    {
      return m_kerbLeft;
    }
    case VCAM_KERB_RIGHT_VIEW:
    {
      return m_kerbRight;
    }
    case VCAM_PERS_FRONT_VIEW:
    {
      return m_persFront;
    }
    case VCAM_PERS_RIGHT_VIEW:
    {
      return m_persRight;
    }
    case VCAM_PERS_REAR_VIEW:
    {
      return m_persRear;
    }
    case VCAM_PERS_LEFT_VIEW:
    {
      return m_persLeft;
    }
    case VCAM_FRONT_THREAT_VIEW:
    {
      return m_frontThreat;
    }
    case VCAM_REAR_THREAT_VIEW:
    {
      return m_rearThreat;
    }
    case VCAM_DRIVE_ASSIST_DUAL_RIGHT_VIEW:
    {
      return m_driveAssistDualRight;
    }
    case VCAM_DRIVE_ASSIST_DUAL_LEFT_VIEW:
    {
      return m_driveAssistDualLeft;
    }
    case VCAM_DRIVE_ASSIST_DUAL_FRONT_VIEW:
    {
      return m_driveAssistDualFront;
    }
    case VCAM_SINGLE_LEFT_VIEW:
    {
      return m_leftView;
    }
    case VCAM_SINGLE_RIGHT_VIEW:
    {
      return m_rightView;
    }
    case VCAM_FULL_SCREEN_VIEW:
    {
      return m_fullScreen;
    }
    case VCAM_FRONT_WHEEL_LEFT_VIEW:
    {
      return m_frontWheelLeft;
    }
    case VCAM_FRONT_WHEEL_RIGHT_VIEW:
    {
      return m_frontWheelRight;
    }
    case VCAM_BOTH_WHEEL_LEFT_VIEW:
    {
      return m_bothWheelLeft;
    }
    case VCAM_BOTH_WHEEL_RIGHT_VIEW:
    {
      return m_bothWheelRight;
    }
    case VCAM_REAR_WHEEL_LEFT_VIEW:
    {
      return m_rearWheelLeft;
    }
    case VCAM_REAR_WHEEL_RIGHT_VIEW:
    {
      return m_rearWheelRight;
    }
    case VCAM_PARK_SEARCHING:
    {
      return m_parkView;
    }
    case VCAM_VIRTUAL_PARK_SEARCHING:
    {
      return m_virtualParkSearching;
    }
    case VCAM_VIRTUAL_PARK_CONFIRM:
    {
      return m_virtualParkConfirm;
    }
    case VCAM_VIRTUAL_PARK_OUT_CONFIRM:
    {
      return m_virtualParkOutConfirm;
    }
    case VCAM_VIRTUAL_PARK_GUIDANCE:
    {
      return m_virtualParkGuidance;
    }
    case VCAM_VIRTUAL_PARK_COMPLETE_CROSS:
    {
      return m_virtualParkCompleteCross;
    }
    case VCAM_VIRTUAL_PARK_COMPLETE_PARA:
    {
      return m_virtualParkCompletePara;
    }
    case VCAM_VIRTUAL_PARK_COMPLETE_DIGN:
    {
      return m_virtualParkCompleteDiagonal;
    }
    case VCAM_FULL_SCREEN_3D_VIEW:
    {
      return m_fullScreen3D;
    }
    case VCAM_NORMAL_3D_VIEW:
    {
      return m_normal3D;
    }
#if ENABLE_VERTICAL_MODE_VIRTCAM
    case VCAM_VERT_PLAN_VIEW:
    {
      return m_vertPlan;
    }
    case VCAM_VERT_PERS_FRONT_VIEW:
    {
      return m_vertPersFront;
    }
    case VCAM_VERT_PERS_REAR_VIEW:
    {
      return m_vertPersRear;
    }
    case VCAM_VERT_REAR_LEFT_VIEW:
    {
      return m_vertRearLeft;
    }
    case VCAM_VERT_REAR_RIGHT_VIEW:
    {
      return m_vertRearRight;
    }
    case VCAM_VERT_FRONT_LEFT_VIEW:
    {
      return m_vertFrontLeft;
    }
    case VCAM_VERT_FRONT_RIGHT_VIEW:
    {
      return m_vertFrontRight;
    }
    case VCAM_VERT_BONNET_VIEW:
    {
      return m_vertBonnet;
    }
    case VCAM_VERT_PARKING_PLAN_VIEW:
    {
      return m_vertParkingPlan;
    }
    case VCAM_VERT_VIRTUAL_PARK_SEARCHING:
    {
      return m_vertVirtualParkSearching;
    }
    case VCAM_VERT_VIRTUAL_PARK_CONFIRM:
    {
      return m_vertVirtualParkConfirm;
    }
    case VCAM_VERT_VIRTUAL_PARK_GUIDANCE:
    {
      return m_vertVirtualParkGuidance;
    }
    case VCAM_VERT_VIRTUAL_PARK_COMPLETE_CROSS:
    {
      return m_vertVirtualParkCompleteCross;
    }
    case VCAM_VERT_VIRTUAL_PARK_COMPLETE_PARA:
    {
      return m_vertVirtualParkCompletePara;
    }
    case VCAM_VERT_VIRTUAL_PARK_COMPLETE_DIGN:
    {
      return m_vertVirtualParkCompleteDiagonal;
    }
#endif
    case VCAM_HORI_PARKING_PLAN_VIEW:
    {
      return m_horiParkingPlan;
    }
    case VCAM_PLANETRAY_VIEW:
    {
      return m_planetary;
    }
    case VCAM_FRONT_BUMPER_VIEW:
    {
      return m_frontBumper;
    }
    case VCAM_REAR_BUMPER_VIEW:
    {
      return m_rearBumper;
    }
    case VCAM_ULTRA_WIDE_SURROUND_VIEW:
    {
      return m_ultraWide;
    }
    default:
    {
      return m_rearLeft;
    }
  }
}

std::string cc::virtcam::CameraPositions::getVirtCamName(vfc::uint32_t f_index)
{
    // clang-format off
    switch (f_index)
    {
      case VCAM_JAPANESE_VIEW: { return "VCAM_JAPANESE_VIEW"; }
      case VCAM_PLAN_VIEW: { return "VCAM_PLAN_VIEW"; }
      case VCAM_FRONT_VIEW: { return "VCAM_FRONT_VIEW"; }
      case VCAM_REAR_VIEW: { return "VCAM_REAR_VIEW"; }
      case VCAM_FRONT_JUNCTION_VIEW: { return "VCAM_FRONT_JUNCTION_VIEW"; }
      case VCAM_REAR_JUNCTION_VIEW: { return "VCAM_REAR_JUNCTION_VIEW"; }
      case VCAM_TRAILER_VIEW: { return "VCAM_TRAILER_VIEW"; }
      case VCAM_TRAILER_ZOOM_VIEW: { return "VCAM_TRAILER_ZOOM_VIEW"; }
      case VCAM_BONNET_VIEW: { return "VCAM_BONNET_VIEW"; }
      case VCAM_DRIVE_ASSIST_LEFT_VIEW: { return "VCAM_DRIVE_ASSIST_LEFT_VIEW"; }
      case VCAM_DRIVE_ASSIST_MAIN_VIEW: { return "VCAM_DRIVE_ASSIST_MAIN_VIEW"; }
      case VCAM_DRIVE_ASSIST_RIGHT_VIEW: { return "VCAM_DRIVE_ASSIST_RIGHT_VIEW"; }
      case VCAM_FRONT_LEFT_VIEW: { return "VCAM_FRONT_LEFT_VIEW"; }
      case VCAM_REAR_LEFT_VIEW: { return "VCAM_REAR_LEFT_VIEW"; }
      case VCAM_FRONT_RIGHT_VIEW: { return "VCAM_FRONT_RIGHT_VIEW"; }
      case VCAM_REAR_RIGHT_VIEW: { return "VCAM_REAR_RIGHT_VIEW"; }
      case VCAM_KERB_LEFT_VIEW: { return "VCAM_KERB_LEFT_VIEW"; }
      case VCAM_KERB_RIGHT_VIEW: { return "VCAM_KERB_RIGHT_VIEW"; }
      case VCAM_PERS_FRONT_VIEW: { return "VCAM_PERS_FRONT_VIEW"; }
      case VCAM_PERS_RIGHT_VIEW: { return "VCAM_PERS_RIGHT_VIEW"; }
      case VCAM_PERS_REAR_VIEW: { return "VCAM_PERS_REAR_VIEW"; }
      case VCAM_PERS_LEFT_VIEW: { return "VCAM_PERS_LEFT_VIEW"; }
      case VCAM_TOW_ASSIST_LEFT_TRIPLE_VIEW: { return "VCAM_TOW_ASSIST_LEFT_TRIPLE_VIEW"; }
      case VCAM_TOW_ASSIST_RIGHT_TRIPLE_VIEW: { return "VCAM_TOW_ASSIST_RIGHT_TRIPLE_VIEW"; }
      case VCAM_TOW_ASSIST_REAR_TRIPLE_VIEW: { return "VCAM_TOW_ASSIST_REAR_TRIPLE_VIEW"; }
      case VCAM_FRONT_THREAT_VIEW: { return "VCAM_FRONT_THREAT_VIEW"; }
      case VCAM_REAR_THREAT_VIEW: { return "VCAM_REAR_THREAT_VIEW"; }
      case VCAM_DRIVE_ASSIST_DUAL_RIGHT_VIEW: { return "VCAM_DRIVE_ASSIST_DUAL_RIGHT_VIEW"; }
      case VCAM_DRIVE_ASSIST_DUAL_LEFT_VIEW: { return "VCAM_DRIVE_ASSIST_DUAL_LEFT_VIEW"; }
      case VCAM_DRIVE_ASSIST_DUAL_FRONT_VIEW: { return "VCAM_DRIVE_ASSIST_DUAL_FRONT_VIEW"; }
      case VCAM_TOW_ASSIST_SINGLE: { return "VCAM_TOW_ASSIST_SINGLE"; }
      case VCAM_TOW_ASSIST_LEFT_DUAL_VIEW: { return "VCAM_TOW_ASSIST_LEFT_DUAL_VIEW"; }
      case VCAM_TOW_ASSIST_RIGHT_DUAL_VIEW: { return "VCAM_TOW_ASSIST_RIGHT_DUAL_VIEW"; }
      case VCAM_SINGLE_LEFT_VIEW: { return "VCAM_SINGLE_LEFT_VIEW"; }
      case VCAM_SINGLE_RIGHT_VIEW: { return "VCAM_SINGLE_RIGHT_VIEW"; }
      case VCAM_FULL_SCREEN_VIEW: { return "VCAM_FULL_SCREEN_VIEW"; }
      case VCAM_FRONT_WHEEL_LEFT_VIEW: { return "VCAM_FRONT_WHEEL_LEFT_VIEW"; }
      case VCAM_FRONT_WHEEL_RIGHT_VIEW: { return "VCAM_FRONT_WHEEL_RIGHT_VIEW"; }
      case VCAM_REAR_WHEEL_LEFT_VIEW: { return "VCAM_REAR_WHEEL_LEFT_VIEW"; }
      case VCAM_REAR_WHEEL_RIGHT_VIEW: { return "VCAM_REAR_WHEEL_RIGHT_VIEW"; }
      case VCAM_OVERTHEROOF: { return "VCAM_OVERTHEROOF"; }
      case VCAM_PARK_SEARCHING: { return "VCAM_PARK_SEARCHING"; }
      case VCAM_VIRTUAL_PARK_SEARCHING: { return "VCAM_VIRTUAL_PARK_SEARCHING"; }
      case VCAM_VIRTUAL_PARK_CONFIRM: { return "VCAM_VIRTUAL_PARK_CONFIRM"; }
      case VCAM_VIRTUAL_PARK_GUIDANCE: { return "VCAM_VIRTUAL_PARK_GUIDANCE"; }
      case VCAM_VIRTUAL_PARK_COMPLETE_CROSS: { return "VCAM_VIRTUAL_PARK_COMPLETE_CROSS"; }
      case VCAM_VIRTUAL_PARK_COMPLETE_PARA: { return "VCAM_VIRTUAL_PARK_COMPLETE_PARA"; }
      case VCAM_VIRTUAL_PARK_COMPLETE_DIGN: { return "VCAM_VIRTUAL_PARK_COMPLETE_DIGN"; }
      case VCAM_FULL_SCREEN_3D_VIEW: { return "VCAM_FULL_SCREEN_3D_VIEW"; }
      case VCAM_VERT_PLAN_VIEW: { return "VCAM_VERT_PLAN_VIEW"; }
      case VCAM_VERT_PERS_FRONT_VIEW: { return "VCAM_VERT_PERS_FRONT_VIEW"; }
      case VCAM_VERT_PERS_REAR_VIEW: { return "VCAM_VERT_PERS_REAR_VIEW"; }
      case VCAM_VERT_REAR_LEFT_VIEW: { return "VCAM_VERT_REAR_LEFT_VIEW"; }
      case VCAM_VERT_REAR_RIGHT_VIEW: { return "VCAM_VERT_REAR_RIGHT_VIEW"; }
      case VCAM_VERT_FRONT_LEFT_VIEW: { return "VCAM_VERT_FRONT_LEFT_VIEW"; }
      case VCAM_VERT_FRONT_RIGHT_VIEW: { return "VCAM_VERT_FRONT_RIGHT_VIEW"; }
      case VCAM_VERT_BONNET_VIEW: { return "VCAM_VERT_BONNET_VIEW"; }
      case VCAM_HORI_PARKING_PLAN_VIEW: { return "VCAM_HORI_PARKING_PLAN_VIEW"; }
      case VCAM_VERT_PARKING_PLAN_VIEW: { return "VCAM_VERT_PARKING_PLAN_VIEW"; }
      case VCAM_VERT_VIRTUAL_PARK_SEARCHING: { return "VCAM_VERT_VIRTUAL_PARK_SEARCHING"; }
      case VCAM_VERT_VIRTUAL_PARK_CONFIRM: { return "VCAM_VERT_VIRTUAL_PARK_CONFIRM"; }
      case VCAM_VERT_VIRTUAL_PARK_GUIDANCE: { return "VCAM_VERT_VIRTUAL_PARK_GUIDANCE"; }
      case VCAM_VERT_VIRTUAL_PARK_COMPLETE_CROSS: { return "VCAM_VERT_VIRTUAL_PARK_COMPLETE_CROSS"; }
      case VCAM_VERT_VIRTUAL_PARK_COMPLETE_PARA: { return "VCAM_VERT_VIRTUAL_PARK_COMPLETE_PARA"; }
      case VCAM_VERT_VIRTUAL_PARK_COMPLETE_DIGN: { return "VCAM_VERT_VIRTUAL_PARK_COMPLETE_DIGN"; }
      case VCAM_BOTH_WHEEL_LEFT_VIEW: { return "VCAM_BOTH_WHEEL_LEFT_VIEW"; }
      case VCAM_BOTH_WHEEL_RIGHT_VIEW: { return "VCAM_BOTH_WHEEL_RIGHT_VIEW"; }
      // case VCAM_DEFAULT_VIEW: { return "VCAM_DEFAULT_VIEW"; }
      case VCAM_PLANETRAY_VIEW: { return "VCAM_PLANETRAY_VIEW"; }
      case VCAM_FRONT_BUMPER_VIEW: { return "VCAM_FRONT_BUMPER_VIEW"; }
      case VCAM_REAR_BUMPER_VIEW: { return "VCAM_REAR_BUMPER_VIEW"; }
      case VCAM_ULTRA_WIDE_SURROUND_VIEW: { return "VCAM_ULTRA_WIDE_SURROUND_VIEW"; }
      case VCAM_VIRTUAL_PARK_OUT_CONFIRM: { return "VCAM_VIRTUAL_PARK_OUT_CONFIRM"; }
      case NUMBER_OF_VIRT_CAMS: { return "NUMBER_OF_VIRT_CAMS"; }
      default: { return "UNKNOWN_VIEW"; }
    }
    // clang-format on
}

} // namespace virtcam
} // namespace cc

