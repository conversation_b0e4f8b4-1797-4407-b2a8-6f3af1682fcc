//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  FixedImpostor.cpp
/// @brief
//=============================================================================



#include "cc/assets/impostor/inc/FixedImpostor.h"
#include "cc/assets/impostor/inc/TransparentVehicleSettings.h"
#include "cc/core/inc/CustomScene.h" //impostor render bin
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/assets/common/inc/Vehicle.h"
#include "cc/core/inc/CustomMechanicalData.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/assets/vehiclemodel/inc/VehicleModel.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"


// #include <osg/BlendFunc>
#include "osg/Camera"
#include "osg/CullFace"
#include "osg/Geode"
#include "osg/Geometry"
#include "osg/MatrixTransform"
#include "osg/PrimitiveSet" //DrawArrays
#include "osg/Texture2D"
#include "osg/Image"
#include "osgDB/WriteFile"
#include "osgUtil/CullVisitor"

#include <array>
#include <cassert>

#define SKIP_INTERIORS 1
#define USE_90_MODEL 1
namespace cc
{
namespace assets
{
namespace impostor
{
using pc::util::logging::g_EngineContext;

pc::util::coding::Item<ImpostorSettings> g_fixedImposterSettings("Impostor");

//!
//! ImpostorDoors: Use this class to remove the interior of the doors so they are not visible in the plan view
//!
ImpostorDoors::ImpostorDoors(cc::core::AssetId f_assetId, pc::core::Framework* f_pFramework)
    : pc::core::Asset(f_assetId) // PRQA S 2323
    , m_frontLeftInnerDoor(nullptr) // PRQA S 2323
    , m_frontRightInnerDoor(nullptr) // PRQA S 2323
    , m_rearLeftInnerDoor(nullptr) // PRQA S 2323
    , m_rearRightInnerDoor(nullptr) // PRQA S 2323
    , m_nodeFL("FL_door_inner_main") // f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL)
    , m_nodeFR("FR_door_inner_main")
    , m_nodeRL("RL_door_inner_main")
    , m_nodeRR("RR_door_inner_main")
    , m_initialized(false) // PRQA S 2323
    , m_pFramework(f_pFramework) // PRQA S 2323
{
}

void ImpostorDoors::traverse( osg::NodeVisitor& f_rNV )
{

#if SKIP_INTERIORS
  pc::core::Asset::traverse(f_rNV);
  return;
#endif

  if ( osg::NodeVisitor::CULL_VISITOR == f_rNV.getVisitorType() ) //Code is unreachable  //PRQA S 2880
  {
      if (!m_initialized)
      {
          init();
      }

      if (!m_initialized) // includes information if nodes were found
      {
          pc::core::Asset::traverse(f_rNV);
          return;
      }

      const vfc::uint32_t l_prevFLNodeMask = m_frontLeftInnerDoor->getNodeMask();
      m_frontLeftInnerDoor->setNodeMask(0u);
      const vfc::uint32_t l_prevFRNodeMask = m_frontRightInnerDoor->getNodeMask();
      m_frontRightInnerDoor->setNodeMask(0u);
      const vfc::uint32_t l_prevRLNodeMask = m_rearLeftInnerDoor->getNodeMask();
      m_rearLeftInnerDoor->setNodeMask(0u);
      const vfc::uint32_t l_prevRRNodeMask = m_rearRightInnerDoor->getNodeMask();
      m_rearRightInnerDoor->setNodeMask(0u);

      pc::core::Asset::traverse(f_rNV);

      m_frontLeftInnerDoor->setNodeMask(l_prevFLNodeMask);
      m_frontRightInnerDoor->setNodeMask(l_prevFRNodeMask);
      m_rearLeftInnerDoor->setNodeMask(l_prevRLNodeMask);
      m_rearRightInnerDoor->setNodeMask(l_prevRRNodeMask);
  }
  else
  {
    pc::core::Asset::traverse(f_rNV); //traverse normally
  }
}

void ImpostorDoors::init()
{
  // find FL interior
  {
    pc::util::osgx::NodeFinder l_nodeFinder(m_nodeFL);
    this->accept(l_nodeFinder);
    m_frontLeftInnerDoor = l_nodeFinder.getFoundNode();
  }
  {
    // find FR interior
    pc::util::osgx::NodeFinder l_nodeFinder(m_nodeFR);
    this->accept(l_nodeFinder);
    m_frontRightInnerDoor = l_nodeFinder.getFoundNode();
  }
  {
    // find RL interior
    pc::util::osgx::NodeFinder l_nodeFinder(m_nodeRL);
    this->accept(l_nodeFinder);
    m_rearLeftInnerDoor = l_nodeFinder.getFoundNode();
  }
  {
    // find RR interior
    pc::util::osgx::NodeFinder l_nodeFinder(m_nodeRR);
    this->accept(l_nodeFinder);
    m_rearRightInnerDoor = l_nodeFinder.getFoundNode();
  }
  if( nullptr != m_frontLeftInnerDoor  &&
      nullptr != m_frontRightInnerDoor &&
      nullptr != m_rearLeftInnerDoor   &&
      nullptr != m_rearRightInnerDoor  )
  {
      m_initialized = true;
  }
}


//!
//! FixedImpostor
//!
FixedImpostor::FixedImpostor(  // PRQA S 6042
  cc::core::AssetId f_assetId,
  osg::Group* f_vehicleGroup,
  pc::vehiclemodel::VehicleModel* f_vehicleModel,
  vfc::int32_t f_textureWidth, vfc::int32_t f_textureHeight,
  vfc::float32_t f_planViewWidthInMeters, vfc::float32_t f_planViewLengthInMeters,
  vfc::float32_t f_vehicleWidthInMeters, vfc::float32_t f_vehicleLengthInMeters,
  const pc::virtcam::VirtualCamera& f_camPos,
  pc::core::Framework* f_pFramework )
  : pc::core::Asset(f_assetId) // PRQA S 4050
  , m_pTexWithDoors{}
  , m_pTexWithoutDoors{}
  , m_pRTTcam{}
  , m_pRTTcamWithoutDoors{}
  , m_pImpostorCamera{}
  , m_pImpostor{}
  , m_pImpostorWithoutDoors{}
  , m_sequenceNumberVehicleModel(0u)
  , m_pVehicleModel(f_vehicleGroup)
  , m_pVehicleModelPC(f_vehicleModel)
  , m_pFramework(f_pFramework)
{
  // Oversample impostor texture by factor 2 (FXAA) --> removed for RAM stability reason
  setupImpostor(    f_textureWidth, f_textureHeight,
                    f_planViewWidthInMeters, f_planViewLengthInMeters,
                    f_vehicleWidthInMeters, f_vehicleLengthInMeters,
                    f_camPos );
}

void FixedImpostor::setupImpostor(  vfc::int32_t f_textureResX, vfc::int32_t f_textureResY,
                                    vfc::float32_t f_planViewWidthInMeters, vfc::float32_t f_planViewLengthInMeters,
                                    vfc::float32_t f_vehicleWidthInMeters, vfc::float32_t f_vehicleLengthInMeters,
                                    const pc::virtcam::VirtualCamera& f_camPos)
{

  assert(f_textureResX >= 0);
  assert(f_textureResY >= 0);

  m_pTexWithDoors = new osg::Texture2D();
  m_pTexWithDoors->setTextureSize(f_textureResX, f_textureResY);
  m_pTexWithDoors->setSourceFormat(GL_RGBA); // PRQA S 3143
  m_pTexWithDoors->setDataVariance(osg::Object::DYNAMIC);
  m_pTexWithDoors->setInternalFormat(GL_RGBA);
  m_pTexWithDoors->setSourceType(GL_UNSIGNED_BYTE); // PRQA S 3143
  m_pTexWithDoors->setResizeNonPowerOfTwoHint(false);
  m_pTexWithDoors->setFilter(osg::Texture2D::MIN_FILTER, osg::Texture2D::LINEAR);
  m_pTexWithDoors->setFilter(osg::Texture2D::MAG_FILTER, osg::Texture2D::LINEAR);
  m_pTexWithDoors->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
  m_pTexWithDoors->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
  m_pTexWithDoors->setBorderColor(osg::Vec4f(.0f, .0f, .0f, .0f));

  m_pRTTcam = new osg::Camera();
  m_pRTTcam->setRenderTargetImplementation(osg::Camera::FRAME_BUFFER_OBJECT);
  m_pRTTcam->attach(osg::Camera::COLOR_BUFFER, m_pTexWithDoors);
  m_pRTTcam->setReferenceFrame(osg::Camera::ABSOLUTE_RF);
  m_pRTTcam->setViewMatrixAsLookAt(f_camPos.m_eye, f_camPos.m_center, f_camPos.m_up);

  // The vehicle is rendered with an ortho camera for JLR
  m_pRTTcam->setProjectionMatrixAsOrtho2D(
    - f_vehicleWidthInMeters /2., f_vehicleWidthInMeters /2.,
    - f_vehicleLengthInMeters /2. , f_vehicleLengthInMeters /2.);

  m_pRTTcam->setRenderOrder(osg::Camera::POST_RENDER);
  m_pRTTcam->setViewport(0,0,f_textureResX, f_textureResY);
  m_pRTTcam->setClearColor(osg::Vec4f(0.f, 0.f, 0.f, 0.f));
  m_pRTTcam->setClearMask(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT); // PRQA S 3143
  m_pRTTcam->setName("Fixed impostor camera");
  m_pRTTcam->addChild(m_pVehicleModel.get());  // PRQA S 3803

  m_pImpostor = new osg::Geode();
  m_pImpostor->setName("Fixed Vehicle impostor");
  osg::Geometry* const l_quad = osg::createTexturedQuadGeometry(
    osg::Vec3f(-f_vehicleWidthInMeters/2.0f,    -f_vehicleLengthInMeters/2.0f, 0.0f), // corner
    osg::Vec3f(f_vehicleWidthInMeters,       0.0f,                       0.0f), // width
    osg::Vec3f(0.0f,                         f_vehicleLengthInMeters,    0.0f)); // height
  l_quad->setUseDisplayList(false);
  l_quad->setUseVertexBufferObjects(true);

  m_pImpostor->addDrawable(l_quad);  // PRQA S 3803

  osg::StateSet* const l_pStateSet = m_pImpostor->getOrCreateStateSet();
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTexAlpha");
  l_basicTexShader.apply(l_pStateSet);  // PRQA S 3803
  l_pStateSet->setTextureAttribute(0u, m_pTexWithDoors);
  l_pStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
  l_pStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
  l_pStateSet->setRenderBinDetails(cc::core::RENDERBIN_ORDER_TV2D_VEHICLE_IMPOSTOR, "RenderBin");
  l_pStateSet->setMode(GL_LIGHTING, osg::StateAttribute::OFF); // PRQA S 3143

  //l_pStateSet->getOrCreateUniform("gbc", osg::Uniform::FLOAT)->set( 1.f ); // do not set here since it will be set on the parent
  //l_pStateSet->getOrCreateUniform("gbc", osg::Uniform::FLOAT)->setUpdateCallback( new GbcUpdateCallback(m_pFramework) );


  // Second RTT for vehicle without doors
  {
  m_pTexWithoutDoors = new osg::Texture2D();
  m_pTexWithoutDoors->setTextureSize(f_textureResX, f_textureResY);
  m_pTexWithoutDoors->setSourceFormat(GL_RGBA); // PRQA S 3143
  m_pTexWithoutDoors->setDataVariance(osg::Object::DYNAMIC);
  m_pTexWithoutDoors->setInternalFormat(GL_RGBA);
  m_pTexWithoutDoors->setSourceType(GL_UNSIGNED_BYTE); // PRQA S 3143
  m_pTexWithoutDoors->setResizeNonPowerOfTwoHint(false);
  m_pTexWithoutDoors->setFilter(osg::Texture2D::MIN_FILTER, osg::Texture2D::LINEAR);
  m_pTexWithoutDoors->setFilter(osg::Texture2D::MAG_FILTER, osg::Texture2D::LINEAR);
  m_pTexWithoutDoors->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
  m_pTexWithoutDoors->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
  m_pTexWithoutDoors->setBorderColor(osg::Vec4f(.0f, .0f, .0f, .0f));
  m_pRTTcamWithoutDoors = new osg::Camera();
  m_pRTTcamWithoutDoors->setRenderTargetImplementation(osg::Camera::FRAME_BUFFER_OBJECT);
  m_pRTTcamWithoutDoors->attach(osg::Camera::COLOR_BUFFER, m_pTexWithoutDoors);
  m_pRTTcamWithoutDoors->setReferenceFrame(osg::Camera::ABSOLUTE_RF);
  m_pRTTcamWithoutDoors->setViewMatrixAsLookAt(f_camPos.m_eye, f_camPos.m_center, f_camPos.m_up);
  // The vehicle is rendered with an ortho camera for JLR
  m_pRTTcamWithoutDoors->setProjectionMatrixAsOrtho2D(
    - f_vehicleWidthInMeters /2., f_vehicleWidthInMeters /2.,
    - f_vehicleLengthInMeters /2. , f_vehicleLengthInMeters /2.);
  m_pRTTcamWithoutDoors->setRenderOrder(osg::Camera::POST_RENDER);
  m_pRTTcamWithoutDoors->setViewport(0,0,f_textureResX, f_textureResY);
  m_pRTTcamWithoutDoors->setClearColor(osg::Vec4f(0.f, 0.f, 0.f, 0.f));
  m_pRTTcamWithoutDoors->setClearMask(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT); // PRQA S 3143
  m_pRTTcamWithoutDoors->setName("Fixed impostor camera without doors");
  m_pRTTcamWithoutDoors->addChild(m_pVehicleModel.get());  // PRQA S 3803
  m_pImpostorWithoutDoors = new osg::Geode();
  m_pImpostorWithoutDoors->setName("Fixed Vehicle impostor without doors");
  osg::Geometry* const l_quad1 = osg::createTexturedQuadGeometry(
    osg::Vec3f(-f_vehicleWidthInMeters/2.0f,    -f_vehicleLengthInMeters/2.0f, 0.0f), // corner
    osg::Vec3f(f_vehicleWidthInMeters,       0.0f,                       0.0f), // width
    osg::Vec3f(0.0f,                         f_vehicleLengthInMeters,    0.0f)); // height
  l_quad1->setUseDisplayList(false);
  l_quad1->setUseVertexBufferObjects(true);
  m_pImpostorWithoutDoors->addDrawable(l_quad1);  // PRQA S 3803
  osg::StateSet* const l_pStateSet1 = m_pImpostorWithoutDoors->getOrCreateStateSet();
  pc::core::TextureShaderProgramDescriptor l_basicTexShader1("basicTexAlpha");
  l_basicTexShader1.apply(l_pStateSet1);  // PRQA S 3803
  l_pStateSet1->setTextureAttribute(0u, m_pTexWithoutDoors);
  l_pStateSet1->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
  l_pStateSet1->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
  l_pStateSet1->setRenderBinDetails(cc::core::RENDERBIN_ORDER_TV2D_VEHICLE_IMPOSTOR, "RenderBin");
  l_pStateSet1->setMode(GL_LIGHTING, osg::StateAttribute::OFF); // PRQA S 3143
  l_pStateSet1->getOrCreateUniform("gbc", osg::Uniform::FLOAT)->set( 1.f );  // PRQA S 3803
  // l_pStateSet1->getOrCreateUniform("gbc", osg::Uniform::FLOAT)->setUpdateCallback( new GbcUpdateCallback(m_pFramework) ); // always alpha 1
  }
  // end of second camera

  //! show the impostor quad with ortho projection
  m_pImpostorCamera = new osg::Camera;
  m_pImpostorCamera->setProjectionMatrixAsOrtho2D(
    (-f_planViewWidthInMeters  * 0.5f),
    (f_planViewWidthInMeters   * 0.5f),
    (-f_planViewLengthInMeters * 0.5f),
    (f_planViewLengthInMeters  * 0.5f));
  m_pImpostorCamera->setViewMatrix(osg::Matrix::identity());
  m_pImpostorCamera->setReferenceFrame(osg::Transform::ABSOLUTE_RF);
  m_pImpostorCamera->setRenderOrder(osg::Camera::POST_RENDER);
  m_pImpostorCamera->addChild(m_pImpostor);  // PRQA S 3803
  m_pImpostorCamera->addChild(m_pImpostorWithoutDoors);  // PRQA S 3803
  m_pImpostor->setNodeMask(~0u);
  m_pImpostorWithoutDoors->setNodeMask(0u);


  //add the vehicle (under rtt cam) to the scene so that the vehicle update callback (for async load) will work
  //we also need to add the RTTcam so it will inherit the parent camera view matrix
  osg::Group::addChild(m_pRTTcam);  // PRQA S 3803
  osg::Group::addChild(m_pRTTcamWithoutDoors);  // PRQA S 3803
  //we also need the impostor quad here
  osg::Group::addChild(m_pImpostorCamera);  // PRQA S 3803
}



// plan: during cull callback, do not render to texture if car asset did not change (based on number of children nodes)
// then always show the impostor
void FixedImpostor::traverse( osg::NodeVisitor& f_rNV )
{
  if ( osg::NodeVisitor::CULL_VISITOR == f_rNV.getVisitorType() )
  {
    if (m_pVehicleModelPC->getModifiedCount() != m_sequenceNumberVehicleModel)
    {
      m_sequenceNumberVehicleModel = m_pVehicleModelPC->getModifiedCount();
      // pf code. #code looks fine
      XLOG_INFO( g_EngineContext, "Rendering fixed impostor (vehicle change detected)");//PRQA S 4060

      // add shallow copy of the vehicle model - avoid animations to avoid e.g. rendering the impostor while doors are open
      m_pRTTcam->addChild(m_pVehicleModelPC->getSteadyStateProxy());  // PRQA S 3803
      m_pRTTcamWithoutDoors->addChild(m_pVehicleModelPC->getSteadyStateProxy());  // PRQA S 3803

      // Hide wheels
      constexpr vfc::uint32_t l_noWheelCullSetting = ~(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FLOOR_PLANE_MASK) | static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::CULL_SETTING_WHEELS));
      m_pRTTcam->setCullMask(l_noWheelCullSetting);

      // Render with doors
      m_pRTTcam->accept(f_rNV);

      // Render without doors
      constexpr vfc::uint32_t l_noWheelNoDoorCullSetting = l_noWheelCullSetting & ~static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::CULL_SETTING_DOORS);
      m_pRTTcamWithoutDoors->setCullMask(l_noWheelNoDoorCullSetting);
      m_pRTTcamWithoutDoors->accept(f_rNV); // no doors

    }
    else
    {
      m_pImpostorCamera->accept(f_rNV);
    }
  }
  else
  {
    pc::core::Asset::traverse(f_rNV); //traverse normally
  }
}

// This function is used to remove the doors for rendering the impostor at startup.
// after the impostor without doors is rendered, the doors nodes are re-activated.
bool FixedImpostor::setDoorMasks(vfc::uint32_t f_mask)  // PRQA S 6041  //PRQA S 1724
{
    static osg::Node* sl_frontLeftDoor  = nullptr;
    static osg::Node* sl_frontRightDoor = nullptr;
    static osg::Node* sl_rearLeftDoor   = nullptr;
    static osg::Node* sl_rearRightDoor  = nullptr;
    static osg::Node* sl_trunkDoor      = nullptr;
    static osg::Node* sl_hoodDoor       = nullptr;
    static bool sl_doorsFound = false;

    if (!sl_doorsFound)
    {
      if (nullptr == sl_frontLeftDoor)  {sl_frontLeftDoor  = getComponent("FrontLeftDoor");}
      if (nullptr == sl_frontRightDoor) {sl_frontRightDoor = getComponent("FrontRightDoor");}
      if (nullptr == sl_rearLeftDoor)   {sl_rearLeftDoor   = getComponent("RearLeftDoor",  true);}
      if (nullptr == sl_rearRightDoor)  {sl_rearRightDoor  = getComponent("RearRightDoor", true);}
      if (nullptr == sl_trunkDoor)      {sl_trunkDoor      = getComponent("Trunk");}
      if (nullptr == sl_hoodDoor)       {sl_hoodDoor       = getComponent("Hood");}

      sl_doorsFound = true;
    }

    if (nullptr != sl_frontLeftDoor)
    {
      sl_frontLeftDoor->setNodeMask(f_mask);
    }

    if (nullptr != sl_frontRightDoor)
    {
      sl_frontRightDoor->setNodeMask(f_mask);
    }

    if (nullptr != sl_rearLeftDoor)
    {
      sl_rearLeftDoor->setNodeMask(f_mask);
    }

    if (nullptr != sl_rearRightDoor)
    {
      sl_rearRightDoor->setNodeMask(f_mask);
    }

    if (nullptr != sl_trunkDoor)
    {
      sl_trunkDoor->setNodeMask(f_mask);
    }

    if (nullptr != sl_hoodDoor)
    {
      sl_hoodDoor->setNodeMask(f_mask);
    }

    return true;
}


// This function is used to remove the Wheels for rendering the impostor at startup.
// after the impostor without Wheels is rendered, the Wheels nodes are re-activated.
bool FixedImpostor::setWheelMasks(vfc::uint32_t f_mask)  //PRQA S 1724
{
    static osg::Node* sl_frontLeftWheel = nullptr;
    static osg::Node* sl_frontRightWheel = nullptr;
    static osg::Node* sl_rearLeftWheel = nullptr;
    static osg::Node* sl_rearRightWheel = nullptr;
    static bool sl_wheelsFound = false;

    if (!sl_wheelsFound)
    {
      {
        pc::util::osgx::NodeFinder l_nodeFinder("FrontLeftWheel");
        m_pVehicleModel->accept(l_nodeFinder);
        if (nullptr != l_nodeFinder.getFoundNode())
        {
            sl_frontLeftWheel = l_nodeFinder.getFoundNode();
        }
      }
      {
        pc::util::osgx::NodeFinder l_nodeFinder("FrontRightWheel");
        m_pVehicleModel->accept(l_nodeFinder);
        if (nullptr != l_nodeFinder.getFoundNode())
        {
            sl_frontRightWheel = l_nodeFinder.getFoundNode();
        }
      }
      {
        pc::util::osgx::NodeFinder l_nodeFinder("RearLeftWheel");
        m_pVehicleModel->accept(l_nodeFinder);
        if (nullptr != l_nodeFinder.getFoundNode())
        {
            sl_rearLeftWheel = l_nodeFinder.getFoundNode();
        }
      }
      {
        pc::util::osgx::NodeFinder l_nodeFinder("RearRightWheel");
        m_pVehicleModel->accept(l_nodeFinder);
        if (nullptr != l_nodeFinder.getFoundNode())
        {
            sl_rearRightWheel = l_nodeFinder.getFoundNode();
        }
      }

      if (nullptr != sl_frontLeftWheel && nullptr!= sl_frontRightWheel && nullptr!= sl_rearLeftWheel && nullptr != sl_rearRightWheel )
      {
        sl_wheelsFound = true;
      }
      else
      {
          return false;
      }
    }

    sl_frontLeftWheel->setNodeMask(f_mask);
    sl_frontRightWheel->setNodeMask(f_mask);
    sl_rearLeftWheel->setNodeMask(f_mask);
    sl_rearRightWheel->setNodeMask(f_mask);

    return true;
}

osg::Node* FixedImpostor::getComponent(const std::string& f_componentName, bool f_suppressErrorMessage)
{
  pc::util::osgx::NodeFinder l_nodeFinder(f_componentName);
  m_pVehicleModel->accept(l_nodeFinder);
  osg::Node* const resultNode = l_nodeFinder.getFoundNode();
  if ((resultNode == nullptr) && (f_suppressErrorMessage))
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_EngineContext, "FixedImpostor::getComponent( " << l_nodeFinder.getNodeName() // PRQA S 4060
                << ") could not be found in vehicle model scene graph"  );//PRQA S 4060
  }

  return resultNode;
}

// This function toggles between both impostor textures - with or without doors.
// It is used when a door is open, so the underlying impostor does not have visible doors.
void FixedImpostor::useTextureWithoutDoors(bool f_val) // PRQA S 4211
{
    if (f_val)
    {
        m_pImpostor->setNodeMask(0u);
        m_pImpostorWithoutDoors->setNodeMask(~0u);
    }
    else
    {
        m_pImpostor->setNodeMask(~0u);
        m_pImpostorWithoutDoors->setNodeMask(0u);
    }
}


///
/// VehicleCullCallback
///
class VehicleCullCallback : public osg::NodeCallback // PRQA S 2119
{
public:
    VehicleCullCallback(pc::vehiclemodel::VehicleModel* f_vehicleModel, const osg::Node::NodeMask f_nodeMask)
        : m_vehicleModel(f_vehicleModel) // PRQA S 2323 // PRQA S 4052
        , m_nodeMask(f_nodeMask) // PRQA S 2323
    {
    }

    void operator () (osg::Node* /*f_node*/, osg::NodeVisitor* f_nv) override
    {
        osgUtil::CullVisitor* const l_cv = dynamic_cast<osgUtil::CullVisitor*> (f_nv); // PRQA S 3077  // PRQA S 3400
        if (l_cv != nullptr)
        {
            cullImplementation(l_cv);
        }
    }

private:
    void cullImplementation(osgUtil::CullVisitor* f_cv) // PRQA S 4211
    {
        if (f_cv == nullptr)
        {
            return;
        }
        const osg::Node::NodeMask l_originalTraversalMask = f_cv->getTraversalMask();
        f_cv->setTraversalMask(m_nodeMask);
        m_vehicleModel->accept(*f_cv);
        f_cv->setTraversalMask(l_originalTraversalMask);
    }
    osg::ref_ptr<pc::vehiclemodel::VehicleModel> m_vehicleModel;
    const osg::Node::NodeMask                    m_nodeMask;
};

osg::Geode* createTextureSamplingQuad(osg::Texture2D* f_texture)
{
  osg::Geometry* const l_quadGeometry = osg::createTexturedQuadGeometry(
    osg::Vec3f(0.0f, 0.0f, 0.0f), // corner
    osg::Vec3f(1.0f, 0.0f, 0.0f), // width
    osg::Vec3f(0.0f, 1.0f, 0.0f)); // height
  l_quadGeometry->setUseDisplayList(false);
  l_quadGeometry->setUseVertexBufferObjects(true);

  osg::Geode* const l_overlayGeode = new osg::Geode;
  l_overlayGeode->addDrawable(l_quadGeometry); // PRQA S 3803
  osg::StateSet* const l_stateSet = l_overlayGeode->getOrCreateStateSet();
  l_stateSet->setTextureAttribute(0u, f_texture);
  return l_overlayGeode;
}

void PostRenderStateSetUpdateCallback::operator() (osg::StateSet* f_stateSet, osg::NodeVisitor* /* f_nv */)
{
  if (nullptr == f_stateSet)
  {
    return;
  }
  const float l_elapsedFrameTime = static_cast<vfc::float32_t>(osg::Timer::instance()->delta_s(m_frameTick, osg::Timer::instance()->tick()));
  m_impostorTransp = g_transpVMData->calculateTransparencyLevel(l_elapsedFrameTime, m_impostorTransp, m_transpLevel);

  m_frameTick = osg::Timer::instance()->tick();

  f_stateSet->getOrCreateUniform("gbc", osg::Uniform::FLOAT)->set(m_impostorTransp); // PRQA S 3803
  f_stateSet->getOrCreateUniform("impostorBrightness", osg::Uniform::FLOAT)->set(1.0f); // PRQA S 3803
}

//!
//! TransparentVehicleImpostor
//!
TransparentVehicleImpostor::TransparentVehicleImpostor(
    cc::core::CustomFramework* f_customFramework,
    cc::core::AssetId f_assetId,
    pc::vehiclemodel::VehicleModel* f_vehicleModel,
    const osg::Vec2us& f_viewportSize,  // alternatively view port size be fetched using CustomFramework?
    bool f_isOnPlanView)
  : pc::core::Asset(f_assetId) // PRQA S 4050
  , m_customFramework(f_customFramework)
  , m_vehicleModel(f_vehicleModel)
  , m_pImpostorWithoutWheels{}
  , m_impostorSnapShotCamNode{}
  , m_impostorPostRenderCamNode{}
  , m_lastEye(0.f, 0.f, 0.f)
  , m_isTransparent(false)
  , m_isOnPlanView(f_isOnPlanView)
  , m_postRenderSSUpdateCallback{}
  , m_prevBaseplateState(cc::daddy::SolidBasePlateState::deactivated)
  , m_SVSVehTransSts(2u)
  , m_1stOff2On(1u)
  , m_stateSets{}
  , m_traversalMasks{}
  , m_lookAtDir(STRAIGHT)
{
  m_impostorSnapShotCamNode = new pc::util::osgx::RenderToTextureCamera(static_cast<vfc::int32_t>(f_viewportSize.x()), static_cast<vfc::int32_t>(f_viewportSize.y()));
  m_impostorSnapShotCamNode->setReferenceFrame(osg::Transform::RELATIVE_RF);
  m_impostorSnapShotCamNode->setProjectionMatrix(osg::Matrix::identity());
  constexpr osg::Node::NodeMask l_nodeMaskWithoutWheels = ~(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::CULL_SETTING_INTERIOR) |
                                                         static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::CULL_SETTING_DOORS_INTERIOR) |
                                                         static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::CULL_SETTING_WHEELS));
  m_impostorSnapShotCamNode->setCullCallback(new VehicleCullCallback(m_vehicleModel, l_nodeMaskWithoutWheels));

  const vfc::int32_t l_renderBinImpostor = pc::core::g_renderOrder->m_carTransparent;
  m_stateSets[RENDER_PASS_BELOW_IMPOSTOR] = createStateSet(RENDER_PASS_BELOW_IMPOSTOR, l_renderBinImpostor); // PRQA S 3143
  m_stateSets[RENDER_PASS_ABOVE_IMPOSTOR] = createStateSet(RENDER_PASS_ABOVE_IMPOSTOR, l_renderBinImpostor); // PRQA S 3143
  updateTraversalMasks();

  m_impostorSnapShotCamNode->addChild(m_vehicleModel); // PRQA S 3803
  pc::core::Asset::addChild(m_impostorSnapShotCamNode); // PRQA S 3803

  // setup Impostor post render pass
  m_impostorPostRenderCamNode = new osg::Camera;
  m_impostorPostRenderCamNode->setName("ImpostorPostRenderCamera");
  m_impostorPostRenderCamNode->setClearMask(0u);
  m_impostorPostRenderCamNode->setProjectionMatrixAsOrtho2D(0.0f, 1.0f, 0.0f, 1.0f);
  m_impostorPostRenderCamNode->setViewMatrix(osg::Matrix::identity());
  m_impostorPostRenderCamNode->setReferenceFrame(osg::Transform::ABSOLUTE_RF);
  m_impostorPostRenderCamNode->setRenderOrder(osg::Camera::NESTED_RENDER);
  m_impostorPostRenderCamNode->setComputeNearFarMode(osg::Camera::DO_NOT_COMPUTE_NEAR_FAR);
  m_pImpostorWithoutWheels = createTextureSamplingQuad(m_impostorSnapShotCamNode->getTexture());
  m_impostorPostRenderCamNode->addChild(m_pImpostorWithoutWheels); // PRQA S 3803
  osg::StateSet* const l_postRenderStateSet = m_pImpostorWithoutWheels->getOrCreateStateSet();
  m_postRenderSSUpdateCallback = new PostRenderStateSetUpdateCallback(m_customFramework);
  l_postRenderStateSet->setUpdateCallback(m_postRenderSSUpdateCallback);
  pc::core::TextureShaderProgramDescriptor l_impostorBasicTexShader("basicTexAlpha");
  l_impostorBasicTexShader.apply(l_postRenderStateSet); // PRQA S 3803
  l_postRenderStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
  l_postRenderStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
  // The blend function is currently disabled as the transparency animation gets abrupt.
  // osg::BlendFunc* l_impostorBlendFunc = new osg::BlendFunc(GL_SRC_ALPHA, GL_ONE);
  // l_postRenderStateSet->setAttribute(l_impostorBlendFunc);
  l_postRenderStateSet->setRenderBinDetails(l_renderBinImpostor, "RenderBin");
  pc::core::Asset::addChild(m_impostorPostRenderCamNode); // PRQA S 3803
}

osg::StateSet* TransparentVehicleImpostor::createStateSet(RenderPass f_renderPass, vfc::int32_t f_renderBinImpostor)
{
  vfc::int32_t l_renderBin = 0;

  osg::StateSet* const l_stateSet = new osg::StateSet;
  switch (f_renderPass)
  {
    case RENDER_PASS_BELOW_IMPOSTOR:
    {
      l_renderBin = f_renderBinImpostor - 1;
      l_stateSet->setMode( GL_CULL_FACE, static_cast<vfc::uint32_t>(osg::StateAttribute::OFF) | static_cast<vfc::uint32_t>(osg::StateAttribute::OVERRIDE) );  // In order to avoid back face culling in the inner side of the wheels // PRQA S 3143
      break;
    }
    case RENDER_PASS_IMPOSTOR:
    {
      l_renderBin = f_renderBinImpostor;
      break;
    }
    case RENDER_PASS_ABOVE_IMPOSTOR:
    {
      l_renderBin = f_renderBinImpostor + 1;
      break;
    }
    default:
    {
      break;
    }
  }
  l_stateSet->setRenderBinDetails(l_renderBin, "RenderBin");
  return l_stateSet;
}

void TransparentVehicleImpostor::updateTraversalMasks(void)
{
  switch(m_lookAtDir)
  {
    case STRAIGHT:
    {
      m_traversalMasks[RENDER_PASS_BELOW_IMPOSTOR]  = pc::vehiclemodel::VehicleModel::CULL_SETTING_WHEELS; // PRQA S 3143  #code looks fine
      m_traversalMasks[RENDER_PASS_ABOVE_IMPOSTOR]  = 0u; // PRQA S 3143  #code looks fine
    }
    break;
    case RIGHT:
    {
      m_traversalMasks[RENDER_PASS_BELOW_IMPOSTOR]  = pc::vehiclemodel::VehicleModel::CULL_SETTING_WHEELS_LEFT;  // PRQA S 3143  #code looks fine
      m_traversalMasks[RENDER_PASS_ABOVE_IMPOSTOR]  = pc::vehiclemodel::VehicleModel::CULL_SETTING_WHEELS_RIGHT; // PRQA S 3143  #code looks fine
    }
    break;
    case LEFT:
    {
      m_traversalMasks[RENDER_PASS_BELOW_IMPOSTOR]  = pc::vehiclemodel::VehicleModel::CULL_SETTING_WHEELS_RIGHT; // PRQA S 3143  #code looks fine
      m_traversalMasks[RENDER_PASS_ABOVE_IMPOSTOR]  = pc::vehiclemodel::VehicleModel::CULL_SETTING_WHEELS_LEFT;  // PRQA S 3143  #code looks fine
    }
    break;
    default:
    {
      break;
    }
  }
}

TransparentVehicleImpostor::LookAtDirection TransparentVehicleImpostor::determineLookAtDirection(const osg::Vec3f &f_eyePosition)
{
  // in order to determine the side of the vehicle, a rough estimate based on the y value of the current eye position is used
  if(f_eyePosition.y() > pc::vehicle::g_mechanicalData->m_width)
  {
    return LEFT;
  }
  else if(f_eyePosition.y() < -pc::vehicle::g_mechanicalData->m_width)
  {
    return RIGHT;
  }
  else
  {
    return STRAIGHT;
  }
}

bool TransparentVehicleImpostor::updateRequired(const osgUtil::CullVisitor* f_cv)  //PRQA S 1724
{
  if (f_cv == nullptr)
  {
    return false;
  }
  bool l_updateRequired = false;

  static cc::target::common::EThemeTypeHU l_lastThemeType = cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI;
  static vfc::uint8_t l_lastColorIndex = cc::daddy::TIME_GRAY;
  // check if the cameraPosition has changed
  osg::Vec3f l_currEye = f_cv->getEyePoint();
  if(isNotEqual(l_currEye.x(), m_lastEye.x()) || isNotEqual(l_currEye.y(), m_lastEye.y()) || isNotEqual(l_currEye.z(), m_lastEye.z()) )
  {
    m_lookAtDir = determineLookAtDirection(l_currEye);
    updateTraversalMasks();
    m_lastEye = l_currEye;
    l_updateRequired = true;
  }

  // check if any of the door is open
  const pc::daddy::DoorStateDaddy* const l_doorStateDaddy = m_customFramework->m_doorStateReceiver.getData();
  bool l_doorsOpen = false;
  if (nullptr != l_doorStateDaddy)
  {
    l_doorsOpen = (pc::daddy::CARDOORSTATE_OPEN == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_LEFT]) ||
                  (pc::daddy::CARDOORSTATE_OPEN == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_RIGHT]) ||
                  (pc::daddy::CARDOORSTATE_OPEN == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_LEFT]) ||
                  (pc::daddy::CARDOORSTATE_OPEN == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_RIGHT]) ||
                  (pc::daddy::CARDOORSTATE_OPEN == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_TRUNK]) ||
                  (pc::daddy::CARDOORSTATE_OPEN == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_HOOD]);
  }
  const pc::daddy::MirrorStateDaddy* const l_mirrorState = m_customFramework->m_mirrorStateReceiver.getData();
  bool l_mirrorFold = false;
  if (nullptr != l_mirrorState)
  {
      l_mirrorFold =  (pc::daddy::MIRRORSTATE_FLAPPED == l_mirrorState->m_Data[static_cast<vfc::uint32_t>(pc::daddy::SIDEMIRROR_LEFT)]) ||
                      (pc::daddy::MIRRORSTATE_FOLDING == l_mirrorState->m_Data[static_cast<vfc::uint32_t>(pc::daddy::SIDEMIRROR_LEFT)]) ||
                      (pc::daddy::MIRRORSTATE_FLAPPED == l_mirrorState->m_Data[static_cast<vfc::uint32_t>(pc::daddy::SIDEMIRROR_RIGHT)])||
                      (pc::daddy::MIRRORSTATE_FOLDING == l_mirrorState->m_Data[static_cast<vfc::uint32_t>(pc::daddy::SIDEMIRROR_RIGHT)]);
  }
  if(l_doorsOpen || l_mirrorFold)
  {
    l_updateRequired = true;
  }

  if(m_customFramework->m_SVSRotateStatusDaddy_Receiver.hasData())
  {
    const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType = m_customFramework->m_SVSRotateStatusDaddy_Receiver.getData();
    if (nullptr != l_themeType)
    {
      const cc::target::common::EThemeTypeHU l_curThemeType = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 3013

      if (l_curThemeType != l_lastThemeType)
      {
          l_lastThemeType = l_curThemeType;
          l_updateRequired = true;
      }
    }
  }

  if(m_customFramework->m_vehColorSts_ReceiverPort.hasData())
  {
    const cc::daddy::ColorIndexDaddy* const l_pColorIndex = m_customFramework->m_vehColorSts_ReceiverPort.getData();
    if (nullptr != l_pColorIndex)
    {
      const vfc::uint8_t  l_curColorIndex = static_cast<vfc::uint8_t >(l_pColorIndex->m_Data);

      if (l_curColorIndex != l_lastColorIndex)
      {
          l_lastColorIndex = l_curColorIndex;
          l_updateRequired = true;
      }
    }
  }

  //check if the floorplate state has changed SysVS_SVS_978
  const cc::daddy::SolidBasePlateStateDaddy* const l_solidBasePlateStateDaddy = m_customFramework->m_solidBaseplateState_ReceiverPort.getData();
  if(nullptr != l_solidBasePlateStateDaddy)
  {
    if(nullptr != l_solidBasePlateStateDaddy)
    {
      if(l_solidBasePlateStateDaddy->m_Data != m_prevBaseplateState)
      {
        m_prevBaseplateState = l_solidBasePlateStateDaddy->m_Data;
        l_updateRequired = true;
      }
    }
  }

  return l_updateRequired;

}

void TransparentVehicleImpostor::traverse(osg::NodeVisitor& f_nv)
{
  if(osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
  {
    osgUtil::CullVisitor* const l_cv = dynamic_cast<osgUtil::CullVisitor*> (&f_nv); // PRQA S 3077  // PRQA S 3400

#if !(DISABLE_ROTATABLE_TRANSPARENT_MODEL)
    m_vehicleModel->accept(*l_cv);
    return;
#else
    const osg::Node::NodeMask l_traversalMask = l_cv->getTraversalMask();

    if (m_customFramework->m_HUvehTransLevelReceiver.isConnected())
    {
      const cc::daddy::SVSVehTransStsDaddy_t* const l_pData = m_customFramework->m_HUvehTransLevelReceiver.getData();
      const vfc::uint8_t l_SVSVehTransSts = l_pData->m_Data;
      m_SVSVehTransSts = l_SVSVehTransSts;
      m_postRenderSSUpdateCallback->setTranspLevel(m_SVSVehTransSts);
    }

    // if transparency is disabled, render vehicle model normally
    if((false == g_transpVMData->m_enableTransparency))
    {
      if (1.0f == m_postRenderSSUpdateCallback->getImpostorTransp())
      {
        m_vehicleModel->accept(*l_cv);
      }
      return;
    }

    if(updateRequired(l_cv) && 1.0f > m_postRenderSSUpdateCallback->getImpostorTransp())
    {
      m_impostorSnapShotCamNode->accept(*l_cv);
    }

    if (0u == m_SVSVehTransSts && 1.0f == m_postRenderSSUpdateCallback->getImpostorTransp()) // normal model
    {
      if (!m_isOnPlanView)
      {
        m_vehicleModel->accept(*l_cv);
      }
      m_isTransparent = false;
    }

    if (0u != m_SVSVehTransSts || 1.0f > m_postRenderSSUpdateCallback->getImpostorTransp())  // half transparent model
    {
      l_cv->setTraversalMask(m_traversalMasks[RENDER_PASS_BELOW_IMPOSTOR]); // PRQA S 3143
      l_cv->pushStateSet(m_stateSets[RENDER_PASS_BELOW_IMPOSTOR].get()); // PRQA S 3143
      m_vehicleModel->accept(*l_cv);
      l_cv->popStateSet();
      l_cv->setTraversalMask(l_traversalMask);

      //! RENDERPASS-2 Impostor
      //! render impostor of the 3D vehicle model
      if(!m_isTransparent)
      {
        //! capture texture the first time the model is not transparent
        m_impostorSnapShotCamNode->accept(*l_cv);
        m_isTransparent = true;
      }
      m_impostorPostRenderCamNode->accept(*l_cv);

      //! RENDERPASS-3 wheels in the direction of camera
      l_cv->setTraversalMask(m_traversalMasks[RENDER_PASS_ABOVE_IMPOSTOR]); // PRQA S 3143
      l_cv->pushStateSet(m_stateSets[RENDER_PASS_ABOVE_IMPOSTOR].get()); // PRQA S 3143
      m_vehicleModel->accept(*l_cv);
      l_cv->popStateSet();
      l_cv->setTraversalMask(l_traversalMask);
    }
#endif
  }
  else
  {
    //! traverse all nodes for other kinds of visitors
    pc::core::Asset::traverse(f_nv);
  }
}


} // namespace impostor
} // namespace assets
} // namespace cc
