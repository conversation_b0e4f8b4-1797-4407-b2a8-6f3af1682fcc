/// @copyright (C) 2025 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "cc/assets/e3parkingoverlay/inc/e3parkingoverlay.h"
#include "cc/assets/trajectory/inc/Helper.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

#include "osgDB/ReadFile"
#include "osg/Depth"
#include <osg/Array>
using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{

constexpr vfc::float32_t HEIGHT    = 0.05f;
constexpr vfc::float32_t PI_RADIAN = 3.1415926f;

class E3ParkingTopviewOverlaySettings : public pc::util::coding::ISerializable
{
public:
    E3ParkingTopviewOverlaySettings()
        : m_startupTime{1.0f}
        , m_arrowRadiusInnerOffset{-0.3f}
        , m_arrowRelativePositionRatio{0.9f}
        , m_arrowHeightWidthRatio{0.4f}
        , m_arrowDistanceRatio{1.5f}
        , m_arrowHeight{0.15f}
        , m_arrowSpeed{8.0f}
        , m_trajRadiusInnerOffset{0.0f}
        , m_trajRadiusOuterOffset{0.5f}
        , m_trajColor{255, 255, 255, 200}
        , m_trajGradientPosition{0.4f, 0.45f, 0.55f, 0.6f}
        , m_arrowfilename(CONCAT_PATH("cc/resources/trajectory/arrow_e3.png"))
    {
    }

    SERIALIZABLE(E3ParkingTopviewOverlaySettings)
    {
        ADD_FLOAT_MEMBER(startupTime);
        ADD_FLOAT_MEMBER(arrowRadiusInnerOffset);
        ADD_FLOAT_MEMBER(arrowRelativePositionRatio);
        ADD_FLOAT_MEMBER(arrowHeightWidthRatio);
        ADD_FLOAT_MEMBER(arrowDistanceRatio);
        ADD_FLOAT_MEMBER(arrowHeight);
        ADD_FLOAT_MEMBER(arrowSpeed);
        ADD_FLOAT_MEMBER(trajRadiusInnerOffset);
        ADD_FLOAT_MEMBER(trajRadiusOuterOffset);
        ADD_MEMBER(osg::Vec4i, trajColor);
        ADD_MEMBER(osg::Vec4f, trajGradientPosition);
        ADD_STRING_MEMBER(arrowfilename);
    }

    vfc::float32_t m_startupTime;
    vfc::float32_t m_arrowRadiusInnerOffset;
    vfc::float32_t m_arrowRelativePositionRatio;
    vfc::float32_t m_arrowHeightWidthRatio;
    vfc::float32_t m_arrowDistanceRatio;
    vfc::float32_t m_arrowHeight;
    vfc::float32_t m_arrowSpeed;
    vfc::float32_t m_trajRadiusInnerOffset;
    vfc::float32_t m_trajRadiusOuterOffset;
    osg::Vec4i     m_trajColor;
    osg::Vec4f     m_trajGradientPosition;
    std::string    m_arrowfilename;
};

static pc::util::coding::Item<E3ParkingTopviewOverlaySettings> g_e3ParkingTopviewSettings("E3ParkingTopviewOverlaySettings");

// Type 1: center rotation
// Type 2: rear center rotation
// Type 3: left front anti-clockwise, right front clockwise
// Type 4: left front clockwise, right front anti-clockwise
// Type 5: left rear anti-clockwise, right rear clockwise
// Type 6: left rear clockwise, right rear anti-clockwise
class E3ParkingOffsetSettings : public pc::util::coding::ISerializable
{
public:
    E3ParkingOffsetSettings()
        : m_trajFrontPositionOffsetX_Type1{0.1f}
        , m_trajFrontPositionOffsetY_Type1{0.1f}
        , m_trajRearPositionOffsetX_Type1{0.1f}
        , m_trajRearPositionOffsetY_Type1{0.1f}
        , m_trajFrontPositionOffsetX_Type2{0.1f}
        , m_trajFrontPositionOffsetY_Type2{0.1f}
        , m_trajRearPositionOffsetX_Type2{0.1f}
        , m_trajRearPositionOffsetY_Type2{0.1f}
        , m_trajFrontPositionOffsetX_Type3{0.1f}
        , m_trajFrontPositionOffsetY_Type3{0.1f}
        , m_trajRearPositionOffsetX_Type3{0.1f}
        , m_trajRearPositionOffsetY_Type3{0.1f}
        , m_trajFrontPositionOffsetX_Type4{0.1f}
        , m_trajFrontPositionOffsetY_Type4{0.1f}
        , m_trajRearPositionOffsetX_Type4{0.1f}
        , m_trajRearPositionOffsetY_Type4{0.1f}
        , m_trajFrontPositionOffsetX_Type5{0.1f}
        , m_trajFrontPositionOffsetY_Type5{0.1f}
        , m_trajRearPositionOffsetX_Type5{0.1f}
        , m_trajRearPositionOffsetY_Type5{0.1f}
        , m_trajFrontPositionOffsetX_Type6{0.1f}
        , m_trajFrontPositionOffsetY_Type6{0.1f}
        , m_trajRearPositionOffsetX_Type6{0.1f}
        , m_trajRearPositionOffsetY_Type6{0.1f}
        , m_trajFrontPositionOffsetX_Type7{0.1f}
        , m_trajRearPositionOffsetX_Type7{-0.1f}
        , m_trajPositionOffsetY_Type7{0.1f}
        , m_trajTotalLength_Type7{2.5f}
    {
    }

    SERIALIZABLE(E3ParkingOffsetSettings)
    {
        ADD_FLOAT_MEMBER(trajFrontPositionOffsetX_Type1);
        ADD_FLOAT_MEMBER(trajFrontPositionOffsetY_Type1);
        ADD_FLOAT_MEMBER(trajRearPositionOffsetX_Type1);
        ADD_FLOAT_MEMBER(trajRearPositionOffsetY_Type1);
        ADD_FLOAT_MEMBER(trajFrontPositionOffsetX_Type2);
        ADD_FLOAT_MEMBER(trajFrontPositionOffsetY_Type2);
        ADD_FLOAT_MEMBER(trajRearPositionOffsetX_Type2);
        ADD_FLOAT_MEMBER(trajRearPositionOffsetY_Type2);
        ADD_FLOAT_MEMBER(trajFrontPositionOffsetX_Type3);
        ADD_FLOAT_MEMBER(trajFrontPositionOffsetY_Type3);
        ADD_FLOAT_MEMBER(trajRearPositionOffsetX_Type3);
        ADD_FLOAT_MEMBER(trajRearPositionOffsetY_Type3);
        ADD_FLOAT_MEMBER(trajFrontPositionOffsetX_Type4);
        ADD_FLOAT_MEMBER(trajFrontPositionOffsetY_Type4);
        ADD_FLOAT_MEMBER(trajRearPositionOffsetX_Type4);
        ADD_FLOAT_MEMBER(trajRearPositionOffsetY_Type4);
        ADD_FLOAT_MEMBER(trajFrontPositionOffsetX_Type5);
        ADD_FLOAT_MEMBER(trajFrontPositionOffsetY_Type5);
        ADD_FLOAT_MEMBER(trajRearPositionOffsetX_Type5);
        ADD_FLOAT_MEMBER(trajRearPositionOffsetY_Type5);
        ADD_FLOAT_MEMBER(trajFrontPositionOffsetX_Type6);
        ADD_FLOAT_MEMBER(trajFrontPositionOffsetY_Type6);
        ADD_FLOAT_MEMBER(trajRearPositionOffsetX_Type6);
        ADD_FLOAT_MEMBER(trajRearPositionOffsetY_Type6);
        ADD_FLOAT_MEMBER(trajFrontPositionOffsetX_Type7);
        ADD_FLOAT_MEMBER(trajRearPositionOffsetX_Type7);
        ADD_FLOAT_MEMBER(trajPositionOffsetY_Type7);
        ADD_FLOAT_MEMBER(trajTotalLength_Type7);
    }

    vfc::float32_t m_trajFrontPositionOffsetX_Type1;
    vfc::float32_t m_trajFrontPositionOffsetY_Type1;
    vfc::float32_t m_trajRearPositionOffsetX_Type1;
    vfc::float32_t m_trajRearPositionOffsetY_Type1;
    vfc::float32_t m_trajFrontPositionOffsetX_Type2;
    vfc::float32_t m_trajFrontPositionOffsetY_Type2;
    vfc::float32_t m_trajRearPositionOffsetX_Type2;
    vfc::float32_t m_trajRearPositionOffsetY_Type2;
    vfc::float32_t m_trajFrontPositionOffsetX_Type3;
    vfc::float32_t m_trajFrontPositionOffsetY_Type3;
    vfc::float32_t m_trajRearPositionOffsetX_Type3;
    vfc::float32_t m_trajRearPositionOffsetY_Type3;
    vfc::float32_t m_trajFrontPositionOffsetX_Type4;
    vfc::float32_t m_trajFrontPositionOffsetY_Type4;
    vfc::float32_t m_trajRearPositionOffsetX_Type4;
    vfc::float32_t m_trajRearPositionOffsetY_Type4;
    vfc::float32_t m_trajFrontPositionOffsetX_Type5;
    vfc::float32_t m_trajFrontPositionOffsetY_Type5;
    vfc::float32_t m_trajRearPositionOffsetX_Type5;
    vfc::float32_t m_trajRearPositionOffsetY_Type5;
    vfc::float32_t m_trajFrontPositionOffsetX_Type6;
    vfc::float32_t m_trajFrontPositionOffsetY_Type6;
    vfc::float32_t m_trajRearPositionOffsetX_Type6;
    vfc::float32_t m_trajRearPositionOffsetY_Type6;
    vfc::float32_t m_trajFrontPositionOffsetX_Type7;
    vfc::float32_t m_trajRearPositionOffsetX_Type7;
    vfc::float32_t m_trajPositionOffsetY_Type7;
    vfc::float32_t m_trajTotalLength_Type7;
};

static pc::util::coding::Item<E3ParkingOffsetSettings> g_e3ParkingTopviewOffset("E3ParkingOffsetSettings");

// Type 1: center rotation
// Type 2: rear center rotation
// Type 3: left front anti-clockwise, right front clockwise
// Type 4: left front clockwise, right front anti-clockwise
// Type 5: left rear anti-clockwise, right rear clockwise
// Type 6: left rear clockwise, right rear anti-clockwise
class E3ParkingCenterOffsetSettings : public pc::util::coding::ISerializable
{
public:
    E3ParkingCenterOffsetSettings()
        : m_trajFrontCenterPositionOffsetX_Type1{0.0f}
        , m_trajFrontCenterPositionOffsetY_Type1{0.0f}
        , m_trajRearCenterPositionOffsetX_Type1{0.0f}
        , m_trajRearCenterPositionOffsetY_Type1{0.0f}
        , m_trajFrontCenterPositionOffsetX_Type2{0.0f}
        , m_trajFrontCenterPositionOffsetY_Type2{0.0f}
        , m_trajRearCenterPositionOffsetX_Type2{0.0f}
        , m_trajRearCenterPositionOffsetY_Type2{0.0f}
        , m_trajFrontCenterPositionOffsetX_Type3{0.0f}
        , m_trajFrontCenterPositionOffsetY_Type3{0.0f}
        , m_trajRearCenterPositionOffsetX_Type3{0.0f}
        , m_trajRearCenterPositionOffsetY_Type3{0.0f}
        , m_trajFrontCenterPositionOffsetX_Type4{0.0f}
        , m_trajFrontCenterPositionOffsetY_Type4{0.0f}
        , m_trajRearCenterPositionOffsetX_Type4{0.0f}
        , m_trajRearCenterPositionOffsetY_Type4{0.0f}
        , m_trajFrontCenterPositionOffsetX_Type5{0.0f}
        , m_trajFrontCenterPositionOffsetY_Type5{0.0f}
        , m_trajRearCenterPositionOffsetX_Type5{0.0f}
        , m_trajRearCenterPositionOffsetY_Type5{0.0f}
        , m_trajFrontCenterPositionOffsetX_Type6{0.0f}
        , m_trajFrontCenterPositionOffsetY_Type6{0.0f}
        , m_trajRearCenterPositionOffsetX_Type6{0.0f}
        , m_trajRearCenterPositionOffsetY_Type6{0.0f}
        , m_trajFrontCenterPositionOffsetX_Type7{0.0f}
        , m_trajFrontCenterPositionOffsetY_Type7{0.0f}
        , m_trajRearCenterPositionOffsetX_Type7{0.0f}
        , m_trajRearCenterPositionOffsetY_Type7{0.0f}
    {
    }

    SERIALIZABLE(E3ParkingCenterOffsetSettings)
    {
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetX_Type1);
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetY_Type1);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetX_Type1);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetY_Type1);
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetX_Type2);
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetY_Type2);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetX_Type2);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetY_Type2);
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetX_Type3);
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetY_Type3);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetX_Type3);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetY_Type3);
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetX_Type4);
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetY_Type4);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetX_Type4);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetY_Type4);
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetX_Type5);
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetY_Type5);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetX_Type5);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetY_Type5);
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetX_Type6);
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetY_Type6);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetX_Type6);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetY_Type6);
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetX_Type7);
        ADD_FLOAT_MEMBER(trajFrontCenterPositionOffsetY_Type7);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetX_Type7);
        ADD_FLOAT_MEMBER(trajRearCenterPositionOffsetY_Type7);
    }
    vfc::float32_t m_trajFrontCenterPositionOffsetX_Type1;
    vfc::float32_t m_trajFrontCenterPositionOffsetY_Type1;
    vfc::float32_t m_trajRearCenterPositionOffsetX_Type1;
    vfc::float32_t m_trajRearCenterPositionOffsetY_Type1;
    vfc::float32_t m_trajFrontCenterPositionOffsetX_Type2;
    vfc::float32_t m_trajFrontCenterPositionOffsetY_Type2;
    vfc::float32_t m_trajRearCenterPositionOffsetX_Type2;
    vfc::float32_t m_trajRearCenterPositionOffsetY_Type2;
    vfc::float32_t m_trajFrontCenterPositionOffsetX_Type3;
    vfc::float32_t m_trajFrontCenterPositionOffsetY_Type3;
    vfc::float32_t m_trajRearCenterPositionOffsetX_Type3;
    vfc::float32_t m_trajRearCenterPositionOffsetY_Type3;
    vfc::float32_t m_trajFrontCenterPositionOffsetX_Type4;
    vfc::float32_t m_trajFrontCenterPositionOffsetY_Type4;
    vfc::float32_t m_trajRearCenterPositionOffsetX_Type4;
    vfc::float32_t m_trajRearCenterPositionOffsetY_Type4;
    vfc::float32_t m_trajFrontCenterPositionOffsetX_Type5;
    vfc::float32_t m_trajFrontCenterPositionOffsetY_Type5;
    vfc::float32_t m_trajRearCenterPositionOffsetX_Type5;
    vfc::float32_t m_trajRearCenterPositionOffsetY_Type5;
    vfc::float32_t m_trajFrontCenterPositionOffsetX_Type6;
    vfc::float32_t m_trajFrontCenterPositionOffsetY_Type6;
    vfc::float32_t m_trajRearCenterPositionOffsetX_Type6;
    vfc::float32_t m_trajRearCenterPositionOffsetY_Type6;
    vfc::float32_t m_trajFrontCenterPositionOffsetX_Type7;
    vfc::float32_t m_trajFrontCenterPositionOffsetY_Type7;
    vfc::float32_t m_trajRearCenterPositionOffsetX_Type7;
    vfc::float32_t m_trajRearCenterPositionOffsetY_Type7;
};

static pc::util::coding::Item<E3ParkingCenterOffsetSettings> g_e3ParkingTopviewCenterOffset("E3ParkingCenterOffsetSettings");

class E3ParkingRadianSettings : public pc::util::coding::ISerializable
{
public:
    E3ParkingRadianSettings()
        : m_radianFront_Type1{PI_RADIAN / 2.0f}
        , m_radianRear_Type1{PI_RADIAN / 2.0f}
        , m_radianFront_Type2{PI_RADIAN / 2.0f}
        , m_radianRear_Type2{PI_RADIAN / 2.0f}
        , m_radianFront_Type3{PI_RADIAN / 2.0f}
        , m_radianRear_Type3{PI_RADIAN / 2.0f}
        , m_radianFront_Type4{PI_RADIAN / 2.0f}
        , m_radianRear_Type4{PI_RADIAN / 2.0f}
        , m_radianFront_Type5{PI_RADIAN / 2.0f}
        , m_radianRear_Type5{PI_RADIAN / 2.0f}
        , m_radianFront_Type6{PI_RADIAN / 2.0f}
        , m_radianRear_Type6{PI_RADIAN / 2.0f}
    {
    }

    SERIALIZABLE(E3ParkingRadianSettings)
    {
        ADD_FLOAT_MEMBER(radianFront_Type1);
        ADD_FLOAT_MEMBER(radianRear_Type1);
        ADD_FLOAT_MEMBER(radianFront_Type2);
        ADD_FLOAT_MEMBER(radianRear_Type2);
        ADD_FLOAT_MEMBER(radianFront_Type3);
        ADD_FLOAT_MEMBER(radianRear_Type3);
        ADD_FLOAT_MEMBER(radianFront_Type4);
        ADD_FLOAT_MEMBER(radianRear_Type4);
        ADD_FLOAT_MEMBER(radianFront_Type5);
        ADD_FLOAT_MEMBER(radianRear_Type5);
        ADD_FLOAT_MEMBER(radianFront_Type6);
        ADD_FLOAT_MEMBER(radianRear_Type6);
    }

    vfc::float32_t m_radianFront_Type1;
    vfc::float32_t m_radianRear_Type1;
    vfc::float32_t m_radianFront_Type2;
    vfc::float32_t m_radianRear_Type2;
    vfc::float32_t m_radianFront_Type3;
    vfc::float32_t m_radianRear_Type3;
    vfc::float32_t m_radianFront_Type4;
    vfc::float32_t m_radianRear_Type4;
    vfc::float32_t m_radianFront_Type5;
    vfc::float32_t m_radianRear_Type5;
    vfc::float32_t m_radianFront_Type6;
    vfc::float32_t m_radianRear_Type6;
};

static pc::util::coding::Item<E3ParkingRadianSettings> g_e3ParkingTopviewRadian("E3ParkingRadianSettings");

namespace
{
/// @brief helper function for calculating angle of two points
vfc::float32_t angleBetweenPoints(const osg::Vec2f& a, const osg::Vec2f& b)
{
    return std::atan2(b.y() - a.y(), b.x() - a.x());
}

/// @brief helper function for calculating absolute distance between two points
vfc::float32_t distanceBetweenPoints(const osg::Vec2f& a, const osg::Vec2f& b)
{
    const vfc::float32_t dx = b.x() - a.x();
    const vfc::float32_t dy = b.y() - a.y();
    return std::sqrt(dx * dx + dy * dy);
}

/// @brief helper function for calculating two points on a tangent line given center, radius and angle
/// the two points will be of on the two sides of the tangent point with equal distance
void calculateTangentEndpoints(
    const osg::Vec2f& center,
    const vfc::float32_t radius,
    const vfc::float32_t angle,
    const vfc::float32_t distance,
    osg::Vec3& tangentEnd1,
    osg::Vec3& tangentEnd2)
{
    const vfc::float32_t x = center.x() + radius * cos(angle);
    const vfc::float32_t y = center.y() + radius * sin(angle);

    const osg::Vec3 pointOnCircle = osg::Vec3(x, y, HEIGHT);
    // Calculate the direction of the radius
    osg::Vec3 radiusDir = pointOnCircle - osg::Vec3(center.x(), center.y(), HEIGHT);
    radiusDir.normalize();

    // Calculate the tangent direction (perpendicular to the radius)
    const osg::Vec3 tangentDir(-radiusDir.y(), radiusDir.x(), 0.0f);

    // Calculate the endpoints of the tangent line
    tangentEnd1 = pointOnCircle + tangentDir * distance;
    tangentEnd2 = pointOnCircle - tangentDir * distance;
}

/// @brief determins the center point from which the circular geometries shall be calculated
osg::Vec2f getRotationCenterForTopViewOverlay(const cc::target::common::ERotationDirection f_direction, const ETopViewTrajPosType f_overlayPosition)
{
    using namespace cc::target::common;
    switch (f_direction)
    {
    case ERotationDirection::REAR_AXLE_CENTER_CLOCKWISE:
    case ERotationDirection::REAR_AXLE_CENTER_ANTICLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return osg::Vec2f{g_e3ParkingTopviewCenterOffset->m_trajFrontCenterPositionOffsetX_Type2, g_e3ParkingTopviewCenterOffset->m_trajFrontCenterPositionOffsetY_Type2};
        }
        else
        {
            return osg::Vec2f{g_e3ParkingTopviewCenterOffset->m_trajRearCenterPositionOffsetX_Type2, g_e3ParkingTopviewCenterOffset->m_trajRearCenterPositionOffsetY_Type2};
        }
    }
    case ERotationDirection::LEFT_FRONT_WHEEL_CLOCKWISE:
    case ERotationDirection::LEFT_FRONT_WHEEL_ANTICLOCKWISE:
    {
        const vfc::float32_t wheelbase  = pc::vehicle::g_mechanicalData->m_wheelbase;
        const vfc::float32_t trackFront = pc::vehicle::g_mechanicalData->m_trackFront;
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return osg::Vec2f{wheelbase + g_e3ParkingTopviewCenterOffset->m_trajFrontCenterPositionOffsetX_Type3, trackFront / 2.f + g_e3ParkingTopviewCenterOffset->m_trajFrontCenterPositionOffsetY_Type3};
        }
        else
        {
            return osg::Vec2f{wheelbase + g_e3ParkingTopviewCenterOffset->m_trajRearCenterPositionOffsetX_Type3, trackFront / 2.f + g_e3ParkingTopviewCenterOffset->m_trajRearCenterPositionOffsetY_Type3};
        }
    }
    case ERotationDirection::RIGHT_FRONT_WHEEL_CLOCKWISE:
    case ERotationDirection::RIGHT_FRONT_WHEEL_ANTICLOCKWISE:
    {
        const vfc::float32_t wheelbase  = pc::vehicle::g_mechanicalData->m_wheelbase;
        const vfc::float32_t trackFront = pc::vehicle::g_mechanicalData->m_trackFront;
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return osg::Vec2f{wheelbase + g_e3ParkingTopviewCenterOffset->m_trajFrontCenterPositionOffsetX_Type4, -trackFront / 2.f + g_e3ParkingTopviewCenterOffset->m_trajFrontCenterPositionOffsetY_Type4};
        }
        else
        {
            return osg::Vec2f{wheelbase + g_e3ParkingTopviewCenterOffset->m_trajRearCenterPositionOffsetX_Type4, -trackFront / 2.f + g_e3ParkingTopviewCenterOffset->m_trajRearCenterPositionOffsetY_Type4};
        }
    }
    case ERotationDirection::LEFT_REAR_WHEEL_ANTICLOCKWISE:
    case ERotationDirection::LEFT_REAR_WHEEL_CLOCKWISE:
    {
        const vfc::float32_t trackRear = pc::vehicle::g_mechanicalData->m_trackRear;
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return osg::Vec2f{g_e3ParkingTopviewCenterOffset->m_trajFrontCenterPositionOffsetX_Type5, trackRear / 2.f + g_e3ParkingTopviewCenterOffset->m_trajFrontCenterPositionOffsetY_Type5};
        }
        else
        {
            return osg::Vec2f{g_e3ParkingTopviewCenterOffset->m_trajRearCenterPositionOffsetX_Type5, trackRear / 2.f + g_e3ParkingTopviewCenterOffset->m_trajRearCenterPositionOffsetY_Type5};
        }
    }
    case ERotationDirection::RIGHT_REAR_WHEEL_ANTICLOCKWISE:
    case ERotationDirection::RIGHT_REAR_WHEEL_CLOCKWISE:
    {
        const vfc::float32_t trackRear = pc::vehicle::g_mechanicalData->m_trackRear;
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return osg::Vec2f{g_e3ParkingTopviewCenterOffset->m_trajFrontCenterPositionOffsetX_Type6, -trackRear / 2.f + g_e3ParkingTopviewCenterOffset->m_trajFrontCenterPositionOffsetY_Type6};
        }
        else
        {
            return osg::Vec2f{g_e3ParkingTopviewCenterOffset->m_trajRearCenterPositionOffsetX_Type6, -trackRear / 2.f + g_e3ParkingTopviewCenterOffset->m_trajRearCenterPositionOffsetY_Type6};
        }
    }
    case ERotationDirection::CENTROID_CLOCKWISE:
    case ERotationDirection::CENTROID_ANTICLOCKWISE:
    default:
    {
        const vfc::float32_t wheelbase = pc::vehicle::g_mechanicalData->m_wheelbase;
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return osg::Vec2f{wheelbase / 2.f + g_e3ParkingTopviewCenterOffset->m_trajFrontCenterPositionOffsetX_Type1, 0.f + g_e3ParkingTopviewCenterOffset->m_trajFrontCenterPositionOffsetY_Type1};
        }
        else
        {
            return osg::Vec2f{wheelbase / 2.f + g_e3ParkingTopviewCenterOffset->m_trajRearCenterPositionOffsetX_Type1, 0.f + g_e3ParkingTopviewCenterOffset->m_trajRearCenterPositionOffsetY_Type1};
        }

    }
    }
}

/// @brief determins the trajectory total radiant length with direction (positive: counterclockwise; negative:
/// clockwise)
vfc::float32_t
getLineRadiant(const cc::target::common::ERotationDirection f_direction, const ETopViewTrajPosType f_overlayPosition)
{
    using namespace cc::target::common;
    switch (f_direction)
    {
    case ERotationDirection::CENTROID_CLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return -g_e3ParkingTopviewRadian->m_radianFront_Type1;
        }
        else
        {
            return -g_e3ParkingTopviewRadian->m_radianRear_Type1;
        }
    }
    case ERotationDirection::REAR_AXLE_CENTER_CLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return -g_e3ParkingTopviewRadian->m_radianFront_Type2;
        }
        else
        {
            return -g_e3ParkingTopviewRadian->m_radianRear_Type2;
        }
    }
    case ERotationDirection::REAR_AXLE_CENTER_ANTICLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return g_e3ParkingTopviewRadian->m_radianFront_Type2;
        }
        else
        {
            return g_e3ParkingTopviewRadian->m_radianRear_Type2;
        }
    }
    case ERotationDirection::LEFT_FRONT_WHEEL_ANTICLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return g_e3ParkingTopviewRadian->m_radianFront_Type3;
        }
        else
        {
            return g_e3ParkingTopviewRadian->m_radianRear_Type3;
        }
    }
    case ERotationDirection::RIGHT_FRONT_WHEEL_CLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return -g_e3ParkingTopviewRadian->m_radianFront_Type3;
        }
        else
        {
            return -g_e3ParkingTopviewRadian->m_radianRear_Type3;
        }
    }
    case ERotationDirection::LEFT_FRONT_WHEEL_CLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return -g_e3ParkingTopviewRadian->m_radianFront_Type4;
        }
        else
        {
            return -g_e3ParkingTopviewRadian->m_radianRear_Type4;
        }
    }
    case ERotationDirection::RIGHT_FRONT_WHEEL_ANTICLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return g_e3ParkingTopviewRadian->m_radianFront_Type4;
        }
        else
        {
            return g_e3ParkingTopviewRadian->m_radianRear_Type4;
        }
    }
    case ERotationDirection::LEFT_REAR_WHEEL_ANTICLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return g_e3ParkingTopviewRadian->m_radianFront_Type5;
        }
        else
        {
            return g_e3ParkingTopviewRadian->m_radianRear_Type5;
        }
    }
    case ERotationDirection::RIGHT_REAR_WHEEL_CLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return -g_e3ParkingTopviewRadian->m_radianFront_Type5;
        }
        else
        {
            return -g_e3ParkingTopviewRadian->m_radianRear_Type5;
        }
    }
    case ERotationDirection::LEFT_REAR_WHEEL_CLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return -g_e3ParkingTopviewRadian->m_radianFront_Type6;
        }
        else
        {
            return -g_e3ParkingTopviewRadian->m_radianRear_Type6;
        }
    }
    case ERotationDirection::RIGHT_REAR_WHEEL_ANTICLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return g_e3ParkingTopviewRadian->m_radianFront_Type6;
        }
        else
        {
            return g_e3ParkingTopviewRadian->m_radianRear_Type6;
        }
    }
    case ERotationDirection::CENTROID_ANTICLOCKWISE:
    default:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            return g_e3ParkingTopviewRadian->m_radianFront_Type1;
        }
        else
        {
            return g_e3ParkingTopviewRadian->m_radianRear_Type1;
        }
    }
    }
}

/// @brief determins the starting point of the overlays
osg::Vec2f getStartingPointTopViewOverlay(
    const cc::target::common::ERotationDirection f_direction,
    const ETopViewTrajPosType                    f_overlayPosition)
{
    using namespace cc::target::common;
    vfc::float32_t frontOffsetX = 0.f;
    vfc::float32_t frontOffsetY = 0.f;
    vfc::float32_t rearOffsetX  = 0.f;
    vfc::float32_t rearOffsetY  = 0.f;

    switch (f_direction)
    {
    case ERotationDirection::REAR_AXLE_CENTER_CLOCKWISE:
    case ERotationDirection::REAR_AXLE_CENTER_ANTICLOCKWISE:
    {
        frontOffsetX = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetX_Type2;
        frontOffsetY = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetY_Type2;
        rearOffsetX  = g_e3ParkingTopviewOffset->m_trajRearPositionOffsetX_Type2;
        rearOffsetY  = g_e3ParkingTopviewOffset->m_trajRearPositionOffsetY_Type2;
        break;
    }
    case ERotationDirection::LEFT_FRONT_WHEEL_ANTICLOCKWISE:
    case ERotationDirection::RIGHT_FRONT_WHEEL_CLOCKWISE:
    {
        frontOffsetX = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetX_Type3;
        frontOffsetY = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetY_Type3;
        rearOffsetX  = g_e3ParkingTopviewOffset->m_trajRearPositionOffsetX_Type3;
        rearOffsetY  = g_e3ParkingTopviewOffset->m_trajRearPositionOffsetY_Type3;
        break;
    }
    case ERotationDirection::LEFT_FRONT_WHEEL_CLOCKWISE:
    case ERotationDirection::RIGHT_FRONT_WHEEL_ANTICLOCKWISE:
    {
        frontOffsetX = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetX_Type4;
        frontOffsetY = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetY_Type4;
        rearOffsetX  = g_e3ParkingTopviewOffset->m_trajRearPositionOffsetX_Type4;
        rearOffsetY  = g_e3ParkingTopviewOffset->m_trajRearPositionOffsetY_Type4;
        break;
    }
    case ERotationDirection::LEFT_REAR_WHEEL_ANTICLOCKWISE:
    case ERotationDirection::RIGHT_REAR_WHEEL_CLOCKWISE:
    {
        frontOffsetX = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetX_Type5;
        frontOffsetY = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetY_Type5;
        rearOffsetX  = g_e3ParkingTopviewOffset->m_trajRearPositionOffsetX_Type5;
        rearOffsetY  = g_e3ParkingTopviewOffset->m_trajRearPositionOffsetY_Type5;
        break;
    }
    case ERotationDirection::LEFT_REAR_WHEEL_CLOCKWISE:
    case ERotationDirection::RIGHT_REAR_WHEEL_ANTICLOCKWISE:
    {
        frontOffsetX = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetX_Type6;
        frontOffsetY = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetY_Type6;
        rearOffsetX  = g_e3ParkingTopviewOffset->m_trajRearPositionOffsetX_Type6;
        rearOffsetY  = g_e3ParkingTopviewOffset->m_trajRearPositionOffsetY_Type6;
        break;
    }
    case ERotationDirection::CENTROID_CLOCKWISE:
    case ERotationDirection::CENTROID_ANTICLOCKWISE:
    default:
    {
        frontOffsetX = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetX_Type1;
        frontOffsetY = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetY_Type1;
        rearOffsetX  = g_e3ParkingTopviewOffset->m_trajRearPositionOffsetX_Type1;
        rearOffsetY  = g_e3ParkingTopviewOffset->m_trajRearPositionOffsetY_Type1;
        break;
    }
    }

    switch (f_direction)
    {
    case ERotationDirection::CENTROID_ANTICLOCKWISE:
    case ERotationDirection::REAR_AXLE_CENTER_ANTICLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            const vfc::float32_t wheelbase    = pc::vehicle::g_mechanicalData->m_wheelbase;
            const vfc::float32_t axleDistance = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront;
            const vfc::float32_t width        = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{wheelbase + axleDistance + frontOffsetX, -width / 2.f - frontOffsetY};
        }
        else
        {
            const vfc::float32_t axleDistance = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear;
            const vfc::float32_t width        = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{-axleDistance - rearOffsetX, width / 2.f + rearOffsetY};
        }
    }
    case ERotationDirection::LEFT_FRONT_WHEEL_CLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            const vfc::float32_t wheelbase         = pc::vehicle::g_mechanicalData->m_wheelbase;
            const vfc::float32_t axleDistanceFront = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront;
            const vfc::float32_t width             = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{axleDistanceFront + wheelbase + frontOffsetX, -width / 2.f - frontOffsetY};
        }
        else
        {
            const vfc::float32_t axleDistanceRear = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear;
            const vfc::float32_t width            = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{-axleDistanceRear - rearOffsetX, -width / 2.f - rearOffsetY};
        }
    }
    case ERotationDirection::LEFT_FRONT_WHEEL_ANTICLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            const vfc::float32_t wheelbase = pc::vehicle::g_mechanicalData->m_wheelbase;
            const vfc::float32_t width     = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{wheelbase + frontOffsetX, -width / 2.f - frontOffsetY};
        }
        else
        {
            const vfc::float32_t axleDistance = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear;
            return osg::Vec2f{-axleDistance - rearOffsetX,  rearOffsetY };
        }
    }
    case ERotationDirection::RIGHT_FRONT_WHEEL_CLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            const vfc::float32_t wheelbase = pc::vehicle::g_mechanicalData->m_wheelbase;
            const vfc::float32_t width     = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{wheelbase + frontOffsetX, width / 2.f + frontOffsetY};
        }
        else
        {
            const vfc::float32_t axleDistanceRear = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear;
            const vfc::float32_t width            = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{-axleDistanceRear - rearOffsetX, - rearOffsetY};
        }
    }
    case ERotationDirection::RIGHT_FRONT_WHEEL_ANTICLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            const vfc::float32_t axleDistanceFront = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront;
            const vfc::float32_t wheelbase         = pc::vehicle::g_mechanicalData->m_wheelbase;
            const vfc::float32_t width             = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{axleDistanceFront + wheelbase + frontOffsetX, width / 2.f + frontOffsetY};
        }
        else
        {
            const vfc::float32_t axleDistanceRear = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear;
            const vfc::float32_t width            = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{-axleDistanceRear - rearOffsetX, width / 2.f + rearOffsetY};
        }
    }
    case ERotationDirection::LEFT_REAR_WHEEL_ANTICLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            const vfc::float32_t wheelbase         = pc::vehicle::g_mechanicalData->m_wheelbase;
            const vfc::float32_t axleDistanceFront = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront;
            return osg::Vec2f{wheelbase + axleDistanceFront + frontOffsetX, -frontOffsetY};
        }
        else
        {
            const vfc::float32_t axleDistanceRear = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear;
            const vfc::float32_t width            = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{-axleDistanceRear - rearOffsetX, -width / 2.f - rearOffsetY};
        }
    }
    case ERotationDirection::LEFT_REAR_WHEEL_CLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            const vfc::float32_t wheelbase         = pc::vehicle::g_mechanicalData->m_wheelbase;
            const vfc::float32_t axleDistanceFront = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront;
            return osg::Vec2f{wheelbase + axleDistanceFront + frontOffsetX, 0.f - frontOffsetY};
        }
        else
        {
            const vfc::float32_t width = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{0.f - rearOffsetX, -width / 2.f - rearOffsetY};
        }
    }
    case ERotationDirection::RIGHT_REAR_WHEEL_ANTICLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            const vfc::float32_t wheelbase         = pc::vehicle::g_mechanicalData->m_wheelbase;
            const vfc::float32_t axleDistanceFront = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront;
            return osg::Vec2f{wheelbase + axleDistanceFront + frontOffsetX, 0.f + frontOffsetY};
        }
        else
        {
            const vfc::float32_t width = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{0.f - rearOffsetX, width / 2.f + rearOffsetY};
        }
    }
    case ERotationDirection::RIGHT_REAR_WHEEL_CLOCKWISE:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            const vfc::float32_t wheelbase         = pc::vehicle::g_mechanicalData->m_wheelbase;
            const vfc::float32_t width             = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            const vfc::float32_t axleDistanceFront = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront;
            return osg::Vec2f{wheelbase + axleDistanceFront + frontOffsetX, frontOffsetY};
        }
        else
        {
            const vfc::float32_t axleDistanceRear = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear;
            const vfc::float32_t width            = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{-rearOffsetX, width / 2.f + rearOffsetY};
        }
    }
    case ERotationDirection::CENTROID_CLOCKWISE:
    case ERotationDirection::REAR_AXLE_CENTER_CLOCKWISE:
    default:
    {
        if (f_overlayPosition == ETopViewTrajPosType::FRONT)
        {
            const vfc::float32_t wheelbase    = pc::vehicle::g_mechanicalData->m_wheelbase;
            const vfc::float32_t axleDistance = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront;
            const vfc::float32_t width        = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{wheelbase + axleDistance + frontOffsetX, width / 2.f + frontOffsetY};
        }
        else
        {
            const vfc::float32_t axleDistance = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear;
            const vfc::float32_t width        = pc::vehicle::g_mechanicalData->m_widthWithMirrors;
            return osg::Vec2f{-axleDistance - rearOffsetX, -width / 2.f - rearOffsetY};
        }
    }
    }
}

//************************************************************************************
///  \brief   ArrowUpdateCallback
///
/// Defines arrow transparency changing logic
//************************************************************************************
class ArrowUpdateCallback : public osg::NodeCallback
{
public:
    ArrowUpdateCallback(cc::core::CustomFramework* f_customFramework)
        : m_customFramework(f_customFramework)
    {
    }

    void operator()(osg::Node* node, osg::NodeVisitor* f_nv) override
    {
        if (nullptr != f_nv)
        {
            if (f_nv->getVisitorType() == osg::NodeVisitor::UPDATE_VISITOR)
            {
                E3ParkingTopViewArrow* const arrowNode = dynamic_cast<E3ParkingTopViewArrow*>(node);

                const vfc::float32_t l_phase = 360.f * 0.33333f * static_cast<vfc::float32_t>(arrowNode->getArrowIndex());
                const vfc::float32_t alpha   = std::abs(std::cos(osg::DegreesToRadians(m_animationCounter + l_phase)));
                m_animationCounter           = m_animationCounter + g_e3ParkingTopviewSettings->m_arrowSpeed;
                arrowNode->setAlphaUniform(alpha);
            }

            traverse(node, f_nv);
        }
    }

private:
    cc::core::CustomFramework* m_customFramework;
    vfc::float32_t             m_animationCounter{0.f};
};

osg::Image* create1DTexture(const osg::Vec4i& f_color, const osg::Vec4f& f_gradient)
{

    constexpr vfc::uint32_t lc_imageWidth         = 256U;   // Image width in pixels.
    constexpr vfc::uint32_t lc_imageHeight        = 1U;     // Image height in pixels.
    constexpr vfc::uint32_t lc_imageDepth         = 1U;     // Image depth in pixels, in case of a 3D image.
    constexpr vfc::float32_t lc_imageGeometryWidth = static_cast<vfc::float32_t>(lc_imageWidth - 1U);

    std::array<vfc::float32_t, 4> l_normalizedPositions; // 0..7: From left to right
    l_normalizedPositions[0] = f_gradient.x();
    l_normalizedPositions[1] = f_gradient.y();
    l_normalizedPositions[2] = f_gradient.z();
    l_normalizedPositions[3] = f_gradient.w();

    const osg::Vec4ub l_lineColor_Inside(
        static_cast<unsigned char>(f_color.r()),
        static_cast<unsigned char>(f_color.g()),
        static_cast<unsigned char>(f_color.b()),
        static_cast<unsigned char>(f_color.a()));

    osg::Vec4ub l_lineColor_Outside = l_lineColor_Inside;
    l_lineColor_Outside.a()         = 0;

    osg::Image* const l_image = new osg::Image;
    l_image->allocateImage(lc_imageWidth, lc_imageHeight, lc_imageDepth, GL_RGBA, GL_UNSIGNED_BYTE);
    osg::Vec4ub* l_data = reinterpret_cast<osg::Vec4ub*>(l_image->data());
    for (vfc::uint32_t x = 0; x < lc_imageWidth; ++x)
    {
        const vfc::float32_t l_x_normalized = static_cast<vfc::float32_t>(x) / lc_imageGeometryWidth;

        if ((l_x_normalized < l_normalizedPositions[0]) || (l_x_normalized > l_normalizedPositions[3]))
        {
            (*l_data) = l_lineColor_Outside;
        }
        else if ((l_x_normalized > l_normalizedPositions[1]) && (l_x_normalized < l_normalizedPositions[2]))
        {
            (*l_data) = l_lineColor_Inside;
        }
        else
        {
            if (l_x_normalized <= l_normalizedPositions[1])
            {
                (*l_data) = cc::assets::trajectory::helper::TrajectoryHelper::smoothstep_Vec4ub(
                    l_lineColor_Outside,
                    l_lineColor_Inside,
                    l_normalizedPositions[0],
                    l_normalizedPositions[1],
                    l_x_normalized);
            }
            else if (l_x_normalized >= l_normalizedPositions[2])
            {
                (*l_data) = cc::assets::trajectory::helper::TrajectoryHelper::smoothstep_Vec4ub(
                    l_lineColor_Inside,
                    l_lineColor_Outside,
                    l_normalizedPositions[2],
                    l_normalizedPositions[3],
                    l_x_normalized);
            }
            else
            {
                // Intentionally do nothing
            }
        }
        ++l_data;
    }

    return l_image;
}

osg::Texture2D* createTexture(const osg::Vec4i& f_color, const osg::Vec4f& f_gradient)
{
    const osg::ref_ptr<osg::Image> l_1Dimage = create1DTexture(f_color, f_gradient);
    osg::Texture2D* const texture   = new osg::Texture2D(l_1Dimage);

    texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    texture->setResizeNonPowerOfTwoHint(false);
    texture->setName("WT-texture");
    return texture;
}

} // namespace

void E3ParkingTopViewOverlayBaseClass::generateGeometry()
{
    removeDrawables(0, getNumDrawables());

    const osg::ref_ptr<osg::Geometry> l_geometry = new osg::Geometry;

    const osg::ref_ptr<osg::StateSet> l_stateSet = createStateSet();
    l_geometry->setStateSet(l_stateSet.get());

    m_vertices = new osg::Vec3Array;
    updateVertices();
    l_geometry->setVertexArray(m_vertices.get());

    osg::Vec2Array* const l_texCoords = createTexCoords();
    l_geometry->setTexCoordArray(0, l_texCoords, osg::Array::BIND_PER_VERTEX);

    l_geometry->addPrimitiveSet(new osg::DrawArrays(GL_TRIANGLE_STRIP, 0, (m_segments) * 2));

    osg::Vec4Array* const l_colors = new osg::Vec4Array(1u);
    (*l_colors)[0]                 = osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f);
    l_geometry->setColorArray(l_colors, osg::Array::BIND_OVERALL);

    osg::Geode::addDrawable(l_geometry);

    m_drawableInitiated = true;
}

E3ParkingTopViewTrajectory::E3ParkingTopViewTrajectory(const ETopViewTrajPosType f_type)
    : E3ParkingTopViewOverlayBaseClass(f_type)
    , m_cutOffUniform{}
    , m_color{}
    , m_gradientPosition{}
{
}

void E3ParkingTopViewTrajectory::updateVertices()
{
    m_vertices->clear();

    const vfc::float32_t delta = m_radiusOuter - m_radiusInner;
    if (m_currentRotationDirection != cc::target::common::ERotationDirection::RIGHT_PARALLEL)
    {
        for (unsigned i = 0; i <= m_segments; ++i)
        {
            vfc::float32_t radiusDelta = 0.f;
            vfc::float32_t t           = 0.f;
            vfc::float32_t angle       = 0.f;
            t = static_cast<vfc::float32_t>(i) / m_segments;
            angle = m_total_line_radiant * t + m_overlayStartingAngle;
            // shrinking the width at the two ends of the trajectory
            if (t > 0.8f)
            {
                radiusDelta = (t - 0.8f) * delta;
            }
            else if (t < 0.2f)
            {
                radiusDelta = (0.2f - t) * delta;
            }
            else
            {
                // do nothing
            }
            vfc::float32_t x_inner = 0.f;
            vfc::float32_t y_inner = 0.f;
            vfc::float32_t x       = 0.f;
            vfc::float32_t y       = 0.f;

            x_inner = m_center.x() + (m_radiusInner + radiusDelta) * std::cos(angle);
            y_inner = m_center.y() + (m_radiusInner + radiusDelta) * std::sin(angle);
            x = m_center.x() + (m_radiusOuter - radiusDelta) * std::cos(angle);
            y = m_center.y() + (m_radiusOuter - radiusDelta) * std::sin(angle);

            m_vertices->push_back(osg::Vec3(x, y, HEIGHT));

            m_vertices->push_back(osg::Vec3(x_inner, y_inner, HEIGHT));
        }
    }
    else
    {
        const vfc::float32_t wheelbase         = pc::vehicle::g_mechanicalData->m_wheelbase;
        const vfc::float32_t axleDistanceFront = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront;
        const vfc::float32_t axleDistanceRear  = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear;
        const vfc::float32_t frontOffsetX      = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetX_Type7;
        const vfc::float32_t rearOffsetX       = g_e3ParkingTopviewOffset->m_trajRearPositionOffsetX_Type7;
        vfc::float32_t       trajectory_x      = 0.f;
        const vfc::float32_t trajectory_y      = g_e3ParkingTopviewOffset->m_trajPositionOffsetY_Type7;
        const vfc::float32_t length            = g_e3ParkingTopviewOffset->m_trajTotalLength_Type7;
        if (m_trajectoryPosition == ETopViewTrajPosType::FRONT)
        {
            trajectory_x = wheelbase + axleDistanceFront + frontOffsetX;
        }
        else
        {
            trajectory_x = -axleDistanceRear + rearOffsetX;
        }
        for (unsigned i = 0; i <= m_segments; ++i)
        {
            vfc::float32_t       radiusDelta = 0.f;
            vfc::float32_t       t           = 0.f;
            t = static_cast<vfc::float32_t>(i) / m_segments;
            const vfc::float32_t position_y  = -length * t + trajectory_y;
            // shrinking the width at the two ends of the trajectory
            if (t > 0.8f)
            {
                radiusDelta = (t - 0.8f) * delta;
            }
            else if (t < 0.2f)
            {
                radiusDelta = (0.2f - t) * delta;
            }
            else
            {
                // do nothing
            }
            vfc::float32_t x_inner = 0.f;
            vfc::float32_t x       = 0.f;
            x_inner = trajectory_x + radiusDelta - 0.5f * delta;
            x = trajectory_x - radiusDelta + 0.5f * delta;
            m_vertices->push_back(osg::Vec3(x, position_y, HEIGHT));
            m_vertices->push_back(osg::Vec3(x_inner, position_y, HEIGHT));
        }
    }
    m_vertices->dirty();
}

osg::Vec2Array* E3ParkingTopViewTrajectory::createTexCoords()
{
    osg::Vec2Array* const l_texCoords = new osg::Vec2Array;
    l_texCoords->setNormalize(true);
    for (unsigned i = 0; i <= m_segments; ++i) // PRQA S 2427
    {
        const vfc::float32_t l_t = static_cast<vfc::float32_t>(i) / m_segments; // PRQA S 3011
        l_texCoords->push_back(osg::Vec2(0.f, l_t));
        l_texCoords->push_back(osg::Vec2(1.f, l_t));
    }
    return l_texCoords;
}

osg::StateSet* E3ParkingTopViewTrajectory::createStateSet()
{
    osg::StateSet* const l_ss = new osg::StateSet;
    l_ss->setMode(GL_BLEND, osg::StateAttribute::ON);
    l_ss->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);

    const osg::ref_ptr<osg::Depth> l_depth = new osg::Depth;
    l_depth->setWriteMask(false);
    l_ss->setAttributeAndModes(l_depth, osg::StateAttribute::ON);
    l_ss->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);

    const osg::ref_ptr<osg::Texture2D> l_texture = createTexture(m_color, m_gradientPosition);
    l_ss->setTextureAttribute(0, l_texture, osg::StateAttribute::ON);

    pc::core::TextureShaderProgramDescriptor l_basicTexStateSet("e3TopviewTrajectoryTex");
    l_basicTexStateSet.apply(l_ss);
    m_cutOffUniform = l_ss->getOrCreateUniform("u_startCutoff", osg::Uniform::FLOAT);
    m_cutOffUniform->set(1.0f);

    return l_ss;
}

void E3ParkingTopViewTrajectory::updateInputs(
    const cc::target::common::ERotationDirection f_direction,
    const osg::Vec2f&                            f_center)
{
    m_currentRotationDirection               = f_direction;
    m_center                                 = f_center;
    const osg::Vec2f startingPoint           = getStartingPointTopViewOverlay(f_direction, m_trajectoryPosition);
    m_overlayStartingAngle                   = angleBetweenPoints(m_center, startingPoint);
    const vfc::float32_t startingPointRadius = distanceBetweenPoints(m_center, startingPoint);
    m_radiusInner = startingPointRadius + g_e3ParkingTopviewSettings->m_trajRadiusInnerOffset;
    m_radiusOuter = startingPointRadius + g_e3ParkingTopviewSettings->m_trajRadiusOuterOffset;

    m_segments         = 64u;
    m_color            = g_e3ParkingTopviewSettings->m_trajColor;
    m_gradientPosition = g_e3ParkingTopviewSettings->m_trajGradientPosition;

    m_total_line_radiant = getLineRadiant(f_direction, m_trajectoryPosition);

    // geometry will be initialized once, afterwards only vertices will be updated based on the inputs (param, direction
    // etc.)
    if (m_drawableInitiated)
    {
        updateVertices();
    }
    else
    {
        generateGeometry();
    }
}

E3ParkingTopViewArrow::E3ParkingTopViewArrow(const ETopViewTrajPosType f_type, vfc::uint32_t f_number)
    : E3ParkingTopViewOverlayBaseClass(f_type)
    , m_alphaUniform{}
    , m_arrowNumber(f_number)
{
}

void E3ParkingTopViewArrow::updateStartupPercentage(const vfc::float32_t f_percentage)
{
    const vfc::float32_t percentage = std::min(f_percentage, 1.0f);

    m_overlayStartingAngle = m_total_line_radiant * percentage + m_refStartingPointAngle;

    updateVertices();
}

void E3ParkingTopViewArrow::updateInputs(
    const cc::target::common::ERotationDirection f_direction,
    const osg::Vec2f&                            f_center)
{
    m_currentRotationDirection                 = f_direction;
    const osg::Vec2f     l_startingPoint       = getStartingPointTopViewOverlay(f_direction, m_trajectoryPosition);
    const vfc::float32_t l_startingPointRadius = distanceBetweenPoints(f_center, l_startingPoint);
    m_refStartingPointAngle                    = angleBetweenPoints(f_center, l_startingPoint);
    m_overlayStartingAngle                     = m_refStartingPointAngle;
    m_center                                   = f_center;
    m_radiusInner                     = l_startingPointRadius + g_e3ParkingTopviewSettings->m_arrowRadiusInnerOffset;
    m_arrowHeight                     = g_e3ParkingTopviewSettings->m_arrowHeight;
    const vfc::float32_t l_arrowWidth = m_arrowHeight / g_e3ParkingTopviewSettings->m_arrowHeightWidthRatio;
    m_radiusOuter                     = m_radiusInner + l_arrowWidth;
    m_segments                        = 2u;

    m_total_line_radiant =
        getLineRadiant(f_direction, m_trajectoryPosition) * g_e3ParkingTopviewSettings->m_arrowRelativePositionRatio;

    // geometry will be initialized once, afterwards only vertices will be updated based on the inputs (param, direction
    // etc.)
    if (m_drawableInitiated)
    {
        updateVertices();
    }
    else
    {
        generateGeometry();
    }
}

void E3ParkingTopViewArrow::updateVertices()
{
    m_vertices->clear();

    osg::Vec3 first_point;
    osg::Vec3 second_point;
    osg::Vec3 third_point;
    osg::Vec3 fourth_point;

    if (m_currentRotationDirection != cc::target::common::ERotationDirection::RIGHT_PARALLEL)
    {
        adjustStartingAngle();
        calculateTangentEndpoints(
            m_center, m_radiusOuter, m_overlayStartingAngle, m_arrowHeight, first_point, second_point);
        calculateTangentEndpoints(
            m_center, m_radiusInner, m_overlayStartingAngle, m_arrowHeight, third_point, fourth_point);
        if (m_total_line_radiant > 0.f) // counterclockwise
        {
            m_vertices->push_back(first_point);
            m_vertices->push_back(second_point);
            m_vertices->push_back(third_point);
            m_vertices->push_back(fourth_point);
        }
        else // clockwise
        {
            m_vertices->push_back(second_point);
            m_vertices->push_back(first_point);
            m_vertices->push_back(fourth_point);
            m_vertices->push_back(third_point);
        }
    }
    else
    {
        const vfc::float32_t wheelbase         = pc::vehicle::g_mechanicalData->m_wheelbase;
        const vfc::float32_t axleDistanceFront = pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront;
        const vfc::float32_t frontOffsetX      = g_e3ParkingTopviewOffset->m_trajFrontPositionOffsetX_Type7;
        const vfc::float32_t arrow_x =
            wheelbase + axleDistanceFront + frontOffsetX + g_e3ParkingTopviewSettings->m_arrowRadiusInnerOffset;

        const vfc::float32_t arrow_y_ref = -g_e3ParkingTopviewOffset->m_trajTotalLength_Type7 *
                                       g_e3ParkingTopviewSettings->m_arrowRelativePositionRatio;
        vfc::float32_t arrow_y = arrow_y_ref;
        if (m_arrowNumber == 0.0f)
        {
            arrow_y = arrow_y_ref + m_arrowHeight;
        }
        else if (m_arrowNumber == 2.0f)
        {
            arrow_y = arrow_y_ref - m_arrowHeight;
        }
        else
        {

        }
        const vfc::float32_t l_arrowWidth = m_arrowHeight / g_e3ParkingTopviewSettings->m_arrowHeightWidthRatio;
        first_point                       = osg::Vec3(arrow_x, arrow_y, HEIGHT);
        second_point                      = osg::Vec3(arrow_x, arrow_y + m_arrowHeight, HEIGHT);
        third_point                       = osg::Vec3(arrow_x - l_arrowWidth, arrow_y, HEIGHT);
        fourth_point                      = osg::Vec3(arrow_x - l_arrowWidth, arrow_y + m_arrowHeight, HEIGHT);
        m_vertices->push_back(first_point);
        m_vertices->push_back(second_point);
        m_vertices->push_back(third_point);
        m_vertices->push_back(fourth_point);
    }

    m_vertices->dirty();
}

void E3ParkingTopViewArrow::adjustStartingAngle()
{
    vfc::float32_t l_arrowHeightWithSign = 0.f;
    if (m_total_line_radiant > 0.f) // counterclockwise
    {
        l_arrowHeightWithSign = m_arrowHeight;
    }
    else
    {
        l_arrowHeightWithSign = -m_arrowHeight;
    }
    if (static_cast<unsigned int>(m_arrowNumber) == 0u)
    {
        m_overlayStartingAngle = m_overlayStartingAngle - l_arrowHeightWithSign *
                                                              g_e3ParkingTopviewSettings->m_arrowDistanceRatio /
                                                              m_radiusInner;
    }
    else if (static_cast<unsigned int>(m_arrowNumber) == 2u)
    {
        m_overlayStartingAngle = m_overlayStartingAngle + l_arrowHeightWithSign *
                                                              g_e3ParkingTopviewSettings->m_arrowDistanceRatio /
                                                              m_radiusInner;
    }
    else
    {
        // nothing
    }
}

osg::Vec2Array* E3ParkingTopViewArrow::createTexCoords()
{
    osg::Vec2Array* const l_texCoords = new osg::Vec2Array;

    l_texCoords->push_back(osg::Vec2(0.f, 0.f));
    l_texCoords->push_back(osg::Vec2(0.f, 1.f));
    l_texCoords->push_back(osg::Vec2(1.f, 0.f));
    l_texCoords->push_back(osg::Vec2(1.f, 1.f));

    return l_texCoords;
}

osg::StateSet* E3ParkingTopViewArrow::createStateSet()
{
    osg::StateSet* const l_ss = new osg::StateSet;
    l_ss->setMode(GL_BLEND, osg::StateAttribute::ON);
    l_ss->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);

    const osg::ref_ptr<osg::Depth> l_depth = new osg::Depth;
    l_depth->setWriteMask(false);
    l_ss->setAttributeAndModes(l_depth, osg::StateAttribute::ON);
    l_ss->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);

    osg::Image* const l_image = osgDB::readImageFile(CONCAT_PATH(g_e3ParkingTopviewSettings->m_arrowfilename));
    const osg::ref_ptr<osg::Texture2D> m_arrowTexture = new osg::Texture2D(l_image);
    m_arrowTexture->setDataVariance(osg::Object::STATIC);
    m_arrowTexture->setUnRefImageDataAfterApply(true);
    m_arrowTexture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    m_arrowTexture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    m_arrowTexture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    m_arrowTexture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    l_ss->setTextureAttribute(0, m_arrowTexture, osg::StateAttribute::ON);

    pc::core::TextureShaderProgramDescriptor l_basicTexStateSet("e3TopviewArrow");
    m_alphaUniform = l_ss->getOrCreateUniform("alpha", osg::Uniform::FLOAT);
    m_alphaUniform->set(1.0f);
    l_basicTexStateSet.apply(l_ss);

    return l_ss;
}

E3ParkingTopViewAsset::E3ParkingTopViewAsset(const cc::core::AssetId f_assetId, cc::core::CustomFramework* f_framework)
    : Asset(f_assetId)
    , m_customFramework{f_framework}
    , m_whiteTrajectoryOne{}
    , m_whiteTrajectoryTwo{}
    , m_rearArrows{}
    , m_frontArrows{}

{
    m_whiteTrajectoryOne = osg_ext::make_ref<E3ParkingTopViewTrajectory>(ETopViewTrajPosType::FRONT);
    m_whiteTrajectoryOne->setName("CircularSection");
    pc::core::Asset::addChild(m_whiteTrajectoryOne);

    m_whiteTrajectoryTwo = osg_ext::make_ref<E3ParkingTopViewTrajectory>(ETopViewTrajPosType::REAR);
    m_whiteTrajectoryTwo->setName("CircularSection");
    pc::core::Asset::addChild(m_whiteTrajectoryTwo);

    for (vfc::uint32_t i = 0; i < 3U; i++)
    {
        m_frontArrows[i] = osg_ext::make_ref<E3ParkingTopViewArrow>(ETopViewTrajPosType::FRONT, i);
        m_frontArrows[i]->addUpdateCallback(new cc::assets::ArrowUpdateCallback(f_framework));
        pc::core::Asset::addChild(m_frontArrows[i]);
    }

    for (vfc::uint32_t i = 0; i < 3U; i++)
    {
        m_rearArrows[i] = osg_ext::make_ref<E3ParkingTopViewArrow>(ETopViewTrajPosType::REAR, i);
        m_rearArrows[i]->addUpdateCallback(new cc::assets::ArrowUpdateCallback(f_framework));
        pc::core::Asset::addChild(m_rearArrows[i]);
    }

    m_whiteTrajectoryOne->setNodeMask(0);
    m_whiteTrajectoryTwo->setNodeMask(0);
    for (vfc::uint32_t i = 0; i < 3U; i++)
    {
        m_frontArrows[i]->setNodeMask(0);
        m_rearArrows[i]->setNodeMask(0);
    }
}

void E3ParkingTopViewAsset::disableAll()
{
    m_whiteTrajectoryOne->setNodeMask(0);
    m_whiteTrajectoryTwo->setNodeMask(0);
    for (vfc::uint32_t i = 0; i < 3U; i++)
    {
        m_frontArrows[i]->setNodeMask(0);
        m_rearArrows[i]->setNodeMask(0);
    }
}

void E3ParkingTopViewAsset::enableAll()
{
    m_whiteTrajectoryOne->setNodeMask(0xffffffff);
    m_whiteTrajectoryTwo->setNodeMask(0xffffffff);
    for (vfc::uint32_t i = 0; i < 3U; i++)
    {
        m_frontArrows[i]->setNodeMask(0xffffffff);
        m_rearArrows[i]->setNodeMask(0xffffffff);
    }

    const osg::Vec2f l_frontCenter = getRotationCenterForTopViewOverlay(m_currentRotationDirection, cc::assets::ETopViewTrajPosType::FRONT);
    const osg::Vec2f l_rearCenter = getRotationCenterForTopViewOverlay(m_currentRotationDirection, cc::assets::ETopViewTrajPosType::REAR);

    m_whiteTrajectoryOne->updateInputs(m_currentRotationDirection, l_frontCenter);
    m_whiteTrajectoryTwo->updateInputs(m_currentRotationDirection, l_rearCenter);
    for (vfc::uint32_t i = 0; i < 3U; i++)
    {
        m_frontArrows[i]->updateInputs(m_currentRotationDirection, l_frontCenter);
        m_rearArrows[i]->updateInputs(m_currentRotationDirection, l_rearCenter);
    }

    m_whiteTrajectoryOne->updateStartupPercentage(0.f);
    m_whiteTrajectoryTwo->updateStartupPercentage(0.f);
    for (vfc::uint32_t i = 0; i < 3U; i++)
    {
        m_frontArrows[i]->updateStartupPercentage(0.f);
        m_rearArrows[i]->updateStartupPercentage(0.f);
    }
}

void E3ParkingTopViewAsset::duringStartup(const vfc::float32_t f_percentage)
{
    m_whiteTrajectoryOne->updateStartupPercentage(f_percentage);
    m_whiteTrajectoryTwo->updateStartupPercentage(f_percentage);
    for (vfc::uint32_t i = 0; i < 3U; i++)
    {
        m_frontArrows[i]->updateStartupPercentage(f_percentage);
        m_rearArrows[i]->updateStartupPercentage(f_percentage);
    }
}

void E3ParkingTopViewAsset::traverse(osg::NodeVisitor& f_nv)
{
    using namespace cc::target::common;
    if (f_nv.getVisitorType() == osg::NodeVisitor::UPDATE_VISITOR)
    {
        bool l_paramChanged = false;
        if (m_modifiedCountSettings != g_e3ParkingTopviewSettings->getModifiedCount())
        {
            m_modifiedCountSettings = g_e3ParkingTopviewSettings->getModifiedCount();
            l_paramChanged          = true;
        }
        if (m_modifiedCountOffset != g_e3ParkingTopviewOffset->getModifiedCount())
        {
            m_modifiedCountOffset = g_e3ParkingTopviewOffset->getModifiedCount();
            l_paramChanged        = true;
        }
        if (m_modifiedCountRadian != g_e3ParkingTopviewRadian->getModifiedCount())
        {
            m_modifiedCountRadian = g_e3ParkingTopviewRadian->getModifiedCount();
            l_paramChanged        = true;
        }
        if (m_modifiedCountCenterOffset != g_e3ParkingTopviewCenterOffset->getModifiedCount())
        {
            m_modifiedCountCenterOffset = g_e3ParkingTopviewCenterOffset->getModifiedCount();
            l_paramChanged        = true;
        }

        // EApaStatus            l_previousApaStatus         = m_currentApaStatus;
        const ESpecialParkingStatus l_previousSpecialStatus     = m_currentSpecialStatus;
        const ERotationDirection    l_previousRotationDirection = m_currentRotationDirection;

        if (m_customFramework->m_ParkhmiToSvs_ReceiverPort.hasData())
        {
            const auto parkHmiContainer = m_customFramework->m_ParkhmiToSvs_ReceiverPort.getData();
            m_currentApaStatus          = parkHmiContainer->m_Data.m_apaStatus;
            m_currentSpecialStatus      = parkHmiContainer->m_Data.m_specialParkingStatus;
            m_currentRotationDirection  = parkHmiContainer->m_Data.m_rotationDirection;
        }

        // check if apa status or special status triggers a shutdown
        constexpr bool l_shutdownTriggeredByApaStatus = /* (l_previousApaStatus != EApaStatus::GuidanceCompleted &&
                                                     l_previousApaStatus != EApaStatus::GuidanceTerminate) &&
                                                    (m_currentApaStatus == EApaStatus::GuidanceCompleted ||
                                                     m_currentApaStatus == EApaStatus::GuidanceTerminate) */
            false;
        const bool l_shutdownTriggeredBySpecialStatus = l_previousSpecialStatus != ESpecialParkingStatus::OFF &&
                                                        m_currentSpecialStatus == ESpecialParkingStatus::OFF;

        const bool l_shutdownTriggered = l_shutdownTriggeredByApaStatus || l_shutdownTriggeredBySpecialStatus;

        // check if startup is triggered by new status or new rotation direction or new parameter values
        const bool l_startupTriggered = /* !(m_currentApaStatus == EApaStatus::GuidanceCompleted ||
                                          m_currentApaStatus == EApaStatus::GuidanceTerminate) && */
            (m_currentSpecialStatus == ESpecialParkingStatus::ON &&
             (m_currentSpecialStatus != l_previousSpecialStatus ||
              m_currentRotationDirection != l_previousRotationDirection || l_paramChanged) &&
             (m_currentRotationDirection != ERotationDirection::INVAILD));

        if (l_startupTriggered)
        {
            m_startTime          = f_nv.getFrameStamp()->getSimulationTime();
            m_startupTimerActive = true;
            enableAll();
            m_overlayEnabled = true;
        }
        else if (m_overlayEnabled == true && m_startupTimerActive == true)
        {
            const vfc::float32_t currentTime = static_cast<vfc::float32_t>(f_nv.getFrameStamp()->getSimulationTime());
            const vfc::float32_t elapsed     = currentTime - static_cast<vfc::float32_t>(m_startTime);
            const vfc::float32_t t           = std::min(elapsed / g_e3ParkingTopviewSettings->m_startupTime, 1.0f);
            duringStartup(t);
            if (t >= 1.0f)
            {
                m_startupTimerActive = false;
            }
        }
        else if (l_shutdownTriggered)
        {
            disableAll();

            // reset status values
            m_currentApaStatus         = EApaStatus::PassiveStandBy;
            m_currentSpecialStatus     = ESpecialParkingStatus::OFF;
            m_currentRotationDirection = ERotationDirection::LEFT_FRONT_WHEEL_ANTICLOCKWISE;
            m_overlayEnabled           = false;
            m_startupTimerActive       = false;
        }
        else
        {
            // Intentionally do nothing
        }
    }

    pc::core::Asset::traverse(f_nv);
}

} // namespace assets
} // namespace cc
