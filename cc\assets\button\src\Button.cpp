//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------


#include "pc/svs/util/osgx/inc/MemberWrapper.h"

#include "cc/assets/button/inc/Button.h"
#include "cc/assets/uielements/inc/CustomIcon.h"
// #include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"

#include "cc/imgui/inc/imgui_manager.h"

#include <chrono>

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace button
{

Button::Button(cc::core::AssetId f_assetId, osg::Camera* f_referenceView)
    : pc::assets::ImageOverlays(f_assetId, f_referenceView) // PRQA S 2323
    , m_size({0.0f, 0.0f}) // PRQA S 2323
    , m_iconCenter({0.0f, 0.0f}) // PRQA S 2323
    , m_responseArea({0.0f, 0.0f}) // PRQA S 2323
    , m_positionHori({0.0f, 0.0f}) // PRQA S 2323
    , m_positionVert({0.0f, 0.0f}) // PRQA S 2323
    , m_icon(nullptr) // PRQA S 2323
    , m_horiReferenceView(nullptr) // PRQA S 2323
    , m_vertReferenceView(nullptr) // PRQA S 2323
    , m_texturePath("")
    , m_settingModifiedCount(~0u) // PRQA S 2323
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}


osg::Vec2f Button::getIconPositionScreen()
{
    if (getReferenceView() == nullptr || (getReferenceView()->getViewport() == nullptr))
    {
        return osg::Vec2f{0.0f, 0.0f};
    }
    const auto viewport = getReferenceView()->getViewport();
    const vfc::float32_t x = m_iconCenter.x() + static_cast<vfc::float32_t>(viewport->x());
    const vfc::float32_t y = (m_rotateTheme == RotateThemeType::ETHEME_TYPE_HORI) ?
        static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y()) - (m_iconCenter.y() + static_cast<vfc::float32_t>(viewport->y())) :
        (m_iconCenter.y() + static_cast<vfc::float32_t>(viewport->y()));
    return osg::Vec2f{x, y};
}


void Button::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        update();
        if (m_dirty)
        {
            init();
            m_dirty = false;
        }
        if (m_dirtyState)
        {
            switch (m_state)
            {
                case UNAVAILABLE:
                {
                    onUnavailable();
                    break;
                }
                case AVAILABLE:
                {
                    onAvailable();
                    break;
                }
                case PRESSED:
                {
                    onPressed();
                    break;
                }
                case RELEASED:
                {
                    onReleased();
                    break;
                }
                case INVALID:
                default:
                {
                    onInvalid();
                    break;
                }
            }
            init(); // prevent showing old texture
            m_dirtyState = false;
        }
        m_icon->setEnabled(m_iconEnable);

        IMGUI_LOG("Buttons", getName() + " Position", std::to_string(getIconPositionScreen().x()) + " " + std::to_string(getIconPositionScreen().y()))
    }
    pc::assets::ImageOverlays::traverse(f_nv);
}


void Button::init()
{
    removeIcon(m_icon);
    m_icon = new cc::assets::uielements::CustomIcon{m_texturePath, false, false, true, m_rotateTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI};
    m_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
    m_icon->setPosition(m_iconCenter, pc::assets::Icon::UnitType::Pixel);
    m_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    m_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    m_icon->setEnabled(m_iconEnable);
    m_icon->setSize(m_size,pc::assets::Icon::UnitType::Pixel);
    m_responseArea = m_icon->getIconSize();
    if (m_rotateTheme != cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    {
        std::swap(m_responseArea.x(), m_responseArea.y());
        m_icon->setSize(m_responseArea, pc::assets::Icon::UnitType::Pixel);
    }
    addIcon(m_icon);
    updateIconCenter();
}


void Button::updateIconCenter()
{
    // using cc::core::CustomViews;
    if (m_rotateTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    {
        if (m_horiReferenceView == nullptr)
        {
            XLOG_ERROR(g_AppContext, "Button::updateIconCenter m_horiReferenceView is nullptr, maybe you forget to set it?");
            return;
        }
        m_iconCenter.x() = (m_iconAtMiddle) ? static_cast<vfc::float32_t>(m_horiReferenceView->getViewport()->width() / 2.0) : m_positionHori.x();
        m_iconCenter.y() = m_positionHori.y();
    }
    else
    {
        if (m_vertReferenceView == nullptr)
        {
            XLOG_ERROR(g_AppContext, "Button::updateIconCenter m_vertReferenceView is nullptr, maybe you forget to set it?");
            return;
        }
        m_iconCenter.x() = static_cast<vfc::float32_t>(m_vertReferenceView->getViewport()->width()) - m_positionVert.x();
        m_iconCenter.y() = (m_iconAtMiddle) ? static_cast<vfc::float32_t>(m_vertReferenceView->getViewport()->height() / 2.0) : m_positionVert.y();
    }
    m_icon->setPosition(m_iconCenter, pc::assets::Icon::UnitType::Pixel);
}


bool Button::checkTouchInsideResponseArea()
{
    if (m_referenceView == nullptr)
    {
        XLOG_WARN(g_AppContext, "Button doesn't have reference view");
        return false;
    }
    const auto viewport = m_referenceView->getViewport();

    vfc::float32_t huX = 0.0f;
    vfc::float32_t huY = 0.0f;
    if (m_rotateTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    {
        huX = static_cast<vfc::float32_t>(m_huX) - static_cast<vfc::float32_t>(viewport->x());
        huY = (static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y()) - static_cast<vfc::float32_t>(viewport->y())) - static_cast<vfc::float32_t>(m_huY);
    }
    else
    {
        huX = static_cast<vfc::float32_t>(m_huY) - static_cast<vfc::float32_t>(viewport->x());
        huY = static_cast<vfc::float32_t>(m_huX) - static_cast<vfc::float32_t>(viewport->y());
    }
    const vfc::float32_t lowerX = (m_iconCenter.x() - m_responseArea.x() * 0.5f);
    const vfc::float32_t upperX = (m_iconCenter.x() + m_responseArea.x() * 0.5f);
    const vfc::float32_t lowerY = (m_iconCenter.y() - m_responseArea.y() * 0.5f);
    const vfc::float32_t upperY = (m_iconCenter.y() + m_responseArea.y() * 0.5f);
    if ((lowerX <= huX) && (huX <= upperX) && (lowerY <= huY) && (huY <= upperY))
    {
        return true;
    }
    return false;
}

} // namespace button
} // namespace assets
} // namespace cc
