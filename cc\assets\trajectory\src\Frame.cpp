//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>sch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/trajectory/inc/Frame.h"
#include "cc/assets/trajectory/inc/Helper.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/assets/drivablepath/inc/DrivablePathManager.h"

#include <array>
#include <cmath>
#include <cassert>

namespace cc
{
namespace assets
{
namespace trajectory
{
namespace frame
{

/**
 * Generates by the passed parameters the regarding vertices so a geometry can be rendered
 */
Frame::Frame() = default;


void Frame::addVertexLines(vfc::uint32_t f_numOfVertexLinesToAdd)
{
  VertexLine_st l_vertexLine;
  l_vertexLine.m_fadeInEnabled = true;
  l_vertexLine.m_fadeOutEnabled = true;
  for (vfc::uint32_t l_vertexLineIndex = 0u; l_vertexLineIndex < f_numOfVertexLinesToAdd; ++l_vertexLineIndex)
  {
    m_vertexLines.push_back(l_vertexLine);    // PRQA S 2976
  }
}


void Frame::setVertexLineRadius(vfc::uint32_t f_vertexLineIndex, vfc::float32_t f_radius)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_radius = f_radius;
  }
}


vfc::float32_t Frame::getVertexLineRadius(vfc::uint32_t f_vertexLineIndex) const
{
  vfc::float32_t l_returnValue = 0.001f; // Dummy value for out of range.
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    l_returnValue = m_vertexLines[f_vertexLineIndex].m_radius;
  }
  return l_returnValue;
}


void Frame::setVertexLineOffset(vfc::uint32_t f_vertexLineIndex, vfc::float32_t f_offset)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_offset = f_offset;
  }
}


vfc::float32_t Frame::getVertexLineOffset(vfc::uint32_t f_vertexLineIndex) const
{
  vfc::float32_t l_returnValue = 0.001f; // Dummy value for out of range.
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    l_returnValue = m_vertexLines[f_vertexLineIndex].m_offset;
  }
  return l_returnValue;
}


void Frame::setBumperLineAngle(vfc::uint32_t f_vertexLineIndex, vfc::float32_t f_angle)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_bumperLineAngle = f_angle;
  }
}


vfc::float32_t Frame::getBumperLineAngle(vfc::uint32_t f_vertexLineIndex) const
{
  vfc::float32_t l_returnValue = 0.0f; // Dummy value for out of range.
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    l_returnValue = m_vertexLines[f_vertexLineIndex].m_bumperLineAngle;
  }
  return l_returnValue;
}


void Frame::setBumperLinePos(vfc::uint32_t f_vertexLineIndex, vfc::float32_t f_pos)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_bumperLinePos = f_pos;
  }
}


vfc::float32_t Frame::getBumperLinePos(vfc::uint32_t f_vertexLineIndex) const
{
  vfc::float32_t l_returnValue = 0.0f; // Dummy value for out of range.
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    l_returnValue = m_vertexLines[f_vertexLineIndex].m_bumperLinePos;
  }
  return l_returnValue;
}


void Frame::setFadeInStartAngle(vfc::uint32_t f_vertexLineIndex, vfc::float32_t f_angle)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_fadeInStartAngle = f_angle;
  }
}


vfc::float32_t Frame::getFadeInStartAngle(vfc::uint32_t f_vertexLineIndex) const
{
  vfc::float32_t l_returnValue = 0.0f; // Dummy value for out of range.
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    l_returnValue = m_vertexLines[f_vertexLineIndex].m_fadeInStartAngle;
  }
  return l_returnValue;
}


void Frame::setFadeInEndAngle(vfc::uint32_t f_vertexLineIndex, vfc::float32_t f_angle)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_fadeInEndAngle = f_angle;
  }
}


vfc::float32_t Frame::getFadeInEndAngle(vfc::uint32_t f_vertexLineIndex) const
{
  vfc::float32_t l_returnValue = 0.0f; // Dummy value for out of range.
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    l_returnValue = m_vertexLines[f_vertexLineIndex].m_fadeInEndAngle;
  }
  return l_returnValue;
}


void Frame::setFadeOutStartAngle(vfc::uint32_t f_vertexLineIndex, vfc::float32_t f_angle)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_fadeOutStartAngle = f_angle;
  }
}


vfc::float32_t Frame::getFadeOutStartAngle(vfc::uint32_t f_vertexLineIndex) const
{
  vfc::float32_t l_returnValue = 0.0f; // Dummy value for out of range.
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    l_returnValue = m_vertexLines[f_vertexLineIndex].m_fadeOutStartAngle;
  }
  return l_returnValue;
}


void Frame::setFadeOutEndAngle(vfc::uint32_t f_vertexLineIndex, vfc::float32_t f_angle)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_fadeOutEndAngle = f_angle;
  }
}


vfc::float32_t Frame::getFadeOutEndAngle(vfc::uint32_t f_vertexLineIndex) const
{
  vfc::float32_t l_returnValue = 0.0f; // Dummy value for out of range.
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    l_returnValue = m_vertexLines[f_vertexLineIndex].m_fadeOutEndAngle;
  }
  return l_returnValue;
}


void Frame::setFadeInStartPos(vfc::uint32_t f_vertexLineIndex, vfc::float32_t f_pos)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_fadeInStartPos = f_pos;
  }
}


vfc::float32_t Frame::getFadeInStartPos(vfc::uint32_t f_vertexLineIndex) const
{
  vfc::float32_t l_returnValue = 0.0f; // Dummy value for out of range.
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    l_returnValue = m_vertexLines[f_vertexLineIndex].m_fadeInStartPos;
  }
  return l_returnValue;
}


void Frame::setFadeInEndPos(vfc::uint32_t f_vertexLineIndex, vfc::float32_t f_pos)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_fadeInEndPos = f_pos;
  }
}


vfc::float32_t Frame::getFadeInEndPos(vfc::uint32_t f_vertexLineIndex) const
{
  vfc::float32_t l_returnValue = 0.0f; // Dummy value for out of range.
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    l_returnValue = m_vertexLines[f_vertexLineIndex].m_fadeInEndPos;
  }
  return l_returnValue;
}


void Frame::setFadeOutStartPos(vfc::uint32_t f_vertexLineIndex, vfc::float32_t f_pos)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_fadeOutStartPos = f_pos;
  }
}


vfc::float32_t Frame::getFadeOutStartPos(vfc::uint32_t f_vertexLineIndex) const
{
  vfc::float32_t l_returnValue = 0.0f; // Dummy value for out of range.
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    l_returnValue = m_vertexLines[f_vertexLineIndex].m_fadeOutStartPos;
  }
  return l_returnValue;
}


void Frame::setFadeOutEndPos(vfc::uint32_t f_vertexLineIndex, vfc::float32_t f_pos)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_fadeOutEndPos = f_pos;
  }
}


vfc::float32_t Frame::getFadeOutEndPos(vfc::uint32_t f_vertexLineIndex) const
{
  vfc::float32_t l_returnValue = 0.0f; // Dummy value for out of range.
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    l_returnValue = m_vertexLines[f_vertexLineIndex].m_fadeOutEndPos;
  }
  return l_returnValue;
}


void Frame::setFadeIn(vfc::uint32_t f_vertexLineIndex, bool f_state)
{
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_fadeInEnabled = f_state;
  }
}


void Frame::setFadeOut(vfc::uint32_t f_vertexLineIndex, bool f_state)
{
  assert(f_vertexLineIndex < m_vertexLines.size());
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_fadeOutEnabled = f_state;
  }
}


void Frame::addControlPoint(vfc::uint32_t f_vertexLineIndex, const cc::assets::trajectory::commontypes::ControlPoint_st & f_controlPoint)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    m_vertexLines[f_vertexLineIndex].m_controlPoints.push_back(f_controlPoint);
  }
}

void Frame::updateControlPoint(vfc::uint32_t f_vertexLineIndex, const osg::Vec4f& f_color)
{
  if (f_vertexLineIndex < m_vertexLines.size())
  {
    // if ( 3 <= m_vertexLines[f_vertexLineIndex].m_controlPoints.size())
    // {
    //     m_vertexLines[f_vertexLineIndex].m_controlPoints[2u].Color = f_color;
    // }

    m_vertexLines[f_vertexLineIndex].m_controlPoints[m_vertexLines[f_vertexLineIndex].m_controlPoints.size()-1u].Color = f_color;
  }
}

// void Frame::updateColorControlPointLateralLine(vfc::uint32_t f_vertexLineIndex, const osg::Vec4f& f_color)
// {
//   if (f_vertexLineIndex < m_vertexLines.size())
//   {
//     m_vertexLines[f_vertexLineIndex].m_controlPoints[m_vertexLines[f_vertexLineIndex].m_controlPoints.size()-1].Color = f_color;
//   }
// }


void Frame::removeAllPoints()
{
  const vfc::uint32_t lc_numOfVertexLines = static_cast<vfc::uint32_t> (m_vertexLines.size());
  for (vfc::uint32_t l_vertexLineIndex = 0u; l_vertexLineIndex < lc_numOfVertexLines; ++l_vertexLineIndex)
  {
    m_vertexLines[l_vertexLineIndex].m_controlPoints.clear();
  }
}


vfc::uint32_t Frame::generateVertices(  // PRQA S 6041  // PRQA S 6042  // PRQA S 6043
  vfc::uint32_t f_vertexLineIndex,
  vfc::uint32_t f_startPointIndex,
  vfc::uint32_t f_endPointIndex,
  const osg::Vec2f& f_rotCenter,
  vfc::float32_t f_z,
  osg::Vec3Array* f_vertexArray,
  osg::Vec4Array* f_colorArray,
  VertexDistributionMode_en /* f_mode */,
  vfc::uint32_t f_numOfVertsIn,
  cc::assets::trajectory::commontypes::VehicleMovementType_en f_vehicleMovementType,
  vfc::float32_t f_translationAngle_Rad)
{
  if (f_vertexArray == nullptr || f_colorArray == nullptr)
  {
    return 0u;
  }
  static constexpr vfc::uint32_t lc_minNumOfVerts_Manual = 1u;
  //static const vfc::uint32_t lc_minNumOfVerts_Auto = 5;

  vfc::uint32_t l_numOfVerts_Out = 0u;

  if (f_vertexLineIndex < m_vertexLines.size())
  {
    if ((f_startPointIndex < m_vertexLines[f_vertexLineIndex].m_controlPoints.size()) &&
        (f_endPointIndex   < m_vertexLines[f_vertexLineIndex].m_controlPoints.size()))
    {
      osg::Vec2f   l_startPoint;
      osg::Vec2f   l_point;
      vfc::float32_t        l_angleDiff = 0.0f;
      vfc::float32_t        l_posDiff = 0.0f;
      vfc::uint32_t l_numOfVerts = 0u;
      osg::Vec4f   l_color;
      cc::assets::trajectory::helper::RotationFunctor const l_translationAngle(f_translationAngle_Rad);
      // calculate startPoint
      if (cc::assets::trajectory::commontypes::Rotation_enm == f_vehicleMovementType)
      {
        l_startPoint.x() = std::cos(m_vertexLines[f_vertexLineIndex].m_controlPoints[f_startPointIndex].Angle);
        l_startPoint.y() = std::sin(m_vertexLines[f_vertexLineIndex].m_controlPoints[f_startPointIndex].Angle);
        l_startPoint *= m_vertexLines[f_vertexLineIndex].m_radius;
        l_startPoint += f_rotCenter; // additive of both vertices for vertex from ackermannpoint to startPoint so the following points can be calculated by rotating around AckPoint
        l_angleDiff = m_vertexLines[f_vertexLineIndex].m_controlPoints[f_endPointIndex].Angle -
                      m_vertexLines[f_vertexLineIndex].m_controlPoints[f_startPointIndex].Angle;
      }
      else// if (Translation_enm == f_vehicleMovementType)
      {
        l_startPoint.x() = m_vertexLines[f_vertexLineIndex].m_controlPoints[f_startPointIndex].LongitudinalPos; // LongitudinalPos has for left and right track same length
        l_startPoint.y() = m_vertexLines[f_vertexLineIndex].m_offset;
        l_posDiff = m_vertexLines[f_vertexLineIndex].m_controlPoints[f_endPointIndex].LongitudinalPos -
                    m_vertexLines[f_vertexLineIndex].m_controlPoints[f_startPointIndex].LongitudinalPos;
      }

      //if (Manual_enm == f_mode)
      //{ // If we want to tell the function how many vertices to generate
      l_numOfVerts = f_numOfVertsIn;
      if (l_numOfVerts < lc_minNumOfVerts_Manual)
      {
        l_numOfVerts = lc_minNumOfVerts_Manual;
      }
      /*}
      else
      { // If we let the function calculate the number of points
        l_numOfVerts = static_cast<vfc::uint32_t>((std::abs(l_angleDiff) / (2.0f * osg::PI)) * static_cast<vfc::float32_t>(f_verticesPerCircle));
        if (l_numOfVerts < lc_minNumOfVerts_Auto) l_numOfVerts = lc_minNumOfVerts_Auto;
      }*/
      l_numOfVerts_Out = l_numOfVerts;

      l_color = m_vertexLines[f_vertexLineIndex].m_controlPoints[f_startPointIndex].Color;
      // calculate colors of vertices
      if (cc::assets::trajectory::commontypes::Rotation_enm == f_vehicleMovementType)
      {
        if (m_vertexLines[f_vertexLineIndex].m_fadeInEnabled)
        { l_color.a() *= getFadingInAlpha(l_startPoint, f_vertexLineIndex, f_rotCenter); }
        if (m_vertexLines[f_vertexLineIndex].m_fadeOutEnabled)
        { l_color.a() *= getFadingOutAlpha(l_startPoint, f_vertexLineIndex, f_rotCenter); }
      }
      else// if (Translation_enm == f_vehicleMovementType)
      {
        if (m_vertexLines[f_vertexLineIndex].m_fadeInEnabled)
        { l_color.a() *= getFadingInAlpha_Straight(l_startPoint, f_vertexLineIndex); }
        if (m_vertexLines[f_vertexLineIndex].m_fadeOutEnabled)
        { l_color.a() *= getFadingOutAlpha_Straight(l_startPoint, f_vertexLineIndex); }
      }

      // push the vertices on the vertexArray
      if (cc::assets::trajectory::commontypes::Rotation_enm == f_vehicleMovementType)
      {
        f_vertexArray->push_back(osg::Vec3f(l_startPoint.x(), l_startPoint.y(), f_z));
      }
      else// if (Translation_enm == f_vehicleMovementType)
      {
        osg::Vec2f l_tempStartPoint = l_startPoint;
        // rotate the points around the center 0/0
        l_translationAngle.rotate(l_tempStartPoint);
        f_vertexArray->push_back(osg::Vec3f(l_tempStartPoint.x(), l_tempStartPoint.y(), f_z));
      }

      f_colorArray->push_back(l_color);
      m_vertexLines[f_vertexLineIndex].m_controlPoints[f_startPointIndex].Index = static_cast<vfc::uint32_t> (f_vertexArray->size()) - 1u;

      // calculating regarding the number of vertices given for geometry interpolated points between Start- and Endposition
      if (cc::assets::trajectory::commontypes::Rotation_enm == f_vehicleMovementType)
      {
        for (vfc::uint32_t l_vertexIndex = 1u; l_vertexIndex < l_numOfVerts; ++l_vertexIndex)
        {
          const vfc::float32_t l_angle = (static_cast<vfc::float32_t>(l_vertexIndex) / (static_cast<vfc::float32_t>(l_numOfVerts) - 1.0f)) * l_angleDiff;
          l_point = l_startPoint;
          // rotate the points around the ackermannPoint
          cc::assets::trajectory::helper::TrajectoryHelper::Rotate2DPoint(l_point, f_rotCenter, l_angle);
          l_color = m_vertexLines[f_vertexLineIndex].m_controlPoints[f_startPointIndex].Color;
          if (m_vertexLines[f_vertexLineIndex].m_fadeInEnabled)
          { l_color.a() *= getFadingInAlpha(l_point, f_vertexLineIndex, f_rotCenter); }
          if (m_vertexLines[f_vertexLineIndex].m_fadeOutEnabled)
          { l_color.a() *= getFadingOutAlpha(l_point, f_vertexLineIndex, f_rotCenter); }
          f_vertexArray->push_back(osg::Vec3f(l_point.x(), l_point.y(), f_z));
          f_colorArray->push_back(l_color);
        }
      }
      else// if (Translation_enm == f_vehicleMovementType)
      {
        for (vfc::uint32_t l_vertexIndex = 1u; l_vertexIndex < l_numOfVerts; ++l_vertexIndex)
        {
          const vfc::float32_t l_pos = (static_cast<vfc::float32_t>(l_vertexIndex) / (static_cast<vfc::float32_t>(l_numOfVerts) - 1.0f)) * l_posDiff;
          l_point = osg::Vec2f(l_startPoint.x() + l_pos, l_startPoint.y());
          l_color = m_vertexLines[f_vertexLineIndex].m_controlPoints[f_startPointIndex].Color;
          if (m_vertexLines[f_vertexLineIndex].m_fadeInEnabled)
          { l_color.a() *= getFadingInAlpha_Straight(l_point, f_vertexLineIndex); }
          if (m_vertexLines[f_vertexLineIndex].m_fadeOutEnabled)
          { l_color.a() *= getFadingOutAlpha_Straight(l_point, f_vertexLineIndex); }
          // rotate the points around the center 0/0
          l_translationAngle.rotate(l_point);
          f_vertexArray->push_back(osg::Vec3f(l_point.x(), l_point.y(), f_z));
          f_colorArray->push_back(l_color);
        }
      }

      if (1u < l_numOfVerts)
      {
        m_vertexLines[f_vertexLineIndex].m_controlPoints[f_endPointIndex].Index = static_cast<vfc::uint32_t> (f_vertexArray->size()) - 1u;
      }
    }
  }
  return l_numOfVerts_Out;
}

vfc::uint32_t Frame::generateDrivablePathVertices(  // PRQA S 6041  // PRQA S 6042  // PRQA S 6043
  vfc::uint32_t f_vertexLineIndex,
  vfc::uint32_t f_startPointIndex,
  vfc::uint32_t f_endPointIndex,
  vfc::float32_t f_z,
  osg::Vec3Array* f_vertexArray,
  osg::Vec4Array* f_colorArray,
  vfc::uint32_t f_numOfVertsIn,
  const PathPlanning& f_pathPlanning,
  bool f_isMovingBackward,
  vfc::uint8_t f_parkOutEndIndex)
{

  static constexpr vfc::uint32_t lc_minNumOfVerts_Manual = 1u;

  vfc::uint32_t l_numOfVerts_Out = 0u;

  std::array<osg::Vec2f, 2> l_previousVertex;

  l_previousVertex[0] = osg::Vec2f(0.f,0.f);
  l_previousVertex[1] = osg::Vec2f(0.f,0.f);

  if (f_vertexLineIndex < m_vertexLines.size())
  {
    if ((f_startPointIndex < m_vertexLines[f_vertexLineIndex].m_controlPoints.size()) &&
        (f_endPointIndex   < m_vertexLines[f_vertexLineIndex].m_controlPoints.size()))
    {
      osg::Vec2f   l_startPoint;
      osg::Vec2f   l_point;
      vfc::uint32_t l_numOfVerts = 0u;
      osg::Vec4f   l_color;
      osg::Vec2f l_offset = osg::Vec2(0.f,0.f);

      l_numOfVerts = f_numOfVertsIn; // less than 50
      if (l_numOfVerts < lc_minNumOfVerts_Manual)
      {
        l_numOfVerts = lc_minNumOfVerts_Manual;
      }

      l_numOfVerts_Out = l_numOfVerts;


      // For starting point

      // vfc::uint32_t l_Index = 1u;

      // while (( l_Index <  l_numOfVerts) && (l_offset.length() < drivablepath::g_drivablePathSettings->m_thresholdDistance))
      // {
      //   // looking for first fitting nnormal
      //   l_offset =  f_pathPlanning.m_point[0] - f_pathPlanning.m_point[l_Index];
      //   l_Index ++;
      // }

      // after resampling, the quality of min gap is okay
      l_offset =  f_pathPlanning.m_point[0] - f_pathPlanning.m_point[1];

      l_offset.normalize(); //PRQA S 3804

      if (f_vertexLineIndex == 0) // left side
      {
        cc::assets::trajectory::helper::TrajectoryHelper::Rotate2DPoint(l_offset, osg::DegreesToRadians(-90.f));
      }
      else // right side
      {
        cc::assets::trajectory::helper::TrajectoryHelper::Rotate2DPoint(l_offset, osg::DegreesToRadians(90.f));
      }

      // calculate startPoint
      {
        l_startPoint = f_pathPlanning.m_point[0] + l_offset;
        l_previousVertex[f_vertexLineIndex] = l_startPoint;
      }

      l_color = m_vertexLines[f_vertexLineIndex].m_controlPoints[f_startPointIndex].Color;


      // calculate colors of vertices
      l_color.a() *= getFadingInAlpha_DrivablePath(l_startPoint, f_isMovingBackward);
      l_color.a() *= getFadingOutAlpha_DrivablePath(l_startPoint, 0, l_numOfVerts, f_parkOutEndIndex);

      // if (m_vertexLines[f_vertexLineIndex].m_fadeOutEnabled) // enable by default
      // {
      //   l_color.a() *= getFadingOutAlpha_DrivablePath(l_startPoint, 0u, l_numOfVerts);
      // }

      // need to push the start point before the loop to avoid primitives issue

      if (f_numOfVertsIn < INITIAL_NUM_VERTICES_DRIVABLE_PATH)
      {
        // adding first dummy point to vertex array to fulfill the lenght of texture coodinate to prevent geometry issue
        // and not showing this
        f_vertexArray->push_back(osg::Vec3f(l_startPoint.x(), l_startPoint.y(), f_z));
        f_colorArray->push_back(osg::Vec4f(0.f, 0.f, 0.f, 0.f));
        m_vertexLines[f_vertexLineIndex].m_controlPoints[f_startPointIndex].Index = static_cast<vfc::uint32_t> (f_vertexArray->size()) - 1u;

        for (vfc::uint32_t l_vertexIndex = f_numOfVertsIn + 1u; l_vertexIndex < INITIAL_NUM_VERTICES_DRIVABLE_PATH; ++l_vertexIndex)
        {
          // adding other dummy points
          f_vertexArray->push_back(osg::Vec3f(l_startPoint.x(), l_startPoint.y(), f_z));
          f_colorArray->push_back(osg::Vec4f(0.f, 0.f, 0.f, 0.f));
        }

        // adding TRUE starting point
        f_vertexArray->push_back(osg::Vec3f(l_startPoint.x(), l_startPoint.y(), f_z));
        f_colorArray->push_back(l_color);
      }
      else
      {
        // just adding TRUE starting point
        f_vertexArray->push_back(osg::Vec3f(l_startPoint.x(), l_startPoint.y(), f_z));
        f_colorArray->push_back(l_color);
        m_vertexLines[f_vertexLineIndex].m_controlPoints[f_startPointIndex].Index = static_cast<vfc::uint32_t> (f_vertexArray->size()) - 1u;
      }


      {
        for (vfc::uint32_t l_vertexIndex = 1u; l_vertexIndex < l_numOfVerts; ++l_vertexIndex)
        {

          if ( l_vertexIndex <  (l_numOfVerts -1) )
          {
            // calculate the normal of line (border left, border right)
            l_offset = f_pathPlanning.m_point[l_vertexIndex] - f_pathPlanning.m_point[l_vertexIndex + 1u];
          }
          else
          {
            // the last element of vetex array
            l_offset = f_pathPlanning.m_point[l_vertexIndex-1u] - f_pathPlanning.m_point[l_vertexIndex];
          }

          //if ((l_offset.length() > drivablepath::g_drivablePathSettings->m_thresholdDistance) && (l_offset.length() < 100.f) && l_vertexIndex < l_numOfVerts)
          if (l_vertexIndex < l_numOfVerts)
          {

            l_offset.normalize();  //PRQA S 3804

            if (f_vertexLineIndex == 0)
            {
              cc::assets::trajectory::helper::TrajectoryHelper::Rotate2DPoint(l_offset, osg::DegreesToRadians(-90.f));
            }
            else
            {
              cc::assets::trajectory::helper::TrajectoryHelper::Rotate2DPoint(l_offset, osg::DegreesToRadians(90.f));
            }

            l_offset = l_offset*std::abs(pc::vehicle::g_mechanicalData->m_widthWithMirrors)*0.5f;

            l_point = f_pathPlanning.m_point[l_vertexIndex] + l_offset;

            if (f_vertexLineIndex < 2u)
            {
              l_previousVertex[f_vertexLineIndex] = l_point;
            }

          }
          // else if ((f_vertexLineIndex < 2u) && (l_vertexIndex > 0u))
          // {
          //   // ignore jitter point (l_offset.length() < 0.1 meter ) because it could lead to the un-expected- normal vector
          //   l_point = l_previousVertex[f_vertexLineIndex];
          // }
          else
          {
            // do nothing
          }


          l_color = m_vertexLines[f_vertexLineIndex].m_controlPoints[f_startPointIndex].Color;

          // calculate colors of vertices
          l_color.a() *= getFadingInAlpha_DrivablePath(l_point, f_isMovingBackward);

          if (m_vertexLines[f_vertexLineIndex].m_fadeOutEnabled) // enable by default
          {
            l_color.a() *= getFadingOutAlpha_DrivablePath(l_point, l_vertexIndex, l_numOfVerts, f_parkOutEndIndex);
          }

          f_vertexArray->push_back(osg::Vec3f(l_point.x(), l_point.y(), f_z));
          f_colorArray->push_back(l_color);
        }
      }

      if (1u < l_numOfVerts)
      {
        m_vertexLines[f_vertexLineIndex].m_controlPoints[f_endPointIndex].Index = static_cast<vfc::uint32_t> (f_vertexArray->size()) - 1u;
      }
    }
  }
  return l_numOfVerts_Out;
}


//*******************************
//      |    \ |
//      |     \|
//  v2: o------o :v3
//      |\     |
//      | \    |
//      |  \   |
//      |   \  |
//      |    \ |
//      |     \|
//  v0: o------o :v1
//
//      ^      ^
//      |      |
//      |   rightStartVertex
//      |   (rightVertexLine)
//      |
//    leftStartVertex
//    (leftVertexLine)
//
void Frame::generateIndices(vfc::uint32_t f_leftVertexLineIndex,  vfc::uint32_t f_leftStartVertexIndex,
                            vfc::uint32_t f_rightVertexLineIndex, vfc::uint32_t f_rightStartVertexIndex,
                            vfc::uint32_t f_numOfVertsPerLine, osg::DrawElementsUShort* f_indexArray)
{
  if (f_numOfVertsPerLine == 0 || (f_indexArray == nullptr))
  {
    return;
  }
  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < (f_numOfVertsPerLine - 1u); ++l_vertexIndex)
  {
    f_indexArray->push_back(
      static_cast<osg::DrawElementsUShort::value_type> (m_vertexLines[f_leftVertexLineIndex].m_controlPoints[f_leftStartVertexIndex].Index + l_vertexIndex));
    f_indexArray->push_back(
      static_cast<osg::DrawElementsUShort::value_type> (m_vertexLines[f_rightVertexLineIndex].m_controlPoints[f_rightStartVertexIndex].Index + l_vertexIndex));
    f_indexArray->push_back(
      static_cast<osg::DrawElementsUShort::value_type> (m_vertexLines[f_leftVertexLineIndex].m_controlPoints[f_leftStartVertexIndex].Index + l_vertexIndex + 1u));
    f_indexArray->push_back(
      static_cast<osg::DrawElementsUShort::value_type> (m_vertexLines[f_leftVertexLineIndex].m_controlPoints[f_leftStartVertexIndex].Index + l_vertexIndex + 1u));
    f_indexArray->push_back(
      static_cast<osg::DrawElementsUShort::value_type> (m_vertexLines[f_rightVertexLineIndex].m_controlPoints[f_rightStartVertexIndex].Index + l_vertexIndex));
    f_indexArray->push_back(
      static_cast<osg::DrawElementsUShort::value_type> (m_vertexLines[f_rightVertexLineIndex].m_controlPoints[f_rightStartVertexIndex].Index + l_vertexIndex + 1u));
  }
}


void Frame::generateDIIndices(osg::DrawElementsUShort* f_indexArray, cc::assets::trajectory::commontypes::Side_en f_side,  // PRQA S 6042
                              vfc::uint32_t f_index_Line0_Vert0, vfc::uint32_t f_index_Line0_Vert1,
                              vfc::uint32_t f_index_Line1_Vert0, vfc::uint32_t f_index_Line1_Vert1,
                              vfc::uint32_t f_index_Line2_Vert0, vfc::uint32_t f_index_Line2_Vert1,
                              vfc::uint32_t f_index_Line2_Vert2, vfc::uint32_t f_index_Line2_Vert3,
                              vfc::uint32_t f_index_Line3_Vert0, vfc::uint32_t f_index_Line3_Vert1,
                              vfc::uint32_t f_index_Line4_Vert0, vfc::uint32_t f_index_Line4_Vert1,
                              vfc::uint32_t f_index_Line5_Vert0, vfc::uint32_t f_index_Line5_Vert1)
{
  if (f_indexArray == nullptr)
  {
    return;
  }
  enum class TmpFace : vfc::int32_t
  {
    NUM_OF_FACES_ON_DI = 16,
    NUM_OF_INDICES_ON_DI = (3 * NUM_OF_FACES_ON_DI)
  };
  std::array<vfc::uint32_t, static_cast<std::size_t>(TmpFace::NUM_OF_INDICES_ON_DI)> l_indices; // PRQA S 4102

  // Face 0
  l_indices[0u] = m_vertexLines[0u].m_controlPoints[f_index_Line0_Vert0].Index;
  l_indices[1u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert0].Index;
  l_indices[2u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert1].Index;
  // Face 1
  l_indices[3u] = m_vertexLines[0u].m_controlPoints[f_index_Line0_Vert0].Index;
  l_indices[4u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert1].Index;
  l_indices[5u] = m_vertexLines[0u].m_controlPoints[f_index_Line0_Vert1].Index;
  // Face 2
  l_indices[6u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert0].Index;
  l_indices[7u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert0].Index;
  l_indices[8u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert1].Index;
  // Face 3
  l_indices[9u]  = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert0].Index;
  l_indices[10u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert1].Index;
  l_indices[11u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert2].Index;
  // Face 4
  l_indices[12u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert0].Index;
  l_indices[13u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert2].Index;
  l_indices[14u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert1].Index;
  // Face 5
  l_indices[15u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert1].Index;
  l_indices[16u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert2].Index;
  l_indices[17u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert3].Index;
  // Face 6
  l_indices[18u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert0].Index;
  l_indices[19u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert0].Index;
  l_indices[20u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert1].Index;
  // Face 7
  l_indices[21u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert1].Index;
  l_indices[22u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert0].Index;
  l_indices[23u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert0].Index;
  // Face 8
  l_indices[24u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert1].Index;
  l_indices[25u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert0].Index;
  l_indices[26u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert1].Index;
  // Face 9
  l_indices[27u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert1].Index;
  l_indices[28u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert1].Index;
  l_indices[29u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert2].Index;
  // Face 10
  l_indices[30u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert2].Index;
  l_indices[31u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert1].Index;
  l_indices[32u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert1].Index;
  // Face 11
  l_indices[33u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert2].Index;
  l_indices[34u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert1].Index;
  l_indices[35u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert3].Index;
  // Face 12
  l_indices[36u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert0].Index;
  l_indices[37u] = m_vertexLines[5u].m_controlPoints[f_index_Line5_Vert0].Index;
  l_indices[38u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert0].Index;
  // Face 13
  l_indices[39u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert0].Index;
  l_indices[40u] = m_vertexLines[5u].m_controlPoints[f_index_Line5_Vert0].Index;
  l_indices[41u] = m_vertexLines[5u].m_controlPoints[f_index_Line5_Vert1].Index;
  // Face 14
  l_indices[42u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert0].Index;
  l_indices[43u] = m_vertexLines[5u].m_controlPoints[f_index_Line5_Vert1].Index;
  l_indices[44u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert1].Index;
  // Face 15
  l_indices[45u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert1].Index;
  l_indices[46u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert1].Index;
  l_indices[47u] = m_vertexLines[5u].m_controlPoints[f_index_Line5_Vert1].Index;

  vfc::int32_t l_startIndex = 0;
  //int l_endIndex;
  vfc::int32_t l_step = 0;
  static constexpr vfc::int32_t lc_lowLimit  = 0;
  static constexpr vfc::int32_t lc_highLimit = static_cast<vfc::int32_t>(TmpFace::NUM_OF_INDICES_ON_DI) - 1;
  if (cc::assets::trajectory::commontypes::Left_enm == f_side)
  {
    l_startIndex = lc_lowLimit;
    //l_endIndex   = lc_highLimit;
    l_step       = 1;
  }
  else// if (Right_enm == f_side)
  {
    l_startIndex = lc_highLimit;
    //l_endIndex   = lc_lowLimit;
    l_step       = -1;
  }

  for (vfc::int32_t l_index = l_startIndex; ((lc_lowLimit <= l_index) && (l_index <= lc_highLimit)); l_index += l_step)
  {
    f_indexArray->push_back(static_cast<osg::DrawElementsUShort::value_type> (l_indices[static_cast<vfc::uint32_t>(l_index)]));
  }
}


void Frame::generateDIIndices_Tex(osg::DrawElementsUShort* f_indexArray, cc::assets::trajectory::commontypes::Side_en f_side,  // PRQA S 6042
                                  vfc::uint32_t f_index_Line0_Vert0, vfc::uint32_t f_index_Line0_Vert1,
                                  vfc::uint32_t f_index_Line1_Vert0, vfc::uint32_t f_index_Line1_Vert1,
                                  vfc::uint32_t f_index_Line2_Vert0, vfc::uint32_t f_index_Line2_Vert1,
                                  vfc::uint32_t f_index_Line3_Vert0, vfc::uint32_t f_index_Line3_Vert1,
                                  vfc::uint32_t f_index_Line4_Vert0, vfc::uint32_t f_index_Line4_Vert1)
{
  if (f_indexArray == nullptr)
  {
    return;
  }
  enum class TmpIndices : vfc::int32_t
  {
    NUM_OF_FACES_ON_DI_TEX = 12,
    NUM_OF_INDICES_ON_DI_TEX = (3 * NUM_OF_FACES_ON_DI_TEX)
  };
  std::array<vfc::uint32_t, static_cast<std::size_t>(TmpIndices::NUM_OF_INDICES_ON_DI_TEX)> l_indices; // PRQA S 4102

  // Face 0
  l_indices[0u] = m_vertexLines[0u].m_controlPoints[f_index_Line0_Vert0].Index;
  l_indices[1u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert1].Index;
  l_indices[2u] = m_vertexLines[0u].m_controlPoints[f_index_Line0_Vert1].Index;
  // Face 1
  l_indices[3u] = m_vertexLines[0u].m_controlPoints[f_index_Line0_Vert0].Index;
  l_indices[4u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert0].Index;
  l_indices[5u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert1].Index;
  // Face 2
  l_indices[6u] = m_vertexLines[0u].m_controlPoints[f_index_Line0_Vert0].Index;
  l_indices[7u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert0].Index;
  l_indices[8u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert0].Index;
  // Face 3
  l_indices[9u]  = m_vertexLines[0u].m_controlPoints[f_index_Line0_Vert1].Index;
  l_indices[10u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert1].Index;
  l_indices[11u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert1].Index;
  // Face 4
  l_indices[12u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert1].Index;
  l_indices[13u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert1].Index;
  l_indices[14u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert1].Index;
  // Face 5
  l_indices[15u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert1].Index;
  l_indices[16u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert1].Index;
  l_indices[17u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert1].Index;
  // Face 6
  l_indices[18u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert0].Index;
  l_indices[19u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert0].Index;
  l_indices[20u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert0].Index;
  // Face 7
  l_indices[21u] = m_vertexLines[2u].m_controlPoints[f_index_Line2_Vert0].Index;
  l_indices[22u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert0].Index;
  l_indices[23u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert0].Index;
  // Face 8
  l_indices[24u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert1].Index;
  l_indices[25u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert0].Index;
  l_indices[26u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert1].Index;
  // Face 9
  l_indices[27u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert0].Index;
  l_indices[28u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert0].Index;
  l_indices[29u] = m_vertexLines[4u].m_controlPoints[f_index_Line4_Vert1].Index;
  // Face 10
  l_indices[30u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert1].Index;
  l_indices[31u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert0].Index;
  l_indices[32u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert1].Index;
  // Face 11
  l_indices[33u] = m_vertexLines[1u].m_controlPoints[f_index_Line1_Vert0].Index;
  l_indices[34u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert0].Index;
  l_indices[35u] = m_vertexLines[3u].m_controlPoints[f_index_Line3_Vert1].Index;

  vfc::int32_t l_startIndex = 0;
  //int l_endIndex = 0;
  vfc::int32_t l_step = 0;
  static constexpr vfc::int32_t lc_lowLimit  = 0;
  static constexpr vfc::int32_t lc_highLimit = static_cast<vfc::int32_t>(TmpIndices::NUM_OF_INDICES_ON_DI_TEX) - 1;
  if (cc::assets::trajectory::commontypes::Left_enm == f_side)
  {
    l_startIndex = lc_lowLimit;
    //l_endIndex   = lc_highLimit;
    l_step       = 1;
  }
  else// if (Right_enm == f_side)
  {
    l_startIndex = lc_highLimit;
    //l_endIndex   = lc_lowLimit;
    l_step       = -1;
  }

  for (vfc::int32_t l_index = l_startIndex; ((lc_lowLimit <= l_index) && (l_index <= lc_highLimit)); l_index += l_step)
  {
    f_indexArray->push_back(static_cast<osg::DrawElementsUShort::value_type> (l_indices[static_cast<vfc::uint32_t>(l_index)]));
  }
}


vfc::float32_t Frame::getFadingInAlpha(const osg::Vec2f& f_vertex, vfc::uint32_t f_vertexLineIndex, const osg::Vec2f& f_ackermannPoint) const
{
  // Transform the point so that the Ackermann point will become the origin.
  osg::Vec2f l_vertex = f_vertex - f_ackermannPoint;
  // atan2f(y, x);
  const vfc::float32_t l_angleFromAckP = std::atan2(l_vertex.y(), l_vertex.x());

  //Smoothstep();
  //Linear();
  vfc::float32_t l_alpha = getSmallerAngle(l_angleFromAckP - m_vertexLines[f_vertexLineIndex].m_fadeInEndAngle) /
    getSmallerAngle(m_vertexLines[f_vertexLineIndex].m_fadeInStartAngle - m_vertexLines[f_vertexLineIndex].m_fadeInEndAngle);
  if (l_alpha < 0.0f)
  {
    l_alpha = 0.0f;
  }
  if (1.0f < l_alpha)
  {
    l_alpha = 1.0f;
  }

  return l_alpha;
}


vfc::float32_t Frame::getFadingOutAlpha(const osg::Vec2f& f_vertex, vfc::uint32_t f_vertexLineIndex, const osg::Vec2f & f_ackermannPoint) const
{
  // Transform the point so that the Ackermann point will become the origin.
  osg::Vec2f l_vertex = f_vertex - f_ackermannPoint;
  // atan2f(y, x);
  const vfc::float32_t l_angleFromAckP = std::atan2(l_vertex.y(), l_vertex.x());

  //Smoothstep();
  //Linear();
  vfc::float32_t l_alpha = 1.0f -
            getSmallerAngle(l_angleFromAckP - m_vertexLines[f_vertexLineIndex].m_fadeOutStartAngle) /
            getSmallerAngle(m_vertexLines[f_vertexLineIndex].m_fadeOutEndAngle - m_vertexLines[f_vertexLineIndex].m_fadeOutStartAngle);
  if (l_alpha < 0.0f)
  {
    l_alpha = 0.0f;
  }
  if (1.0f < l_alpha)
  {
  l_alpha = 1.0f;
  }

  return l_alpha;
}


vfc::float32_t Frame::getFadingInAlpha_Straight(const osg::Vec2f& f_vertex, vfc::uint32_t f_vertexLineIndex) const
{
  //Smoothstep();
  //Linear();
  vfc::float32_t l_alpha = (f_vertex.x() - m_vertexLines[f_vertexLineIndex].m_fadeInEndPos) /
    (m_vertexLines[f_vertexLineIndex].m_fadeInStartPos - m_vertexLines[f_vertexLineIndex].m_fadeInEndPos);
  if (l_alpha < 0.0f)
  {
    l_alpha = 0.0f;
  }
  if (1.0f < l_alpha)
  {
    l_alpha = 1.0f;
  }
  return l_alpha;
}


vfc::float32_t Frame::getFadingOutAlpha_Straight(const osg::Vec2f& f_vertex, vfc::uint32_t f_vertexLineIndex) const
{
  //Smoothstep();
  //Linear();
  vfc::float32_t l_alpha = 1.0f -
            (f_vertex.x() - m_vertexLines[f_vertexLineIndex].m_fadeOutStartPos) /
            (m_vertexLines[f_vertexLineIndex].m_fadeOutEndPos - m_vertexLines[f_vertexLineIndex].m_fadeOutStartPos);
  if (l_alpha < 0.0f)
  {
    l_alpha = 0.0f;
  }
  if (1.0f < l_alpha)
  {
    l_alpha = 1.0f;
  }

  return l_alpha;
}


// The atan2f function returns angles ranging from -Pi to +Pi.
// This function is for returning the smaller angle difference between two angles.
// Input:  Difference between two angles [rad] (-2Pi to +2Pi).
// Output: Angle difference which ranges from -Pi to +Pi [rad].
vfc::float32_t Frame::getSmallerAngle(vfc::float32_t f_angle)
{
  vfc::float32_t l_angle = f_angle;
  if (static_cast<vfc::float32_t> (osg::PI) < f_angle)
  {
    l_angle = f_angle - static_cast<vfc::float32_t> (2.0 * osg::PI);
  }
  if (f_angle < static_cast<vfc::float32_t> (-osg::PI))
  {
    l_angle = f_angle + static_cast<vfc::float32_t> (2.0 * osg::PI);
  }
  return l_angle;
}

vfc::float32_t Frame::getFadingInAlpha_DrivablePath(const osg::Vec2f& f_vertex, bool f_isMovingBackward)
{
  vfc::float32_t l_alpha = 1.0f;

  if(f_isMovingBackward == true)
  {
      l_alpha = (f_vertex.x() > 0.f) ? 0.f : 1.f;
  }
  else  // on moving forward
  {
    // the vehicle model couldn't be large enough to cover the area of the drivable path
    l_alpha = (f_vertex.x() <= 0.1f) ? 0.f : 1.f;
  }


  return l_alpha;

}

vfc::float32_t Frame::getFadingOutAlpha_DrivablePath(const osg::Vec2f& /*f_vertex*/, vfc::uint32_t f_vertexIndex, vfc::uint32_t f_vertexBufferSize, vfc::uint8_t f_parkOutEndIndex)
{
  vfc::float32_t l_alpha = 1.0f;
  //For parking in, we will use full length
  if (f_parkOutEndIndex > 50)
  {
    const vfc::float32_t invDenom = static_cast<vfc::float32_t>(f_vertexBufferSize);
    std::uint32_t gap = f_vertexBufferSize - f_vertexIndex;
    const vfc::float32_t diff     = static_cast<vfc::float32_t>(gap);
    l_alpha = diff / invDenom;
    //l_alpha = (static_cast<vfc::float32_t>(f_vertexBufferSize -f_vertexIndex)/(static_cast<vfc::float32_t>(f_vertexBufferSize)));  //PRQA S 3131

    if (l_alpha < 0.0f)
    {
      l_alpha = 0.0f;
    }
    else if (1.0f < l_alpha)
    {
      l_alpha = 1.0f;
    }
    else
    {
      // do nothing here
    }
  }
  else //For parking out
  {
    if (f_vertexIndex <= f_parkOutEndIndex)
    {
      std::uint8_t index_gap = f_parkOutEndIndex - f_vertexIndex;
      const vfc::float32_t diff  = static_cast<vfc::float32_t>(index_gap);
      const vfc::float32_t denom = static_cast<vfc::float32_t>(f_parkOutEndIndex);
      l_alpha = diff / denom;
      //l_alpha = (static_cast<vfc::float32_t>(f_parkOutEndIndex -f_vertexIndex)/(static_cast<vfc::float32_t>(f_parkOutEndIndex)));  //PRQA S 3131

      if (l_alpha < 0.0f)
      {
        l_alpha = 0.0f;
      }
      else if (1.0f < l_alpha)
      {
        l_alpha = 1.0f; //Total transperency
      }
      else
      {
        // do nothing here
      }
    }
    else
    {
      l_alpha = 0.0f;
    }

  }

  return l_alpha;

}


} // frame
} // namespace trajectory
} // namespace assets
} // namespace cc
