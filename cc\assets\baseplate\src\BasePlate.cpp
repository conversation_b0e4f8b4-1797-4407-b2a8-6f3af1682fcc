//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EIK2LR Karim Eid (CC-DA/EAV1)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  BasePlate.cpp
/// @brief
//=============================================================================

#include "cc/assets/baseplate/inc/BasePlate.h"

#include "osg/Geometry"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/util/math/inc/FloatComp.h"   //for isZero vfc::float32_t compare
#include "pc/svs/util/math/inc/VecMath.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/osgx/inc/MemberWrapper.h"


using pc::util::logging::g_EngineContext;

namespace cc
{
namespace assets
{
namespace baseplate
{
using pc::util::logging::g_EngineContext;

//!
//! BasePlateData
//!
class BasePlateData : public pc::util::coding::ISerializable
{
public:

  BasePlateData()
    : m_offsetFront(0.08f, 0.04f) // PRQA S 2323 // PRQA S 4052
    , m_offsetCenter(0.0f, 0.04f) // PRQA S 2323
    , m_offsetRear(0.03f, 0.04f) // PRQA S 2323
    , m_offsetZ(0.0f) // PRQA S 2323
  {
  }

  SERIALIZABLE(BasePlateData) // PRQA S 3401
  {
    ADD_MEMBER(osg::Vec2f, offsetFront);
    ADD_MEMBER(osg::Vec2f, offsetCenter);
    ADD_MEMBER(osg::Vec2f, offsetRear);
    ADD_MEMBER(vfc::float32_t, offsetZ);
  }

  osg::Vec2f m_offsetFront;
  osg::Vec2f m_offsetCenter;
  osg::Vec2f m_offsetRear;
  vfc::float32_t m_offsetZ;
};


pc::util::coding::Item<BasePlateData> g_basePlateData("BasePlate");


//!
//! BasePlate
//!
BasePlate::BasePlate() :
  m_vertexArray(new osg::Vec3Array(static_cast<vfc::uint32_t>(NUM_BASE_PLATE_POINTS))), // PRQA S 2323 // PRQA S 4052
  m_textureArray(new osg::Vec2Array(static_cast<vfc::uint32_t>(NUM_BASE_PLATE_POINTS))), // PRQA S 2323
  m_basePlateCorners(new osg::Vec3Array(static_cast<vfc::uint32_t>(NUM_BASE_PLATE_POINTS))), // PRQA S 2323
  m_stitchingCorners(new osg::Vec3Array(static_cast<vfc::uint32_t>(NUM_CORNER_POINTS))), // PRQA S 2323
  m_geometry(nullptr), // PRQA S 2323
  m_borderWidth(0.0f, 0.0f, 0.0f, 0.0f), // PRQA S 2323
  m_bounds() // PRQA S 2323
{
  m_geometry = new osg::Geometry();
  m_geometry->setUseDisplayList(false);
  m_geometry->setUseVertexBufferObjects(true);
  m_geometry->setVertexArray(m_vertexArray.get());
  m_geometry->setTexCoordArray(0u, m_textureArray, osg::Array::BIND_PER_VERTEX);

  osg::DrawElementsUByte* const indices = new osg::DrawElementsUByte(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), 12u);

  // first triangle lower part
  (*indices)[0u] = 0u;
  (*indices)[1u] = 1u;
  (*indices)[2u] = 3u;
  // second triangle lower part
  (*indices)[3u] = 3u;
  (*indices)[4u] = 1u;
  (*indices)[5u] = 4u;
  // third triangle upper part
  (*indices)[6u] = 1u;
  (*indices)[7u] = 2u;
  (*indices)[8u] = 4u;
  // fourth triangle upper part
  (*indices)[9u] = 4u;
  (*indices)[10u] = 2u;
  (*indices)[11u] = 5u;

  m_geometry->addPrimitiveSet(indices);  // PRQA S 3803
  osg::Geode::addDrawable(m_geometry);  // PRQA S 3803

}


BasePlate::BasePlate(const BasePlate& f_basePlate, const osg::CopyOp& f_copyOp) :
  osg::Geode(f_basePlate, f_copyOp), // PRQA S 2323
  m_vertexArray(dynamic_cast<osg::Vec3Array*> (f_copyOp(f_basePlate.m_vertexArray.get()))), // PRQA S 2323 // PRQA S 3400
  m_textureArray(dynamic_cast<osg::Vec2Array*> (f_copyOp(f_basePlate.m_textureArray.get()))), // PRQA S 2323 // PRQA S 3400
  m_basePlateCorners(dynamic_cast<osg::Vec3Array*> (f_copyOp(f_basePlate.m_basePlateCorners.get()))), // PRQA S 2323 // PRQA S 3400
  m_stitchingCorners(dynamic_cast<osg::Vec3Array*> (f_copyOp(f_basePlate.m_stitchingCorners.get()))), // PRQA S 2323 // PRQA S 3400
  m_geometry(f_basePlate.m_geometry), // PRQA S 2323
  m_borderWidth(f_basePlate.m_borderWidth), // PRQA S 2323
  m_bounds(f_basePlate.m_bounds) // PRQA S 2323
{
  m_geometry = dynamic_cast<osg::Geometry*> (getDrawable(0u)); // PRQA S 3400
  m_vertexArray = dynamic_cast<osg::Vec3Array*> (m_geometry->getVertexArray()); // PRQA S 3400
  m_textureArray = dynamic_cast<osg::Vec2Array*> (m_geometry->getTexCoordArray(0u)); // PRQA S 3400
}


BasePlate::~BasePlate() = default;


const osg::Vec3Array* BasePlate::getStitchingCorners() const
{
  return m_stitchingCorners.get();
}


const pc::util::Box2D BasePlate::getBounds() const
{
  return m_bounds;
}


void BasePlate::calculateTextureCoordinates(const osg::Vec3& f_lowerLeftCorner, const osg::Vec3& f_upperRightCorner)
{

  const vfc::float32_t width = std::abs((f_upperRightCorner - f_lowerLeftCorner).x());
  const vfc::float32_t height = std::abs((f_upperRightCorner - f_lowerLeftCorner).y());

  if (true == isZero(width) || true == isZero(height))
  {
    return;
  }

  for (vfc::uint32_t i = 0u; i < static_cast<vfc::uint32_t>(NUM_BASE_PLATE_POINTS); ++i)
  {
    const vfc::float32_t xTexPos = ((*m_vertexArray)[i].x() - f_lowerLeftCorner.x()) / width;
    const vfc::float32_t yTexPos = ((*m_vertexArray)[i].y() - f_lowerLeftCorner.y()) / height;
    (*m_textureArray)[i].set(xTexPos, yTexPos);
  }
  m_textureArray->dirty();
}


void BasePlate::updateBasePlate()
{
  (*m_vertexArray)[0u] = osg::Vec3f((*m_basePlateCorners)[5u].x() + m_borderWidth.front(),
      (*m_basePlateCorners)[5u].y() + m_borderWidth.left(), g_basePlateData->m_offsetZ);  // left up
  (*m_vertexArray)[1u] = osg::Vec3f((*m_basePlateCorners)[4u].x(),
      (*m_basePlateCorners)[4u].y() + m_borderWidth.left(), g_basePlateData->m_offsetZ);  // left middle
  (*m_vertexArray)[2u] = osg::Vec3f((*m_basePlateCorners)[3u].x() - m_borderWidth.rear(),
      (*m_basePlateCorners)[3u].y() + m_borderWidth.left(), g_basePlateData->m_offsetZ);  // left down
  (*m_vertexArray)[3u] = osg::Vec3f((*m_basePlateCorners)[2u].x() + m_borderWidth.front(),
      (*m_basePlateCorners)[2u].y() - m_borderWidth.right(), g_basePlateData->m_offsetZ); // right up
  (*m_vertexArray)[4u] = osg::Vec3f((*m_basePlateCorners)[1u].x(),
      (*m_basePlateCorners)[1u].y() - m_borderWidth.right(), g_basePlateData->m_offsetZ); // right middle
  (*m_vertexArray)[5u] = osg::Vec3f((*m_basePlateCorners)[0u].x() - m_borderWidth.rear(),
      (*m_basePlateCorners)[0u].y() - m_borderWidth.right(), g_basePlateData->m_offsetZ); // right down

  m_vertexArray->dirty();
  m_geometry->dirtyBound();

  // find bounds of base plate
  vfc::float32_t l_minX = std::numeric_limits<vfc::float32_t>::max();
  vfc::float32_t l_minY = std::numeric_limits<vfc::float32_t>::max();
  vfc::float32_t l_maxX = std::numeric_limits<vfc::float32_t>::min();
  vfc::float32_t l_maxY = std::numeric_limits<vfc::float32_t>::min();
  for(vfc::uint32_t i = 0u; i < static_cast<vfc::uint32_t>(NUM_BASE_PLATE_POINTS); ++i)
  {
    const osg::Vec3f& l_basePlateCorner = (*m_basePlateCorners)[i];
    pc::util::updateIfLess(l_minX, l_basePlateCorner.x());
    pc::util::updateIfLess(l_minY, l_basePlateCorner.y());
    pc::util::updateIfGreater(l_maxX, l_basePlateCorner.x());
    pc::util::updateIfGreater(l_maxY, l_basePlateCorner.y());
  }
  m_bounds = pc::util::Box2D(l_minX, l_minY, l_maxX, l_maxY);
}


void BasePlate::calculateBasePlateCorners(const osg::Vec3* f_corners)
{
  // calculate the base plate corners  depending on the adaptive mesh corners
  std::vector<vfc::float32_t> l_yVector(static_cast<vfc::uint32_t>(NUM_BASE_PLATE_POINTS));
  std::vector<vfc::float32_t> l_maxXDownVector(2u);
  std::vector<vfc::float32_t> l_maxXUpVector(2u);
  std::vector<vfc::float32_t> l_maxYVector(2u);

  for(vfc::uint32_t i = 0u; i < static_cast<vfc::uint32_t>(NUM_BASE_PLATE_POINTS); ++i)
  {
    l_yVector[i] = std::abs(f_corners[i].y());
  }

  l_maxXDownVector[0u] = std::abs(f_corners[0u].x());
  l_maxXDownVector[1u] = std::abs(f_corners[2u].x());

  l_maxXUpVector[0u] = std::abs(f_corners[1u].x());
  l_maxXUpVector[1u] = std::abs(f_corners[3u].x());

  l_maxYVector[0u] = std::max(l_yVector[0u], l_yVector[2u]); // left vs. right DOWN
  l_maxYVector[1u] = std::max(l_yVector[1u], l_yVector[3u]); // left vs. right UP

  const vfc::float32_t l_maxXDown = pc::util::maxElement(l_maxXDownVector);
  const vfc::float32_t l_maxXUp = pc::util::maxElement(l_maxXUpVector);

  // calculate the base plate corners
  (*m_basePlateCorners)[0u].x() = -l_maxXDown - g_basePlateData->m_offsetRear.x();        // right down
  (*m_basePlateCorners)[1u].x() = f_corners[4u].x() + g_basePlateData->m_offsetCenter.x(); // right middle
  (*m_basePlateCorners)[2u].x() = l_maxXUp + g_basePlateData->m_offsetFront.x();          // right up
  (*m_basePlateCorners)[3u].x() = -l_maxXDown - g_basePlateData->m_offsetRear.x();        // left down
  (*m_basePlateCorners)[4u].x() = f_corners[5u].x() + g_basePlateData->m_offsetCenter.x(); // left middle
  (*m_basePlateCorners)[5u].x() = l_maxXUp + g_basePlateData->m_offsetFront.x();          // left up

  (*m_basePlateCorners)[0u].y() = -l_maxYVector[0u] - g_basePlateData->m_offsetRear.y();   // right down
  (*m_basePlateCorners)[1u].y() = f_corners[4u].y() - g_basePlateData->m_offsetCenter.y(); // right middle
  (*m_basePlateCorners)[2u].y() = -l_maxYVector[1u] - g_basePlateData->m_offsetFront.y();  // right up
  (*m_basePlateCorners)[3u].y() = l_maxYVector[0u] + g_basePlateData->m_offsetRear.y();    // left down
  (*m_basePlateCorners)[4u].y() = f_corners[5u].y() + g_basePlateData->m_offsetCenter.y(); // left middle
  (*m_basePlateCorners)[5u].y() = l_maxYVector[1u] + g_basePlateData->m_offsetFront.y();   // left up

  //store the corners for later use and push them accordingly the base plate corners
  (*m_stitchingCorners)[0u] = (*m_basePlateCorners)[5u]; // FrontLeft corner
  (*m_stitchingCorners)[1u] = (*m_basePlateCorners)[2u]; // FrontRight corner
  (*m_stitchingCorners)[2u] = (*m_basePlateCorners)[0u]; // RearRight corner
  (*m_stitchingCorners)[3u] = (*m_basePlateCorners)[3u]; // RearLeft corner
}


const BasePlate::BorderWidth BasePlate::getBorderWidth() const
{
  return m_borderWidth;
}


void BasePlate::setBorderWidth(const BorderWidth& f_borderWidth)
{
  m_borderWidth = f_borderWidth;
  updateBasePlate();
}


} // namespace baseplate
} // namespace assets
} // namespace cc
