//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC BYD DENZA&MR
/// @file  ArrowTrajectory.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_ARROW_TRAJECTORY_ACTIONPOINT
#define CC_ASSETS_ARROW_TRAJECTORY_ACTIONPOINT

#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"

#include <osg/Geode>
#include <osg/MatrixTransform>
#include <osg/NodeCallback>
#include <osgAnimation/EaseMotion>


namespace cc
{
namespace assets
{
namespace trajectory
{

  enum AnimationStyle : vfc::uint32_t
  {
    NONE_EFFECT = 0u,
    FLASHING_EFFECT = 1u,
    FADEIN_FADEOUT_EFFECT = 2u,
    AUGMENTED_WAVE_EFFECT = 3u
  };

  const vfc::uint32_t NUM_ARROWS = 3u;

class ArrowTrajectory;

class ArrowTrajectorySettings;

class ArrowTrajectoryModel;

extern pc::util::coding::Item<ArrowTrajectorySettings>   g_arrowTrajectorySettings;


osg::Vec4f computeAtlasTexCoord(const osg::Vec2ui& f_gridSize, const osg::Vec2ui& f_selection);

//!
//! ArrowTrajectoryNode interface

//!
class ArrowTrajectoryNode
{
public:

  virtual ~ArrowTrajectoryNode() {};
 
  virtual void updateGeometry() = 0;
  virtual void updateOrientation() = 0;
  virtual void setPosition(const osg::Vec3f& f_position) = 0;
  virtual void setAngleRad(const vfc::float32_t& f_angle_rad) = 0;
  virtual void setAlpha(const vfc::float32_t& f_alpha) = 0;
  virtual void setSize(const osg::Vec3f& f_size) = 0;
};

//!
//! ArrowTrajectoryPlane
//!
class ArrowTrajectoryPlane : public osg::MatrixTransform, public ArrowTrajectoryNode
{
public:

  ArrowTrajectoryPlane();

  ArrowTrajectoryPlane(const ArrowTrajectoryPlane& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::trajectory, ArrowTrajectoryPlane);  // PRQA S 2504

  const osg::Vec3f& getPosition() const
  {
    return m_position;
  }

  void setSize(const osg::Vec3f& f_size)
  {
    m_size = f_size;
  }

  void setPosition(const osg::Vec3f& f_position)
  {
    m_position = f_position;
  }
  
  const vfc::float32_t& getAngleRad() const
  {
    return m_angle_rad;
  }

  void setAngleRad(const vfc::float32_t& f_angle_rad)
  {
    m_angle_rad = f_angle_rad;
  }

  void setAlpha(const vfc::float32_t& f_alpha)
  {
    m_alpha = f_alpha;
  }

  void updateGeometry()
  {
    m_updateGeometry = true;
  }

  void updateOrientation()
  {
    m_updateOrientation = true;
  }

  void updateShaderUniform();

  virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:
    ~ArrowTrajectoryPlane() = default;

private:
    //! Copy constructor is not permitted.
    ArrowTrajectoryPlane(const ArrowTrajectoryPlane& other); // = delete
    //! Copy assignment operator is not permitted.
    ArrowTrajectoryPlane& operator=(const ArrowTrajectoryPlane& other); // = delete

private:
  osg::Vec3f m_size;
  osg::Vec3f m_position;
  vfc::float32_t m_angle_rad;
  bool       m_updateGeometry;
  bool       m_updateOrientation;
  osg::observer_ptr<osg::Geode> m_planeGeode;
  vfc::float32_t m_alpha;

};

//!
//! ArrowTrajectory
//!
class ArrowTrajectory : public osg::MatrixTransform
{
public:

  ArrowTrajectory();

  ArrowTrajectory(pc::core::Framework* f_framework, const TrajectoryParams_st& f_params, bool f_isArrowAVM);

  ArrowTrajectory(const ArrowTrajectory& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::trajectory, ArrowTrajectory);  // PRQA S 2504

  bool getVisibility()
  {
    return (m_visible);
  }

  void setVisibility(bool f_visible)
  {
    m_visible = f_visible;
  }

  void calculateColorAnimation();

  bool readDynamicInputs();

  void update();

  virtual void traverse(osg::NodeVisitor& f_nv);

private:

protected:
    ~ArrowTrajectory() = default;

private:
    //! Copy constructor is not permitted.
    ArrowTrajectory(const ArrowTrajectory& other); // = delete
    //! Copy assignment operator is not permitted.
    ArrowTrajectory& operator=(const ArrowTrajectory& other); // = delete

private:
    bool                                   m_visible;
    osg::ref_ptr<osg::Switch>              m_arrowIcon;
    std::array<osg::Vec2f, NUM_ARROWS>     m_position;
    std::array<vfc::float32_t, NUM_ARROWS> m_angle_rad;
    std::array<vfc::float32_t, NUM_ARROWS> m_alpha;
    AnimationStyle m_animationStyle;
    vfc::float32_t m_animationCounter;

  osg::ref_ptr<ArrowTrajectoryModel> m_arrowTrajectoryModel;
  pc::core::Framework* m_framework;
  bool m_isArrowAVM;

};


class ArrowTrajectoryModel : public cc::assets::trajectory::GeneralTrajectoryLine
{
public:


  ArrowTrajectoryModel(
    pc::core::Framework* f_framework,
    vfc::float32_t f_height,
    const cc::assets::trajectory::TrajectoryParams_st& f_trajParams);

  osg::Vec2f getPosition(vfc::uint32_t f_arrow_index)
  {
    return m_position[f_arrow_index];
  }

  vfc::float32_t getAngle_rad(vfc::uint32_t f_arrow_index)
  {
    return m_angle_rad[f_arrow_index];
  }

  virtual void generateVertexData() 
  { 
    // just dummy is needed
  }

  void updateAkermanPos();

  void updateStaticArrow();

protected:

  virtual ~ArrowTrajectoryModel() = default;

  const vfc::uint32_t mc_numOfVerts;
  vfc::float32_t m_geomHeight;

private:

  //! Copy constructor is not permitted.
  ArrowTrajectoryModel (const ArrowTrajectoryModel& other); // = delete
  //! Copy assignment operator is not permitted.
  ArrowTrajectoryModel& operator=(const ArrowTrajectoryModel& other); // = delete

  std::array<osg::Vec2f,     NUM_ARROWS> m_position;
  std::array<vfc::float32_t, NUM_ARROWS> m_angle_rad;
};

} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_ARROW_TRAJECTORY_ACTIONPOINT
