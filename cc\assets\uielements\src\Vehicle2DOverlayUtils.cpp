//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "CustomSystemConf.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

#include "cc/assets/uielements/inc/HmiElementsSettings.h"
#include "cc/assets/uielements/inc/Vehicle2DOverlay.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/imgui/inc/imgui_manager.h"
#include "cc/views/planview/inc/PlanViewEnlargeCallback.h"

using pc::util::logging::g_AppContext;

using cc::views::planview::g_planViewEnlargeSettings;

#define CHECK_PORT_DATA(containerName, port, flag)                                                                     \
    auto(containerName) = (port).getData();                                                                            \
    if ((containerName) == nullptr)                                                                                    \
    {                                                                                                                  \
        (flag) = false;                                                                                                \
    }

namespace cc
{
namespace assets
{
namespace uielements
{

// static is for qac.
static osg::Vec2f calculateMaskSize(osg::Camera* f_camera, osg::Vec2f f_size, bool f_isHori)
{
    if (f_camera == nullptr)
    {
        return osg::Vec2f{}; // todo: maybe add more loggings.
    }
    const auto           viewport   = f_camera->getViewport();
    const vfc::float32_t iconWidth  = static_cast<vfc::float32_t>(viewport->width());
    const vfc::float32_t iconLength = iconWidth / f_size.x() * f_size.y();
    return (f_isHori) ? osg::Vec2f{iconWidth, iconLength} : osg::Vec2f{iconLength, iconWidth};
}

static osg::Vec2f calculateIconSize(osg::Camera* f_camera, bool f_isHori)
{
    if (f_camera == nullptr)
    {
        return osg::Vec2f{}; // todo: maybe add more loggings.
    }
    using pc::vehicle::g_mechanicalData;
    vfc::float64_t left = 0.0;
    vfc::float64_t right = 0.0;
    vfc::float64_t bottom = 0.0;
    vfc::float64_t top = 0.0;
    vfc::float64_t zFar = 0.0;
    vfc::float64_t zNear = 0.0;
    vfc::float64_t pixelPerMeter = 0.0;
    if (!f_camera->getProjectionMatrixAsOrtho(left, right, bottom, top, zNear, zFar))
    {
        XLOG_ERROR(g_AppContext, "Vehicle2DOverlay: Cannot retrieve projection matrix!");
        return osg::Vec2f{0.0f, 0.0f};
    }
    const auto           viewport            = f_camera->getViewport();
    const vfc::float64_t viewportWidth_Pixel = viewport->width();
    const vfc::float64_t viewportWidth_Meter = right - left;
    pixelPerMeter                      = viewportWidth_Pixel / viewportWidth_Meter;
    vfc::float32_t iconLength          = static_cast<vfc::float32_t>(pixelPerMeter) * g_mechanicalData->getLength();
    iconLength = std::floor(iconLength / g_Vehicle2DSettings->m_proportion.x() * g_Vehicle2DSettings->m_proportion.y());
    const vfc::float32_t iconWidth = iconLength * g_Vehicle2DSettings->m_aspectRatioOfVehicle;
    return (f_isHori) ? osg::Vec2f{iconWidth, iconLength} : osg::Vec2f{iconLength, iconWidth};
}

static vfc::float64_t calculateIconScaler(osg::Camera* f_camera)
{
    if (f_camera == nullptr)
    {
        return 0; // todo: maybe add more loggings.
    }
    using pc::vehicle::g_mechanicalData;
    vfc::float64_t left = 0.0;
    vfc::float64_t right = 0.0;
    vfc::float64_t bottom = 0.0;
    vfc::float64_t top = 0.0;
    vfc::float64_t zFar = 0.0;
    vfc::float64_t zNear = 0.0;
    vfc::float64_t pixelPerMeter = 0.0;
    if (!f_camera->getProjectionMatrixAsOrtho(left, right, bottom, top, zNear, zFar))
    {
        XLOG_ERROR(g_AppContext, "Vehicle2DOverlay: Cannot retrieve projection matrix!");
        return 0.f;
    }
    const auto           viewport            = f_camera->getViewport();
    const vfc::float64_t viewportWidth_Pixel = viewport->width();
    const vfc::float64_t viewportWidth_Meter = right - left;
    pixelPerMeter                      = viewportWidth_Pixel / viewportWidth_Meter;
    const vfc::float64_t iconLength          = static_cast<vfc::float32_t>(pixelPerMeter) * g_mechanicalData->getLength();
    return iconLength / g_Vehicle2DSettings->m_proportion.x();
}

void Vehicle2DOverlayManager::updateIconsSizePosition()
{
    using namespace cc::daddy;
    using namespace cc::core;

    //! hori screen hack
    // m_viewId = CustomViews::PLAN_VIEW_VEHICLE2D;
    const bool l_isVert =
        (m_veh2dViewId == CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D ||
         m_veh2dViewId == CustomViews::VERT_PARKING_PLAN_VIEW_VEHICLE2D ||
         m_veh2dViewId == CustomViews::VERTICAL_PLAN_VIEW_FULLSCREEN_VEHICLE2D);
    const bool l_isHori = (!l_isVert);
    const osg::Camera* const refView  = m_overlay->getReferenceView();
    if (refView == nullptr)
    {
        return;
    }
    const osg::Viewport* const viewport = refView->getViewport();
    if (viewport == nullptr)
    {
        return;
    }
    osg::Vec2f     l_positionMiddle;
    osg::Vec2f     l_positionFront;
    osg::Vec2f     l_positionRear;
    osg::Vec2f     l_position;
    osg::Vec2f     l_gearIconPosition;
    osg::Vec2f     l_maskPositionLeft;
    osg::Vec2f     l_maskPositionRight;
    osg::Vec2f     l_maskPositionBottom;
    osg::Vec2f     l_iconSize;
    osg::Vec2f     l_maskIconSize;
    osg::Vec2f     l_gearIconSize;
    const osg::Vec2f l_arrowIconSize;
    osg::Vec2f     l_maskScaleFactor;

    m_iconScaler = static_cast<vfc::float32_t>(calculateIconScaler(m_overlay->getReferenceView()));
    m_arrowOffsetPos = static_cast<float>(g_Vehicle2DSettings->m_arrowOffsetPos.y() * m_iconScaler);
    m_arrowGap       = static_cast<float>(g_Vehicle2DSettings->m_arrowGap.y()   * m_iconScaler);
    const vfc::float32_t gearWidth = (static_cast<vfc::float32_t>(l_isHori ? viewport->width() : viewport->height())) *
                               g_Vehicle2DSettings->m_gearSizePercentage;

    l_gearIconSize = osg::Vec2f{gearWidth, gearWidth};

    constexpr float maskAspectRatio = 2600.0f / 1728.0f; // mask picture size
    l_maskIconSize.y()    = static_cast<vfc::float32_t>(viewport->height());
    l_maskIconSize.x()    = l_maskIconSize.y() * maskAspectRatio;

    constexpr bool                                 l_canEnlarge    = true;
    const cc::views::planview::EnlargeSettings enlargeSettings = g_planViewEnlargeSettings->m_planViewEnlargeSettings;

    l_iconSize   = calculateIconSize(m_overlay->getReferenceView(), l_isHori);


    l_position = osg::Vec2f{
        static_cast<vfc::float32_t>(viewport->width() * 0.5f) + g_Vehicle2DSettings->m_proportion.x() *
                                                                     static_cast<vfc::float32_t>(m_iconScaler) *
                                                                     g_Vehicle2DSettings->m_planViewBodyPosOffset.x(),
        static_cast<vfc::float32_t>(viewport->height() * 0.5f) + g_Vehicle2DSettings->m_proportion.x() *
                                                                     static_cast<vfc::float32_t>(m_iconScaler) *
                                                                     g_Vehicle2DSettings->m_planViewBodyPosOffset.y()};
    l_positionMiddle = l_position;

    // auto frontOffsetFactor = enlargeSettings.m_enlargeFrontOffset * m_enlargeProgressPercentage;
    auto frontOffsetFactor = enlargeSettings.m_enlargeFrontOffset;
    l_positionFront = (l_isHori) ?
        osg::Vec2f{static_cast<vfc::float32_t>(viewport->width()  * (0.5 - frontOffsetFactor.x())), static_cast<vfc::float32_t>(viewport->height() * (0.5 - frontOffsetFactor.y()))} :
        osg::Vec2f{static_cast<vfc::float32_t>(viewport->width()  * (0.5 + frontOffsetFactor.x())), static_cast<vfc::float32_t>(viewport->height() * (0.5 + frontOffsetFactor.y()))};
    l_maskIconSize /= static_cast<vfc::float32_t>(m_enlargeFactor);
    l_gearIconSize /= static_cast<vfc::float32_t>(m_enlargeFactor);
    if (l_canEnlarge && (m_enlargeStatus == ENLARGE_FRONT))
    {
        l_position = l_positionFront;
    }

    // auto rearOffsetFactor = enlargeSettings.m_enlargeRearOffset * m_enlargeProgressPercentage;
    auto rearOffsetFactor = enlargeSettings.m_enlargeRearOffset;
    l_positionRear = (l_isHori) ?
        osg::Vec2f{static_cast<vfc::float32_t>(viewport->width()  * (0.5 - rearOffsetFactor.x())), static_cast<vfc::float32_t>(viewport->height() * (0.5 - rearOffsetFactor.y()))} :
        osg::Vec2f{static_cast<vfc::float32_t>(viewport->width()  * (0.5 + rearOffsetFactor.x())), static_cast<vfc::float32_t>(viewport->height() * (0.5 + rearOffsetFactor.y()))};
    l_maskIconSize /= static_cast<vfc::float32_t>(m_enlargeFactor);
    l_gearIconSize /= static_cast<vfc::float32_t>(m_enlargeFactor);
    if (l_canEnlarge && (m_enlargeStatus == ENLARGE_REAR))
    {
        l_position = l_positionRear;
    }

    if (m_startPositionDirty || (m_startPosition.x() == 0.0f && m_startPosition.y() == 0.0f))
    {
        switch (m_enlargeStatusPrevious)
        {
        case PlanViewEnlargeStatus::NO_ENLARGE:
        case PlanViewEnlargeStatus::ENLARGE_MIDDLE:
        {
            m_startPosition = l_positionMiddle;
            break;
        }
        case PlanViewEnlargeStatus::ENLARGE_FRONT:
        {
            m_startPosition = l_positionFront;
            break;
        }
        case PlanViewEnlargeStatus::ENLARGE_REAR:
        {
            m_startPosition = l_positionRear;
            break;
        }
        default:
        {
            // for qac
        }
        }
        m_startPositionDirty = false;
    }
    m_targetPosition = l_position;
    // smoothing is already done at planview enlarge callback, just update is enough
    m_currentPosition =
        m_startPosition * (1.0f - m_enlargeProgressPercentage) + m_targetPosition * m_enlargeProgressPercentage;
    l_gearIconPosition = m_currentPosition;
    l_gearIconPosition +=
        (l_isHori)
            ? osg::
                  Vec2f{0.0f, static_cast<vfc::float32_t>(viewport->height()) * (g_Vehicle2DSettings->m_gearOffsetPercentage)}
            : osg::Vec2f{
                  static_cast<vfc::float32_t>(viewport->width()) * (g_Vehicle2DSettings->m_gearOffsetPercentage), 0.0f};
    l_maskPositionLeft   = m_currentPosition;
    l_maskPositionRight  = m_currentPosition;
    l_maskPositionBottom = m_currentPosition;
    if (IMGUI_GET_CHECKBOX_BOOL("Settings", "Calibrate Vehicle 2D"))
    {
        l_maskScaleFactor.x() = IMGUI_GET_SLIDER_FLOAT("Settings", "MaskScaleHori", 0.1f, 3.0f);
        l_maskScaleFactor.y() = IMGUI_GET_SLIDER_FLOAT("Settings", "MaskScaleVert", 0.1f, 3.0f);
    }
    else if (m_isFullscreen)
    {
        l_maskScaleFactor = g_Vehicle2DSettings->m_fullscreenMaskScaleFactor;
    }
    else if (m_isImageInImage)
    {
        l_maskScaleFactor = g_Vehicle2DSettings->m_imageInImageScaleFactor;
    }
    else
    {
        l_maskScaleFactor = g_Vehicle2DSettings->m_normalMaskScaleFactor;
    }

    l_maskIconSize.x() *= l_maskScaleFactor.x();
    l_maskIconSize.y() *= l_maskScaleFactor.y();

    if (m_veh2dViewId == CustomViews::VERT_PARKING_PLAN_VIEW_VEHICLE2D ||
        m_veh2dViewId == CustomViews::HORI_PARKING_PLAN_VIEW_VEHICLE2D)
    {
        if (l_isHori)
        {
            l_maskIconSize *= g_Vehicle2DSettings->m_horiParkingMaskScaleFactor;
            l_maskPositionLeft += g_Vehicle2DSettings->m_horiParkingLeftMaskOffset;
            l_maskPositionRight += g_Vehicle2DSettings->m_horiParkingRightMaskOffset;
            l_maskPositionBottom += g_Vehicle2DSettings->m_horiParkingBottomMaskOffset;
        }
        else
        {
            l_maskIconSize *= g_Vehicle2DSettings->m_vertParkingMaskScaleFactor;
            l_maskPositionLeft += g_Vehicle2DSettings->m_vertParkingLeftMaskOffset;
            l_maskPositionRight += g_Vehicle2DSettings->m_vertParkingRightMaskOffset;
        }
    }

    IMGUI_LOG(
        "Vehicle2DOverlay",
        (refView->getName() + "_StartPosition").c_str(),
        std::to_string(m_startPosition.x()) + " " + std::to_string(m_startPosition.y()));
    IMGUI_LOG(
        "Vehicle2DOverlay",
        (refView->getName() + "_CurrentPosition").c_str(),
        std::to_string(m_currentPosition.x()) + " " + std::to_string(m_currentPosition.y()));
    IMGUI_LOG(
        "Vehicle2DOverlay",
        (refView->getName() + "_TargetPosition").c_str(),
        std::to_string(m_targetPosition.x()) + " " + std::to_string(m_targetPosition.y()));
    IMGUI_LOG("Vehicle2DOverlay", (refView->getName() + "_EnlargePercentage").c_str(), m_enlargeProgressPercentage);

    // clang-format off
    //! Vehicle
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_BODY_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewBodyWithoutDoorPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_HOOD_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewHoodPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_TRUNK_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTrunkPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_TRUNK_CLOSE)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTrunkPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewFLPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_CLOSE)),  m_currentPosition, g_Vehicle2DSettings->m_planViewFLPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewFRPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_CLOSE)),  m_currentPosition, g_Vehicle2DSettings->m_planViewFRPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewRLPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_CLOSE)),  m_currentPosition, g_Vehicle2DSettings->m_planViewRLPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewRRPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_CLOSE)),  m_currentPosition, g_Vehicle2DSettings->m_planViewRRPosOffset, m_iconScaler, l_isHori); // PRQA S 3076

    //! Vehicle Transparent
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_BODY_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTransparentBodyWithoutDoorPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_HOOD_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTransparentHoodPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_TRUNK_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTransparentTrunkPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_TRUNK_CLOSE)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTransparentTrunkPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTransparentFLPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_CLOSE)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTransparentFLPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTransparentFRPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_CLOSE)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTransparentFRPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTransparentRLPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_CLOSE)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTransparentRLPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_OPEN)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTransparentRRPosOffset, m_iconScaler, l_isHori); // PRQA S 3076
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_Vehicle2DTransparentOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_CLOSE)),  m_currentPosition, g_Vehicle2DSettings->m_planViewTransparentRRPosOffset, m_iconScaler, l_isHori); // PRQA S 3076

    //! Door Masks
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_doorMasks.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_OPEN_MASK)), m_currentPosition, g_Vehicle2DSettings->m_planViewFrontLeftDoorOpenMaskPosOffset, m_iconScaler,  l_isHori);
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_doorMasks.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN_MASK)), m_currentPosition, g_Vehicle2DSettings->m_planViewFrontRightDoorOpenMaskPosOffset, m_iconScaler, l_isHori);
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_doorMasks.getIcon(VEHICLE2D_TRUNK_OPEN_MASK)), m_currentPosition, g_Vehicle2DSettings->m_planViewTrunkOpenMaskPosOffset, m_iconScaler, l_isHori);

    //! Gears
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_overlays.getIcon(GEAR_STATUS_D)),  m_currentPosition, osg::Vec2f{0.0f, 0.0f}, m_iconScaler, l_isHori);
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_overlays.getIcon(GEAR_STATUS_R)),  m_currentPosition, osg::Vec2f{0.0f, 0.0f}, m_iconScaler, l_isHori);
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_overlays.getIcon(GEAR_STATUS_P)),  m_currentPosition, osg::Vec2f{0.0f, 0.0f}, m_iconScaler, l_isHori);
    updateVehicleIconSizePosition( static_cast<CustomIcon*>(m_overlays.getIcon(GEAR_STATUS_N)),  m_currentPosition, osg::Vec2f{0.0f, 0.0f}, m_iconScaler, l_isHori);

    // clang-format on
}

void Vehicle2DOverlayManager::updateIconSizePosition(
    cc::assets::uielements::CustomIcon* f_icon,
    const osg::Vec2f&                   f_iconSize,
    const osg::Vec2f&                   f_iconCenter,
    const osg::Vec2f&                   /*f_iconPosOffset*/,
    const osg::Vec2f&                   /*f_iconScale*/,
    bool                                f_isHoriView)
{
    if (f_icon == nullptr)
    {
        return;
    }
    f_icon->setHorizontalScreen(f_isHoriView);
    const osg::Vec2f l_iconPos  = f_iconCenter;
    const osg::Vec2f l_iconSize = f_iconSize;
    f_icon->setPosition(l_iconPos, pc::assets::Icon::UnitType::Pixel);
    f_icon->setSize(l_iconSize, pc::assets::Icon::UnitType::Pixel);
}

void Vehicle2DOverlayManager::updateVehicleIconSizePosition(
    cc::assets::uielements::CustomIcon* f_icon,
    const osg::Vec2f&                   f_iconCenter,
    const osg::Vec2f&                   f_iconPosOffset,
    vfc::float64_t                      f_iconScaler,
    bool                                f_isHoriView)
{
    if (f_icon == nullptr)
    {
        return;
    }
    f_icon->setHorizontalScreen(f_isHoriView);
    osg::Vec2f l_iconOriginSize = f_icon->getIconSize();
    const osg::Vec2f l_iconSize       = osg::Vec2f{
        l_iconOriginSize.x() * static_cast<vfc::float32_t>(f_iconScaler),
        l_iconOriginSize.y() * static_cast<vfc::float32_t>(f_iconScaler)};

    const osg::Vec2f l_iconOffset = osg::Vec2f{
        g_Vehicle2DSettings->m_proportion.x() * static_cast<vfc::float32_t>(f_iconScaler) * f_iconPosOffset.x(),
        g_Vehicle2DSettings->m_proportion.x() * static_cast<vfc::float32_t>(f_iconScaler) * f_iconPosOffset.y()};
    const osg::Vec2f l_iconPos = f_iconCenter + l_iconOffset;

    f_icon->setPosition(l_iconPos, pc::assets::Icon::UnitType::Pixel);
    f_icon->setSize(l_iconSize, pc::assets::Icon::UnitType::Pixel);
}

} // namespace uielements
} // namespace assets
} // namespace cc
