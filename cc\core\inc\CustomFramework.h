//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomFramework.h
/// @brief
//=============================================================================

#ifndef CC_CORE_CUSTOMFRAMEWORK_H
#define CC_CORE_CUSTOMFRAMEWORK_H

#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "pc/svs/core/inc/Framework.h"
#include "cc/target/common/inc/freeparking_types.h"
#include "cc/target/common/inc/commonInterface.h"

namespace cc
{
namespace core
{

class ViewModeStateTransitionManager;

//======================================================
// CustomFramework
//------------------------------------------------------
/// Framework for Customer Projects
/// Bind the communication channels for certain
/// behaviors and manage the communication through these
/// <AUTHOR>
//======================================================
class CustomFramework : public pc::core::Framework
{
public:

  // Constructor
  CustomFramework(pc::core::Scene* f_pScene);

  void preFrame() override;

  void postFrame() override;

  bool isImguiEnabled();

  virtual cc::core::CustomFramework* asCustomFramework()
  {
    return this;
  }

  virtual bool init(osg::GraphicsContext* f_pGc);

  // Bind communication channels for certain behaviors
  ::daddy::TLatestReceiverPort <cc::daddy::SocketCmdDaddy_t>                   m_socketCmdReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::PIVI_DayNightThemeReq_t>            m_dayNightModeReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::DippedBeamStateDaddy>               m_alsDippedBeamStateReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::TrailerSvsDaddy>                    m_trailerSvsReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::VMStateDaddy_t>                     m_ViewModeState_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::DistanceToStopDaddy>                m_distanceToStop_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::StopLineLocationDaddy>              m_stopLineLocation_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::AutomaticParkingDaddy>              m_automaticParking_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::PIVI_ManualVideoSetupReq_t>         m_piviManualVideoSetup_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::NFSM_MovementDirectionUpdate_t>     m_movementDirection_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::PowerModeDaddy_t>                   m_powerMode_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::FCTARightDaddy_t>                   m_FCTARight_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::FCTALeftDaddy_t>                    m_FCTALeft_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::RCTALeftDaddy_t>                    m_RCTALeft_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::RCTARightDaddy_t>                   m_RCTARight_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::VRSwitchSVMDaddy_t>                 m_VRSwitchSVM_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::FreeparkingParkable_t>              m_FreeParkingParkable_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::FCP_SVMButtonPressed_t>             m_FCP_SVMButtonPressed_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::CcfDaddy>                           m_CcfReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::CustomVehicleLightsDaddy>           m_VehicleLightsReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::NFSM_ViewBufferStatus_t >           m_NFSM_ViewBufferStatus_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::GbcVehicleTransparency_t >          m_GbcVehicleTransparency_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::GbcWheelTransparency_t >            m_GbcWheelTransparency_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::AebVmcOpModeDaddy_t>                m_aebVmcOpMode_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::C2WCalibStatusDaddy_t>              m_camCalibrationStatus_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::NormalCalibrationDaddy>             m_CpcNorminalCalibration_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::HUoverlayDistReqDaddy_t >           m_ObjectOverlayReq_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::HmiData_Daddy>                      m_hmiDataReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::HUCameraCommandsDaddy>              m_HUCameraCommandDaddyReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::HUtouchEvenTypeDaddy_t>             m_HUTouchTypeReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::HUTwoFingerTouchEvenTypeDaddy_t>    m_HUTwoFingerTouchTypeReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkStatusDaddy_t>                  m_parkHmiParkingStatusReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkTypeDaddy_t>                    m_parkHmiParkingTypeReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkParkngTypeSeld_t>               m_parkHmiParkParkngTypeSeldReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkAPAPARKMODE_t>                  m_parkHmiParkAPAPARKMODEReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkTypeVariantDaddy_t>             m_parkHmiParkingTypeVariantReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkTypeSelectedStDaddy_t>          m_parkHmiParkingTypeSelectedReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::SettingAutoCamStDaddy_t>            m_AutoCamActivateButtonPressedStsReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::SVSVehTransStsDaddy_t>              m_VehTransparenceStsFromSM_Receiver;
  ::daddy::TLatestReceiverPort <cc::daddy::SVSVehTransStsInternalDaddy_t>      m_VehTransparenceStsInternalReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::CpcOverlaySwitchDaddy_t>            m_CpcOverlaySwitch_Receiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkModeDaddy_t>                    m_parkHmiParkingModeReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkDriverIndDaddy_t>               m_parkHmiParkDriverIndReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkDriverIndExtDaddy_t>            m_parkHmiParkDriverIndExtReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkSideDaddy_t>                    m_parkHmiParkingSideReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkFunctionIndDaddy_t>             m_parkHmiParkingFuncIndReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkQuitIndDaddy_t>                 m_parkHmiParkingQuitIndReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkQuitIndExtDaddy_t>              m_parkHmiParkingQuitIndExtReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkRecoverIndDaddy_t>              m_parkHmiParkingRecoverIndReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkSpaceDaddy_t>                   m_parkHmiParkingSpaceReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkReqReleaseBtnDaddy_t>           m_parkHmiParkingReqReleaseBtnReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkFrontObjDaddy_t>                m_parkHmiParkingFrontObjReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkEndPositionSearchingDaddy_t>       m_parkEndPositionSearchingReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkFinalEndPositionDaddy_t>        m_parkFinalEndPositionReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkCurMoveTargetPositionDaddy_t>   m_parkCurMoveTargetPositionReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkCarmoveNumberDaddy_t>           m_parkCarmoveNumberReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkCartravelDistDesiredDaddy_t>    m_parkCartravelDistDesiredReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkIsLastMoveDaddy_t>              m_parkIsLastMoveReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::HUbasePlateReqDaddy_t>              m_basePlateReq_RecieverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::HUvehTransReq_t>                    m_vehTransReq_RecieverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::SVSDisplayedViewDaddy_t>            m_displayedView_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::SVSFreeModeStDaddy_t>               m_freeModeSt_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::SVSCurrentViewDaddy_t>              m_currentView_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::SVSViewModeStsDaddy_t>              m_viewModeStatus_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::PasButtonStatusDaddy_t>             m_pasButtonStatus_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkConfirmButtonStDaddy_t>         m_parkConfirmButton_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkPauseButton_t>                  m_parkPauseButton_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::PARkDirection_t>                    m_parkDirection_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::PasWarnToneDaddy_t>                 m_pasWarnToneStatus_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::PasStatusDaddy_t>                   m_pasStatus_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::SdwStatusDaddy_t>                   m_sdwStatus_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::SdwStatusFDaddy_t>                  m_sdwStatusF_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::SdwStatusFMDaddy_t>                 m_sdwStatusFM_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::SdwStatusRMDaddy_t>                 m_sdwStatusRM_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::SdwStatusRDaddy_t>                  m_sdwStatusR_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkingSpotsListDaddy>              m_parkingInSpotsReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkAPA_ParkSpace_t>                m_parkAPASlotsReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkAPA_ParkSpaceMark_t>            m_parkAPASlotsMarkReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkPSDirectionSelected_t>          m_parkPSDirectionSelectedReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkDriverIndSearchDaddy_t>         m_ParkDriverIndSearchReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::FreeparkingData_Daddy>              m_freeparkingReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::FreeparkingSocket_Daddy>            m_freeparkingSocketReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::FreeparkingRectInfo_Daddy>          m_freeparkingRectInfoReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::ParkFreeParkingActive_t>            m_freeparkingActiveReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::FreeParkingConfirmButtonStDaddy_t>  m_freeparkingConfirmButtonReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::FreeParkingSpaceTypeButtonStDaddy_t>m_freeparkingSpaceTypeReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::ParkRPAAvaliableDaddy_t>            m_parkHmiRPAAvailableReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ColorIndexDaddy>                    m_vehColorSts_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::SwVersionShowSwitchDaddy_t>         m_swVersionShowSwitch_ReceiverPort;
  ::daddy::TLatestReceiverPort <cc::daddy::SWInfoDaddy_t>                      m_swInfo_ReceiverPort;
  ::daddy::TLatestReceiverPort< cc::daddy::DriverSteeringWheelAngleDaddy_t >   m_driverSteeringWheelAngleReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::CustomVhmAbstRaw_t >                m_customResetGuidanceVhmReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::APG_VehiclePathDaddy >              m_vehiclePathAPGReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::DynamicGearActive_t >               m_dynamicGearStatus_ReceiverPort;
  ::daddy::TLatestReceiverPort< pc::daddy::UltrasonicDataDaddy >               m_customUltrasonicDataReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::DoorLockStsDaddy_t>                 m_DoorLockSts_ReceiverPort;
  ::daddy::TLatestReceiverPort< cc::daddy::TileSplineInterpolateArrayDaddy >   m_tileSplineInterpolateArrayReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::SolidBasePlateStateDaddy>           m_solidBaseplateState_ReceiverPort;
  ::daddy::TLatestReceiverPort < cc::daddy::ParkDispLowPolyModelStsDaddy_t >   m_ParkDispLowPolyModelStsReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::HURemoteScreenReqDaddy_t >          m_HURemoteScreenReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::NoticeRolling_t >                   m_noticeRolling_ReceiverPort;
  ::daddy::TLatestReceiverPort < cc::daddy::HUTransparencyModeDaddy_t >        m_HUvehTrans5xReceiver;
  ::daddy::TLatestReceiverPort < cc::daddy::HURadarWallButtonDaddy_t >         m_HURadarWallButtonReceiver;
  ::daddy::TLatestReceiverPort < cc::daddy::TrailerModeDaddy_t >               m_HUTrailerButtonReceiver;
  ::daddy::TLatestReceiverPort < cc::daddy::HUvehTransReq_t >                  m_HUvehTransCCReceiver;
  ::daddy::TLatestReceiverPort < cc::daddy::HUvehTransLevel_t >                m_HUvehTransLevelReceiver;
  ::daddy::TLatestReceiverPort < cc::daddy::HUFreemodeAngleDaddy_t >           m_HUfreemodeAngleAzimuthReceiver;
  ::daddy::TLatestReceiverPort < cc::daddy::HUFreemodeAngleDaddy_t >           m_HUfreemodeAngleElevationReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkSlotDaddy_t>                    m_parkSlotReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkSlotDaddy_t>                    m_parkSlotRefinedReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::FusionObjectDaddy_t >               m_fusionObject_ReceiverPort;
  ::daddy::TLatestReceiverPort< cc::daddy::SitOcpDaddy_t >                     m_sitOcp_ReceiverPort;
  ::daddy::TLatestReceiverPort< cc::daddy::PedestrianDaddy_t >                 m_pedestrianObj_ReceiverPort;
  ::daddy::TLatestReceiverPort< cc::daddy::EViewAnimationDaddy >               m_animationDaddy_ReceiverPort;

  // BYD Head unit input
  ::daddy::TLatestReceiverPort <cc::daddy::HURotateStatusDaddy_t>              m_HURotationPadReceiver;

  // // BYD Valout
  // ::daddy::TLatestReceiverPort< cc::daddy::SVSViewStateDaddy_t              >         m_SVSViewStateDaddy_Receiver;
  // ::daddy::TLatestReceiverPort< cc::daddy::SVSWorkModeDaddy_t               >         m_SVSWorkModeDaddy_Receiver;
  ::daddy::TLatestReceiverPort< cc::daddy::SVSRotateStatusDaddy_t           >         m_SVSRotateStatusDaddy_Receiver;
  ::daddy::TLatestReceiverPort< cc::daddy::DayNightThemeDaddy_t          >         m_dayNightThemeDaddy_Receiver;
  // ::daddy::TLatestReceiverPort< cc::daddy::SVSExpandedViewStateDaddy_t      >         m_SVSExpandedViewStateDaddy_Receiver;
  // ::daddy::TLatestReceiverPort< cc::daddy::SVSNewExpandedViewStateDaddy_t   >         m_SVSNewExpandedViewStateDaddy_Receiver;

  ::daddy::TLatestReceiverPort< cc::daddy::Pdm3DCruStsDaddy_t >                m_pdm3DCruSts_Receiver;
  ::daddy::TLatestReceiverPort< cc::daddy::PdmIntegtOpenStsDaddy_t >           m_pdmIntegtOpenSts_Receiver;
  ::daddy::TLatestReceiverPort< cc::daddy::PdmAutoCamStsDaddy_t >              m_pdmAutoCamSts_Receiver;
  ::daddy::TLatestReceiverPort< cc::daddy::PdmDynOverlayStsDaddy_t >           m_pdmDynOverlaySts_Receiver;
  ::daddy::TLatestReceiverPort< cc::daddy::PdmOverlayDistStsDaddy_t >          m_pdmOverlayDistSts_Receiver;
  ::daddy::TLatestReceiverPort< cc::daddy::PdmBasePlateStsDaddy_t >            m_pdmBasePlateSts_Receiver;
  ::daddy::TLatestReceiverPort< cc::daddy::PdmVehTransStsDaddy_t >             m_pdmVehTransSts_Receiver;

  ::daddy::TLatestReceiverPort< cc::daddy::DegradationFid_t >                  m_degradationFid_ReceiverPort;
  ::daddy::TLatestReceiverPort< cc::daddy::ImpostorTransparencyDaddy>          m_impostorTranspReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::PlanViewEnlargeStatusDaddy>         m_PlanViewEnlargeStatusReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::SideViewEnableStatusDaddy>          m_sideViewEnableStatusReceiver;
  ::daddy::TLatestReceiverPort< cc::daddy::TopViewEnableStatusDaddy>           m_topViewEnableStatusReceiver;
  ::daddy::TLatestReceiverPort<cc::daddy::HUDislayModeSwitchDaddy_t>           m_HUDislayModeSwitchDaddyReceiver;

  //! CPC
  // ::daddy::TLatestReceiverPort< cc::daddy::CpcCornerDetectionStatus_t >        m_cpcCornerDetection_Receiverport;
  // ::daddy::TLatestReceiverPort< cc::daddy::CpcStatus_st_t >                    m_cpcStatus_Receiverport;
  ::daddy::TLatestReceiverPort<cc::daddy::CpcToCpcWrapper_t>                   m_cpcToCpcWrapper_ReceiverPort;
  ::daddy::TLatestReceiverPort<cc::daddy::CpcWrapperToCpc_t>                   m_cpcWrapperToCpc_ReceiverPort;
  ::daddy::TLatestReceiverPort<cc::daddy::CpcToSvsOverlay_t>                   m_cpcToSvsOverlay_ReceiverPort;
  ::daddy::TLatestReceiverPort<cc::daddy::SvsOverlayToCpc_t>                   m_svsOverlayToCpc_ReceiverPort;

  //! Brake Pedal
  ::daddy::TLatestReceiverPort <cc::daddy::ParkBreakPedalBeenReleasedBf_t>     m_parkHmiParkBreakPedalBeenReleasedBfReceiver;
  ::daddy::TLatestReceiverPort <cc::daddy::ParkbrkPedlAppld_t>                 m_parkHmiParkParkbrkPedlAppldReceiver;

  //! ParkHmiToSvs
  ::daddy::TLatestReceiverPort <cc::daddy::ParkhmiToSvs_t>                     m_ParkhmiToSvs_ReceiverPort;

  //! RemoveDistortion
  ::daddy::TLatestReceiverPort <cc::daddy::RemoveDistortion_t>                 m_RemoveDistortion_ReceiverPort;

  //! ZoomLevel
  ::daddy::TLatestReceiverPort <cc::daddy::ZoomLevel_t>                        m_ZoomLevel_ReceiverPort;

  //! BirdEyeViewSwitch
  ::daddy::TLatestReceiverPort <cc::daddy::BirdEyeViewSwitch_t>                m_BirdEyeViewSwitch_ReceiverPort;

  //! SurroundViewAngle
  ::daddy::TLatestReceiverPort <cc::daddy::SurroundViewRotateAngle_t>          m_SurroundViewRotateAngle_ReceiverPort;

  //! Crab display
  ::daddy::TLatestReceiverPort <cc::daddy::CrabGuideline_t>                m_CrabGuideline_ReceiverPort{};

  //! GoldenEmblem
  ::daddy::TLatestReceiverPort <cc::daddy::GoldenEmblem_t>                m_GoldenEmblem_ReceiverPort{};


protected:
  // Destructor
  virtual ~CustomFramework();

  // Get the data from the ports and after it the cleanup should be called.
  virtual void updateReceiverPorts();
  virtual void cleanupReceiverPorts();

private:

  //! Copy constructor is not permitted.
  CustomFramework (const CustomFramework& other); // = delete
  //! Copy assignment operator is not permitted.
  CustomFramework& operator=(const CustomFramework& other); // = delete

  //! Handles view mode state events and translates them into program state changes
  osg::ref_ptr<cc::core::ViewModeStateTransitionManager> m_viewModeStateTransitionManager;
};

} // namespace core
} // namespace cc

#endif // CC_CORE_CUSTOMFRAMEWORK_H