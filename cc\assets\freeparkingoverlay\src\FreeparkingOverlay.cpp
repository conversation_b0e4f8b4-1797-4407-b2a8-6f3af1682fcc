//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname:
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EPF2-CN)
//  Department: CC-DA/EPF
//=============================================================================
/// @swcomponent
/// @file  FreeParkingOverlay.cpp
/// @brief
//=============================================================================

#include "cc/assets/freeparkingoverlay/inc/FreeparkingOverlay.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingRotateButton.h"

#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

#include "cc/imgui/inc/imgui_manager.h" // PRQA S 1060

#include "osgDB/ReadFile"
#include "osg/Depth"
#include "osg/Geometry"
#include "osg/Texture2D"
#include "osg/Vec2ui" // PRQA S 1060

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

static osg::ref_ptr<osg::Texture2D> g_blueTexture;
static osg::ref_ptr<osg::Texture2D> g_whiteTexture;
static osg::ref_ptr<osg::Texture2D> g_redTexture;
static osg::ref_ptr<osg::Texture2D> g_Ptexture;
static osg::ref_ptr<osg::Geode>     g_fpPlaneGeode;
static osg::ref_ptr<osg::StateSet>  g_sharedParkingSpotStateSet;

pc::util::coding::Item<FreeparkingSettings> g_freeparkingSettings("Freeparking");

static osg::Texture2D* createTexture(const std::string& f_filename)
{
    osg::Image* const l_image = osgDB::readImageFile(f_filename);
    if (l_image == nullptr)
    {
        // XLOG_ERROR_OS(g_AppContext) << "Failed to load image file" << f_filename << XLOG_ENDL;
        return nullptr;
    }
    // assert(l_image);
    osg::Texture2D* const l_texture = new osg::Texture2D(l_image);
    // set texture options
    l_texture->setDataVariance(osg::Object::STATIC);
    l_texture->setUnRefImageDataAfterApply(true);
    l_texture->setUseHardwareMipMapGeneration(true);
    l_texture->setResizeNonPowerOfTwoHint(false);
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP);
    l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP);
    return l_texture;
}

//!
//! FreeparkingPlane
//!
FreeparkingPlane::FreeparkingPlane()
    : m_visible(true) // PRQA S 2323 // PRQA S 4052
    , m_spotType(cc::daddy::ParkingSpot::PARKINGTYPE_PARALLEL) // PRQA S 2323
    , m_spotSide(cc::daddy::ParkingSpot::PARKINGSIDE_LEFT_SIDE_PSX) // PRQA S 2323
    , m_textureState(cc::assets::parkingspots::ParkingSpot::POTENTIAL) // PRQA S 2323
{
    if (!g_fpPlaneGeode.valid())
    {
        // build the plane for the parking slot itself
        osg::Geometry* const l_geometry = new osg::Geometry;
        l_geometry->setUseDisplayList(false);
        l_geometry->setUseVertexBufferObjects(true);

        osg::Vec3Array* const l_vertices = new osg::Vec3Array(4u);
        (*l_vertices)[0u]          = osg::Vec3f(
            -0.5f,
            -0.5f,
            g_freeparkingSettings->m_groundLevel); // create a high planar to make it placed upper than vehicle model
        (*l_vertices)[1u] =
            osg::Vec3f(0.5f, -0.5f, g_freeparkingSettings->m_groundLevel); // just set the height as 0.1 also be fine
        (*l_vertices)[2u] =
            osg::Vec3f(0.5f, 0.5f, g_freeparkingSettings->m_groundLevel); // 14.5 ~ maximum value of height that we can
                                                                          // we see thought planview camera
        (*l_vertices)[3u] = osg::Vec3f(-0.5f, 0.5f, g_freeparkingSettings->m_groundLevel);
        l_geometry->setVertexArray(l_vertices);

        osg::Vec2Array* const l_texCoords = new osg::Vec2Array(4u);
        (*l_texCoords)[0u]          = osg::Vec2f(1.0f, 0.0f);
        (*l_texCoords)[1u]          = osg::Vec2f(1.0f, 1.0f);
        (*l_texCoords)[2u]          = osg::Vec2f(0.0f, 1.0f);
        (*l_texCoords)[3u]          = osg::Vec2f(0.0f, 0.0f);
        l_geometry->setTexCoordArray(0u, l_texCoords);

        osg::Vec4Array* const l_colours = new osg::Vec4Array(1u);
        (*l_colours)[0u]          = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f);
        l_geometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);

        osg::Vec3Array* const l_normals = new osg::Vec3Array(1u);
        (*l_normals)[0u]          = osg::Z_AXIS;
        l_geometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);

        osg::DrawElementsUByte* const l_indices =
            new osg::DrawElementsUByte(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), 6u);
        (*l_indices)[0u] = 1u;
        (*l_indices)[1u] = 0u;
        (*l_indices)[2u] = 2u;
        (*l_indices)[3u] = 2u;
        (*l_indices)[4u] = 0u;
        (*l_indices)[5u] = 3u;
        l_geometry->addPrimitiveSet(l_indices); // PRQA S 3803

        g_fpPlaneGeode = new osg::Geode;
        g_fpPlaneGeode->addDrawable(l_geometry); // PRQA S 3803
    }
    osg::Group::addChild(g_fpPlaneGeode.get()); // PRQA S 3803
}

FreeparkingPlane::FreeparkingPlane(const FreeparkingPlane& f_other, const osg::CopyOp& f_copyOp)
    : osg::MatrixTransform(f_other, f_copyOp) // PRQA S 2323 // PRQA S 4052
    , m_visible(f_other.m_visible) // PRQA S 2323
    , m_spotType(cc::daddy::ParkingSpot::PARKINGTYPE_PARALLEL) // PRQA S 2323
    , m_spotSide(cc::daddy::ParkingSpot::PARKINGSIDE_LEFT_SIDE_PSX) // PRQA S 2323
    , m_textureState(cc::assets::parkingspots::ParkingSpot::POTENTIAL) // PRQA S 2323
{
}

FreeparkingPlane::~FreeparkingPlane() = default;

void FreeparkingPlane::update(FreeparkingOverlay* f_freeparkingOverlay, const bool /*f_isLeft*/)
{
    if (f_freeparkingOverlay == nullptr)
    {
        XLOG_ERROR(g_AppContext, "FreeparkingPlane::update - f_freeparkingOverlay is nullptr");
        return;
    }
    osg::StateSet* const l_stateSet = getOrCreateStateSet();

    osg::Uniform* const l_mipmapBiasUniform = l_stateSet->getOrCreateUniform("u_bias", osg::Uniform::FLOAT);
    l_mipmapBiasUniform->set(g_freeparkingSettings->m_mipmapBias); // PRQA S 3803

    const osg::Vec2f     l_size            = f_freeparkingOverlay->getSize();
    constexpr vfc::float32_t l_typeAngleOffset = 0.0f;

    getOrCreateStateSet()->setTextureAttribute(
        0u,
        f_freeparkingOverlay->getParkingPlanState() == ParkingPlaneState::DEFAULT     ? g_whiteTexture.get()
        : f_freeparkingOverlay->getParkingPlanState() == ParkingPlaneState::AVAILABLE ? g_blueTexture.get()
                                                                                      : g_redTexture.get(),
        osg::StateAttribute::OVERRIDE);

    f_freeparkingOverlay->setMatrix(
        osg::Matrix::rotate(osg::DegreesToRadians(l_typeAngleOffset), osg::Z_AXIS) * f_freeparkingOverlay->getMatrix());
    m_visible = f_freeparkingOverlay->getVisibility();
    const osg::Vec3f l_scale(l_size, 1.0f);
    setMatrix(osg::Matrix::scale(l_scale));
}

void FreeparkingPlane::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
    {
        if (m_visible)
        {
            osg::MatrixTransform::traverse(f_nv);
        }
    }
    else
    {
        osg::MatrixTransform::traverse(f_nv);
    }
}

//!
//! FrontPlane
//!
FrontPlane::FrontPlane()
    : FreeparkingPlane()
{
    if (g_Ptexture == nullptr)
    {
        g_Ptexture = createTexture(g_freeparkingSettings->m_pTexture);
    }

    const auto stateset = getOrCreateStateSet();
    stateset->setTextureAttribute(0u, g_Ptexture.get());
}

void FrontPlane::update(FreeparkingOverlay* f_freeparkingOverlay, const bool f_isLeft)
{
    if (f_freeparkingOverlay == nullptr)
    {
        XLOG_ERROR(g_AppContext, "FrontPlane::update - f_freeparkingOverlay is nullptr");
        return;
    }
    osg::StateSet* const l_stateSet = getOrCreateStateSet();

    vfc::float32_t l_typeAngleOffset = 0.0f;

    const vfc::uint32_t l_girdSizeIdx = f_freeparkingOverlay->getType(); // PRQA S 3803
    if (l_girdSizeIdx == cc::daddy::ParkingSpot::PARKINGTYPE_PERPENDICULAR)
    {
        l_typeAngleOffset = (f_isLeft) ? 90.0f : -90.0f;
    }

    // if (IMGUI_GET_CHECKBOX_BOOL("FreeparkingOverlay", "Add 90"))
    // {
    //   l_typeAngleOffset = 90.0f;
    // }
    // if (IMGUI_GET_CHECKBOX_BOOL("FreeparkingOverlay", "Add -90"))
    // {
    //   l_typeAngleOffset = -90.0f;
    // }

    osg::Uniform* const l_mipmapBiasUniform = l_stateSet->getOrCreateUniform("u_bias", osg::Uniform::FLOAT);
    l_mipmapBiasUniform->set(g_freeparkingSettings->m_mipmapBias); // PRQA S 3803
    m_visible = (f_freeparkingOverlay->getParkable() == cc::target::common::EFreeParkingSlotState::AVAILABLE) &&
                f_freeparkingOverlay->getVisibility();

    const osg::Vec3f l_scale(g_freeparkingSettings->m_pSize, 1.0f);
    setMatrix(osg::Matrix::scale(l_scale) * osg::Matrix::rotate(osg::DegreesToRadians(l_typeAngleOffset), osg::Z_AXIS));
}

//!
//! ParkingSpot
//!
FreeparkingOverlay::FreeparkingOverlay()
    : cc::assets::parkingspots::ParkingSpot() // PRQA S 4050
    , m_type{cc::daddy::ParkingSpot::PARKINGTYPE_INVALID}
    , m_selectionStateFwd{POTENTIAL}
    , m_selectionStateBwd{POTENTIAL}
    , m_position{osg::Vec2f{0.0f, 0.0f}}
    , m_middle{osg::Vec2f{0.0f, 0.0f}}
    , m_size{osg::Vec2f{0.0f, 0.0f}}
    , m_dirty{false}
    , m_visible{false}
    , m_rotateButtonVisible{}
    , m_angle{0.0f}
    , m_isLeft(true)
    , m_freeparkingPlane{}
    , m_rotateButton(nullptr)
    , m_frontPlane{}
    , m_parkingPlaneState(ParkingPlaneState::DEFAULT)
{
    setNumChildrenRequiringUpdateTraversal(1u);

    m_freeparkingPlane = new FreeparkingPlane;
    m_rotateButton     = new RotateButton;
    // m_frontPlane       = new FrontPlane;
    osg::MatrixTransform::addChild(m_freeparkingPlane); // PRQA S 3803
    osg::MatrixTransform::addChild(m_rotateButton);     // PRQA S 3803
    // osg::MatrixTransform::addChild(m_frontPlane);       // PRQA S 3803

    if (!g_sharedParkingSpotStateSet.valid())
    {
        g_sharedParkingSpotStateSet = new osg::StateSet;

        g_blueTexture  = createTexture(CONCAT_PATH_VEHICLE_MODEL(g_freeparkingSettings->m_blueTextureCross));
        g_whiteTexture = createTexture(CONCAT_PATH_VEHICLE_MODEL(g_freeparkingSettings->m_whiteTexture));
        g_redTexture   = createTexture(CONCAT_PATH_VEHICLE_MODEL(g_freeparkingSettings->m_redTexture));
        g_sharedParkingSpotStateSet->setTextureAttribute(0u, g_whiteTexture.get());
        g_sharedParkingSpotStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
        g_sharedParkingSpotStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);       // PRQA S 3143
        g_sharedParkingSpotStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
        g_sharedParkingSpotStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_FREEPARKING_OVERLAY, "RenderBin");

        pc::core::TextureShaderProgramDescriptor l_parkingSpotShader("advancedTex");
        g_sharedParkingSpotStateSet->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)
            ->set(osg::Vec4f(1.0f, 1.0f, 0.0f, 0.0f));                                              // PRQA S 3803
        g_sharedParkingSpotStateSet->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(1.0f); // PRQA S 3803
        g_sharedParkingSpotStateSet->getOrCreateUniform("u_bias", osg::Uniform::FLOAT)->set(0.0f);  // PRQA S 3803
        l_parkingSpotShader.apply(g_sharedParkingSpotStateSet.get());                               // PRQA S 3803
    }
    // m_ParkingSpotPlane->setStateSet(g_sharedParkingSpotStateSet.get());
    setStateSet(g_sharedParkingSpotStateSet.get());
}

FreeparkingOverlay::FreeparkingOverlay(const FreeparkingOverlay& f_other, const osg::CopyOp& f_copyOp)
    : cc::assets::parkingspots::ParkingSpot(f_other, f_copyOp) // PRQA S 4050
    , m_type{f_other.m_type}
    , m_selectionStateFwd{f_other.m_selectionStateFwd}
    , m_selectionStateBwd{f_other.m_selectionStateBwd}
    , m_position{f_other.m_position}
    , m_middle{f_other.m_middle}
    , m_size{f_other.m_size}
    , m_dirty{f_other.m_dirty}
    , m_visible{f_other.m_visible}
    , m_rotateButtonVisible{f_other.m_rotateButtonVisible}
    , m_angle{f_other.m_angle}
    , m_isLeft{f_other.m_isLeft}
    , m_mousePressed{f_other.m_mousePressed}
    , m_slitherActionType{f_other.m_slitherActionType}
    , m_parkable{f_other.m_parkable}
    , m_freeparkingPlane{f_other.m_freeparkingPlane}
    , m_rotateButton{f_other.m_rotateButton}
    , m_frontPlane{f_other.m_frontPlane}
    , m_parkingPlaneState{f_other.m_parkingPlaneState}
{
}

FreeparkingOverlay::~FreeparkingOverlay() = default;

void FreeparkingOverlay::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        if (m_dirty)
        {
            const osg::Vec3f l_position(m_position, g_freeparkingSettings->m_groundLevel);
            setMatrix(
                osg::Matrix::rotate(osg::DegreesToRadians(m_angle), osg::Z_AXIS) * osg::Matrix::translate(l_position));
            m_freeparkingPlane->update(this, m_isLeft);
            // m_frontPlane->update(this, m_isLeft);
            m_dirty = false;
        }
        m_rotateButton->update(this, m_isLeft);
    }
    osg::MatrixTransform::traverse(f_nv);
}

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc
