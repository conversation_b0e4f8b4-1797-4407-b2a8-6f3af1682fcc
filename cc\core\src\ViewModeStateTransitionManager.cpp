//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ViewModeStateTransitionManager.cpp
/// @brief
//=============================================================================

#include "ViewModeStateTransitionManager.h"
#include "ResizeManager.h"
#include "ViewModeToggle.h"

#include "cc/assets/common/inc/AnimationTimeSettings.h"
#include "cc/assets/common/inc/Bowl.h"
#include "cc/assets/fisheyetransition/inc/FisheyeTransitionOverlay.h"
#include "cc/assets/uielements/inc/Vehicle2DOverlay.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/ViewModeSynchronizerAnimation.h"
#include "cc/core/inc/ViewTransitions.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/imgui/inc/imgui_manager.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/views/parkview/inc/ParkView.h"
#include "cc/views/surroundview/inc/SurroundView.h"
#include "cc/virtcam/inc/HeadUnitHemisphereCameraUpdater.h"

#include "pc/generic/util/chrono/inc/chrono.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/animation/inc/Action.h"
#include "pc/svs/animation/inc/Animation.h"
#include "pc/svs/animation/inc/AnimationManager.h"
#include "pc/svs/animation/inc/BowlAnimationFactory.h"
#include "pc/svs/animation/inc/CameraAnimationFactory.h"
#include "pc/svs/animation/inc/Easing.h"
#include "pc/svs/animation/inc/FadeOutAnimation.h"
#include "pc/svs/animation/inc/SerialAnimation.h"
#include "pc/svs/animation/inc/ViewportAnimation.h"
#include "pc/svs/core/inc/Asset.h"
#include "pc/svs/core/inc/View.h"
#include "pc/svs/core/inc/ViewToggleAction.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/math/inc/FloatComp.h"

#include "osgGA/GUIEventAdapter"

namespace cc
{
namespace core
{

using cc::util::logging::g_animationContext;
using cc::util::logging::g_viewModeSMContext;
using pc::util::logging::g_AppContext;
std::atomic<bool> g_enableFadeAnimation(true);

static bool isCameraDegraded(pc::core::Framework* f_framework, pc::core::sysconf::Cameras f_camera)
{
    if (f_framework == nullptr)
    {
        return false;
    }
    const pc::daddy::CameraDegradationMaskDaddy* const l_degradationMaskDaddy =
        f_framework->m_degradationMaskReceiver.getData();
    if (nullptr != l_degradationMaskDaddy)
    {
        auto l_mask = l_degradationMaskDaddy->m_Data;
        return pc::daddy::isBitSet(static_cast<vfc::uint32_t>(f_camera), l_mask);
    }
    return false;
}

static vfc::float32_t getMorphingDuration(vfc::int32_t f_viewMode, pc::core::Framework* f_framework)
{
    // in case the view is degraded, skip the morphing to avoid showing incorrect images
    if ((isRearView(f_viewMode) && isCameraDegraded(f_framework, pc::core::sysconf::Cameras::REAR_CAMERA)) ||
        (isFrontView(f_viewMode) && isCameraDegraded(f_framework, pc::core::sysconf::Cameras::FRONT_CAMERA)))
    {
        return 0.0f;
    }
    else
    {
        return cc::assets::common::g_animationTimeSettings->m_morphingTime;
    }
}

static inline bool checkViewNotSurround(const pc::daddy::ViewMode& f_mode)
{
#if ENABLE_VERTICAL_MODE
    return (
        !isSurroundView(f_mode.m_prev) || !isSurroundView(f_mode.m_curr) || !isVertSurroundView(f_mode.m_prev) ||
        !isVertSurroundView(f_mode.m_curr));
#else
    return (!isSurroundView(f_mode.m_prev) || !isSurroundView(f_mode.m_curr));
#endif
}

static inline bool isSurroundViewDirectFlight(int f_viewId)
{
    switch (f_viewId)
    {
    case EScreenID_FRONT_BUMPER:
    case EScreenID_REAR_BUMPER:
    case EScreenID_ULTRA_WIDE_SURROUND_VIEW:
    {
        // case EScreenID_PLANETARY_VIEW:
        return true;
    }
    default:
    {
        return false;
    }
    }
}

//======================================================
// ViewAnimationAction
//------------------------------------------------------
/// A simple action which notifies daddy when a view animation is finished.
/// <AUTHOR> Jose (CC-DA/EAV3)
/// @ingroup ViewModeStateTransition
//======================================================
class ViewAnimationAction : public pc::animation::Action
{
public:
    ViewAnimationAction(vfc::int32_t f_screenId, cc::daddy::EAanimState f_state)
        : m_screenId(f_screenId)
        , m_state(f_state)
    {
    }

private:
    void onAction() override // PRQA S 1724
    {
        // Inform SM, so further manual requests can be handled
        if (cc::daddy::CustomDaddyPorts::sm_viewAnimationCompleted_SenderPort.isConnected())
        {
            cc::daddy::ViewAnimationCompleted_t& l_container =
                cc::daddy::CustomDaddyPorts::sm_viewAnimationCompleted_SenderPort.reserve();
            l_container.m_Data.m_screenId = m_screenId;
            l_container.m_Data.m_state    = m_state;
            cc::daddy::CustomDaddyPorts::sm_viewAnimationCompleted_SenderPort.deliver();
        }
    }

private:
    vfc::int32_t           m_screenId;
    cc::daddy::EAanimState m_state;
};

//======================================================
// CustomSerialAnimation
//------------------------------------------------------
/// Custom serial animation supporting cancellation.
/// ViewModeStateTransitionManager makes use of this class for camera transitions,
/// including camera flights. By cancelling this animation, the onEnd method
/// of each animation is ensured to be called, so all actions are completed.
/// This helps, among others, the SM to keep in sync with the animation states.
/// <AUTHOR> Jose (CC-DA/EAV3)
/// @ingroup ViewModeStateTransition
//======================================================
class CustomSerialAnimation : public pc::animation::SerialAnimation
{
public:
    CustomSerialAnimation(
        ViewModeStateTransitionManager* f_transitionManager,
        pc::animation::Animation*       f_a0,
        pc::animation::Animation*       f_a1,
        pc::animation::Animation*       f_a2,
        pc::animation::Animation*       f_a3)
        : pc::animation::SerialAnimation(f_a0, f_a1, f_a2, f_a3)
        , m_transitionManager(f_transitionManager)
    {
    }

    CustomSerialAnimation(
        ViewModeStateTransitionManager* f_transitionManager,
        pc::animation::Animation*       f_a0,
        pc::animation::Animation*       f_a1,
        pc::animation::Animation*       f_a2,
        pc::animation::Animation*       f_a3,
        pc::animation::Animation*       f_a4,
        pc::animation::Animation*       f_a5,
        pc::animation::Animation*       f_a6)
        : pc::animation::SerialAnimation(f_a0, f_a1, f_a2, f_a3, f_a4, f_a5, f_a6)
        , m_transitionManager(f_transitionManager)
    {
    }

    bool supportsCancellation() const override
    {
        return true;
    }

    void onEnd(bool f_canceled) override
    {
        if (true == f_canceled)
        {
            for (std::size_t i = 0u; i < m_animations_size; i++)
            {
                pc::animation::Animation* const animation = m_animations[i];
                if (!animation->hasFinished())
                {
                    animation->update(std::numeric_limits<vfc::float32_t>::max()); // PRQA S 3803
                }
            }
        }
        m_transitionManager->checkDelayedTransition();
    }

private:
    ViewModeStateTransitionManager* m_transitionManager;
};


//!
//! ViewModeStateTransitionManager
//!
ViewModeStateTransitionManager::ViewModeStateTransitionManager(pc::core::Framework* f_framework)
    : m_sequenceNumber(0u)
    , m_framework(f_framework)
    , m_isStandStillChanged(false)
    , m_lastParkingState(cc::target::common::PARK_Off)
    , m_parkingStandStill(cc::target::common::EVehMoveDir::UNKNOWN)
    , m_lastViewMode()
    , m_lastAnimatedViewMode()
    , m_lastScreenId(0u)
    , m_lastScreenIdStruct()
    , m_currentScreenIdStruct()
    , m_mutex()
{
    if (f_framework != nullptr)
    {
        setName("ViewModeStateTransitionManager");
        setIgnoreHandledEventsMask(osgGA::GUIEventAdapter::FRAME); //! we always want to receive frame events regardless if
                                                                //! marked as handled //PRQA S 3143
        m_scene           = m_framework->getScene();
        m_transitionQueue = m_framework->getAnimationManager()->createAnimationQueue(0u);

        m_surroundViewResizeManager = std::make_unique<ResizeManager>(
            m_scene->getView(cc::core::CustomViews::SURROUND_VIEW),
            cc::core::g_views->m_mainViewport,
            cc::core::g_views->m_usableCanvasViewport,
            cc::assets::common::g_animationTimeSettings->m_resizeDurationTime,
            isFullscreenPlanOrSurroundView);

        m_planViewResizeManager = std::make_unique<ResizeManager>(
            std::initializer_list<pc::core::View*>{
                m_scene->getView(cc::core::CustomViews::PLAN_VIEW),
                m_scene->getView(cc::core::CustomViews::PLAN_VIEW_VEHICLE2D),
                m_scene->getView(cc::core::CustomViews::PLAN_VIEW_USS_OVERLAYS),
            },
            cc::core::g_views->m_planViewport,
            cc::core::g_views->m_usableCanvasViewport,
            cc::assets::common::g_animationTimeSettings->m_resizeDurationTime,
            isFullscreenPlanOrSurroundView);
        m_planViewResizeManager->setEnableCameraUpdater(false);
    }
}

ViewModeStateTransitionManager::~ViewModeStateTransitionManager() = default;

bool ViewModeStateTransitionManager::handle(
    const osgGA::GUIEventAdapter& f_ea,
    osgGA::GUIActionAdapter& /* f_aa */) // PRQA S 1724
{
    if (osgGA::GUIEventAdapter::FRAME == f_ea.getEventType())
    {
        // XLOG_INFO(g_animationContext, "ViewModeStateTransitionManager - m_frameCount: " << m_frameCount++);
        // auto l_surroundView = m_framework->getScene()->getView(cc::core::CustomViews::SURROUND_VIEW);
        // if (l_surroundView != nullptr)
        // {
        //     static vfc::float64_t s_fovy;
        //     vfc::float64_t fovy, aspectRatio, zNear, zFar;
        //     l_surroundView->getProjectionMatrixAsPerspective(fovy, aspectRatio, zNear, zFar);
        //     if (s_fovy != fovy)
        //     {
        //         XLOG_INFO(g_animationContext, "SurroundView fovy: [" << m_frameCount << "]: " << fovy);
        //         s_fovy = fovy;
        //     }
        // }
        vfc::uint32_t l_displayMode = m_currentScreenId;
        if (m_framework->asCustomFramework()->m_sideViewEnableStatusReceiver.hasNewData())
        {
            // XLOG_ERROR(g_AppContext, "DebugGear - ViewModeStateTransitionManager - handle [" << m_frameCount << "]: m_framework->asCustomFramework()->m_sideViewEnableStatusReceiver.hasNewData()");
            m_currentScreenIdStruct.m_sideEnableStatus =
                m_framework->asCustomFramework()->m_sideViewEnableStatusReceiver.getData()->m_Data;
        }
        if (m_framework->asCustomFramework()->m_topViewEnableStatusReceiver.hasNewData())
        {
            // XLOG_ERROR(g_AppContext, "DebugGear - ViewModeStateTransitionManager - handle [" << m_frameCount << "]: m_framework->asCustomFramework()->m_topViewEnableStatusReceiver.hasNewData()");
            m_currentScreenIdStruct.m_topEnableStatus =
                m_framework->asCustomFramework()->m_topViewEnableStatusReceiver.getData()->m_Data;
        }
        if (m_framework->asCustomFramework()->m_HUDislayModeSwitchDaddyReceiver.hasData())
        {
            // XLOG_ERROR(g_AppContext, "DebugGear - ViewModeStateTransitionManager - handle [" << m_frameCount << "]: m_framework->asCustomFramework()->m_HUDislayModeSwitchDaddyReceiver.hasData()");
            l_displayMode = m_framework->asCustomFramework()->m_HUDislayModeSwitchDaddyReceiver.getData()->m_Data;
            // if( l_displayMode == EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL_WITHOUT_PLAN ||
            //     l_displayMode == EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL_WITHOUT_PLAN ||
            //     l_displayMode == EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL ||
            //     l_displayMode == EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL ||
            //     l_displayMode == EScreenID_WHEEL_FRONT_DUAL_ENLARGED)
            // {
            //     m_currentScreenIdStruct.m_wheelEnableStatus = cc::daddy::WHEELVIEW_FRONT_ENABLE;
            // }
            // else if( l_displayMode == EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL_WITHOUT_PLAN ||
            //     l_displayMode == EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL_WITHOUT_PLAN ||
            //     l_displayMode == EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL ||
            //     l_displayMode == EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL ||
            //     l_displayMode == EScreenID_WHEEL_REAR_DUAL_ENLARGED)
            // {
            //     m_currentScreenIdStruct.m_wheelEnableStatus = cc::daddy::WHEELVIEW_REAR_ENABLE;
            // }
            // else
            // {
            //     m_currentScreenIdStruct.m_wheelEnableStatus = cc::daddy::WHEELVIEW_DISABLE;
            // }
        }
        if (m_framework->asCustomFramework()->m_RemoveDistortion_ReceiverPort.hasNewData())
        {
            // XLOG_ERROR(g_AppContext, "DebugGear - ViewModeStateTransitionManager - handle [" << m_frameCount << "]: m_framework->asCustomFramework()->m_RemoveDistortion_ReceiverPort.hasNewData()");
            m_distortionOn = !(m_framework->asCustomFramework()->m_RemoveDistortion_ReceiverPort.getData()->m_Data);
        }

        if (m_framework->m_viewModeReceiver.hasNewData())
        {
            typedef pc::core::Framework::ViewModeReceiverPort::PortDataContainer_t ViewModeContainer;
            const ViewModeContainer& l_viewModeRequests = m_framework->m_viewModeReceiver.getData();
            for (auto l_request = l_viewModeRequests.begin(); l_request != l_viewModeRequests.end(); ++l_request)
            {
                const pc::daddy::ViewModeDaddy* const l_vm = (*l_request);
                m_framework->setCurrentScreenId(static_cast<vfc::uint32_t>(l_vm->m_Data.m_curr));
                m_currentScreenId  = static_cast<vfc::uint32_t>(l_vm->m_Data.m_curr);
                m_previousScreenId = static_cast<vfc::uint32_t>(l_vm->m_Data.m_prev);
                if (l_vm->m_Data.m_dumpPixmap)
                {
                    m_framework->dumpCurrentFrame();
                }
                // TODO: Figure out why is it called multiple times
                if (l_request + 1 == l_viewModeRequests.end())
                {

                    executeViewportSize();

                    static int s_count = 0;
                    XLOG_INFO(g_animationContext, getName() << ": counter " << ++s_count);
                    bool l_isRequireInstantToggle = false;
                    if (( m_currentScreenId == EScreenID_SINGLE_FRONT_NORMAL && m_previousScreenId == EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL) 
                       ||( m_currentScreenId == EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL && m_previousScreenId == EScreenID_SINGLE_FRONT_NORMAL)  
                    ||(isFullscreenPlanView(m_currentScreenId) || isFullscreenPlanView(m_previousScreenId))
                    )
                    {
                        // TODO Test wheelview <-> fullscreen 2d
                        l_isRequireInstantToggle = false;
                        // check instant toggle condition
                        const auto l_previousPlanviewType = checkPlanViewType(l_vm->m_Data.m_prev);
                        const auto l_currentPlanviewType = checkPlanViewType(l_vm->m_Data.m_curr);
                        const auto l_isPreviousEnlarge = isEnlargeFrontRearView(l_vm->m_Data.m_prev);
                        const auto l_isCurrentEnlarge = isEnlargeFrontRearView(l_vm->m_Data.m_curr);
                        l_isRequireInstantToggle = l_isRequireInstantToggle || ((l_previousPlanviewType != l_currentPlanviewType) && (
                            l_isPreviousEnlarge || l_isCurrentEnlarge
                        ));
                    }
                    else
                    {
                        // l_isRequireInstantToggle |= ( !isFullscreenPlanView(m_currentScreenId)) || (
                        // EScreenID_PLANETARY_VIEW == m_previousScreenId);
                        l_isRequireInstantToggle = l_isRequireInstantToggle ||
                            (isImageInImageView(m_currentScreenId) || isImageInImageView(m_previousScreenId));
                        l_isRequireInstantToggle = l_isRequireInstantToggle || (isWheelView(m_currentScreenId) &&
                                                    !isWheelView(m_previousScreenId)); // 2d/3d views -> wheelview
                        l_isRequireInstantToggle = l_isRequireInstantToggle || (!isWheelView(m_currentScreenId) &&
                                                    isWheelView(m_previousScreenId)); // wheelview -> 2d/3d views
                        l_isRequireInstantToggle = l_isRequireInstantToggle || (m_previousScreenId == EScreenID_NO_CHANGE);
                    }

                    if (l_isRequireInstantToggle)
                    {
                        m_instantToggle = true;
                    }
                    {
                        const std::lock_guard<std::mutex> lock(m_mutex);
                        handleViewModeStateChange(l_vm->m_Data);
                        m_lastViewMode = l_vm->m_Data;
                    }
                    // int64_t l_handleViewModeStateChange = chrono_ms();
                    // XLOG_INFO_OS(g_animationContext) << "[svs]: l_handleViewModeStateChange is "
                    //                            << static_cast<vfc::int32_t>(l_handleViewModeStateChange) <<
                    //                            XLOG_ENDL;
                }
            }
        }
        else
        {
            //Change the distortion even no view id change
            if (m_distortionOn != m_distortionOnPrevious)
            {
                m_distortionOnPrevious = m_distortionOn;
                if ((isFrontView(m_currentScreenId)) || (isRearView(m_currentScreenId)))
                {
                    m_transitionQueue->append(
                    new DefaultViewToggleAction(m_scene, m_lastViewMode, m_distortionOn, m_currentScreenIdStruct));
                }
            }

            // Main purpose is to update combine viewport
            // if ((m_currentScreenIdStruct != m_lastScreenIdStruct) && (l_displayMode == m_currentScreenId))
            // {
            //     handleViewportSize();
            //     m_lastScreenIdStruct = m_currentScreenIdStruct;
            //     pc::daddy::ViewMode l_viewModeToggle{
            //         m_lastViewMode.m_curr, m_lastViewMode.m_curr}; // only trigger off, on for current view id
            //     m_transitionQueue->append(
            //         new DefaultViewToggleAction(m_scene, l_viewModeToggle, m_distortionOn, m_currentScreenIdStruct));
            // }


        }
    }
    return false;
}

void ViewModeStateTransitionManager::executeViewportSize()
{
    //Do the viewport handling first and change the view id
    static bool alreadyHandled = false;
    if (EScreenID_APA_FRONT_HUBVIEW == m_currentScreenId)
    {
        if (!alreadyHandled)
        {
            handleViewportSize();
            alreadyHandled = true;
        }
    }
    else
    {
        alreadyHandled = false;
    }

    if ((m_currentScreenIdStruct != m_lastScreenIdStruct) || isWheelView(m_currentScreenId) || (isWheelView(m_previousScreenId)&&!isWheelView(m_currentScreenId)) /*&& (l_displayMode == m_currentScreenId)*/)
    {
        handleViewportSize();
        m_lastScreenIdStruct = m_currentScreenIdStruct;
        // pc::daddy::ViewMode l_viewModeToggle{
        //     m_lastViewMode.m_curr, m_lastViewMode.m_curr}; // only trigger off, on for current view id
        // m_transitionQueue->append(
        //     new DefaultViewToggleAction(m_scene, l_viewModeToggle, m_distortionOn, m_currentScreenIdStruct));
    }
}

void ViewModeStateTransitionManager::checkDelayedTransition()
{
    if (m_isTransitionDelayed)
    {
        const std::lock_guard<std::mutex> lock(m_mutex);
        m_isTransitionDelayed = false;
        if (m_lastAnimatedViewMode.m_curr != m_lastViewMode.m_curr)
        {
            m_lastViewMode.m_prev = m_lastAnimatedViewMode.m_curr;
            // XLOG_ERROR(g_AppContext, "DebugGear - ViewModeStateTransitionManager - checkDelayedTransition [" << m_frameCount << "]: m_isTransitionDelayed == true");
            handleViewModeStateChange(m_lastViewMode);
        }
    }
}

static void updateFullScreen3dVirtcam(pc::core::View* l_surroundView, bool isFullScreen)
{
    if (l_surroundView == nullptr)
    {
        return;
    }
    const auto l_cameraUpdater =
        dynamic_cast<cc::virtcam::HeadUnitHemisphereCameraUpdater*>(l_surroundView->getCameraUpdater());
    auto& l_virtcam = cc::virtcam::g_positions->getFullScreen3D();

    const vfc::float32_t l_minElevation = isFullScreen ? cc::virtcam::g_huhemisPara->m_fullscreenElevation
                                                       : cc::virtcam::g_hemisphereCameraParameters->m_minElevation;

    if (l_cameraUpdater != nullptr)
    {
        osg::Vec3f     l_center;
        vfc::float32_t l_elevation = 0.0f;
        vfc::float32_t l_azimuth = 0.0f;
        vfc::float32_t l_distance = 0.0f;
        // vfc::float32_t aspectRatio, zNear, zFar;

        l_cameraUpdater->setElevationRange(l_minElevation, cc::virtcam::g_hemisphereCameraParameters->m_maxElevation);
        l_cameraUpdater->projectCameraToParametricDomain(l_surroundView, l_center, l_elevation, l_azimuth, l_distance);

        const vfc::float32_t l_targetDistance   = l_cameraUpdater->getCurrentDistance();
        const osg::Vec3f     l_hemisphereCenter = l_cameraUpdater->getTargetHemisphereCenter();

        if (l_elevation < l_minElevation)
        {
            XLOG_INFO(g_animationContext,
                "updateFullScreen3dVirtCam: update elevation from " << osg::RadiansToDegrees(l_elevation) << " to "
                << osg::RadiansToDegrees(l_minElevation));
            l_elevation = l_minElevation;
        }

        const auto l_viewMatrix =
            l_cameraUpdater->getViewMatrix(l_elevation, l_azimuth, l_targetDistance, l_hemisphereCenter);

        // Set to the fullscreen 3d l_virtcam
        l_viewMatrix.getLookAt(l_virtcam.m_eye, l_virtcam.m_center, l_virtcam.m_up, 1.0f);
        // l_viewMatrix.getPerspective(l_virtcam.m_fovy, aspectRatio, zNear, zFar);
        XLOG_INFO(g_animationContext, "updateFullScreen3dVirtCam: updated virtualCam Fullscreen3D position: "
                                         << "Eye [" << l_virtcam.m_eye.x() << ", " << l_virtcam.m_eye.y() << ", "
                                         << l_virtcam.m_eye.z() << "], "
                                         << "Center [" << l_virtcam.m_center.x() << ", " << l_virtcam.m_center.y()
                                         << ", " << l_virtcam.m_center.z() << "], "
                                         << "Up [" << l_virtcam.m_up.x() << ", " << l_virtcam.m_up.y() << ", "
                                         << l_virtcam.m_up.z() << "]");
    }
}

// Remember the fullscreen 3d camera parameter when exit the full screen
static void updateNormal3dVirtcam(pc::core::View* l_surroundView)
{
    if (l_surroundView == nullptr)
    {
        return;
    }
    const auto l_cameraUpdater =
        dynamic_cast<cc::virtcam::HeadUnitHemisphereCameraUpdater*>(l_surroundView->getCameraUpdater());
    auto& l_virtcam = cc::virtcam::g_positions->getNormal3D();

    if (l_cameraUpdater != nullptr)
    {
        osg::Vec3f     l_center;
        vfc::float32_t l_elevation = 0.0f;
        vfc::float32_t l_azimuth = 0.0f;
        vfc::float32_t l_distance = 0.0f;

        const vfc::float32_t l_minElevation =  cc::virtcam::g_hemisphereCameraParameters->m_minElevation;

        l_cameraUpdater->setElevationRange(l_minElevation,
        cc::virtcam::g_hemisphereCameraParameters->m_maxElevation);
        l_cameraUpdater->projectCameraToParametricDomain(l_surroundView, l_center, l_elevation, l_azimuth, l_distance);

        const vfc::float32_t l_targetDistance   = l_cameraUpdater->getCurrentDistance();
        const osg::Vec3f     l_hemisphereCenter = l_cameraUpdater->getTargetHemisphereCenter();
        const auto           l_viewMatrix       = l_cameraUpdater->getViewMatrix(
            l_elevation, l_azimuth, l_targetDistance, l_hemisphereCenter);

        l_viewMatrix.getLookAt(l_virtcam.m_eye, l_virtcam.m_center, l_virtcam.m_up, 1.0f);
        XLOG_INFO(g_animationContext, "updateNormal3dVirtcam: updated virtualCam Normal3D position: "
                                         << "Eye [" << l_virtcam.m_eye.x() << ", " << l_virtcam.m_eye.y() << ", "
                                         << l_virtcam.m_eye.z() << "], "
                                         << "Center [" << l_virtcam.m_center.x() << ", " << l_virtcam.m_center.y()
                                         << ", " << l_virtcam.m_center.z() << "], "
                                         << "Up [" << l_virtcam.m_up.x() << ", " << l_virtcam.m_up.y() << ", "
                                         << l_virtcam.m_up.z() << "]");
    }
}

void ViewModeStateTransitionManager::handleViewModeStateChange(const pc::daddy::ViewMode& f_mode)
{
    XLOG_INFO(g_animationContext, "handleViewModeStateChange " << f_mode.m_curr);
    // using namespace pc::animation;
    osg::ref_ptr<pc::animation::Animation> animation = nullptr;
    osg::ref_ptr<pc::animation::Animation> l_a0 = nullptr;
    osg::ref_ptr<pc::animation::Animation> l_a1 = nullptr;
    osg::ref_ptr<pc::animation::Animation> l_a2 = nullptr;
    osg::ref_ptr<pc::animation::Animation> l_a3 = nullptr;
    osg::ref_ptr<pc::animation::Animation> l_a4 = nullptr;
    osg::ref_ptr<pc::animation::Animation> l_a5 = nullptr;
    osg::ref_ptr<pc::animation::Animation> l_a6 = nullptr;

    if (!m_transitionQueue->isEmpty())
    {
        // XLOG_ERROR(g_AppContext, "DebugGear - ViewModeStateTransitionManager - handleViewModeStateChange [" << m_frameCount << "]: !m_transitionQueue->isEmpty()");
        if (!m_transitionQueue->getCurrentAnimation()->hasFinished())
        {
            // XLOG_ERROR(g_AppContext, "DebugGear - ViewModeStateTransitionManager - handleViewModeStateChange [" << m_frameCount << "]: !m_transitionQueue->getCurrentAnimation()->hasFinished()");
            m_isTransitionDelayed = true;
            // XLOG_ERROR(g_AppContext, "DebugGear - ViewModeStateTransitionManager - handleViewModeStateChange [" << m_frameCount << "]: set m_isTransitionDelayed = true");
            return;
        }
    }

    m_lastAnimatedViewMode = f_mode;

    // Notify when started
    {
        ViewAnimationAction* const l_action = new ViewAnimationAction(f_mode.m_curr, cc::daddy::ANIM_ONGOING);
        l_a0                          = l_action;
    }

    // Case: entering or leaving kerb view
    animation = createBowlJumpPreAction(f_mode);
    if (animation != nullptr)
    {
        l_a1 = animation;
    }

    // Change initial camera position for SurroundView activations without animation (e.g. from rear-view)
    animation = createCameraJumpAction(f_mode);
    if (animation != nullptr)
    {
        if (l_a1.valid())
        {
            l_a2 = pc::animation::parallel(l_a1.get(), animation);
            l_a1 = nullptr;
        }
        else
        {
            l_a2 = animation;
        }
    }

    // Create transitioning animations
    animation = createViewTransition(f_mode);
    if (animation != nullptr)
    {
        l_a3 = animation;
    }

    // Create animations for actions (e.g. camera flights)
    const bool           l_isPreviousBigBowl = (getViewBowl(f_mode.m_prev) == EViewBowl::EDEFAULT_BIG_BOWL);
    const bool           l_isCurrentBigBowl  = (getViewBowl(f_mode.m_curr) == EViewBowl::EDEFAULT_BIG_BOWL);
    vfc::float32_t l_flightDuration    = 0.f;
    animation                          = createViewAction(f_mode, l_flightDuration);
    if (animation != nullptr)
    {
        const osg::ref_ptr<pc::animation::Animation> bowlAnimation = createBowlAnimation(f_mode, l_flightDuration);
        if (bowlAnimation != nullptr)
        {
            if (l_a1 != nullptr)
            {
                l_a1 = nullptr;
            }
            if (l_isCurrentBigBowl && !l_isPreviousBigBowl)
            {
                pc::animation::Animation* const l_parallel = pc::animation::serial(bowlAnimation, animation);
                l_a4                                 = l_parallel;
            }
            else if (!l_isCurrentBigBowl && l_isPreviousBigBowl)
            {
                pc::animation::Animation* const l_parallel = pc::animation::serial(animation, bowlAnimation);
                l_a4                                 = l_parallel;
            }
            else
            {
                pc::animation::Animation* const l_parallel = pc::animation::parallel(bowlAnimation, animation);
                l_a4                                 = l_parallel;
            }
        }
        else
        {
            l_a4 = animation;
        }
    }

    const auto l_surroundView = m_scene->getView(cc::core::CustomViews::SURROUND_VIEW);
    const auto l_frontView = m_scene->getView(cc::core::CustomViews::FRONT_VIEW_PANO);
    l_frontView->setProjectionResizePolicy(osg::Camera::VERTICAL);
    //! Resize animation begin
    // Resize from fullscreen 3d view or Resize to fullscreen 3d view
    if ((!isFullscreenPlanOrSurroundView(f_mode.m_curr) && !isImageInImageView(f_mode.m_curr)) &&
        (f_mode.m_prev == EscreenID_FULL_SCREEN_3D))
    {
        // Resize from fullscreen 3d view
        XLOG_INFO(g_animationContext
            , getName() << ": l_surroundView->setProjectionResizePolicy(osg::Camera::VERTICAL) as entering "
            << getScreenName(f_mode.m_curr));
        l_surroundView->setProjectionResizePolicy(osg::Camera::VERTICAL);
    }
    else if (
        (l_surroundView->getNodeMask() != 0) && (f_mode.m_curr == EscreenID_FULL_SCREEN_3D) &&
        (!isFullscreenPlanOrSurroundView(f_mode.m_prev) && !isImageInImageView(f_mode.m_prev)))
    {
        // Resize to fullscreen 3d view
        XLOG_INFO(g_animationContext
            , getName() << ": l_surroundView->setProjectionResizePolicy(osg::Camera::VERTICAL) as entering "
            << getScreenName(f_mode.m_curr));
        l_surroundView->setProjectionResizePolicy(osg::Camera::VERTICAL);
        // TODO: Nicer solution
        {
            const auto l_cameraUpdater =
                dynamic_cast<cc::virtcam::HeadUnitHemisphereCameraUpdater*>(l_surroundView->getCameraUpdater());
            if (l_cameraUpdater != nullptr)
            {
                // suspend to avoid jump because of the range change
                XLOG_INFO(g_animationContext, getName() << ": l_cameraUpdater->setSuspended(true) as entering "
                                                 << getScreenName(f_mode.m_curr));
                l_cameraUpdater->setSuspended(true);
            }
        }
    }
    else
    {
        XLOG_INFO(g_animationContext
            , getName() << ": l_surroundView->setProjectionResizePolicy(osg::Camera::HORIZONTAL) as entering "
            << getScreenName(f_mode.m_curr));
        l_surroundView->setProjectionResizePolicy(osg::Camera::HORIZONTAL);
    }


    if ( f_mode.m_curr == EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL && f_mode.m_prev == EScreenID_SINGLE_FRONT_NORMAL)
    {
        animation = m_singleViewResizeManager->getAnimation(f_mode, m_instantToggle);
        if (animation != nullptr)
        {
            l_a4 = pc::animation::parallel(l_a4, animation);
        }
        animation = l_a4;
        l_a4      = l_a3;
        l_a3      = animation;

    }       
    else if (f_mode.m_curr == EScreenID_SINGLE_FRONT_NORMAL && f_mode.m_prev == EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL)
    {
        animation = m_singleViewResizeManager->getAnimation(f_mode, m_instantToggle);
        if (animation != nullptr)
        {
            l_a4 = pc::animation::parallel(l_a4, animation);
        }
        animation = l_a4;
        l_a4      = l_a3;
        l_a3      = animation;
    }

    // // Going to fullscreen
    // if (isFullscreenPlanOrSurroundView(f_mode.m_curr) && !isFullscreenPlanOrSurroundView(f_mode.m_prev))
    // {
    //     updateFullScreen3dVirtcam(l_surroundView, true);
    //     XLOG_INFO(g_animationContext, getName() << ": Updating current SurroundView camera position as entering "
    //                                      << getScreenName(f_mode.m_curr) << " from " << getScreenName(f_mode.m_prev))
    //                                     ;

    //     if (isFullscreenPlanView(f_mode.m_curr))
    //     {
    //         animation = m_planViewResizeManager->getAnimation(f_mode, m_instantToggle);
    //     }
    //     else
    //     {
    //         animation = m_surroundViewResizeManager->getAnimation(f_mode, m_instantToggle);
    //     }

    //     if (animation != nullptr)
    //     {
    //         l_a4 = pc::animation::parallel(l_a4, animation);
    //     }

    //     // Toggle after, if entering fullscreen
    //     // Exchange animation a3 and a4
    //     animation = l_a4;
    //     l_a4      = l_a3;
    //     l_a3      = animation;
    // }
    // else
    // {
    //     animation = m_surroundViewResizeManager->getAnimation(f_mode, m_instantToggle);
    //     if (animation != nullptr)
    //     {
    //         l_a4 = pc::animation::parallel(l_a4, animation);
    //     }

    //     animation = m_planViewResizeManager->getAnimation(f_mode, m_instantToggle);
    //     if (animation != nullptr)
    //     {
    //         l_a4 = pc::animation::parallel(l_a4, animation);
    //     }

    //     // Exit Fullscreen
    //     if (!isFullscreenPlanOrSurroundView(f_mode.m_curr) && (EscreenID_FULL_SCREEN_3D == f_mode.m_prev))
    //     {
    //         XLOG_INFO(g_animationContext
    //             , getName() << ": Updating current SurroundView camera position as entering "
    //             << getScreenName(f_mode.m_curr) << " from " << getScreenName(f_mode.m_prev));
    //         updateNormal3dVirtcam(l_surroundView);
    //     }

    //     if (isFullscreenPlanOrSurroundView(f_mode.m_curr) && isFullscreenPlanOrSurroundView(f_mode.m_prev))
    //     {
    //         // Toggle after, if switching between fullscreen views
    //         animation = l_a4;
    //         l_a4      = l_a3;
    //         l_a3      = animation;
    //     }
    // }

    // Exit the normal surrounding view and keep the camera position
    // if (isSurroundViewAngleNeedKeep(f_mode.m_prev))
    // {
    //     updateNormal3dVirtcam(l_surroundView);
    // }

    //! Resize animation end

    // Notify when finished
    {
        ViewAnimationAction* const l_action = new ViewAnimationAction(f_mode.m_curr, cc::daddy::ANIM_FINISHED);
        l_a6                          = l_action;
    }

    l_a6 = pc::animation::serial(new VehicleEnlargeAction{m_framework->asCustomFramework(), f_mode}, l_a6);

    // l_a5 = pc::animation::parallel(l_a5, l_a3);
    // l_a3 = nullptr;

    // l_a0: ViewAnimationAction ANIM_ONGOING
    // l_a1: BowlJump
    // l_a2: SurroundView Camera Jump
    // l_a4: SurroundView Camera Flight Animation & Fullscreen resize animation
    // l_a3: View Toggle & Fade
    // l_a5: testing
    // l_a6: ViewAnimationAction ANIM_FINISHED

    if (m_instantToggle)
    {
        m_instantToggle = false;
    }

    const osg::ref_ptr<CustomSerialAnimation> l_customAnim =
        new CustomSerialAnimation(this, l_a0, l_a1, l_a5, l_a2, l_a3, l_a4, l_a6);
    m_transitionQueue->append(l_customAnim, false);
}

EViewBowl getViewBowl(vfc::int32_t f_mode)
{
    // handle only special cases where a medium or small bowls are needed. Rest use default.
    switch (f_mode)
    {
    // small bowl for kerb views
    case EScreenID_PERSPECTIVE_KL:
    case EScreenID_PERSPECTIVE_KR:
    case EScreenID_PLANETARY_VIEW:
    {
#if ENABLE_VERTICAL_MODE
    case EScreenID_VERT_PERSPECTIVE_PFR:
    case EScreenID_VERT_PERSPECTIVE_PRE:
    case EScreenID_VERT_PERSPECTIVE_RL:
    case EScreenID_VERT_PERSPECTIVE_RR:
    case EScreenID_VERT_PERSPECTIVE_FL:
    case EScreenID_VERT_PERSPECTIVE_FR:
    case EScreenID_VERT_PERSPECTIVE_FL_L:
    case EScreenID_VERT_PERSPECTIVE_FL_R:
    case EScreenID_VERT_PERSPECTIVE_FR_L:
    case EScreenID_VERT_PERSPECTIVE_FR_R:
    case EScreenID_VERT_PERSPECTIVE_RL_L:
    case EScreenID_VERT_PERSPECTIVE_RL_R:
    case EScreenID_VERT_PERSPECTIVE_RR_L:
    case EScreenID_VERT_PERSPECTIVE_RR_R:
#endif
        return ESMALL_BOWL;
    }

    // medium bowl for the rest of persp views
    case EScreenID_NORMAL3D_KEEP:
    case EScreenID_PERSPECTIVE_FL:
    case EScreenID_PERSPECTIVE_FR:
    case EScreenID_PERSPECTIVE_RL:
    case EScreenID_PERSPECTIVE_RR:
    case EScreenID_PERSPECTIVE_PRI:
    case EScreenID_PERSPECTIVE_PLE:
    case EScreenID_PERSPECTIVE_PFR:
    case EScreenID_PERSPECTIVE_PRE:
    case EScreenID_FRONT_BUMPER:
    case EScreenID_REAR_BUMPER:
    case EscreenID_FULL_SCREEN_3D:
    {
        return EMEDIUM_BOWL;
    }

    // default big bowl for all others
    case EScreenID_ULTRA_WIDE_SURROUND_VIEW:
    default:
    {
        return EDEFAULT_BIG_BOWL;
    }
    }
}

pc::worker::bowlshaping::BowlShaperData getBowlData(EViewBowl f_bowl)
{
    switch (f_bowl)
    {
    // small bowl
    case ESMALL_BOWL:
    {
        return cc::assets::common::g_bowlShaperSmall.data();
    }

    // medium bowl
    case EMEDIUM_BOWL:
    {
        return cc::assets::common::g_bowlShaperMedium.data();
    }

    // default bowl
    default:
    {
        return pc::worker::bowlshaping::g_bowlShaperDefault.data();
    }
    }
}

// TODO: Proper fade duration
constexpr float FADE_DURATION = 0.3f;

pc::animation::Animation* ViewModeStateTransitionManager::createViewTransition(const pc::daddy::ViewMode& f_mode) const
{
    pc::animation::Animation* l_animation =
        new DefaultViewToggleAction(m_scene, f_mode, m_distortionOn, m_currentScreenIdStruct);

    { // Animation between 2D & 3D front views
        pc::animation::Animation* l_fadeAnimation = nullptr;
        const bool l_enableFadeAnimation = g_enableFadeAnimation.load(std::memory_order_relaxed);
        if (!l_enableFadeAnimation)
        {
            XLOG_INFO(g_animationContext
                , getName() << ": FadeOutAnimation disabled ");
            g_enableFadeAnimation = true;
        }
        // From 2D to 3D
        else if ((EScreenID_SINGLE_FRONT_NORMAL == f_mode.m_prev) && (EScreenID_PERSPECTIVE_PRE == f_mode.m_curr))
        {
            XLOG_INFO(g_animationContext
                , getName() << ": FadeOutAnimation created as entering " << getScreenName(f_mode.m_curr) << " from "
                << getScreenName(f_mode.m_prev));
            l_fadeAnimation = new pc::animation::FadeOutAnimation(
                (m_distortionOn) ? m_scene->getView(cc::core::CustomViews::FRONT_VIEW_PANO)
                                 : m_scene->getView(cc::core::CustomViews::FRONT_VIEW),
                FADE_DURATION);
        }
        // From 3D to 2D
        else if ((EScreenID_PERSPECTIVE_PRE == f_mode.m_prev) && (EScreenID_SINGLE_FRONT_NORMAL == f_mode.m_curr))
        {
            XLOG_INFO(g_animationContext
                , getName() << ": FadeOutAnimation created as entering " << getScreenName(f_mode.m_curr) << " from "
                << getScreenName(f_mode.m_prev));
            l_fadeAnimation = new pc::animation::FadeOutAnimation(
                m_scene->getView(cc::core::CustomViews::SURROUND_VIEW), FADE_DURATION);
        }
        else
        {
        }

        if (l_fadeAnimation != nullptr)
        {
            // Do not disable the view immediately, but fade it out
            l_animation = pc::animation::serial(l_animation, l_fadeAnimation);
        }
    }

    // { // Animation between full screen 2D & 3D
        // pc::animation::Animation* l_fadeAnimation = nullptr;

        // // From 2D to 3D
        // if (isFullscreenPlanView(f_mode.m_prev) && isFullscreenSurroundView(f_mode.m_curr))
        // {
        //     XLOG_INFO_OS(g_animationContext)
        //         << getName() << ": FadeOutAnimation for PLAN_VIEW(S) created as entering "
        //         << getScreenName(f_mode.m_curr) << " from " << getScreenName(f_mode.m_prev) << XLOG_ENDL;
        //     l_fadeAnimation = new pc::animation::FadeOutAnimation(
        //         {
        //             m_scene->getView(cc::core::CustomViews::PLAN_VIEW),
        //             m_scene->getView(cc::core::CustomViews::PLAN_VIEW_VEHICLE2D),
        //             m_scene->getView(cc::core::CustomViews::PLAN_VIEW_USS_OVERLAYS),
        //         },
        //         FADE_DURATION);
        // }
        // From 2D Fullscreen to Planetary
        // if (isFullscreenPlanView(f_mode.m_prev) && (f_mode.m_curr == EScreenID_PLANETARY_VIEW))
        // {
        //     XLOG_INFO_OS(g_animationContext)
        //         << getName() << ": FadeOutAnimation for PLAN_VIEW(S) created as entering "
        //         << getScreenName(f_mode.m_curr) << " from " << getScreenName(f_mode.m_prev) << XLOG_ENDL;
        //     l_fadeAnimation = new pc::animation::FadeOutAnimation(
        //         {
        //             m_scene->getView(cc::core::CustomViews::PLAN_VIEW),
        //             m_scene->getView(cc::core::CustomViews::PLAN_VIEW_VEHICLE2D),
        //             m_scene->getView(cc::core::CustomViews::PLAN_VIEW_USS_OVERLAYS),
        //         },
        //         FADE_DURATION);
        // }
        // // From Planetary to 2D Fullscreen
        // else if ((f_mode.m_prev == EScreenID_PLANETARY_VIEW) && isFullscreenPlanView(f_mode.m_curr))
        // {
        //     XLOG_INFO_OS(g_animationContext)
        //         << getName() << ": FadeOutAnimation for PLANETARY_VIEW created as entering "
        //         << getScreenName(f_mode.m_curr) << " from " << getScreenName(f_mode.m_prev) << XLOG_ENDL;
        //     l_fadeAnimation = new pc::animation::FadeOutAnimation(
        //         m_scene->getView(cc::core::CustomViews::PLANETARY_VIEW), FADE_DURATION);
        // }
        // From 3D to 2D
        // if (isFullscreenSurroundView(f_mode.m_prev) && isFullscreenPlanView(f_mode.m_curr))
        // {
        //     XLOG_INFO_OS(g_animationContext)
        //         << getName() << ": FadeOutAnimation for SURROUND_VIEW created as entering "
        //         << getScreenName(f_mode.m_curr) << " from " << getScreenName(f_mode.m_prev) << XLOG_ENDL;
        //     l_fadeAnimation = new pc::animation::FadeOutAnimation(
        //         m_scene->getView(cc::core::CustomViews::SURROUND_VIEW), FADE_DURATION);
        // }

        // if (l_fadeAnimation)
        // {
        //     // Do not disable the view immediately, but fade it out
        //     l_animation = pc::animation::serial(l_animation, l_fadeAnimation);
        // }
    // }

    // XLOG_ERROR(g_AppContext, "DebugGear - ViewModeStateTransitionManager - createViewTransition [" << m_frameCount << "]: new ViewModeSynchronizerAnimation{}");
    const auto viewSyncAction = new ViewModeSynchronizerAnimation{m_framework->asCustomFramework()};
    if (viewSyncAction != nullptr)
    {
        l_animation = pc::animation::serial(l_animation, viewSyncAction);
    }

    return l_animation;
}

// Create animation when pervious view is not surround view
pc::animation::Animation*
ViewModeStateTransitionManager::createCameraJumpAction(const pc::daddy::ViewMode& f_mode) const
{
    // Setting of the camera position
    if (isSurroundView(f_mode.m_curr))
    {
        const cc::virtcam::VirtualCamEnum cameraPos = convertCameraModeToVirtualCameraPosition(f_mode.m_curr);
        assert(cameraPos < cc::virtcam::NUMBER_OF_VIRT_CAMS);

        pc::core::View* const l_surroundView = m_scene->getView(cc::core::CustomViews::SURROUND_VIEW);
        assert(l_surroundView);
        if (!isSurroundView(f_mode.m_prev))
        {
            // return l_surroundView->createCameraJumpAction(cameraPos);
            return m_framework->getCameraAnimationFactory()->createCameraJumpAction(
                l_surroundView, l_surroundView->getRenderManager(), cameraPos);
        }
    }
#if ENABLE_VERTICAL_MODE
    // Setting of the camera position
    if (isVertSurroundView(f_mode.m_curr))
    {
        cc::virtcam::VirtualCamEnum cameraPos = convertCameraModeToVirtualCameraPosition(f_mode.m_curr);
        assert(cameraPos < cc::virtcam::NUMBER_OF_VIRT_CAMS);

        pc::core::View* l_vertSurroundView = m_scene->getView(cc::core::CustomViews::VERTICAL_SURROUND_VIEW);
        assert(l_vertSurroundView);
        if (!isVertSurroundView(f_mode.m_prev))
        {
            // return l_surroundView->createCameraJumpAction(cameraPos);
            return m_framework->getCameraAnimationFactory()->createCameraJumpAction(
                l_vertSurroundView, l_vertSurroundView->getRenderManager(), cameraPos);
        }
    }
#endif
    return nullptr;
}

// Animation between to surround view
pc::animation::Animation* ViewModeStateTransitionManager::createViewAction(
    const pc::daddy::ViewMode& f_mode,
    vfc::float32_t&            f_animDuration) const
{
    if (m_instantToggle)
    {
        return nullptr;
    }
    const auto l_surroundView = m_scene->getView(cc::core::CustomViews::SURROUND_VIEW);
    const bool l_enableFov =
        (!isFullscreenPlanOrSurroundView(f_mode.m_curr) && !isFullscreenPlanOrSurroundView(f_mode.m_prev)) ||
        (isFullscreenPlanOrSurroundView(f_mode.m_prev) && isFullscreenPlanOrSurroundView(f_mode.m_curr));

    // Disable camera updater for static views
    const auto l_cameraUpdater = dynamic_cast<cc::virtcam::HeadUnitHemisphereCameraUpdater*>(
        m_scene->getView(cc::core::CustomViews::SURROUND_VIEW)->getCameraUpdater());
    if (l_cameraUpdater != nullptr)
    {
        // if ((f_mode.m_curr == EScreenID_PLANETARY_VIEW) || (f_mode.m_curr == EScreenID_ULTRA_WIDE_SURROUND_VIEW))
        if (f_mode.m_curr == EScreenID_ULTRA_WIDE_SURROUND_VIEW)
        {
            XLOG_INFO(g_animationContext
                , getName() << ": HeadUnitHemisphereCameraUpdater->setSuspended(true) as entering "
                << getScreenName(f_mode.m_curr));
            l_cameraUpdater->setSuspended(true);
        }
    }
    // Leaving full 3D to an other full screen
    if (isFullscreenPlanOrSurroundView(f_mode.m_curr) && (f_mode.m_prev == EscreenID_FULL_SCREEN_3D))
    {
        XLOG_INFO(g_animationContext, getName()
                                         << ": Updating current SurroundView camera elevation range as entering "
                                         << getScreenName(f_mode.m_curr) << " from " << getScreenName(f_mode.m_prev))
                                        ;
        updateFullScreen3dVirtcam(l_surroundView, true);
    }
    // Not fullscreen, restore elevation range
    else if (!isFullscreenPlanOrSurroundView(f_mode.m_curr))
    {
        if (l_cameraUpdater != nullptr)
        {
            XLOG_INFO(g_animationContext
                , getName() << ": Updating restore SurroundView camera elevation range as entering "
                << getScreenName(f_mode.m_curr));
            l_cameraUpdater->setElevationRange(
                cc::virtcam::g_hemisphereCameraParameters->m_minElevation,
                cc::virtcam::g_hemisphereCameraParameters->m_maxElevation);
        }
    }
    else
    {
    }

    // if (f_mode.m_curr == EScreenID_NORMAL3D_KEEP)
    // {
    //     XLOG_INFO(g_animationContext, getName() << ": Skip SurroundView Animation as entering " <<
    //     getScreenName(f_mode.m_curr)); return nullptr;
    // }

    if (isSurroundView(f_mode.m_curr) && isSurroundView(f_mode.m_prev))
    {
        const cc::virtcam::VirtualCamEnum cameraPos = convertCameraModeToVirtualCameraPosition(f_mode.m_curr);
        assert(cameraPos < cc::virtcam::NUMBER_OF_VIRT_CAMS);

        cc::views::surroundview::SurroundView* const l_surroundView = dynamic_cast<cc::views::surroundview::SurroundView*>(
            m_scene->getView(cc::core::CustomViews::SURROUND_VIEW)); // PRQA S 3077  // PRQA S 3400
        assert(l_surroundView);

        // Play back flight animation
        const cc::virtcam::VirtualCamEnum prevCameraPos = convertCameraModeToVirtualCameraPosition(f_mode.m_prev);
        assert(prevCameraPos < cc::virtcam::NUMBER_OF_VIRT_CAMS);
        XLOG_INFO(g_animationContext, getName() << ": new virtual camera position: "
                                         << cc::virtcam::CameraPositions::getVirtCamName(
                                                static_cast<vfc::uint32_t>(cameraPos))
                                         << " as entering " << getScreenName(f_mode.m_curr) << " from "
                                         << getScreenName(f_mode.m_prev));

        if (f_mode.m_curr == EScreenID_NORMAL3D_KEEP && f_mode.m_prev == EscreenID_FULL_SCREEN_3D)
        {
            // direct flight when going from fullscreen to normal 3D
            XLOG_INFO(g_animationContext
                , getName() << ": create surround view camera direct flight as entering "
                << getScreenName(f_mode.m_curr) << " from " << getScreenName(f_mode.m_prev));
            const auto l_animation =
                (l_surroundView->createNonQueuedCameraOrbitAnimation(cameraPos, f_animDuration, l_enableFov));
            return l_animation;
        }
        else if (isSurroundViewDirectFlight(f_mode.m_curr) || isSurroundViewDirectFlight(f_mode.m_prev))
        {
            XLOG_INFO(g_animationContext
                , getName() << ": create surround view camera direct flight as entering "
                << getScreenName(f_mode.m_curr) << " from " << getScreenName(f_mode.m_prev));
            const auto l_animation =
                (l_surroundView->createNonQueuedCameraOrbitAnimation(cameraPos, f_animDuration, l_enableFov));
            return l_animation;
        }
        else
        {
            XLOG_INFO(g_animationContext
                , getName() << ": create surround view camera orbit flight as entering "
                << getScreenName(f_mode.m_curr) << " from " << getScreenName(f_mode.m_prev));
            return l_surroundView->createNonQueuedCameraOrbitAnimation(cameraPos, f_animDuration, l_enableFov);
        }
    }
#if ENABLE_VERTICAL_MODE
    // Actions for vertical surround view
    if (isVertSurroundView(f_mode.m_curr) && isVertSurroundView(f_mode.m_prev))
    {
        cc::virtcam::VirtualCamEnum cameraPos = convertCameraModeToVirtualCameraPosition(f_mode.m_curr);
        assert(cameraPos < cc::virtcam::NUMBER_OF_VIRT_CAMS);

        cc::views::surroundview::SurroundView* l_vertSurroundView =
            dynamic_cast<cc::views::surroundview::SurroundView*>(
                m_scene->getView(cc::core::CustomViews::VERTICAL_SURROUND_VIEW)); // PRQA S 3077  //PRQA S 3400
        assert(l_vertSurroundView);

        // Play back flight animation
        cc::virtcam::VirtualCamEnum prevCameraPos = convertCameraModeToVirtualCameraPosition(f_mode.m_prev);
        assert(prevCameraPos < cc::virtcam::NUMBER_OF_VIRT_CAMS);
        return l_vertSurroundView->createNonQueuedCameraOrbitAnimation(cameraPos, f_animDuration, l_enableFov);
    }
#endif
    return nullptr;
}

// this action is used for a small to big bowl jump
pc::animation::Animation*
ViewModeStateTransitionManager::createBowlJumpPreAction(const pc::daddy::ViewMode& f_mode) const
{
    const EViewBowl l_prev = getViewBowl(f_mode.m_prev);
    const EViewBowl l_curr = getViewBowl(f_mode.m_curr);
    // same bowl before for prev and curr -> no action
    if (l_prev == l_curr)
    {
        return nullptr;
    }

    // If you reached this point, it means you need a bowl change - let's do it!
    // Get pointer to the bowl asset via framework - scene
    cc::core::CustomScene* const l_pCustomScene =
        dynamic_cast<cc::core::CustomScene*>(m_framework->getScene()); // PRQA S 3077  // PRQA S 3400
    cc::assets::common::Bowl* const l_pBowlAsset = dynamic_cast<cc::assets::common::Bowl*>(
        const_cast<pc::core::Asset*>(l_pCustomScene->getBowlAsset())); // PRQA S 3077  // PRQA S 3400
    pc::animation::BowlAnimationFactory* const bowlAnimationFactory = m_framework->getBowlAnimationFactory();

    // no flight is required -> direct jump
    if (checkViewNotSurround(f_mode))
    {
        XLOG_INFO(g_animationContext
            , getName() << ": checkViewNotSurround(f_mode) == TRUE, create bowl jump action as entering "
            << getScreenName(f_mode.m_curr) << " from " << getScreenName(f_mode.m_prev));
        return bowlAnimationFactory->createBowlJumpAction(
            l_pBowlAsset->getSV3DNode(), m_framework, getBowlData(l_curr));
    }

    return nullptr;
}

pc::animation::Animation*
ViewModeStateTransitionManager::createBowlAnimation(const pc::daddy::ViewMode& f_mode, vfc::float32_t f_duration) const
{
    // Bowl animation only needed if transitioning within surround views
    // if (checkViewNotSurround(f_mode))
    // {
    //     return nullptr;
    // }

    // Check previous and current bowls
    const EViewBowl l_prev = getViewBowl(f_mode.m_prev);
    const EViewBowl l_curr = getViewBowl(f_mode.m_curr);

    // same bowl before for prev and curr -> no action
    if (l_prev == l_curr)
    {
        return nullptr;
    }

    XLOG_INFO(g_animationContext, getName() << ": bowl size changed, create bowl animation to"
                                     << ((l_curr == EViewBowl::EDEFAULT_BIG_BOWL) ? "DEFAULT_BIG_BOWL"
                                         : (l_curr == EViewBowl::EMEDIUM_BOWL)    ? "MEDIUM_BOWL"
                                                                                  : "SMALL_BOWL")
                                     << " as entering " << getScreenName(f_mode.m_curr) << " from "
                                     << getScreenName(f_mode.m_prev));

    // If you reached this  point, it means you need an animated reshape. Let's do it!
    // Get pointer to the bowl asset via framework - scene
    cc::core::CustomScene* const l_pCustomScene =
        dynamic_cast<cc::core::CustomScene*>(m_framework->getScene()); // PRQA S 3077  // PRQA S 3400
    cc::assets::common::Bowl* const l_pBowlAsset = dynamic_cast<cc::assets::common::Bowl*>(
        const_cast<pc::core::Asset*>(l_pCustomScene->getBowlAsset())); // PRQA S 3077  // PRQA S 3400

    // Create a bowl animation from start to target, with a given duration
    pc::animation::BowlAnimationFactory* const bowlAnimationFactory = m_framework->getBowlAnimationFactory();
    return bowlAnimationFactory->createBowlAnimation(
        l_pBowlAsset->getSV3DNode(), m_framework, getBowlData(l_prev), getBowlData(l_curr), f_duration);
}

pc::animation::Animation* ViewModeStateTransitionManager::createToFishEyeTransition(
    pc::core::Framework*       f_framework,
    const pc::daddy::ViewMode& f_mode)
{
    if (f_framework == nullptr)
    {
        return nullptr;
    }
    using namespace pc::animation;
//    const vfc::int32_t              l_screenId           = (f_mode.m_prev);
    pc::core::Scene*          const l_scene              = f_framework->getScene();
    pc::core::View*           l_singleCamViewFront = nullptr;
    pc::core::View*           l_singleCamViewRear  = nullptr;
    pc::animation::Animation* l_frontAnimation     = nullptr;
    pc::animation::Animation* l_rearAnimation      = nullptr;
    if (isFrontView(f_framework->asCustomFramework()->getCurrentScreenId()))
    {
        l_singleCamViewFront = l_scene->getView(cc::core::CustomViews::FRONT_VIEW);
    }
    if (isRearView(f_framework->asCustomFramework()->getCurrentScreenId()))
    {
        l_singleCamViewRear = l_scene->getView(cc::core::CustomViews::REAR_VIEW);
    }
    using namespace cc::assets::fisheyetransition;
    if (l_singleCamViewFront != nullptr)
    {
        FisheyeTransitionOverlay* const l_transitionOverlayFS = dynamic_cast<FisheyeTransitionOverlay*>(
            l_singleCamViewFront->getAsset(cc::core::AssetId::EASSETS_FISHEYE_TRANSITION));
        if (l_transitionOverlayFS != nullptr)
        {
            const float l_duration = getMorphingDuration((f_mode.m_prev), f_framework);
            l_singleCamViewFront->setProjectionResizePolicy(osg::Camera::HORIZONTAL);
            if (l_duration == 0)
            {
                return nullptr;
            }
            l_frontAnimation =
                easeSineInOut(l_transitionOverlayFS->createTransitionAnimation(0.0f, 1.0f, l_duration, false));
        }
    }
    if (l_singleCamViewRear != nullptr)
    {
        FisheyeTransitionOverlay* const l_transitionOverlayFS = dynamic_cast<FisheyeTransitionOverlay*>(
            l_singleCamViewRear->getAsset(cc::core::AssetId::EASSETS_FISHEYE_TRANSITION));
        if (l_transitionOverlayFS != nullptr)
        {
            const float l_duration = getMorphingDuration((f_mode.m_prev), f_framework);
            l_singleCamViewRear->setProjectionResizePolicy(osg::Camera::HORIZONTAL);
            if (l_duration == 0)
            {
                return nullptr;
            }
            l_rearAnimation =
                easeSineInOut(l_transitionOverlayFS->createTransitionAnimation(0.0f, 1.0f, l_duration, false));
        }
    }
    return parallel(l_frontAnimation, l_rearAnimation);
}

pc::animation::Animation* ViewModeStateTransitionManager::createFromFishEyeTransition(
    pc::core::Framework*       f_framework,
    const pc::daddy::ViewMode& f_mode)
{
    if (f_framework == nullptr)
    {
        return nullptr;
    }
    using namespace pc::animation;
//    const vfc::int32_t              l_screenId           = (f_mode.m_curr);
    pc::core::Scene*          const l_scene              = f_framework->getScene();
    pc::core::View*           l_singleCamViewFront = nullptr;
    pc::core::View*           l_singleCamViewRear  = nullptr;
    pc::animation::Animation* l_frontAnimation     = nullptr;
    pc::animation::Animation* l_rearAnimation      = nullptr;
    if (isFrontView(f_framework->asCustomFramework()->getCurrentScreenId()))
    {
        l_singleCamViewFront = l_scene->getView(cc::core::CustomViews::FRONT_VIEW);
    }
    if (isRearView(f_framework->asCustomFramework()->getCurrentScreenId()))
    {
        l_singleCamViewRear = l_scene->getView(cc::core::CustomViews::REAR_VIEW);
    }
    using namespace cc::assets::fisheyetransition;
    if (l_singleCamViewFront != nullptr)
    {
        FisheyeTransitionOverlay* const l_transitionOverlayFS = dynamic_cast<FisheyeTransitionOverlay*>(
            l_singleCamViewFront->getAsset(cc::core::AssetId::EASSETS_FISHEYE_TRANSITION));
        if (l_transitionOverlayFS != nullptr)
        {
            const float l_duration = getMorphingDuration((f_mode.m_prev), f_framework);
            l_singleCamViewFront->setProjectionResizePolicy(osg::Camera::HORIZONTAL);
            if (l_duration == 0)
            {
                return nullptr;
            }
            l_frontAnimation =
                easeSineInOut(l_transitionOverlayFS->createTransitionAnimation(1.0f, 0.0f, l_duration, true));
        }
    }
    if (l_singleCamViewRear != nullptr)
    {
        FisheyeTransitionOverlay* const l_transitionOverlayFS = dynamic_cast<FisheyeTransitionOverlay*>(
            l_singleCamViewRear->getAsset(cc::core::AssetId::EASSETS_FISHEYE_TRANSITION));
        if (l_transitionOverlayFS != nullptr)
        {
            const float l_duration = getMorphingDuration((f_mode.m_prev), f_framework);
            l_singleCamViewRear->setProjectionResizePolicy(osg::Camera::HORIZONTAL);
            if (l_duration == 0)
            {
                return nullptr;
            }
            l_rearAnimation =
                easeSineInOut(l_transitionOverlayFS->createTransitionAnimation(1.0f, 0.0f, l_duration, true));
        }
    }
    return parallel(l_frontAnimation, l_rearAnimation);
}

} // namespace core
} // namespace cc
