//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_PARKOUT_BUTTON_H
#define CC_ASSETS_PARKOUT_BUTTON_H

#include "cc/assets/button/inc/Button.h"
#include "cc/assets/button/inc/ButtonGroup.h"

namespace pc
{
namespace core
{
class Framework;
} // namespace core

} // namespace pc

namespace cc
{
namespace assets
{
namespace button
{
namespace parkout
{

class ParkoutButtonSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(ParkoutButtonSettings) // PRQA S 2428
    {
        ADD_MEMBER(ButtonTextureSettings, textures);
        ADD_MEMBER(osg::Vec2f, horiPos);
        ADD_MEMBER(osg::Vec2f, vertPos);
        ADD_MEMBER(osg::Vec2f, size);
    }

    ButtonTextureSettings m_textures;
    osg::Vec2f            m_horiPos = osg::Vec2f(0.0f, 100.0f);
    osg::Vec2f            m_vertPos = osg::Vec2f(100.0f, 0.0f);
    osg::Vec2f            m_size    = osg::Vec2f(134.0f, 134.0f);
};

extern pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutFrontLeftCrossSettings;
extern pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutFrontLeftParallelSettings;
extern pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutFrontOutSettings;
extern pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutFrontRightCrossSettings;
extern pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutFrontRightParallelSettings;
extern pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutBackOutSettings;
extern pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutBackLeftCrossSettings;
extern pc::util::coding::Item<ParkoutButtonSettings> g_ParkoutBackRightCrossSettings;

class ParkoutButton : public Button
{
public:
    using ParkStatusType = cc::target::common::EPARKStatusR2L;
    using ParkSideType   = cc::target::common::EPARKSideR2L;

public:
    ParkoutButton(
        cc::core::AssetId              f_assetId,
        pc::core::Framework*           f_framework,
        const ParkoutButtonSettings*   f_settings,
        cc::target::common::EPocDirSel f_parkoutDirection,
        const std::string&             f_name,
        osg::Camera*                   f_referenceView = nullptr);

protected:
    void update() override;
    ParkoutButton(const ParkoutButton&) = delete;
    ParkoutButton& operator=(const ParkoutButton&) = delete;
    ~ParkoutButton()= default;
private:
    void onInvalid() override;
    void onUnavailable() override;
    void onAvailable() override;
    void onPressed() override;
    void onReleased() override;

private:
    pc::core::Framework*              m_framework;
    vfc::uint32_t                     m_modifiedCount = ~0u;
    const ParkoutButtonSettings*      m_settings;
    bool                              m_enable           = false;
    cc::target::common::EParkingMode  m_parkMode         = cc::target::common::EParkingMode::Invalid;
    cc::target::common::EApaStatus    m_apaStatus        = cc::target::common::EApaStatus::PassiveStandBy;
    cc::target::common::EParkingStage m_parkingStage     = cc::target::common::EParkingStage::Invalid;
    cc::target::common::EPocDirSel    m_parkoutDirection = cc::target::common::EPocDirSel::None;
};

class ParkoutButtonGroup : public ButtonGroup
{
public:
    ParkoutButtonGroup(cc::core::AssetId f_assetId, pc::core::View* f_referenceView, pc::core::Framework* f_framework);
    ParkoutButtonGroup(const ParkoutButtonGroup&) = delete;
    ParkoutButtonGroup& operator=(const ParkoutButtonGroup&) = delete;
    ~ParkoutButtonGroup()= default;
protected:
    void update() override;

private:
    pc::core::Framework* m_framework;
};

} // namespace parkout
} // namespace button
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PARKOUT_BUTTON_H