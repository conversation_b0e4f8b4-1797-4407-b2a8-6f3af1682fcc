//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomTaskManager.cpp
/// @brief
//=============================================================================

#include "cc/worker/core/inc/CustomTaskManager.h"
#include "cc/core/inc/CustomUltrasonic.h"
#include "pc/svs/worker/bowlshaping/inc/BowlShaperTask.h"
#include "pc/svs/worker/fusion/inc/FusionTask.h"
#include "pc/svs/worker/stitching/inc/StitchTask.h"
#include "cc/cpc/inc/task.hpp"
#ifdef TARGET_STANDALONE
#include "cc/test/inc/TestTask.hpp"
#endif

namespace cc
{
namespace worker
{
namespace core
{

CustomTaskManager::CustomTaskManager()
: m_customTaskUltrasonicDataReceiver() // PRQA S 2323 // PRQA S 4052
, m_HUDislayModeSwitchDaddy_ReceiverPort() // PRQA S 2323
, m_displayedView_Receiver() // PRQA S 2323
, m_svsOverlayToCpc_Receiver() // PRQA S 2323
,m_componentTestSwitch_Receiver() // PRQA S 2323
,m_HUCalibrationFlagCpcReceiver() // PRQA S 2323
,m_customTaskPasStatus_ReceiverPort() // PRQA S 2323
,m_airSuspensionHeight_ReceiverPort() // PRQA S 2323
// , m_runnable()
{
    //! connect daddy ports
    cc::daddy::CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort.connect(m_customTaskUltrasonicDataReceiver);
    cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.connect( m_displayedView_Receiver );
    cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.connect( m_HUDislayModeSwitchDaddy_ReceiverPort );
    cc::daddy::CustomDaddyPorts::sm_svsOverlayToCpc_SenderPort.connect( m_svsOverlayToCpc_Receiver );
    cc::daddy::CustomDaddyPorts::sm_componentTestSwitch_SenderPort.connect( m_componentTestSwitch_Receiver );
    cc::daddy::CustomDaddyPorts::sm_HUCalibrationStartDaddy_SenderPort.connect(m_HUCalibrationFlagCpcReceiver);
    cc::daddy::CustomDaddyPorts::sm_PasStatusDaddy_SenderPort.connect(m_customTaskPasStatus_ReceiverPort);
    cc::daddy::CustomDaddyPorts::sm_AirSuspensionHeight_SenderPort.connect(m_airSuspensionHeight_ReceiverPort);
}

CustomTaskManager::~CustomTaskManager()
{
    try
    {
        //! disconnect daddy ports
        cc::daddy::CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort.disconnect(m_customTaskUltrasonicDataReceiver);
        cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.disconnect( m_displayedView_Receiver );
        cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.disconnect( m_HUDislayModeSwitchDaddy_ReceiverPort );
        cc::daddy::CustomDaddyPorts::sm_svsOverlayToCpc_SenderPort.disconnect( m_svsOverlayToCpc_Receiver );
        cc::daddy::CustomDaddyPorts::sm_componentTestSwitch_SenderPort.disconnect( m_componentTestSwitch_Receiver );
        cc::daddy::CustomDaddyPorts::sm_HUCalibrationStartDaddy_SenderPort.disconnect(m_HUCalibrationFlagCpcReceiver);
        cc::daddy::CustomDaddyPorts::sm_PasStatusDaddy_SenderPort.disconnect(m_customTaskPasStatus_ReceiverPort);
        cc::daddy::CustomDaddyPorts::sm_AirSuspensionHeight_SenderPort.disconnect(m_airSuspensionHeight_ReceiverPort);
    }
    catch(...)
    {

    }
}


void CustomTaskManager::OnInit()
{
    pc::worker::core::TaskManager::OnInit();
#if USE_RADAR_WALL
    addTask(new pc::worker::fusion::FusionTask(new cc::core::CustomZoneLayout(true)));  // PRQA S 3803
#else
    addTask(new pc::worker::fusion::FusionTask(new cc::core::CustomZoneLayout()));  // PRQA S 3803
#endif
    addTask(new pc::worker::bowlshaping::BowlShaperTask());  // PRQA S 3803
    addTask(new pc::worker::stitching::StitchTask());  // PRQA S 3803
    addTask(new cc::cpc::CpcTask);
#ifdef TARGET_STANDALONE
    addTask(new cc::test::TestTask);
#endif

}


void CustomTaskManager::updateReceiverPorts()
{
    pc::worker::core::TaskManager::updateReceiverPorts();
    m_customTaskUltrasonicDataReceiver.update();
    m_displayedView_Receiver.update();
    m_HUDislayModeSwitchDaddy_ReceiverPort.update();
    m_svsOverlayToCpc_Receiver.update();
    m_componentTestSwitch_Receiver.update();
    m_HUCalibrationFlagCpcReceiver.update();
    m_customTaskPasStatus_ReceiverPort.update();
    m_airSuspensionHeight_ReceiverPort.update();
}


void CustomTaskManager::cleanupReceiverPorts()
{
    pc::worker::core::TaskManager::cleanupReceiverPorts();
    m_customTaskUltrasonicDataReceiver.cleanup();
    m_displayedView_Receiver.cleanup();
    m_HUDislayModeSwitchDaddy_ReceiverPort.cleanup();
    m_svsOverlayToCpc_Receiver.cleanup();
    m_componentTestSwitch_Receiver.cleanup();
    m_HUCalibrationFlagCpcReceiver.cleanup();
    m_customTaskPasStatus_ReceiverPort.cleanup();
    m_airSuspensionHeight_ReceiverPort.cleanup();
}

} // namespace core
} // namespace worker
} // namespace cc

